# 代码清理分析规范

## 概述
本规范定义了对Python项目进行代码清理分析的标准流程和要求，用于识别项目中未使用的文件，确保代码库的整洁性和可维护性。

## 分析目标
识别项目中未被使用的Python文件，为代码清理提供准确的依据，避免误删重要文件。

## 分析流程

### 1. 文件发现阶段
- **目标**: 列出指定目录下的所有 `.py` 文件
- **要求**: 
  - 递归扫描所有子目录
  - 包含隐藏文件（如果存在）
  - 记录完整的文件路径

### 2. 内容分析阶段
- **目标**: 分析每个Python文件的内容结构
- **分析要素**:
  - 类定义 (`class ClassName`)
  - 函数定义 (`def function_name`)
  - 全局变量定义
  - 模块级别的可调用对象
  - 装饰器定义
  - 异常类定义

### 3. 引用检查阶段
- **目标**: 在整个项目中搜索符号引用
- **检查范围**:
  - 直接导入 (`import module`, `from module import symbol`)
  - 动态导入 (`importlib.import_module`, `__import__`)
  - 字符串引用（配置文件、工厂模式、反射调用）
  - 文档字符串中的引用
  - 注释中的重要引用

### 4. 特殊情况处理

#### 4.1 保护性文件
以下类型的文件通常不应删除：
- `__init__.py` 文件（除非整个包不再使用）
- 入口点文件（`main.py`, `app.py`, `run_*.py`）
- 配置文件加载器
- 测试文件（除非对应的被测试代码已删除）

#### 4.2 间接引用检查
- **配置文件引用**: 检查YAML、JSON、INI等配置文件中的字符串引用
- **工厂模式**: 检查通过字符串名称动态创建对象的模式
- **插件系统**: 检查插件注册和发现机制
- **CLI命令**: 检查命令行工具的子命令定义
- **API路由**: 检查Web框架的路由定义

#### 4.3 框架特定检查
- **FastAPI/Flask**: 检查路由装饰器和依赖注入
- **Django**: 检查模型、视图、URL配置
- **Celery**: 检查任务定义和注册
- **SQLAlchemy**: 检查模型定义和关系

## 输出格式规范

### 分类标准
1. **UNUSED - CAN BE DELETED**: 确定未被使用的文件
2. **IN USE - DO NOT DELETE**: 确定正在使用的文件
3. **UNCERTAIN - NEEDS REVIEW**: 无法确定使用状态的文件

### 输出模板
```markdown
## 代码清理分析报告

### 📁 分析范围
- 目录: {target_directory}
- 扫描文件数: {total_files}
- 分析时间: {analysis_time}

### ❌ 未使用的文件 (可以删除)
- `{file_path}` - **UNUSED - CAN BE DELETED**
  - 原因: {detailed_reason}
  - 定义的符号: {symbols_list}
  - 最后修改: {last_modified}

### ✅ 正在使用的文件 (不要删除)
- `{file_path}` - **IN USE - DO NOT DELETE**
  - 原因: {usage_details}
  - 被引用位置: {reference_locations}
  - 关键符号: {important_symbols}

### ⚠️ 无法确定的文件 (需要人工审查)
- `{file_path}` - **UNCERTAIN - NEEDS REVIEW**
  - 原因: {uncertainty_reason}
  - 建议: {manual_check_suggestions}
```

## 分析质量保证

### 准确性要求
- **零误报**: 绝不能将正在使用的文件标记为未使用
- **详细说明**: 每个判断都必须提供清晰的理由
- **证据支持**: 对于"正在使用"的判断，必须提供具体的引用位置

### 安全检查清单
- [ ] 检查了所有可能的导入方式
- [ ] 检查了配置文件中的字符串引用
- [ ] 检查了动态加载和反射调用
- [ ] 检查了测试文件的对应关系
- [ ] 检查了框架特定的使用模式
- [ ] 验证了入口点和关键模块
- [ ] 考虑了插件和扩展机制

## 执行建议

### 分析前准备
1. 确保项目代码是最新版本
2. 了解项目的架构和主要模块
3. 识别项目使用的框架和库
4. 检查项目的配置文件和文档

### 分析过程
1. 从核心模块开始分析
2. 逐步扩展到边缘模块
3. 特别关注工具类和辅助模块
4. 最后分析测试和示例代码

### 结果验证
1. 对"未使用"的判断进行二次确认
2. 运行项目测试确保功能完整
3. 检查CI/CD流程是否受影响
4. 考虑向后兼容性要求

## 风险控制

### 删除前检查
- 创建完整的代码备份
- 记录删除文件的详细信息
- 准备回滚计划
- 通知相关开发人员

### 渐进式清理
- 分批次进行文件删除
- 每次删除后运行完整测试
- 监控生产环境的稳定性
- 保留删除日志和恢复方案
