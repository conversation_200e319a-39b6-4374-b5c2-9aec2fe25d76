"""
AutoGen兼容的基础Agent类
"""
import sys
import os
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from autogen import ConversableAgent
    from backend.config.logging_config import get_logger
    from backend.config import config_service
except ImportError as e:
    # 如果导入失败，提供更好的错误信息
    raise ImportError(f"无法导入必要的模块: {e}. 请确保项目依赖已正确安装。")


class AutoGenBaseAgent(ConversableAgent):
    """
    所有AutoGen Agent的基类，继承自autogen.AgentBase

    无状态设计：
    - 不存储任何用户相关的状态信息
    - 所有状态通过参数传递或从数据库实时获取
    - 支持多用户并发访问，无数据混乱风险
    """
    def __init__(self, name: str, **kwargs):
        """
        初始化Agent实例（无状态）
        """
        # 移除user_id参数，不再作为实例变量存储
        kwargs.pop('user_id', None)

        # 如果没有提供llm_config，则禁用AutoGen的默认LLM配置
        if 'llm_config' not in kwargs or kwargs['llm_config'] is None:
            kwargs['llm_config'] = False  # 使用False而不是None来完全禁用LLM
        super().__init__(name=name, **kwargs)

        # 移除状态相关的实例变量
        # self.state: Dict[str, Any] = {}  # 已移除
        # self.user_id = user_id  # 已移除

        self._oai_client = None  # 禁用内置的OpenAI客户端

        # 初始化日志记录器
        self.logger = get_logger(f"autogen_agent.{self.name}")
        self.logger.info(f"AutoGen {self.name} Agent已初始化（无状态模式）")

    def ensure_response_format(func):
        """
        装饰器：确保响应消息格式正确
        功能：
        1. 自动添加"正在处理您的请求"前缀
        2. 捕获异常并返回标准化错误格式
        3. 记录错误日志
        """
        async def wrapper(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
            # 调用原始方法
            try:
                response = await func(self, message, context)
                if not response.startswith("正在处理您的请求"):
                    response = "正在处理您的请求。\n" + response
                return response
            except Exception as e:
                error_msg = f"处理消息时出错: {str(e)}"
                self.logger.error(error_msg)
                template = config_service.get_config_value("message_templates.base_agent.processing_with_error")
                return template.format(error_msg=error_msg)
        return wrapper

    # 状态管理方法已移除，改为无状态设计
    # 所有状态信息通过SessionContext参数传递

    async def process_message(self, message: Dict[str, Any], session_context=None, **kwargs) -> Dict[str, Any]:
        """
        处理AutoGen格式的消息（需由子类实现）

        无状态设计：
        - 接收SessionContext参数而不是依赖实例变量
        - 所有状态信息通过参数传递
        - 支持多用户并发访问

        Args:
            message: 消息数据
            session_context: 会话上下文（SessionContext实例）
            **kwargs: 其他参数

        标准消息格式:
        {
            "content": "消息内容",
            "context": {},  # 上下文信息
            "type": "message_type",  # 消息类型
            "sender": "agent_name"   # 发送者
        }

        标准响应格式:
        {
            "status": "success|error",
            "content": "响应内容",
            "context": {},  # 更新后的上下文
            "type": "response_type"
        }

        注意事项:
        1. 子类实现必须处理所有可能的异常情况
        2. 应使用create_response方法创建标准响应
        3. 状态信息通过session_context参数获取
        4. 所有关键操作都应记录日志
        """
        raise NotImplementedError("子类必须实现process_message方法")

    def create_message(self, content: str, context: Optional[Dict[str, Any]] = None,
                       msg_type: str = "default", sender: str = None) -> Dict[str, Any]:
        """
        创建标准格式的消息

        Args:
            content: 消息内容
            context: 上下文信息
            msg_type: 消息类型
            sender: 发送者名称

        Returns:
            标准消息字典
        """
        return {
            "content": content,
            "context": context or {},
            "type": msg_type,
            "sender": sender or self.name
        }

    def create_response(self, content: str, context: Optional[Dict[str, Any]] = None,
                        status: str = "success", resp_type: str = "default") -> Dict[str, Any]:
        """
        创建标准格式的响应

        Args:
            content: 响应内容
            context: 上下文信息
            status: 状态(success/error)
            resp_type: 响应类型

        Returns:
            标准响应字典
        """
        return {
            "status": status,
            "content": content,
            "context": context or {},
            "type": resp_type
        }
