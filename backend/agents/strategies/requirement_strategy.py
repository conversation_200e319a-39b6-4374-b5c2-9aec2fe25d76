"""
需求收集策略实现

处理用户的业务需求，包括：
- 项目需求（网站、APP、系统开发等）
- 设计需求（海报、Logo、UI设计等）
- 咨询需求（技术咨询、方案咨询等）

优先级: 8 (高)
支持意图: business_requirement
"""

import logging
from typing import List, Dict, Any
import re
from backend.config import config_service

from ..decision_types import DecisionStrategy, AnalyzedContext, DecisionResult, create_decision_result
from ..unified_state_manager import ConversationState


class RequirementStrategy(DecisionStrategy):
    """需求收集策略实现"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 🔧 [配置化重构] 获取关键词加载器
        from backend.config.keywords_loader import get_keywords_loader
        self.keywords_loader = get_keywords_loader()

        # 🔧 [配置化重构] 从关键词配置加载需求关键词分类
        self.requirement_keywords = {
            "development": (
                self.keywords_loader.get_keywords("strategy_keywords.requirement_strategy.development") or
                ["网站", "app", "应用", "系统", "开发"]  # 最小化硬编码安全网
            ),
            "design": (
                self.keywords_loader.get_keywords("strategy_keywords.requirement_strategy.design") or
                ["海报", "logo", "设计", "UI", "界面"]  # 最小化硬编码安全网
            ),
            "consulting": (
                self.keywords_loader.get_keywords("strategy_keywords.requirement_strategy.consulting") or
                ["咨询", "方案", "建议", "规划", "策略"]  # 最小化硬编码安全网
            ),
            "content": (
                self.keywords_loader.get_keywords("strategy_keywords.requirement_strategy.content") or
                ["文案", "内容", "文章", "资料", "文档"]  # 最小化硬编码安全网
            )
        }

        # 🔧 [配置化重构] 从配置加载需求收集问题模板
        config_questions = self.config.get_config_value("strategy_templates.requirement_strategy.templates.collection_questions")
        self.collection_questions = config_questions or {
            # 最小化硬编码安全网
            "development": ["好的，我来帮您了解开发需求。请问您想开发什么类型的项目？"],
            "design": ["好的，我来帮您了解设计需求。请问您需要设计什么类型的作品？"],
            "consulting": ["好的，我来为您提供咨询服务。请问您遇到了什么问题？"],
            "content": ["好的，我来帮您了解内容需求。请问您需要什么类型的内容？"]
        }

        # 🔧 [配置化重构] 从配置加载需求确认模板
        self.confirmation_templates = (
            self.config.get_config_value("strategy_templates.requirement_strategy.templates.confirmation_templates") or
            ["我已经了解了您的基本需求，让我来帮您进一步梳理详细信息。"]  # 最小化硬编码安全网
        )

    @property
    def name(self) -> str:
        return "requirement_strategy"

    @property
    def supported_intents(self) -> List[str]:
        return ["business_requirement"]

    @property
    def priority(self) -> int:
        return 8  # 高优先级

    async def can_handle(self, context: AnalyzedContext) -> bool:
        """
        判断是否能处理当前上下文

        Args:
            context: 分析后的上下文

        Returns:
            bool: 是否能处理
        """
        # 1. 基于意图判断
        if context.intent == "business_requirement":
            return True

        # 2. 基于关键词判断
        message_lower = context.message.lower()
        for category, keywords in self.requirement_keywords.items():
            for keyword in keywords:
                if keyword in message_lower:
                    return True

        # 🔧 [配置化重构] 基于配置化表达模式判断
        requirement_patterns = (
            self.config.get_config_value("strategy_templates.requirement_strategy.patterns.requirement_patterns") or
            ["我想.*", "我需要.*", "帮我.*", "能否.*"]  # 最小化硬编码安全网
        )

        for pattern in requirement_patterns:
            if re.search(pattern, context.message):
                return True

        return False

    async def execute(self, context: AnalyzedContext) -> DecisionResult:
        """
        执行需求收集策略

        Args:
            context: 分析后的上下文

        Returns:
            DecisionResult: 决策结果
        """
        try:
            # 分析需求类型
            requirement_type = self._analyze_requirement_type(context)

            # 提取需求实体
            entities = self._extract_requirement_entities(context)

            # 确定下一步动作
            action, next_state = self._determine_next_action(context, requirement_type, entities)

            # 选择回复模板
            response_template = self._select_response_template(requirement_type, action)

            # 计算置信度
            confidence = await self.calculate_confidence(context)

            # 创建决策结果
            result = create_decision_result(
                action=action,
                confidence=confidence,
                intent=context.intent,
                emotion=context.emotion,
                response_template=response_template,
                next_state=next_state,
                strategy_name=self.name,
                metadata={
                    "requirement_type": requirement_type,
                    "extracted_entities": entities,
                    "analysis_confidence": confidence,
                    "message_complexity": self._assess_message_complexity(context)
                }
            )

            self.logger.info(f"需求收集策略执行成功: type={requirement_type}, action={action}")
            return result

        except Exception as e:
            self.logger.error(f"需求收集策略执行失败: {e}", exc_info=True)

            # 返回基础需求收集结果
            return create_decision_result(
                action="start_requirement_collection",
                confidence=0.7,
                intent="business_requirement",
                emotion="neutral",
                response_template="我来帮您了解需求详情，请告诉我您的具体要求。",
                next_state=ConversationState.COLLECTING_INFO,
                strategy_name=self.name,
                metadata={"error": str(e), "fallback": True}
            )

    async def calculate_confidence(self, context: AnalyzedContext) -> float:
        """
        计算处理置信度

        Args:
            context: 分析后的上下文

        Returns:
            float: 置信度 (0.0-1.0)
        """
        confidence_factors = []

        # 1. 意图匹配度
        if context.intent == "business_requirement":
            confidence_factors.append(0.9)
        else:
            confidence_factors.append(0.4)

        # 2. 关键词匹配度
        keyword_score = self._calculate_keyword_match_score(context)
        confidence_factors.append(keyword_score)

        # 3. 实体提取质量
        entities = self._extract_requirement_entities(context)
        if entities:
            entity_score = min(0.8, len(entities) * 0.2)
            confidence_factors.append(entity_score)
        else:
            confidence_factors.append(0.3)

        # 4. 消息复杂度（需求通常较复杂）
        complexity = self._assess_message_complexity(context)
        if complexity == "high":
            confidence_factors.append(0.8)
        elif complexity == "medium":
            confidence_factors.append(0.6)
        else:
            confidence_factors.append(0.4)

        # 5. 情感状态（积极情感通常表示明确需求）
        if context.emotion == "positive":
            confidence_factors.append(0.7)
        elif context.emotion == "neutral":
            confidence_factors.append(0.6)
        else:
            confidence_factors.append(0.5)

        return sum(confidence_factors) / len(confidence_factors)

    def _analyze_requirement_type(self, context: AnalyzedContext) -> str:
        """分析需求类型"""
        message_lower = context.message.lower()

        # 计算每个类型的匹配分数
        type_scores = {}
        for req_type, keywords in self.requirement_keywords.items():
            score = sum(1 for keyword in keywords if keyword in message_lower)
            if score > 0:
                type_scores[req_type] = score

        if type_scores:
            # 返回得分最高的类型
            return max(type_scores.keys(), key=lambda k: type_scores[k])

        return "general"  # 通用需求

    def _extract_requirement_entities(self, context: AnalyzedContext) -> Dict[str, Any]:
        """提取需求相关实体"""
        entities = {}
        message = context.message

        # 1. 项目类型
        for req_type, keywords in self.requirement_keywords.items():
            matched_keywords = [kw for kw in keywords if kw in message.lower()]
            if matched_keywords:
                entities["project_keywords"] = matched_keywords
                entities["project_type"] = req_type
                break

        # 2. 数字信息（预算、时间等）
        numbers = re.findall(r'\d+', message)
        if numbers:
            entities["numbers"] = numbers

        # 3. 时间表达
        time_patterns = [
            r'(\d+)天', r'(\d+)周', r'(\d+)月', r'(\d+)年',
            r'尽快', r'紧急', r'立即', r'马上'
        ]
        for pattern in time_patterns:
            matches = re.findall(pattern, message)
            if matches:
                entities["time_requirements"] = matches
                break

        # 4. 预算相关
        budget_patterns = [
            r'(\d+)元', r'(\d+)万', r'预算.*?(\d+)',
            r'费用', r'价格', r'多少钱'
        ]
        for pattern in budget_patterns:
            if re.search(pattern, message):
                entities["has_budget_mention"] = True
                break

        return entities

    def _determine_next_action(self, context: AnalyzedContext,
                              requirement_type: str,
                              entities: Dict[str, Any]) -> tuple:
        """确定下一步动作和状态"""

        # 如果已经在收集信息状态，继续收集
        if context.current_state == ConversationState.COLLECTING_INFO:
            return "collect_requirement_details", ConversationState.COLLECTING_INFO

        # 如果需求信息较完整，可以开始详细收集
        if len(entities) >= 2:
            return "start_detailed_collection", ConversationState.COLLECTING_INFO

        # 否则开始基础需求收集
        return "start_requirement_collection", ConversationState.COLLECTING_INFO

    def _select_response_template(self, requirement_type: str, action: str) -> str:
        """选择回复模板"""
        import random

        if action == "collect_requirement_details":
            return random.choice(self.confirmation_templates)

        if requirement_type in self.collection_questions:
            return random.choice(self.collection_questions[requirement_type])

        # 默认模板
        return "我来帮您了解具体需求，请详细描述一下您的项目要求。"

    def _calculate_keyword_match_score(self, context: AnalyzedContext) -> float:
        """计算关键词匹配分数"""
        message_lower = context.message.lower()
        total_matches = 0
        total_keywords = 0

        for keywords in self.requirement_keywords.values():
            total_keywords += len(keywords)
            total_matches += sum(1 for keyword in keywords if keyword in message_lower)

        if total_keywords == 0:
            return 0.0

        max_score = self.config.get_config_value("thresholds.strategy.requirement.max_keyword_score")
        multiplier = self.config.get_config_value("thresholds.strategy.requirement.keyword_match_multiplier")
        return min(max_score, total_matches / total_keywords * multiplier)  # 放大匹配效果

    def _assess_message_complexity(self, context: AnalyzedContext) -> str:
        """评估消息复杂度"""
        message = context.message

        # 基于长度
        length = len(message)
        if length > 50:
            complexity = "high"
        elif length > 20:
            complexity = "medium"
        else:
            complexity = "low"

        # 基于标点符号（复杂句子通常有更多标点）
        punctuation_count = sum(1 for char in message if char in "，。！？；：")
        if punctuation_count > 3:
            complexity = "high"
        elif punctuation_count > 1:
            complexity = "medium" if complexity != "high" else "high"

        return complexity

    async def calculate_context_match(self, context: AnalyzedContext) -> float:
        """
        计算与上下文的匹配度

        Args:
            context: 分析后的上下文

        Returns:
            float: 匹配度 (0.0-1.0)
        """
        if context.intent in self.supported_intents:
            return 1.0

        # 基于关键词的匹配度
        return self._calculate_keyword_match_score(context)


    def _calculate_keyword_match_score(self, context: AnalyzedContext) -> float:
        """计算关键词匹配分数"""
        message_lower = context.message.lower()
        total_matches = 0
        total_keywords = 0

        for keywords in self.requirement_keywords.values():
            total_keywords += len(keywords)
            total_matches += sum(1 for keyword in keywords if keyword in message_lower)

        if total_keywords == 0:
            return 0.0

        return min(0.9, total_matches / total_keywords * 3)  # 放大匹配效果

    def _assess_message_complexity(self, context: AnalyzedContext) -> str:
        """评估消息复杂度"""
        message = context.message

        # 基于长度
        length = len(message)
        if length > 50:
            complexity = "high"
        elif length > 20:
            complexity = "medium"
        else:
            complexity = "low"

        # 基于标点符号（复杂句子通常有更多标点）
        punctuation_count = sum(1 for char in message if char in "，。！？；：")
        if punctuation_count > 3:
            complexity = "high"
        elif punctuation_count > 1:
            complexity = "medium" if complexity != "high" else "high"

        return complexity

    async def calculate_context_match(self, context: AnalyzedContext) -> float:
        """
        计算与上下文的匹配度

        Args:
            context: 分析后的上下文

        Returns:
            float: 匹配度 (0.0-1.0)
        """
        if context.intent in self.supported_intents:
            return 1.0

        # 基于关键词的匹配度
        return self._calculate_keyword_match_score(context)


# 导出策略类
__all__ = ['RequirementStrategy']
