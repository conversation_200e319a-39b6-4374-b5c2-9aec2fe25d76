"""
知识库查询策略实现

处理用户的知识查询请求，包括：
- 功能介绍查询（如何使用、功能说明等）
- 操作指导查询（注册流程、使用方法等）
- 常见问题查询（FAQ、疑难解答等）
- 技术支持查询（技术问题、故障排除等）

优先级: 7 (中高)
支持意图: search_knowledge_base
"""

import logging
from typing import List
import re

from ..decision_types import DecisionStrategy, AnalyzedContext, DecisionResult, create_decision_result
from ..unified_state_manager import ConversationState
from backend.config.keywords_loader import get_keywords_loader
from backend.config import config_service


class KnowledgeBaseStrategy(DecisionStrategy):
    """知识库查询策略实现"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 🔧 [关键词重构] 初始化统一关键词加载器
        try:
            self.keywords_loader = get_keywords_loader()
            self.knowledge_keywords = self.keywords_loader.get_knowledge_base_keywords()
            self.logger.info("知识库策略：成功加载统一关键词配置")
        except Exception as e:
            self.logger.error(f"知识库策略：统一关键词加载失败，使用备用配置: {e}")
            # 🔧 [配置化重构] 从关键词配置加载备用关键词
            try:
                backup_keywords_loader = get_keywords_loader()
                self.knowledge_keywords = {
                    "how_to": backup_keywords_loader.get_keywords("strategy_keywords.knowledge_base_strategy.how_to") or ["如何", "怎么", "使用方法"],
                    "what_is": backup_keywords_loader.get_keywords("strategy_keywords.knowledge_base_strategy.what_is") or ["什么是", "介绍", "说明"],
                    "registration": backup_keywords_loader.get_keywords("strategy_keywords.knowledge_base_strategy.registration") or ["注册", "账号", "登录"],
                    "features": backup_keywords_loader.get_keywords("strategy_keywords.knowledge_base_strategy.features") or ["功能", "特点", "能力"],
                    "pricing": backup_keywords_loader.get_keywords("strategy_keywords.knowledge_base_strategy.pricing") or ["价格", "费用", "收费"],
                    "support": backup_keywords_loader.get_keywords("strategy_keywords.knowledge_base_strategy.support") or ["客服", "支持", "帮助"],
                    "faq": backup_keywords_loader.get_keywords("strategy_keywords.knowledge_base_strategy.faq") or ["常见问题", "FAQ", "疑问"]
                }
                self.keywords_loader = backup_keywords_loader
                self.logger.info("知识库策略：成功加载配置化备用关键词")
            except Exception as backup_error:
                self.logger.error(f"知识库策略：配置化备用关键词也加载失败，使用最小化硬编码: {backup_error}")
                # 最小化硬编码安全网
                self.knowledge_keywords = {
                    "how_to": ["如何", "怎么", "使用方法"],
                    "what_is": ["什么是", "介绍", "说明"],
                    "features": ["功能", "特点", "能力"],
                    "support": ["客服", "支持", "帮助"]
                }
                self.keywords_loader = None

        # 🔧 [关键词重构] 处理关键词格式映射
        self._normalize_keywords_format()

        # 🔧 [配置化重构] 从配置加载查询回复模板
        
        config_response_templates = config.get_config_value("strategy_templates.knowledge_base_strategy.templates.response_templates")
        self.response_templates = config_response_templates or {
            # 最小化硬编码安全网
            "how_to": ["我来为您介绍具体的操作方法和步骤。"],
            "what_is": ["我来为您介绍相关的功能和服务。"],
            "features": ["我来为您介绍我们的功能和服务。"],
            "support": ["我很乐意为您提供技术支持和帮助。"]
        }

        # 🔧 [配置化重构] 从配置加载知识库搜索提示
        config_search_prompts = config.get_config_value("strategy_templates.knowledge_base_strategy.templates.search_prompts")
        self.search_prompts = config_search_prompts or {
            # 最小化硬编码安全网
            "specific": "我正在为您查找相关信息，请稍等...",
            "general": "我来为您搜索相关的知识和解答。",
            "complex": "您的问题比较复杂，我来为您查找详细的资料。"
        }

    @property
    def name(self) -> str:
        return "knowledge_base_strategy"

    @property
    def supported_intents(self) -> List[str]:
        return ["search_knowledge_base"]

    @property
    def priority(self) -> int:
        return 7  # 中高优先级

    async def can_handle(self, context: AnalyzedContext) -> bool:
        """
        判断是否能处理当前上下文

        Args:
            context: 分析后的上下文

        Returns:
            bool: 是否能处理
        """
        # 1. 基于意图判断
        if context.intent == "search_knowledge_base":
            return True

        # 2. 基于关键词判断
        message_lower = context.message.lower()
        for category, keywords in self.knowledge_keywords.items():
            for keyword in keywords:
                if keyword in message_lower:
                    return True

        # 🔧 [配置化重构] 基于配置化疑问句模式判断
        
        question_patterns = (
            config.get_config_value("strategy_templates.knowledge_base_strategy.patterns.question_patterns") or
            [".*\\?$", "^(什么|怎么|如何).*", "^(请问|想问|咨询).*"]  # 最小化硬编码安全网
        )

        for pattern in question_patterns:
            if re.search(pattern, context.message):
                return True

        return False

    async def execute(self, context: AnalyzedContext) -> DecisionResult:
        """
        执行知识库查询策略

        Args:
            context: 分析后的上下文

        Returns:
            DecisionResult: 决策结果
        """
        try:
            # 分析查询类型
            query_type = self._analyze_query_type(context)

            # 提取查询关键词
            query_keywords = self._extract_query_keywords(context)

            # 评估查询复杂度
            complexity = self._assess_query_complexity(context)

            # 确定处理动作
            action = self._determine_action(query_type, complexity)

            # 选择回复模板
            response_template = self._select_response_template(query_type, complexity)

            # 计算置信度
            confidence = await self.calculate_confidence(context)

            # 创建决策结果
            result = create_decision_result(
                action=action,
                confidence=confidence,
                intent=context.intent,
                emotion=context.emotion,
                response_template=response_template,
                next_state=ConversationState.IDLE,  # 查询后保持空闲状态
                strategy_name=self.name,
                metadata={
                    "query_type": query_type,
                    "query_keywords": query_keywords,
                    "complexity": complexity,
                    "estimated_search_time": self._estimate_search_time(complexity),
                    "requires_followup": self._requires_followup(context)
                }
            )

            self.logger.info(f"知识库查询策略执行成功: type={query_type}, complexity={complexity}")
            return result

        except Exception as e:
            self.logger.error(f"知识库查询策略执行失败: {e}", exc_info=True)

            # 返回基础查询结果
            return create_decision_result(
                action="search_knowledge_base",
                confidence=0.7,
                intent="search_knowledge_base",
                emotion="neutral",
                response_template="我来为您查找相关信息，请稍等片刻。",
                next_state=ConversationState.IDLE,
                strategy_name=self.name,
                metadata={"error": str(e), "fallback": True}
            )

    async def calculate_confidence(self, context: AnalyzedContext) -> float:
        """
        计算处理置信度

        Args:
            context: 分析后的上下文

        Returns:
            float: 置信度 (0.0-1.0)
        """
        confidence_factors = []

        # 1. 意图匹配度
        if context.intent == "search_knowledge_base":
            confidence_factors.append(0.9)
        else:
            confidence_factors.append(0.4)

        # 2. 关键词匹配度
        keyword_score = self._calculate_keyword_match_score(context)
        confidence_factors.append(keyword_score)

        # 3. 疑问句特征
        if self._is_question(context.message):
            confidence_factors.append(0.8)
        else:
            confidence_factors.append(0.4)

        # 4. 查询复杂度（复杂查询更可能是知识库查询）
        complexity = self._assess_query_complexity(context)
        if complexity == "high":
            confidence_factors.append(0.8)
        elif complexity == "medium":
            confidence_factors.append(0.6)
        else:
            confidence_factors.append(0.5)

        # 5. 消息长度（知识查询通常较详细）
        message_length = len(context.message)
        if message_length > 30:
            confidence_factors.append(0.7)
        elif message_length > 10:
            confidence_factors.append(0.6)
        else:
            confidence_factors.append(0.4)

        return sum(confidence_factors) / len(confidence_factors)

    def _analyze_query_type(self, context: AnalyzedContext) -> str:
        """分析查询类型"""
        message_lower = context.message.lower()

        # 计算每个类型的匹配分数
        type_scores = {}
        for query_type, keywords in self.knowledge_keywords.items():
            score = sum(1 for keyword in keywords if keyword in message_lower)
            if score > 0:
                type_scores[query_type] = score

        if type_scores:
            # 返回得分最高的类型
            return max(type_scores.keys(), key=lambda k: type_scores[k])

        return "general"  # 通用查询

    def _extract_query_keywords(self, context: AnalyzedContext) -> List[str]:
        """提取查询关键词"""
        message = context.message
        keywords = []

        # 提取所有匹配的关键词
        for category, category_keywords in self.knowledge_keywords.items():
            for keyword in category_keywords:
                if keyword in message.lower():
                    keywords.append(keyword)

        # 提取实体中的关键词
        if hasattr(context, 'entities') and context.entities:
            for entity_type, entity_values in context.entities.items():
                if isinstance(entity_values, list):
                    keywords.extend(entity_values)
                elif isinstance(entity_values, str):
                    keywords.append(entity_values)

        return list(set(keywords))  # 去重

    def _assess_query_complexity(self, context: AnalyzedContext) -> str:
        """评估查询复杂度"""
        message = context.message

        # 基于长度
        length = len(message)
        if length > 50:
            complexity = "high"
        elif length > 20:
            complexity = "medium"
        else:
            complexity = "low"

        # 基于关键词数量
        keywords = self._extract_query_keywords(context)
        if len(keywords) > 3:
            complexity = "high"
        elif len(keywords) > 1:
            complexity = "medium" if complexity != "high" else "high"

        # 基于疑问词数量
        question_words = ["什么", "怎么", "如何", "为什么", "哪里", "哪个", "多少"]
        question_count = sum(1 for word in question_words if word in message)
        if question_count > 1:
            complexity = "high"

        return complexity

    def _determine_action(self, query_type: str, complexity: str) -> str:
        """确定处理动作"""
        if complexity == "high":
            return "search_knowledge_base_detailed"
        elif query_type in ["how_to", "registration"]:
            return "provide_step_by_step_guide"
        elif query_type in ["features", "pricing"]:
            return "provide_feature_overview"
        else:
            return "search_knowledge_base"

    def _select_response_template(self, query_type: str, complexity: str) -> str:
        """选择回复模板"""
        import random

        # 根据复杂度选择搜索提示
        if complexity == "high":
            search_prompt = self.search_prompts["complex"]
        elif complexity == "medium":
            search_prompt = self.search_prompts["specific"]
        else:
            search_prompt = self.search_prompts["general"]

        # 根据查询类型选择模板
        if query_type in self.response_templates:
            template = random.choice(self.response_templates[query_type])
            return f"{template} {search_prompt}"

        # 默认模板
        template = config_service.get_config_value("message_templates.knowledge_base_strategy.search_template")
        return template.format(search_prompt=search_prompt)

    def _calculate_keyword_match_score(self, context: AnalyzedContext) -> float:
        """计算关键词匹配分数"""
        message_lower = context.message.lower()
        total_matches = 0
        total_keywords = 0

        for keywords in self.knowledge_keywords.values():
            total_keywords += len(keywords)
            total_matches += sum(1 for keyword in keywords if keyword in message_lower)

        if total_keywords == 0:
            return 0.0

        return min(0.9, total_matches / total_keywords * 5)  # 放大匹配效果

    def _is_question(self, message: str) -> bool:
        """判断是否为疑问句"""
        # 检查疑问标点
        if message.endswith('?') or message.endswith('？'):
            return True

        # 检查疑问词
        question_words = ["什么", "怎么", "如何", "为什么", "哪里", "哪个", "多少", "是否", "能否"]
        for word in question_words:
            if word in message:
                return True

        # 检查疑问语气词
        question_particles = ["吗", "呢", "啊"]
        for particle in question_particles:
            if message.endswith(particle):
                return True

        return False

    def _estimate_search_time(self, complexity: str) -> int:
        """估算搜索时间（秒）"""
        if complexity == "high":
            return 3
        elif complexity == "medium":
            return 2
        else:
            return 1

    def _requires_followup(self, context: AnalyzedContext) -> bool:
        """判断是否需要后续跟进"""
        # 复杂查询通常需要跟进
        complexity = self._assess_query_complexity(context)
        if complexity == "high":
            return True

        # 包含多个关键词的查询可能需要跟进
        keywords = self._extract_query_keywords(context)
        if len(keywords) > 2:
            return True

        return False

    async def calculate_context_match(self, context: AnalyzedContext) -> float:
        """
        计算与上下文的匹配度

        Args:
            context: 分析后的上下文

        Returns:
            float: 匹配度 (0.0-1.0)
        """
        if context.intent in self.supported_intents:
            return 1.0

        # 基于关键词和疑问句特征的匹配度
        keyword_score = self._calculate_keyword_match_score(context)
        question_score = 0.3 if self._is_question(context.message) else 0.0

        return min(1.0, keyword_score + question_score)

    def _normalize_keywords_format(self):
        """
        🔧 [关键词重构] 标准化关键词格式

        将统一配置的关键词格式映射到策略模块期望的格式
        """
        if not self.keywords_loader:
            return

        try:
            # 获取统一配置的关键词
            unified_keywords = self.keywords_loader.get_knowledge_base_keywords()

            # 创建映射关系：统一配置 -> 策略模块格式
            format_mapping = {
                # 统一配置分类 -> 策略模块分类
                "usage": "how_to",           # 使用方法 -> 如何使用
                "features": "features",      # 功能 -> 功能 (保持一致)
                "pricing": "pricing",        # 价格 -> 价格 (保持一致)
                "registration": "registration", # 注册 -> 注册 (保持一致)
                "support": "support",        # 支持 -> 支持 (保持一致)
                "product_info": "what_is"    # 产品信息 -> 什么是
            }

            # 重新组织关键词
            normalized_keywords = {}

            # 处理映射的分类
            for unified_category, strategy_category in format_mapping.items():
                if unified_category in unified_keywords:
                    normalized_keywords[strategy_category] = unified_keywords[unified_category]

            # 添加策略模块特有的分类（如果统一配置中没有）
            if "how_to" not in normalized_keywords:
                normalized_keywords["how_to"] = [
                    "如何", "怎么", "怎样", "如何使用", "怎么用", "使用方法",
                    "操作步骤", "使用流程", "操作指南", "使用说明"
                ]

            if "what_is" not in normalized_keywords:
                normalized_keywords["what_is"] = [
                    "什么是", "介绍一下", "说明", "解释", "定义",
                    "功能介绍", "产品介绍", "服务介绍"
                ]

            if "faq" not in normalized_keywords:
                normalized_keywords["faq"] = [
                    "常见问题", "FAQ", "疑问", "问题", "疑难",
                    "解答", "答案", "帮助文档"
                ]

            # 更新关键词配置
            self.knowledge_keywords = normalized_keywords

            self.logger.debug(f"关键词格式标准化完成，共{len(normalized_keywords)}个分类")

        except Exception as e:
            self.logger.error(f"关键词格式标准化失败: {e}")
            # 保持原有的备用配置


# 导出策略类
__all__ = ['KnowledgeBaseStrategy']
