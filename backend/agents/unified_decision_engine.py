"""
统一决策引擎核心实现

这是统一决策引擎的核心实现，整合了：
- 策略注册和管理
- 上下文分析
- 冲突检测和解决
- 决策执行和监控
"""

import time
import logging
from typing import Any, Dict, List, Optional, Set

from .decision_engine_interface import DecisionEngineInterface
from .decision_types import (
    DecisionContext, AnalyzedContext, DecisionResult, DecisionStrategy,
    StrategyConflict, ConflictResolution, create_decision_result
)
from .strategy_registry import StrategyRegistry, get_strategy_registry
from .context_analyzer import ContextAnalyzer, get_context_analyzer
from .decision_cache import get_decision_cache
from .decision_monitor import get_decision_monitor


class UnifiedDecisionEngine(DecisionEngineInterface):
    """统一决策引擎实现"""

    def __init__(self,
                 strategy_registry: Optional[StrategyRegistry] = None,
                 context_analyzer: Optional[ContextAnalyzer] = None,
                 llm_service: Optional[Any] = None,
                 enable_cache: bool = True,
                 enable_monitoring: bool = True):
        """
        初始化统一决策引擎

        Args:
            strategy_registry: 策略注册中心
            context_analyzer: 上下文分析器
            llm_service: LLM服务
            enable_cache: 是否启用缓存
            enable_monitoring: 是否启用监控
        """
        self.logger = logging.getLogger(__name__)

        # 核心组件
        self.strategy_registry = strategy_registry or get_strategy_registry()
        self.context_analyzer = context_analyzer or get_context_analyzer(llm_service)
        self.llm_service = llm_service

        # 性能优化组件
        self.cache = get_decision_cache() if enable_cache else None
        self.monitor = get_decision_monitor() if enable_monitoring else None

        # 自动加载和注册策略
        self._auto_load_strategies()

        # 决策缓存
        self._decision_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_ttl = 300  # 5分钟缓存

        # 监控数据
        self._decision_history: List[Dict[str, Any]] = []
        self._conflict_history: List[Dict[str, Any]] = []
        self._performance_metrics = {
            "total_decisions": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "average_decision_time": 0.0,
            "conflict_count": 0
        }

        self.logger.info("统一决策引擎初始化完成")

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = {}

        # 缓存统计
        if self.cache:
            stats["cache"] = self.cache.get_stats()

        # 监控统计
        if self.monitor:
            stats["overall"] = self.monitor.get_overall_stats()
            stats["strategies"] = self.monitor.get_strategy_stats()
            stats["intents"] = self.monitor.get_intent_stats()
            stats["hourly"] = self.monitor.get_hourly_stats()

        # 策略注册统计
        stats["registry"] = {
            "total_strategies": len(self.strategy_registry.get_all_strategies()),
            "strategy_names": [s.name for s in self.strategy_registry.get_all_strategies()]
        }

        return stats

    def get_recent_errors(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的错误记录"""
        if self.monitor:
            return self.monitor.get_recent_errors(limit)
        return []

    def get_performance_trends(self, minutes: int = 60) -> Dict[str, Any]:
        """获取性能趋势数据"""
        if self.monitor:
            return self.monitor.get_performance_trends(minutes)
        return {"timestamps": [], "response_times": [], "success_rates": []}

    def clear_cache(self) -> None:
        """清空缓存"""
        if self.cache:
            self.cache.clear()

    def reset_monitoring(self) -> None:
        """重置监控统计"""
        if self.monitor:
            self.monitor.reset_stats()

    def _auto_load_strategies(self):
        """自动加载和注册策略"""
        try:
            # 导入并注册所有策略
            from .strategies.greeting_strategy import GreetingStrategy
            from .strategies.requirement_strategy import RequirementStrategy
            from .strategies.knowledge_base_strategy import KnowledgeBaseStrategy
            from .strategies.capabilities_strategy import CapabilitiesStrategy
            from .strategies.emotional_support_strategy import EmotionalSupportStrategy
            from .strategies.fallback_strategy import FallbackStrategy

            # 创建策略实例并注册
            strategies = [
                GreetingStrategy(),
                RequirementStrategy(),
                KnowledgeBaseStrategy(),
                CapabilitiesStrategy(),
                EmotionalSupportStrategy(),
                FallbackStrategy()
            ]

            for strategy in strategies:
                self.strategy_registry.register_strategy(strategy)

            self.logger.info(f"自动加载并注册了 {len(strategies)} 个策略")

        except Exception as e:
            self.logger.error(f"自动加载策略失败: {e}", exc_info=True)

    async def make_decision(self, context: DecisionContext) -> DecisionResult:
        """
        核心决策方法

        Args:
            context: 决策上下文

        Returns:
            DecisionResult: 决策结果
        """
        start_time = time.time()
        error_message = None

        try:
            # 1. 检查缓存
            if self.cache:
                cached_result = self.cache.get(context)
                if cached_result:
                    execution_time = time.time() - start_time
                    cached_result.execution_time = execution_time
                    self.logger.debug(f"使用缓存决策: {cached_result.action}")

                    # 记录缓存命中到监控
                    if self.monitor:
                        self.monitor.record_decision(context, cached_result, execution_time, True)

                    return cached_result

            # 2. 分析上下文
            analyzed_context = await self.analyze_context(context)

            # 3. 匹配策略
            matched_strategies = await self.strategy_registry.match_strategies(analyzed_context)

            if not matched_strategies:
                # 没有匹配的策略，返回默认结果
                result = self._create_fallback_result(analyzed_context)
            else:
                # 4. 检测冲突
                conflicts = await self._detect_conflicts(matched_strategies, analyzed_context)

                if conflicts:
                    # 5. 解决冲突
                    selected_strategy = await self._resolve_conflicts(conflicts, matched_strategies, analyzed_context)
                else:
                    # 没有冲突，选择第一个策略（已按优先级排序）
                    selected_strategy = matched_strategies[0]

                # 6. 执行策略
                result = await selected_strategy.execute(analyzed_context)
                result.strategy_name = selected_strategy.name

            # 7. 记录执行时间
            execution_time = time.time() - start_time
            result.execution_time = execution_time

            # 8. 缓存结果
            if self.cache:
                self.cache.put(context, result)

            # 9. 记录监控数据
            if self.monitor:
                self.monitor.record_decision(context, result, execution_time, True)

            self.logger.info(f"决策完成: action={result.action}, strategy={result.strategy_name}, time={execution_time:.3f}s")

            return result

        except Exception as e:
            execution_time = time.time() - start_time
            error_message = str(e)
            self.logger.error(f"决策执行失败: {e}", exc_info=True)

            # 返回错误回退结果
            error_result = create_decision_result(
                action="handle_unknown_situation",
                confidence=0.1,
                intent="unknown",
                emotion="neutral",
                response_template="system.error",
                execution_time=execution_time,
                metadata={"error": str(e), "fallback": True}
            )

            # 记录错误到监控
            if self.monitor:
                self.monitor.record_decision(context, error_result, execution_time, False, error_message)

            return error_result

    async def analyze_context(self, context: DecisionContext) -> AnalyzedContext:
        """
        分析决策上下文

        Args:
            context: 原始决策上下文

        Returns:
            AnalyzedContext: 分析后的上下文
        """
        return await self.context_analyzer.analyze(context)

    def register_strategy(self, strategy: DecisionStrategy) -> None:
        """
        注册决策策略

        Args:
            strategy: 要注册的策略
        """
        self.strategy_registry.register_strategy(strategy)
        self.logger.info(f"策略 {strategy.name} 已注册到统一决策引擎")

    def unregister_strategy(self, strategy_name: str) -> bool:
        """
        注销决策策略

        Args:
            strategy_name: 策略名称

        Returns:
            bool: 是否成功注销
        """
        success = self.strategy_registry.unregister_strategy(strategy_name)
        if success:
            self.logger.info(f"策略 {strategy_name} 已从统一决策引擎注销")
        return success

    def get_registered_strategies(self) -> List[str]:
        """
        获取已注册的策略列表

        Returns:
            List[str]: 策略名称列表
        """
        return [s.name for s in self.strategy_registry.get_all_strategies()]

    def get_supported_intents(self) -> Set[str]:
        """
        获取支持的意图集合

        Returns:
            Set[str]: 支持的意图集合
        """
        return self.strategy_registry.get_supported_intents()

    # ==================== 私有方法 ====================

    def _generate_cache_key(self, context: DecisionContext) -> str:
        """生成缓存键"""
        import hashlib
        key_data = f"{context.message}:{context.current_state.value}:{context.user_id}"
        return hashlib.md5(key_data.encode()).hexdigest()

    def _get_cached_decision(self, cache_key: str) -> Optional[DecisionResult]:
        """获取缓存的决策结果"""
        if cache_key in self._decision_cache:
            cached_item = self._decision_cache[cache_key]
            if time.time() - cached_item["timestamp"] < self._cache_ttl:
                return cached_item["result"]
            else:
                # 缓存过期，删除
                del self._decision_cache[cache_key]
        return None

    def _cache_decision(self, cache_key: str, result: DecisionResult) -> None:
        """缓存决策结果"""
        self._decision_cache[cache_key] = {
            "result": result,
            "timestamp": time.time()
        }

        # 限制缓存大小
        if len(self._decision_cache) > 1000:
            # 删除最旧的缓存项
            oldest_key = min(self._decision_cache.keys(),
                           key=lambda k: self._decision_cache[k]["timestamp"])
            del self._decision_cache[oldest_key]

    async def _detect_conflicts(self,
                               strategies: List[DecisionStrategy],
                               context: AnalyzedContext) -> List[StrategyConflict]:
        """检测策略冲突"""
        conflicts = []

        if len(strategies) > 1:
            # 检查优先级冲突
            priorities = [s.priority for s in strategies]
            if len(set(priorities)) < len(priorities):
                # 有相同优先级的策略
                from .decision_types import ConflictType, ConflictSeverity
                conflict = StrategyConflict(
                    conflict_type=ConflictType.PRIORITY_CONFLICT,
                    severity=ConflictSeverity.MEDIUM,
                    conflicted_strategies=[s.name for s in strategies],
                    context=context,
                    conflict_details={
                        "priorities": priorities,
                        "strategy_count": len(strategies)
                    }
                )
                conflicts.append(conflict)

        return conflicts

    async def _resolve_conflicts(self,
                                conflicts: List[StrategyConflict],
                                strategies: List[DecisionStrategy],
                                context: AnalyzedContext) -> DecisionStrategy:
        """解决策略冲突"""
        # 简单的冲突解决策略：选择置信度最高的策略
        best_strategy = None
        best_confidence = 0.0

        for strategy in strategies:
            try:
                confidence = await strategy.calculate_confidence(context)
                if confidence > best_confidence:
                    best_confidence = confidence
                    best_strategy = strategy
            except Exception as e:
                self.logger.error(f"计算策略 {strategy.name} 置信度失败: {e}")

        if best_strategy is None:
            best_strategy = strategies[0]  # 回退到第一个策略

        # 记录冲突解决
        self._performance_metrics["conflict_count"] += 1
        self._conflict_history.append({
            "conflicts": [c.to_dict() for c in conflicts],
            "selected_strategy": best_strategy.name,
            "resolution_method": "confidence_based",
            "timestamp": time.time()
        })

        self.logger.info(f"冲突解决: 选择策略 {best_strategy.name} (置信度: {best_confidence:.2f})")

        return best_strategy

    def _create_fallback_result(self, context: AnalyzedContext) -> DecisionResult:
        """创建回退决策结果"""
        return create_decision_result(
            action="handle_unknown_situation",
            confidence=0.3,
            intent=context.intent,
            emotion=context.emotion,
            response_template="system.fallback",
            strategy_name="fallback_strategy",
            metadata={"fallback": True, "reason": "no_matching_strategy"}
        )

    def _update_metrics(self, result: DecisionResult, execution_time: float) -> None:
        """更新性能指标"""
        self._performance_metrics["total_decisions"] += 1

        # 更新平均决策时间
        total = self._performance_metrics["total_decisions"]
        current_avg = self._performance_metrics["average_decision_time"]
        new_avg = (current_avg * (total - 1) + execution_time) / total
        self._performance_metrics["average_decision_time"] = new_avg

    def _record_decision(self,
                        context: DecisionContext,
                        result: DecisionResult,
                        execution_time: float) -> None:
        """记录决策历史"""
        record = {
            "session_id": context.session_id,
            "user_id": context.user_id,
            "message": context.message[:100],  # 截断长消息
            "intent": result.intent,
            "action": result.action,
            "strategy": result.strategy_name,
            "confidence": result.confidence,
            "execution_time": execution_time,
            "timestamp": time.time()
        }

        self._decision_history.append(record)

        # 限制历史记录大小
        if len(self._decision_history) > 1000:
            self._decision_history = self._decision_history[-1000:]

    # ==================== 监控和调试方法 ====================

    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return self._performance_metrics.copy()

    def get_decision_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取决策历史"""
        return self._decision_history[-limit:]

    def get_conflict_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取冲突历史"""
        return self._conflict_history[-limit:]

    def clear_cache(self) -> None:
        """清空决策缓存"""
        self._decision_cache.clear()
        self.logger.info("决策缓存已清空")


# 全局统一决策引擎实例
_global_unified_engine: Optional[UnifiedDecisionEngine] = None


def get_unified_decision_engine(llm_service: Optional[Any] = None) -> UnifiedDecisionEngine:
    """获取全局统一决策引擎实例"""
    global _global_unified_engine
    if _global_unified_engine is None:
        _global_unified_engine = UnifiedDecisionEngine(llm_service=llm_service)
    return _global_unified_engine


def reset_unified_decision_engine() -> None:
    """重置全局统一决策引擎（主要用于测试）"""
    global _global_unified_engine
    _global_unified_engine = None
