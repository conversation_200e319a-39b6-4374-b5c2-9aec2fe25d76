# -*- coding: utf-8 -*-
"""
信息提取器Agent (v2.1 - 集成了对话摘要管理)
该模块的核心职责是作为一个具备上下文感知能力的状态更新器。
它接收历史摘要和用户的最新输入，调用大语言模型（LLM）生成一份更新后的、
完整的JSON摘要，并返回给总调度模块。
"""

import logging
from typing import Dict, Any, Optional, List
import json
import re

# 导入AutoGen基础类和项目配置
from .base import AutoGenBaseAgent
from ..config import config_service

# --- 这是我们统一使用的、健壮的JSON提取函数 ---

def extract_json_from_text(text: str) -> Optional[Dict[str, Any]]:
    """
    Args:
        text: 包含JSON的原始字符串。

    Returns:
        一个字典形式的JSON对象，如果找不到则返回None。
    """
    if not isinstance(text, str):
        return None

    # 1. 优先匹配被 ```json ... ``` 包裹的代码块
    match = re.search(r'```json\s*(\{.*?\})\s*```', text, re.DOTALL)
    if match:
        json_str = match.group(1)
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            logging.warning(f"在json代码块中发现无效的JSON格式: {json_str[:100]}...")
            # Fallback to the next method if parsing fails

    # 2. 如果没有代码块，则贪婪地寻找第一个 '{' 和最后一个 '}'
    start_index = text.find('{')
    end_index = text.rfind('}')

    if start_index != -1 and end_index > start_index:
        json_str = text[start_index:end_index + 1]
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            logging.error(f"无法将提取的子字符串解析为JSON: {json_str[:100]}...")
            return None

    logging.warning(f"在文本中找不到任何有效的JSON对象: {text[:100]}...")
    return None

# --- 信息提取器Agent ---
class InformationExtractorAgent(AutoGenBaseAgent):
    """
    负责信息提取的AutoGen Agent。
    在新架构中，它作为一个专业的服务被总调度模块调用，
    而不是一个独立的、能发起对话的Agent。
    """

    def __init__(self,
                 llm_service=None,
                 **kwargs):
        """
        初始化信息提取器。

        Args:
            llm_service: 提供调用大语言模型服务的客户端。
        """
        # Set up basic agent properties
        conversable_agent_kwargs = {k: v for k, v in kwargs.items()
                                   if k in ['system_message', 'is_termination_msg',
                                           'max_consecutive_auto_reply', 'human_input_mode',
                                           'code_execution_config', 'llm_config', 'default_auto_reply']}
        conversable_agent_kwargs['name'] = kwargs.get('name', "InformationExtractor")
        super().__init__(**conversable_agent_kwargs)

        self.llm_service = llm_service
        self.logger = logging.getLogger(__name__)

        # 从统一配置获取调试设置
        enable_debug = config_service.get_threshold("system.debug_mode", False)
        if enable_debug:
            self.logger.setLevel(logging.DEBUG)

    async def extract_values(
        self,
        user_input: str,
        focus_points: List[Dict[str, Any]],
        conversation_history: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        从用户输入中提取信息，基于对话历史上下文。
        这是该模块暴露给外部调用的核心接口。

        Args:
            user_input: 用户的最新输入字符串。
            focus_points: 所有可能的关注点定义列表，用于给LLM提供上下文。
            conversation_history: 对话历史记录，用于提供上下文信息。

        Returns:
            一个字典，包含:
            - "updated_summary_json": 更新后的完整摘要JSON字符串。
            - "extracted_points": 本轮交互中发生变化的关注点列表。
        """
        try:
            self.logger.info(f"开始提取信息，关注点数量: {len(focus_points)}")

            # 1. 构建新的、具备上下文感知能力的Prompt
            prompt = self._build_extraction_prompt(user_input, focus_points, conversation_history)

            # 2. 调用LLM进行提取和融合
            llm_response = await self._call_llm_for_extraction(prompt)

            # 3. 从LLM的响应中安全地解析出更新后的完整摘要JSON
            updated_summary_json_str = llm_response.get("content", "{}")
            self.logger.debug(f"LLM原始响应: {updated_summary_json_str}")

            new_summary_data = extract_json_from_text(updated_summary_json_str) or {"extracted_points": []}
            self.logger.debug(f"解析后的数据: {new_summary_data}")

            # 4. 提取本次变化的关注点
            extracted_points_this_turn = new_summary_data.get("extracted_points", [])

            # 详细记录提取结果
            self.logger.info(f"信息提取与融合完成, 本次提取/更新了 {len(extracted_points_this_turn)} 个点。")
            if extracted_points_this_turn:
                for i, point in enumerate(extracted_points_this_turn, 1):
                    point_name = point.get("name", "未知关注点")
                    point_value = point.get("value", "")
                    point_completeness = point.get("completeness", 0.0)
                    self.logger.info(f"  [{i}] {point_name}: {point_value} (完整度: {point_completeness})")
            else:
                self.logger.info("  本次未提取到任何关注点信息")

            # 5. 按照总调度模块的期望格式返回结果
            return {
                "updated_summary_json": json.dumps(new_summary_data, ensure_ascii=False),
                "extracted_points": extracted_points_this_turn
            }

        except Exception as e:
            self.logger.error(f"提取信息时失败: {e}", exc_info=True)
            # 在出错时，返回空摘要
            return {
                "updated_summary_json": '{"extracted_points": []}',
                "extracted_points": []
            }


    # 构建信息提取提示词
    def _build_extraction_prompt(self, user_input: str, focus_points: List[Dict[str, Any]], conversation_history: Optional[str] = None) -> str:
        """
        构建集成了对话历史上下文的信息提取提示词。

        Args:
            user_input: 用户的最新输入。
            focus_points: 所有可能的关注点定义列表。
            conversation_history: 对话历史记录。

        Returns:
            构建完成的、可以直接发送给LLM的字符串提示。
        """
        try:
            # Assume a PromptLoader utility exists for loading templates
            from ..utils.prompt_loader import PromptLoader
            prompt_loader = PromptLoader()

            # 将所有可能的关注点定义转换为字符串，供LLM参考
            focus_points_str = "\n".join([
                f"- {point['name']}: {point.get('description', '')}"
                for point in focus_points
            ])

            # 处理空的对话历史
            formatted_conversation_history = conversation_history
            if not conversation_history or conversation_history.strip() == "":
                formatted_conversation_history = "暂无历史信息（这是新的对话）"

            # 使用统一的对话历史模板
            prompt = prompt_loader.load_prompt(
                "information_extraction",
                {
                    "focus_points_str": focus_points_str,
                    "conversation_history": formatted_conversation_history,
                    "user_input": user_input
                }
            )

            return prompt

        except Exception as e:
            self.logger.error(f"构建提取提示词失败: {e}", exc_info=True)
            raise

    # 调用LLM进行信息提取
    async def _call_llm_for_extraction(self, prompt: str) -> Dict[str, Any]:
        """
        调用LLM进行信息提取，并期望返回JSON对象。

        Args:
            prompt: 已经构建好的完整提示词。

        Returns:
            LLM服务返回的原始响应字典。
        """
        try:
            # 从统一配置获取参数
            # 通过统一配置服务获取information_extractor场景的配置
            try:
                from backend.config import config_service
                config = config_service.get_llm_config_with_metadata("information_extractor")
                temperature = config.get("temperature", 0.3)
                timeout = config.get("timeout", 30)
            except Exception:
                # 从thresholds配置获取默认值
                from backend.config import config_service
                unified_config = config_service.get_unified_config()
                temperature = unified_config.get_threshold("llm.default_temperature", 0.3)
                timeout = unified_config.get_threshold("performance.timeout.llm_request", 30)

            response = await self.llm_service.call_llm(
                messages=[{
                    "role": "system",
                    "content": "你是一个专业的信息提取与整合助手。你的任务是根据历史摘要和用户最新输入，生成一份更新后的、完整的JSON摘要。请严格遵循输出格式，只返回JSON对象。"
                }, {
                    "role": "user",
                    "content": prompt
                }],
                agent_name="information_extractor",
                temperature=temperature,
                timeout=timeout,
                response_format={"type": "json_object"}
            )
            return response
        except Exception as e:
            self.logger.error(f"调用LLM进行信息提取失败: {e}", exc_info=True)
            raise

    # 比较新旧两个摘要JSON，找出发生变化的关注点
    def _diff_summaries(self, old_summary_str: str, new_summary_str: str) -> List[Dict[str, Any]]:
        """
        比较新旧两个摘要JSON，找出发生变化的关注点，用于返回给上层模块。

        Args:
            old_summary_str: 旧的摘要JSON字符串。
            new_summary_str: 新的摘要JSON字符串。

        Returns:
            一个列表，包含本轮发生变化的关注点信息。
        """
        try:
            # Safely load JSON strings
            old_data = json.loads(old_summary_str) if old_summary_str else {}
            new_data = json.loads(new_summary_str) if new_summary_str else {}

            # Create dictionaries for quick lookups
            old_points = {p['name']: p for p in old_data.get('extracted_points', []) if 'name' in p}
            new_points = {p['name']: p for p in new_data.get('extracted_points', []) if 'name' in p}

            changed_points = []

            for name, new_point in new_points.items():
                old_point = old_points.get(name)

                # A point is considered "changed" if it's new, or its value/completeness has been modified.
                if not old_point or \
                   old_point.get('value') != new_point.get('value') or \
                   old_point.get('completeness') != new_point.get('completeness'):

                    # Ensure the returned format matches what the calling module expects
                    changed_points.append({
                        "name": new_point.get("name"),
                        "value": new_point.get("value"),
                        "completeness": new_point.get("completeness", 0.0)
                        # Add 'id' or other fields here if needed by the orchestrator
                    })
            return changed_points
        except (json.JSONDecodeError, TypeError) as e:
            self.logger.error(f"对比摘要时出错 (可能JSON格式不正确): {e}", exc_info=True)
            return []

