#!/usr/bin/env python3
"""
LLM服务接口定义

为LLM服务定义抽象接口，实现服务解耦，便于测试和替换不同的LLM实现。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional


class LLMServiceInterface(ABC):
    """LLM服务接口"""

    @abstractmethod
    async def call_llm(self, messages: List[Dict[str, str]],
                       agent_name: Optional[str] = None,
                       scenario: Optional[str] = None,
                       session_id: Optional[str] = None,
                       **kwargs) -> Dict[str, Any]:
        """
        调用LLM服务

        Args:
            messages: 消息列表
            agent_name: Agent名称
            scenario: 场景名称
            session_id: 会话ID，用于日志记录
            **kwargs: 其他参数

        Returns:
            LLM响应结果
        """
        pass

    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息

        Returns:
            模型信息字典
        """
        pass


class PromptLoaderInterface(ABC):
    """提示加载器接口"""

    @abstractmethod
    def load_prompt(self, template_name: str, variables: Optional[Dict[str, Any]] = None) -> str:
        """
        加载提示模板

        Args:
            template_name: 模板名称
            variables: 模板变量

        Returns:
            渲染后的提示文本
        """
        pass
