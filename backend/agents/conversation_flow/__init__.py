"""
对话流程管理模块

这个模块将原来的conversation_flow.py拆分为多个子模块，以提高可维护性。
主要组件：
- core: 核心业务逻辑和主类
- state_manager: 状态管理（focus points等）
- message_processor: 消息处理逻辑
- session_manager: 会话管理
- utils: 工具函数和辅助方法

向后兼容性：保持原有的导入方式正常工作
"""

# 保持向后兼容性 - 导入主要类
from .core import AutoGenConversationFlowAgent
# 从其他模块导入可能需要的类
from ..session_context import ConversationState

# 为了向后兼容，提供别名
ConversationFlowAgent = AutoGenConversationFlowAgent

# 导出主要接口
__all__ = [
    'AutoGenConversationFlowAgent',
    'ConversationFlowAgent',  # 别名
    'ConversationConstants',
    'ConversationState'
]
