"""
消息处理器模块

负责处理用户消息的核心逻辑：
- 消息解析和验证
- 意图决策
- 响应生成
- 会话上下文管理
"""

import logging
from typing import Any, Dict, List

from backend.config import config_service
from backend.agents.decision_engine_adapter import get_decision_engine_adapter
from backend.handlers.base_action_handler import ActionResult, ActionContext
from backend.handlers.action_executor_interface import MessageProcessorInterface
from ..session_context import SessionContext


class MessageProcessor(MessageProcessorInterface):
    """消息处理器"""

    def __init__(self, message_manager, session_context_manager, action_executor, knowledge_base_agent, intent_decision_engine=None):
        """
        初始化消息处理器

        Args:
            message_manager: 消息管理器
            session_context_manager: 会话上下文管理器
            action_executor: 动作执行器
            knowledge_base_agent: 知识库代理
            intent_decision_engine: 意图决策引擎（可选，默认使用统一决策引擎适配器）
        """
        self.intent_decision_engine = intent_decision_engine or get_decision_engine_adapter(use_unified_engine=True)
        self.message_manager = message_manager
        self.session_context_manager = session_context_manager
        self.action_executor = action_executor
        self.knowledge_base_agent = knowledge_base_agent
        self.logger = logging.getLogger(__name__)

        # 缓存配置对象，避免重复调用
        from backend.config import config_service
        self.config = config_service

        # 记录使用的决策引擎类型
        engine_type = type(self.intent_decision_engine).__name__
        self.logger.info(f"MessageProcessor 初始化完成，使用决策引擎: {engine_type}")

    async def process_message(self,
                              message: str,
                              session_context: Any,
                              **kwargs) -> Dict[str, Any]:
        """
        处理消息 - 实现MessageProcessorInterface接口

        Args:
            message: 用户消息
            session_context: 会话上下文
            **kwargs: 其他参数，包括session_id, user_id, progress_callback等

        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 提取进度回调
            progress_callback = kwargs.get("progress_callback")

            # 构建消息数据格式，兼容原有的process_message_data方法
            message_data = {
                "text": message,
                "session_id": kwargs.get("session_id", "unknown"),
                "user_id": kwargs.get("user_id", "unknown")
            }

            # 调用原有的处理方法，传递进度回调
            return await self.process_message_data(message_data, session_context, progress_callback=progress_callback)

        except Exception as e:
            self.logger.error(f"处理消息失败: {e}")
            error_message = self.config.get_message_template("error.message_processing")
            return {
                "success": False,
                "error": str(e),
                "reply": error_message
            }

    async def _load_session_context(self,
                                    session_id: str,
                                    user_id: str) -> Any:
        """
        加载会话上下文 - 实现MessageProcessorInterface接口

        Args:
            session_id: 会话ID
            user_id: 用户ID

        Returns:
            Any: 会话上下文对象
        """
        try:
            if self.session_context_manager:
                return await self.session_context_manager.load_session_context(session_id, user_id)
            else:
                self.logger.warning("session_context_manager未初始化")
                return None
        except Exception as e:
            self.logger.error(f"加载会话上下文失败: {e}")
            return None

    async def process_message_data(self, message_data: Dict[str, Any], session_context=None, progress_callback=None) -> Dict[str, Any]:
        """
        处理用户消息的核心方法 - 无状态版（保持向后兼容）

        Args:
            message_data: 消息数据
            session_context: 可选的会话上下文（如果未提供则从数据库加载）
            progress_callback: 进度更新回调函数

        Returns:
            Dict[str, Any]: 处理结果
        """
        session_id = message_data.get("session_id", "")
        user_id = message_data.get("user_id", "")
        message = message_data.get("text", "").strip()

        # 处理用户消息

        try:
            # 发送初始进度更新
            if progress_callback:
                from backend.utils.progress_indicator import ProgressStage, ProgressUpdate
                progress_callback(ProgressUpdate(
                    stage=ProgressStage.STARTING,
                    percentage=10,
                    message="🚀 开始处理您的消息..."
                ))

            # 1. 加载或使用提供的会话上下文
            if session_context is None:
                session_context = await self.session_context_manager.load_session_context(session_id, user_id)

            if progress_callback:
                progress_callback(ProgressUpdate(
                    stage=ProgressStage.ANALYZING,
                    percentage=25,
                    message="📋 正在分析消息内容..."
                ))

            # 2. 确保会话上下文包含最新的领域和分类信息
            session_context = await self._ensure_domain_category_info(session_context)

            # 3. 保存用户消息
            await self.message_manager.save_message(
                conversation_id=session_id, sender_type="user", content=message, user_id=user_id
            )

            if progress_callback:
                progress_callback(ProgressUpdate(
                    stage=ProgressStage.PROCESSING,
                    percentage=50,
                    message="🧠 正在理解您的需求..."
                ))

            # 4. 进行意图决策
            decision_result = await self._make_decision_stateless(message, session_context)

            if progress_callback:
                progress_callback(ProgressUpdate(
                    stage=ProgressStage.GENERATING,
                    percentage=75,
                    message="✨ 正在生成回复..."
                ))

            # 5. 生成回复
            response = await self._generate_response_stateless(message, session_context, decision_result, progress_callback)

            if progress_callback:
                progress_callback(ProgressUpdate(
                    stage=ProgressStage.FINALIZING,
                    percentage=90,
                    message="🏁 正在完成最后处理..."
                ))

            # 6. 构建最终响应，使用当前的session_context（包含最新的领域和类别分类结果）
            result = await self._build_final_response(response, session_context)

            if progress_callback:
                progress_callback(ProgressUpdate(
                    stage=ProgressStage.COMPLETED,
                    percentage=100,
                    message="✅ 处理完成！"
                ))

            return result

        except Exception as e:
            self.logger.error(self.config.get_message_template("error.processing", error=str(e)), exc_info=True)
            error_msg = self.config.get_message_template("error.general_unknown")
            return await self._build_error_response(error_msg, session_id, user_id)

    async def _build_final_response(self, response: Dict[str, Any], session_context: SessionContext) -> Dict[str, Any]:
        """
        构建最终响应，使用最新的session_context信息

        Args:
            response: 初始响应数据
            session_context: 最新的会话上下文

        Returns:
            Dict[str, Any]: 最终响应
        """
        try:
            # 获取响应文本
            response_text = response.get("reply") or response.get("text_response", "")

            # 保存AI回复
            await self.message_manager.save_message(
                conversation_id=session_context.session_id,
                sender_type="assistant",
                content=response_text,
                user_id=session_context.user_id
            )

            # 确定最终的领域和类别结果 - 不使用演示数据
            final_domain_result = session_context.latest_domain_result
            final_category_result = session_context.latest_category_result

            # 添加调试日志，跟踪数据来源
            if final_domain_result:
                self.logger.info(f"[数据流跟踪] 使用真实领域分类结果: {final_domain_result.get('domain_id')}")
            else:
                self.logger.info(f"[数据流跟踪] 暂无领域分类结果（可能在澄清中）")

            if final_category_result:
                self.logger.info(f"[数据流跟踪] 使用真实类别分类结果: {final_category_result.get('category_id')}")
            else:
                self.logger.info(f"[数据流跟踪] 暂无类别分类结果（可能在澄清中）")

            return {
                "success": True,
                "reply": response_text,
                "text_response": response_text,  # 保持向后兼容
                "domain_result": final_domain_result,
                "category_result": final_category_result,
                "focus_points_status": await self._get_formatted_focus_points_status_stateless(session_context),
                "session_id": session_context.session_id,
                "user_id": session_context.user_id
            }

        except Exception as e:
            self.logger.error(f"构建最终响应失败: {e}", exc_info=True)
            return await self._build_error_response("处理失败", session_context.session_id, session_context.user_id)



    async def _ensure_domain_category_info(self, session_context: SessionContext) -> SessionContext:
        """
        确保会话上下文包含最新的领域和分类信息

        Args:
            session_context: 当前会话上下文

        Returns:
            SessionContext: 更新后的会话上下文
        """
        # 如果已有领域和分类信息，直接返回
        if (session_context.latest_domain_result and
            session_context.latest_category_result and
            session_context.current_domain and
            session_context.current_category):
            return session_context

        # 不设置默认领域和类别，让澄清引导机制处理分类失败的情况
        # 这样可以确保系统在没有明确分类结果时不会错误地加载关注点
        self.logger.debug("保持领域和类别信息为空，等待澄清引导或正常分类流程")

        return session_context

    async def _make_decision_stateless(self, message: str, session_context: SessionContext) -> Dict[str, Any]:
        """
        无状态的意图决策方法

        Args:
            message: 用户消息
            session_context: 会话上下文

        Returns:
            Dict[str, Any]: 决策结果
        """
        try:
            # 加载对话历史
            history = await self.message_manager.load_conversation_history(
                session_context.session_id, session_context.user_id
            )

            # 使用意图决策引擎
            decision_result = await self.intent_decision_engine.analyze(
                message,
                context=[{
                    "session_id": session_context.session_id,
                    "user_id": session_context.user_id,
                    "current_state": session_context.current_state.name,
                    "domain": session_context.current_domain,
                    "category": session_context.current_category,
                    "history": history,
                    "conversation_history": history  # 添加conversation_history字段以兼容simplified_decision_engine
                }]
            )

            return decision_result

        except Exception as e:
            self.logger.error(f"意图决策失败: {e}")
            return {"decision": {"action": "handle_unknown_action"}}

    async def _generate_response_stateless(self, message: str, session_context: SessionContext, decision_result: Dict[str, Any], progress_callback=None) -> Dict[str, Any]:
        """
        无状态的响应生成方法

        Args:
            message: 用户消息
            session_context: 会话上下文
            decision_result: 决策结果

        Returns:
            Dict[str, Any]: 响应结果
        """
        try:
            action_command = decision_result.get('decision', {}).get('action')
            # 执行动作

            # 统一使用ActionExecutor处理所有动作（包括知识库查询）
            if self.action_executor and action_command:
                # 使用ActionExecutor处理动作
                action_result = await self._process_with_action_executor_stateless(
                    action_command, message, session_context, decision_result
                )
            else:
                # 如果没有action executor, 创建一个失败的ActionResult
                error_message = self.config.get_message_template("error.request_processing")
                action_result = ActionResult(success=False, content=error_message, error_message="ActionExecutor not available")
                self.logger.warning(f"无法处理动作 {action_command}：ActionExecutor 不可用")

            response_text = action_result.content

            # 从action result的metadata中更新session_context
            if action_result.success and action_result.metadata:
                # 处理领域分类结果 - 只在明确提供有效结果或澄清引导时更新
                if "domain_result" in action_result.metadata and action_result.metadata["domain_result"] is not None:
                    # 有明确的分类结果，更新
                    domain_result = action_result.metadata["domain_result"]
                    session_context.latest_domain_result = domain_result
                    session_context.current_domain = domain_result.get("domain_id")
                    self.logger.debug(f"更新session_context中的领域信息: {session_context.current_domain}")
                elif action_result.metadata.get("requires_domain_clarification"):
                    # 只有在明确的澄清引导场景下才清空
                    session_context.latest_domain_result = None
                    session_context.current_domain = None
                    self.logger.debug("清除session_context中的current_domain（澄清引导中）")

                # 处理类别分类结果 - 只在明确提供有效结果或澄清引导时更新
                if "category_result" in action_result.metadata and action_result.metadata["category_result"] is not None:
                    # 有明确的分类结果，更新
                    category_result = action_result.metadata["category_result"]
                    session_context.latest_category_result = category_result
                    session_context.current_category = category_result.get("category_id")
                    self.logger.debug(f"更新session_context中的类别信息: {session_context.current_category}")
                elif action_result.metadata.get("requires_domain_clarification"):
                    # 只有在明确的澄清引导场景下才清空
                    session_context.latest_category_result = None
                    session_context.current_category = None
                    self.logger.debug("清除session_context中的current_category（澄清引导中）")

            # 返回简化的响应，最终响应构建将在_build_final_response中完成
            return {
                "reply": response_text,
                "text_response": response_text,  # 保持向后兼容
            }

        except Exception as e:
            self.logger.error(f"响应生成失败: {e}", exc_info=True)
            return await self._build_error_response("处理失败", session_context.session_id, session_context.user_id)

    async def _process_with_action_executor_stateless(self, action_command: str, message: str, session_context: SessionContext, decision_result: Dict[str, Any]) -> 'ActionResult':
        """
        使用ActionExecutor处理动作（无状态版本）
        """
        try:
            # 构建ActionContext对象
            action_context = ActionContext(
                action=action_command,
                message=message,
                session_id=session_context.session_id,
                user_id=session_context.user_id,
                decision_result=decision_result,
                history=[],  # 暂时为空，可以后续添加历史信息
                current_state=session_context.current_state.name,
                current_domain=session_context.current_domain,
                current_category=session_context.current_category,
                conversation_flow=self,  # 传递当前的message_processor作为conversation_flow
                emotion=decision_result.get("emotion", "neutral"),  # 传递情绪信息
                additional_data={}
            )

            # 执行动作
            action_result = await self.action_executor.execute_action(action_command, action_context)
            return action_result

        except Exception as e:
            self.logger.error(f"ActionExecutor执行失败: {e}")
            return ActionResult(success=False, content="处理失败", error_message=str(e))

    async def _get_formatted_focus_points_status_stateless(self, session_context: SessionContext) -> List[Dict[str, Any]]:
        """
        获取格式化的关注点状态（无状态版本）
        """
        try:
            # 如果没有领域和类别信息，返回空列表（可能在澄清阶段）
            if not session_context.current_domain or not session_context.current_category:
                self.logger.debug("暂无领域和类别信息，返回空关注点列表")
                return []

            # 加载关注点定义
            focus_points_definitions = await self._load_focus_points(
                session_context.session_id,
                session_context.user_id,
                session_context.current_domain,
                session_context.current_category
            )

            if not focus_points_definitions:
                self.logger.debug(f"未找到关注点定义 - domain: {session_context.current_domain}, category: {session_context.current_category}")
                return []

            # 加载关注点状态
            try:
                # 使用正确的数据库查询方式
                query = self.config.get_database_query("focus_points.get_status")
                focus_point_statuses_records = await self.session_context_manager.db_manager.execute_query(
                    query, (session_context.session_id, session_context.user_id)
                ) or []

                # 转换为字典格式 {focus_id: {status, value}}
                latest_statuses = {}
                for record in focus_point_statuses_records:
                    focus_id = record.get("focus_id")  # 修复：使用正确的字段名
                    if focus_id:
                        latest_statuses[focus_id] = {
                            "status": record.get("status", "pending"),
                            "value": record.get("extracted_info")  # 使用正确的字段名
                        }
            except Exception as e:
                self.logger.warning(f"加载关注点状态失败，使用空状态: {e}")
                latest_statuses = {}

            formatted_status_list = []
            for point_def in focus_points_definitions:
                point_id = point_def.get("id")
                if not point_id:
                    continue

                status_info = latest_statuses.get(point_id, {})
                # 格式化为前端期望的数据结构
                formatted_status_list.append({
                    "priority": point_def.get("priority", "P2"),
                    "point": point_def.get("name", ""),
                    "status": status_info.get("status", "pending"),
                    "value": status_info.get("value")
                })

            return formatted_status_list

        except Exception as e:
            self.logger.error(f"获取关注点状态失败: {e}")
            return []

    # 演示数据方法已移除，系统不再使用演示数据
    # 澄清引导机制确保用户获得真实的分类结果

    async def _load_focus_points(self, session_id: str, user_id: str, domain: str, category: str) -> List[Dict[str, Any]]:
        """加载关注点定义 - 从数据库获取"""
        try:
            # 从数据库获取关注点定义
            focus_points = await self.knowledge_base_agent.get_concern_points(category_id=category)

            if not focus_points:
                self.logger.warning(f"没有找到类别 {category} 的关注点定义")
                return []

            self.logger.info(f"成功加载 {len(focus_points)} 个关注点定义")
            return focus_points

        except Exception as e:
            self.logger.error(f"加载关注点定义失败: {e}")
            return []

    async def _build_error_response(self, error_msg: str, session_id: str, user_id: str) -> Dict[str, Any]:
        """构建错误响应"""
        return {
            "success": False,
            "reply": error_msg,
            "text_response": error_msg,  # 保持向后兼容
            "domain_result": None,
            "category_result": None,
            "focus_points_status": None,
            "error": True,
            "session_id": session_id,
            "user_id": user_id
        }
