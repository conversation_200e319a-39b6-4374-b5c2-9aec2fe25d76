"""
对话流程工具函数和常量

包含：
- ConversationConstants: 配置常量类
- 通用工具函数
- 共享的导入和配置
"""

from backend.config import config_service


class ConversationConstants:
    """集中管理对话流程中使用的配置常量"""

    # 缓存配置对象，避免重复调用
    _config = config_service.get_unified_config()

    # 使用配置文件中的阈值，保留作为回退值
    DEFAULT_TEMPERATURE = _config.get_config("confidence.default", 0.7)
    COMPLETENESS_THRESHOLD = _config.get_config("business.requirement_completion_threshold", 0.8)
    MAX_PENDING_ATTEMPTS = _config.get_config("performance.retry.default", 3)
    CACHE_TTL = _config.get_config("limits.cache_max_size", 300)

    @classmethod
    def get_temperature(cls):
        try:
            from backend.config import config_service
            config = config_service.get_llm_config_with_metadata("conversation_flow")
            return config.get("temperature", cls.DEFAULT_TEMPERATURE)
        except Exception:
            return cls.DEFAULT_TEMPERATURE

    @classmethod
    def get_completeness_threshold(cls):
        return config_service.get_config("business_rules.requirement_collection.completion_threshold", cls.COMPLETENESS_THRESHOLD)

    @classmethod
    def get_max_pending_attempts(cls):
        return config_service.get_config("retry.max_pending_attempts", cls.MAX_PENDING_ATTEMPTS)
