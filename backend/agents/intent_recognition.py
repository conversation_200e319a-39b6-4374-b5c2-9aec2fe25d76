"""意图识别Agent - v3.0 (架构优化版)"""
import logging
import json
import re
from typing import Any, Dict, List, Optional
from dataclasses import dataclass
from abc import ABC, abstractmethod

# 意图识别结果类
@dataclass
class IntentResult:
    """包含了意图、子意图和情感的意图识别结果。"""
    intent: str
    emotion: str
    confidence: float
    entities: Dict[str, Any]
    sub_intent: Optional[str] = None  # 新增子意图字段，可选


# 响应解析器基类
class ResponseParser(ABC):
    """响应解析器抽象基类"""

    @abstractmethod
    def can_parse(self, model_name: str) -> bool:
        """判断是否可以解析指定模型的响应"""

    @abstractmethod
    def parse(self, content: str, logger) -> IntentResult:
        """解析模型响应内容"""


# 通义千问意图识别模型解析器
class QwenIntentParser(ResponseParser):
    """通义千问意图识别模型专用解析器"""

    def can_parse(self, model_name: str) -> bool:
        return "qwen-intent" in model_name

    def parse(self, content: str, logger) -> IntentResult:
        logger.info("使用通义千问意图识别模型解析结果")
        try:
            intent_data = json.loads(content)

            if isinstance(intent_data, list) and len(intent_data) > 0:
                top_intent = intent_data[0]
                return IntentResult(
                    intent=top_intent.get("intent_type", "unknown"),
                    confidence=top_intent.get("confidence", 0.0),
                    emotion=top_intent.get("emotion", "neutral"),
                    entities=top_intent.get("entities", {}),
                    sub_intent=top_intent.get("sub_intent")
                )
            else:
                logger.warning("意图识别模型未返回有效结果")
                return IntentResult(
                    intent="unknown",
                    confidence=0.0,
                    emotion="neutral",
                    entities={},
                    sub_intent=None
                )
        except json.JSONDecodeError:
            logger.error(f"无法解析意图识别结果: {content}")
            return IntentResult(
                intent="unknown",
                confidence=0.0,
                emotion="neutral",
                entities={},
                sub_intent=None
            )


# 通用模型解析器
class GeneralParser(ResponseParser):
    """通用模型解析器"""

    def can_parse(self, model_name: str) -> bool:
        return True  # 作为默认解析器

    def parse(self, content: str, logger) -> IntentResult:
        try:
            # 尝试从响应中提取JSON
            json_data = self._extract_json(content)

            if json_data and all(k in json_data for k in ["intent", "emotion", "confidence"]):
                return IntentResult(
                    intent=json_data.get("intent", "unknown"),
                    emotion=json_data.get("emotion", "neutral"),
                    confidence=float(json_data.get("confidence", 0.0)),
                    entities=json_data.get("entities", {}),
                    sub_intent=json_data.get("sub_intent")
                )
            else:
                logger.warning(f"意图识别结果缺少必要字段: {json_data}")
                return IntentResult(
                    intent="unknown",
                    confidence=0.0,
                    emotion="neutral",
                    entities={},
                    sub_intent=None
                )
        except Exception as e:
            logger.error(f"解析意图识别结果时出错: {str(e)}")
            return IntentResult(
                intent="unknown",
                confidence=0.0,
                emotion="neutral",
                entities={},
                sub_intent=None
            )

    def _extract_json(self, text: str) -> Optional[dict]:
        """从可能包含额外文本的字符串中稳健地提取第一个有效的JSON对象"""
        if not text or not isinstance(text, str):
            return None

        # 匹配被```json ... ```包裹的代码块
        match = re.search(r'```json\s*(\{.*?\})\s*```', text, re.DOTALL)
        if match:
            try:
                return json.loads(match.group(1))
            except json.JSONDecodeError:
                pass

        # 寻找第一个 '{' 和最后一个 '}'
        start_index = text.find('{')
        end_index = text.rfind('}')
        if start_index != -1 and end_index > start_index:
            json_str = text[start_index:end_index+1]
            try:
                return json.loads(json_str)
            except json.JSONDecodeError:
                return None

        return None

# 意图识别Agent
class IntentRecognitionAgent:
    """
    意图识别代理类。
    负责分析用户输入的意图和情感状态，返回结构化的识别结果。

    特性：
    - 支持多种LLM模型
    - 可扩展的响应解析策略
    - 完善的错误处理和降级机制
    - 详细的日志记录
    """

    def __init__(self, llm_service: Any, agent_name: str = "intent_recognition_agent"):
        self.llm_service = llm_service
        self.llm_client = llm_service  # 添加llm_client别名以保持兼容性
        self.agent_name = agent_name
        self.logger = logging.getLogger(__name__)

        # 初始化提示词加载器
        from backend.utils.prompt_loader import PromptLoader
        self.prompt_loader = PromptLoader()

        # 初始化响应解析器
        self.parsers = [
            QwenIntentParser(),
            GeneralParser()  # 作为默认解析器，放在最后
        ]


    async def recognize_intent(self, text: str, conversation_history: List[Dict[str, str]] = None, current_state: str = "IDLE") -> IntentResult:
        """识别用户输入的意图

        参数:
            text: 用户输入文本
            conversation_history: 对话历史记录
            current_state: 当前会话状态

        返回:
            Dict[str, Any]: 意图识别结果
        """
        self.logger.info(f"[三层识别-Layer3] 开始LLM意图识别: '{text}'")

        # 获取当前使用的模型信息 - 修复配置访问
        try:
            from backend.config.service import config_service
            model_config = config_service.get_model_config(agent_name=self.agent_name)
            # 添加模型验证日志 - 显示Agent到模型的映射
            self.logger.info(f"[模型验证] Agent: {self.agent_name} -> 实际模型: {model_config.get('model_name', 'unknown')} ({model_config.get('provider', 'unknown')})")
        except Exception as e:
            self.logger.error(f"获取模型配置失败: {e}, 使用默认配置", exc_info=True)
            model_config = {"model_name": "unknown", "provider": "unknown"}
            self.logger.warning(f"[模型验证] 意图识别使用默认模型配置: {model_config}")

        # 准备对话历史
        formatted_history = self._format_conversation_history(conversation_history) if conversation_history else ""

        # 加载提示词
        try:
            prompt = self.prompt_loader.load_prompt(
                "intent_recognition",
                {
                    "user_input": text,
                    "full_conversation": formatted_history,
                    "current_state": current_state
                }
            )
        except Exception as e:
            # 如果加载失败，使用硬编码的备用模板
            self.logger.warning(f"从文件加载Prompt失败 ('{e}'), 将使用硬编码的备用提示词。")
            prompt = f"""
请分析以下用户输入的意图和情感状态，并以JSON格式返回结果。

用户输入: {text}

对话历史:
{formatted_history}

请返回JSON格式的结果，包含以下字段：
- intent: 用户意图类型
- emotion: 情感状态
- confidence: 置信度(0-1)
- entities: 提取的实体信息

JSON格式示例：
{{
    "intent": "需求描述",
    "emotion": "neutral",
    "confidence": 0.8,
    "entities": {{}}
}}
"""

        # 调用LLM
        self.logger.debug(f"发送意图识别请求到LLM，提示词长度: {len(prompt)}")
        response = await self.llm_client.call_llm(
            messages=[{"role": "user", "content": prompt}],
            agent_name=self.agent_name,
            scenario="intent_recognition"
        )

        # 记录模型响应信息
        self.logger.info(
            f"LLM响应 - 模型: {response.get('model', 'unknown')}, "
            f"耗时: {response.get('duration', 0):.2f}s, "
            f"Tokens: {response.get('token_usage', {}).get('total_tokens', 'N/A')}"
        )

        # 解析响应
        content = response.get("content", "")
        model_name = model_config.get("model_name", "")

        # 记录LLM响应内容摘要
        content_preview = content[:200] + "..." if len(content) > 200 else content
        self.logger.info(f"意图识别LLM响应内容: {content_preview}")

        # 添加调试信息
        self.logger.debug(f"LLM原始响应内容: {content[:500]}...")

        # 使用策略模式选择合适的解析器
        parser = self._get_parser(model_name)
        result = parser.parse(content, self.logger)

        # 记录识别结果
        result_dict = {
            'intent': result.intent,
            'emotion': result.emotion,
            'confidence': result.confidence,
            'entities': result.entities,
            'sub_intent': result.sub_intent
        }
        self.logger.info(f"[两层识别-Layer2] LLM意图识别结果: {json.dumps(result_dict, ensure_ascii=False)}")

        return result

    def _get_parser(self, model_name: str) -> ResponseParser:
        """根据模型名称选择合适的解析器"""
        for parser in self.parsers:
            if parser.can_parse(model_name):
                return parser
        # 如果没有找到合适的解析器，返回默认的通用解析器
        return self.parsers[-1]

    def _format_conversation_history(self, conversation_history: List[Dict[str, str]]) -> str:
        """格式化对话历史为字符串"""
        if not conversation_history:
            return ""

        formatted = []
        for item in conversation_history:
            role = item.get("role", "unknown")
            content = item.get("content", "")
            formatted.append(f"{role}: {content}")

        return "\n".join(formatted)


