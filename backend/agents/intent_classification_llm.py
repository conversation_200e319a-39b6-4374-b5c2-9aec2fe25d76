"""
结构化意图分类LLM
提供基于LLM的结构化意图分类能力，支持单一意图和复合意图的精确识别
"""

import json
import logging
import re
from typing import Dict, List, Optional, Any
from backend.agents.llm_interface import LLMServiceInterface, PromptLoaderInterface
from backend.config.unified_config_loader import UnifiedConfigLoader


class IntentClassificationResult:
    """意图分类结果封装类"""

    def __init__(self, raw_result: Dict[str, Any]):
        self.raw_result = raw_result
        self.is_composite = raw_result.get("is_composite", False)
        self.intents = raw_result.get("intents", [])
        self.primary_intent = raw_result.get("primary_intent", "unknown")
        self.analysis = raw_result.get("analysis", "")

    def get_confidence(self) -> float:
        """获取主要意图的置信度"""
        if not self.intents:
            return 0.5

        # 找到主要意图的置信度
        for intent in self.intents:
            if intent.get("intent") == self.primary_intent:
                return intent.get("confidence", 0.5)

        # 如果没找到，返回第一个意图的置信度
        return self.intents[0].get("confidence", 0.5)

    def get_intent_names(self) -> List[str]:
        """获取所有意图名称列表"""
        return [intent.get("intent", "unknown") for intent in self.intents]

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return self.raw_result


class IntentClassificationLLM:
    """结构化意图分类LLM"""

    def __init__(self, llm_service: LLMServiceInterface, config: UnifiedConfigLoader, prompt_loader: PromptLoaderInterface):
        self.llm_service = llm_service
        self.config = config
        self.prompt_loader = prompt_loader
        self.logger = logging.getLogger(__name__)

        # 配置参数
        self.max_retries = self.config.get_config_value("intent_classification.max_retries", 2)
        self.fallback_enabled = self.config.get_config_value("intent_classification.fallback_enabled", True)

    async def classify_intent(self,
                            message: str,
                            context: Optional[List[Dict]] = None,
                            keyword_hints: Optional[List[str]] = None) -> IntentClassificationResult:
        """
        执行结构化意图分类

        Args:
            message: 用户输入
            context: 对话上下文
            keyword_hints: 关键词提示（来自第一层筛选）

        Returns:
            IntentClassificationResult: 结构化分类结果
        """
        try:
            # 构建提示词
            prompt = self._build_classification_prompt(message, context, keyword_hints)

            # 调用LLM进行分类
            for attempt in range(self.max_retries + 1):
                try:
                    response = await self.llm_service.call_llm(
                        messages=[{"role": "user", "content": prompt}],
                        agent_name="intent_classifier",
                        scenario="structured_intent_classification"
                    )

                    # 解析结果
                    content = response.get("content", "").strip()
                    classification_result = self._parse_classification_result(content)

                    if classification_result:
                        # 验证和标准化结果
                        validated_result = self._validate_and_normalize_result(classification_result)

                        self.logger.info(f"结构化意图分类成功: {validated_result.get('primary_intent')}", extra={
                            "is_composite": validated_result.get("is_composite", False),
                            "intent_count": len(validated_result.get("intents", [])),
                            "confidence": validated_result.get("intents", [{}])[0].get("confidence", 0.0),
                            "attempt": attempt + 1
                        })

                        return IntentClassificationResult(validated_result)

                except Exception as e:
                        self.logger.warning(f"意图分类尝试 {attempt + 1} 失败: {e}")
                        if attempt == self.max_retries:
                            raise

            # 所有尝试都失败，返回回退结果
            return self._get_fallback_result(message, keyword_hints)

        except Exception as e:
            self.logger.error(f"结构化意图分类失败: {e}", exc_info=True)
            return self._get_fallback_result(message, keyword_hints)

    def _build_classification_prompt(self,
                                   message: str,
                                   context: Optional[List[Dict]],
                                   keyword_hints: Optional[List[str]]) -> str:
        """构建分类提示词"""
        try:
            # 提取上下文信息
            current_state = "IDLE"
            conversation_history = ""

            if context and len(context) > 0:
                ctx = context[0]
                current_state = ctx.get("current_state", "IDLE")

                # 构建对话历史
                if "conversation_history" in ctx:
                    history = ctx["conversation_history"]
                    if isinstance(history, list) and len(history) > 0:
                        # 取最近3轮对话作为上下文
                        recent_history = history[-6:] if len(history) > 6 else history

                        formatted_messages = []
                        for msg in recent_history:
                            if isinstance(msg, dict):
                                role = msg.get("role", "unknown")
                                content = msg.get("content", "")
                                role_display = "用户" if role == "user" else "AI"
                                formatted_messages.append(f"{role_display}: {content}")
                            elif isinstance(msg, str):
                                formatted_messages.append(msg)

                        conversation_history = "\n".join(formatted_messages)

            # 处理关键词提示
            keyword_hints_text = ""
            if keyword_hints and keyword_hints != ["needs_llm_analysis"]:
                keyword_hints_text = f"关键词提示：{keyword_hints}（仅供参考，以语义分析为准）"
            else:
                keyword_hints_text = "关键词提示：无明确匹配，需要语义分析"

            # 使用模板加载提示词
            prompt = self.prompt_loader.load_prompt(
                "structured_intent_classification",
                {
                    "user_input": message,
                    "current_state": current_state,
                    "keyword_hints": keyword_hints_text,
                    "full_conversation": conversation_history or "无对话历史"
                }
            )

            self.logger.debug(f"构建结构化分类提示词成功，状态: {current_state}")
            return prompt

        except Exception as e:
            self.logger.warning(f"加载结构化分类模板失败: {e}，使用备用提示词")
            return self._build_fallback_prompt(message, current_state, keyword_hints_text)

    def _build_fallback_prompt(self, message: str, current_state: str, keyword_hints_text: str) -> str:
        """构建备用提示词"""
        return f"""
你是一个专业的意图分析专家。请分析用户输入，判断其包含的所有意图。

用户输入：{message}
当前状态：{current_state}
{keyword_hints_text}

请严格按照JSON格式输出：
{{
    "is_composite": true/false,
    "intents": [
        {{
            "intent": "意图名称",
            "confidence": 0.95,
            "text_span": "对应的文本片段",
            "reason": "判断理由"
        }}
    ],
    "primary_intent": "最主要的意图",
    "analysis": "整体分析说明"
}}

可能的意图类型：
- business_requirement: 业务需求
- search_knowledge_base: 知识库查询
- process_answer: 处理回答（在COLLECTING_INFO状态下优先）
- greeting: 问候
- confirm: 确认
- restart: 重新开始
- general_chat: 一般聊天

只返回JSON，不要其他内容。
"""

    def _parse_classification_result(self, content: str) -> Optional[Dict[str, Any]]:
        """
        解析分类结果.
        该方法被设计为健壮的，能够处理LLM返回的、可能包含额外文本的JSON字符串.
        """
        try:
            # 找到第一个 '{' 和最后一个 '}' 来提取JSON块
            start_brace = content.find('{')
            end_brace = content.rfind('}')

            if start_brace != -1 and end_brace != -1 and end_brace > start_brace:
                json_str = content[start_brace:end_brace + 1]
                try:
                    return json.loads(json_str)
                except json.JSONDecodeError as e:
                    self.logger.warning(f"提取后JSON解析失败: {e}, 内容: {json_str[:200]}...")
                    # 如果解析失败，可能是因为JSON不完整，尝试正则作为后备
                    pass

            # 如果找不到大括号或解析失败，尝试使用正则表达式作为后备
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                try:
                    return json.loads(json_str)
                except json.JSONDecodeError as e:
                    self.logger.warning(f"正则提取后JSON解析失败: {e}, 内容: {json_str[:200]}...")
                    return None

            self.logger.warning(f"无法从响应中提取JSON: {content[:200]}...")
            return None

        except Exception as e:
            self.logger.error(f"解析分类结果时发生未知错误: {e}")
            return None

    def _validate_and_normalize_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """验证和标准化分类结果"""
        # 验证必需字段
        if "intents" not in result or not isinstance(result["intents"], list):
            result["intents"] = [{"intent": "unknown", "confidence": 0.5, "text_span": "", "reason": "解析失败"}]

        if "is_composite" not in result:
            result["is_composite"] = len(result["intents"]) > 1

        if "primary_intent" not in result:
            result["primary_intent"] = result["intents"][0].get("intent", "unknown") if result["intents"] else "unknown"

        if "analysis" not in result:
            result["analysis"] = "自动生成的分析结果"

        # 验证意图有效性
        valid_intents = self._get_valid_intents()
        for intent_obj in result["intents"]:
            intent_name = intent_obj.get("intent", "unknown")
            if intent_name not in valid_intents:
                self.logger.warning(f"检测到无效意图: {intent_name}，替换为unknown")
                intent_obj["intent"] = "unknown"

        # 确保置信度在合理范围内
        for intent_obj in result["intents"]:
            confidence = intent_obj.get("confidence", 0.5)
            if not isinstance(confidence, (int, float)) or confidence < 0 or confidence > 1:
                intent_obj["confidence"] = 0.5

        return result

    def _get_valid_intents(self) -> List[str]:
        """获取有效的意图列表"""
        return [
            "business_requirement", "search_knowledge_base", "modify_document",
            "clarification", "chitchat", "greeting", "confirm", "restart",
            "process_answer", "request_clarification", "feedback", "general_chat",
            "modify", "unknown", "emotional_support", "ask_question",
            "ask_introduction", "ask_capabilities",  # 🆕 添加缺失的意图
            "domain_specific_query", "process_query", "system_capability_query",
            "provide_information"
        ]

    def _get_fallback_result(self, message: str, keyword_hints: Optional[List[str]]) -> IntentClassificationResult:
        """获取回退结果"""
        if not self.fallback_enabled:
            # 如果禁用回退，返回错误结果
            fallback_result = {
                "is_composite": False,
                "intents": [{"intent": "unknown", "confidence": 0.3, "text_span": message, "reason": "分类失败"}],
                "primary_intent": "unknown",
                "analysis": "意图分类失败，无法确定用户意图"
            }
        else:
            # 基于关键词提示生成回退结果
            if keyword_hints and keyword_hints != ["needs_llm_analysis"]:
                primary_intent = keyword_hints[0]
                is_composite = len(keyword_hints) > 1

                intents = []
                for hint in keyword_hints:
                    intents.append({
                        "intent": hint,
                        "confidence": 0.6,
                        "text_span": message,
                        "reason": f"基于关键词匹配的回退结果"
                    })

                fallback_result = {
                    "is_composite": is_composite,
                    "intents": intents,
                    "primary_intent": primary_intent,
                    "analysis": f"LLM分类失败，基于关键词提示的回退结果"
                }
            else:
                # 完全无法确定，返回通用聊天
                fallback_result = {
                    "is_composite": False,
                    "intents": [{"intent": "general_chat", "confidence": 0.4, "text_span": message, "reason": "无法确定具体意图"}],
                    "primary_intent": "general_chat",
                    "analysis": "无关键词匹配且LLM分类失败，默认为一般聊天"
                }

        self.logger.warning(f"使用回退结果: {fallback_result['primary_intent']}")
        return IntentClassificationResult(fallback_result)
