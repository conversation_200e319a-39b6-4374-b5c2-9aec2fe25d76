"""
统一决策引擎数据类型定义

定义决策引擎中使用的核心数据结构，包括：
- 决策上下文
- 决策结果
- 策略接口
- 冲突解决相关类型
"""

import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional

from backend.agents.unified_state_manager import ConversationState


# ==================== 基础枚举类型 ====================

class DecisionConfidence(Enum):
    """决策置信度等级"""
    VERY_LOW = 0.2
    LOW = 0.4
    MEDIUM = 0.6
    HIGH = 0.8
    VERY_HIGH = 0.95


class ConflictType(Enum):
    """冲突类型"""
    PRIORITY_CONFLICT = "priority_conflict"
    INTENT_OVERLAP = "intent_overlap"
    STATE_TRANSITION_CONFLICT = "state_transition_conflict"
    CONDITION_CONFLICT = "condition_conflict"


class ConflictSeverity(Enum):
    """冲突严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


# ==================== 决策上下文 ====================

@dataclass
class DecisionContext:
    """决策上下文 - 包含决策所需的所有信息"""

    # 基础信息
    session_id: str
    user_id: str
    message: str
    timestamp: float = field(default_factory=time.time)

    # 对话历史和状态
    conversation_history: List[Dict[str, Any]] = field(default_factory=list)
    current_state: ConversationState = ConversationState.IDLE

    # 用户和业务上下文
    user_profile: Dict[str, Any] = field(default_factory=dict)
    business_context: Dict[str, Any] = field(default_factory=dict)

    # 扩展信息
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "message": self.message,
            "timestamp": self.timestamp,
            "conversation_history": self.conversation_history,
            "current_state": self.current_state.value if self.current_state else None,
            "user_profile": self.user_profile,
            "business_context": self.business_context,
            "metadata": self.metadata
        }


@dataclass
class AnalyzedContext:
    """分析后的上下文 - 包含意图识别和情感分析结果"""

    # 原始上下文
    original_context: DecisionContext

    # 分析结果
    intent: str
    confidence: float
    emotion: str = "neutral"
    entities: Dict[str, Any] = field(default_factory=dict)

    # 分析元数据
    analysis_timestamp: float = field(default_factory=time.time)
    analyzer_version: str = "1.0"

    @property
    def session_id(self) -> str:
        return self.original_context.session_id

    @property
    def user_id(self) -> str:
        return self.original_context.user_id

    @property
    def message(self) -> str:
        return self.original_context.message

    @property
    def current_state(self) -> ConversationState:
        return self.original_context.current_state

    @property
    def conversation_history(self) -> List[Dict[str, Any]]:
        return self.original_context.conversation_history


# ==================== 决策结果 ====================

@dataclass
class DecisionResult:
    """决策结果 - 统一的决策输出格式"""

    # 核心决策信息
    action: str
    confidence: float
    intent: str
    emotion: str = "neutral"

    # 状态和模板
    next_state: Optional[ConversationState] = None
    response_template: str = ""

    # 执行信息
    strategy_name: str = ""
    execution_time: float = 0.0

    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式（兼容现有系统）"""
        return {
            "action": self.action,
            "confidence": self.confidence,
            "intent": self.intent,
            "emotion": self.emotion,
            "next_state": self.next_state.value if self.next_state else None,
            "response_template": self.response_template,
            "strategy_name": self.strategy_name,
            "execution_time": self.execution_time,
            "metadata": self.metadata,
            "timestamp": self.timestamp
        }

    def to_legacy_format(self) -> Dict[str, Any]:
        """转换为旧版本格式（向后兼容）"""
        return {
            "decision": {
                "action": self.action,
                "priority": int(self.confidence * 10),  # 转换置信度为优先级
                "response_template": self.response_template,
                "next_state": self.next_state.value if self.next_state else None
            },
            "intent": self.intent,
            "emotion": self.emotion,
            "confidence": self.confidence,
            "metadata": self.metadata
        }


# ==================== 策略冲突相关 ====================

@dataclass
class StrategyConflict:
    """策略冲突信息"""

    conflict_type: ConflictType
    severity: ConflictSeverity
    conflicted_strategies: List[str]
    context: AnalyzedContext

    # 冲突详情
    conflict_details: Dict[str, Any] = field(default_factory=dict)
    resolution_suggestion: str = ""

    # 时间信息
    detected_at: float = field(default_factory=time.time)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "conflict_type": self.conflict_type.value,
            "severity": self.severity.value,
            "conflicted_strategies": self.conflicted_strategies,
            "conflict_details": self.conflict_details,
            "resolution_suggestion": self.resolution_suggestion,
            "detected_at": self.detected_at,
            "context": {
                "session_id": self.context.session_id,
                "intent": self.context.intent,
                "confidence": self.context.confidence,
                "current_state": self.context.current_state.value
            }
        }


@dataclass
class ConflictResolution:
    """冲突解决结果"""

    original_conflict: StrategyConflict
    selected_strategy: str
    resolution_method: str
    resolution_confidence: float

    # 解决过程信息
    resolution_time: float = 0.0
    resolution_metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "selected_strategy": self.selected_strategy,
            "resolution_method": self.resolution_method,
            "resolution_confidence": self.resolution_confidence,
            "resolution_time": self.resolution_time,
            "resolution_metadata": self.resolution_metadata,
            "original_conflict": self.original_conflict.to_dict()
        }


# ==================== 策略接口 ====================

class DecisionStrategy(ABC):
    """决策策略基类 - 所有策略必须实现的接口"""

    @property
    @abstractmethod
    def name(self) -> str:
        """策略名称"""
        pass

    @property
    @abstractmethod
    def supported_intents(self) -> List[str]:
        """支持的意图列表"""
        pass

    @property
    def priority(self) -> int:
        """策略优先级（数字越大优先级越高）"""
        return 0

    @abstractmethod
    async def can_handle(self, context: AnalyzedContext) -> bool:
        """判断是否能处理当前上下文"""
        pass

    @abstractmethod
    async def execute(self, context: AnalyzedContext) -> DecisionResult:
        """执行决策策略"""
        pass

    async def calculate_confidence(self, context: AnalyzedContext) -> float:
        """计算对当前上下文的处理置信度"""
        if await self.can_handle(context):
            return DecisionConfidence.MEDIUM.value
        return DecisionConfidence.VERY_LOW.value

    async def calculate_context_match(self, context: AnalyzedContext) -> float:
        """计算与上下文的匹配度"""
        if context.intent in self.supported_intents:
            return 1.0
        return 0.0


# ==================== 工具函数 ====================

def create_decision_context(session_id: str,
                          user_id: str,
                          message: str,
                          **kwargs) -> DecisionContext:
    """创建决策上下文的便捷函数"""
    return DecisionContext(
        session_id=session_id,
        user_id=user_id,
        message=message,
        conversation_history=kwargs.get('conversation_history', []),
        current_state=kwargs.get('current_state', ConversationState.IDLE),
        user_profile=kwargs.get('user_profile', {}),
        business_context=kwargs.get('business_context', {}),
        metadata=kwargs.get('metadata', {})
    )


def create_decision_result(action: str,
                         confidence: float,
                         intent: str,
                         **kwargs) -> DecisionResult:
    """创建决策结果的便捷函数"""
    return DecisionResult(
        action=action,
        confidence=confidence,
        intent=intent,
        emotion=kwargs.get('emotion', 'neutral'),
        next_state=kwargs.get('next_state'),
        response_template=kwargs.get('response_template', ''),
        strategy_name=kwargs.get('strategy_name', ''),
        execution_time=kwargs.get('execution_time', 0.0),
        metadata=kwargs.get('metadata', {})
    )


# ==================== 类型别名 ====================

# 为了向后兼容，定义一些类型别名
LegacyDecisionResult = Dict[str, Any]
LegacyContext = List[Dict[str, Any]]

# 导出所有公共类型
__all__ = [
    # 枚举
    'DecisionConfidence', 'ConflictType', 'ConflictSeverity',
    # 数据类
    'DecisionContext', 'AnalyzedContext', 'DecisionResult',
    'StrategyConflict', 'ConflictResolution',
    # 接口
    'DecisionStrategy',
    # 工具函数
    'create_decision_context', 'create_decision_result',
    # 类型别名
    'LegacyDecisionResult', 'LegacyContext'
]
