"""
统一决策引擎接口定义

定义统一决策引擎的核心接口，包括：
- 决策引擎主接口
- 策略注册接口
- 上下文分析接口
- 冲突解决接口
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Set

from .decision_types import (
    DecisionContext, AnalyzedContext, DecisionResult,
    DecisionStrategy, StrategyConflict, ConflictResolution,
    LegacyDecisionResult, LegacyContext
)


# ==================== 核心决策引擎接口 ====================

class DecisionEngineInterface(ABC):
    """统一决策引擎接口 - 所有决策引擎必须实现的核心接口"""

    @abstractmethod
    async def make_decision(self,
                          context: DecisionContext) -> DecisionResult:
        """
        核心决策方法 - 基于上下文做出决策

        Args:
            context: 决策上下文

        Returns:
            DecisionResult: 决策结果
        """
        pass

    @abstractmethod
    async def analyze_context(self,
                            context: DecisionContext) -> AnalyzedContext:
        """
        分析决策上下文

        Args:
            context: 原始决策上下文

        Returns:
            AnalyzedContext: 分析后的上下文
        """
        pass

    @abstractmethod
    def register_strategy(self, strategy: DecisionStrategy) -> None:
        """
        注册决策策略

        Args:
            strategy: 要注册的策略
        """
        pass

    @abstractmethod
    def unregister_strategy(self, strategy_name: str) -> bool:
        """
        注销决策策略

        Args:
            strategy_name: 策略名称

        Returns:
            bool: 是否成功注销
        """
        pass

    @abstractmethod
    def get_registered_strategies(self) -> List[str]:
        """
        获取已注册的策略列表

        Returns:
            List[str]: 策略名称列表
        """
        pass

    @abstractmethod
    def get_supported_intents(self) -> Set[str]:
        """
        获取支持的意图集合

        Returns:
            Set[str]: 支持的意图集合
        """
        pass

    # ==================== 向后兼容接口 ====================

    async def analyze(self,
                     message: str,
                     context: Optional[LegacyContext] = None) -> LegacyDecisionResult:
        """
        向后兼容的分析方法

        Args:
            message: 用户消息
            context: 旧版本上下文格式

        Returns:
            LegacyDecisionResult: 旧版本决策结果格式
        """
        # 转换为新格式
        session_id = "legacy_session"
        user_id = "legacy_user"

        if context and len(context) > 0:
            ctx_data = context[0]
            session_id = ctx_data.get("session_id", session_id)
            user_id = ctx_data.get("user_id", user_id)

        decision_context = DecisionContext(
            session_id=session_id,
            user_id=user_id,
            message=message,
            conversation_history=context or [],
            metadata={"legacy_call": True}
        )

        # 执行决策
        result = await self.make_decision(decision_context)

        # 转换为旧格式
        return result.to_legacy_format()


# ==================== 策略注册中心接口 ====================

class StrategyRegistryInterface(ABC):
    """策略注册中心接口"""

    @abstractmethod
    def register_strategy(self, strategy: DecisionStrategy) -> None:
        """注册策略"""
        pass

    @abstractmethod
    def unregister_strategy(self, strategy_name: str) -> bool:
        """注销策略"""
        pass

    @abstractmethod
    def get_strategy(self, strategy_name: str) -> Optional[DecisionStrategy]:
        """获取指定策略"""
        pass

    @abstractmethod
    def get_all_strategies(self) -> List[DecisionStrategy]:
        """获取所有策略"""
        pass

    @abstractmethod
    async def match_strategies(self,
                             context: AnalyzedContext) -> List[DecisionStrategy]:
        """
        匹配适用的策略

        Args:
            context: 分析后的上下文

        Returns:
            List[DecisionStrategy]: 匹配的策略列表
        """
        pass

    @abstractmethod
    def get_strategies_by_intent(self, intent: str) -> List[DecisionStrategy]:
        """根据意图获取策略"""
        pass

    @abstractmethod
    def validate_strategy_consistency(self) -> List[Dict[str, Any]]:
        """验证策略一致性"""
        pass


# ==================== 上下文分析器接口 ====================

class ContextAnalyzerInterface(ABC):
    """上下文分析器接口"""

    @abstractmethod
    async def analyze(self,
                     context: DecisionContext) -> AnalyzedContext:
        """
        分析决策上下文

        Args:
            context: 原始决策上下文

        Returns:
            AnalyzedContext: 分析后的上下文
        """
        pass

    @abstractmethod
    async def analyze_intent(self, message: str) -> str:
        """分析意图"""
        pass

    @abstractmethod
    async def analyze_emotion(self, message: str) -> str:
        """分析情感"""
        pass

    @abstractmethod
    async def extract_entities(self, message: str) -> Dict[str, Any]:
        """提取实体"""
        pass

    @abstractmethod
    def get_analysis_confidence(self,
                                  context: AnalyzedContext) -> float:
        """获取分析置信度"""
        pass


# ==================== 冲突检测和解决接口 ====================

class ConflictDetectorInterface(ABC):
    """冲突检测器接口"""

    @abstractmethod
    async def detect_conflicts(self,
                             matched_strategies: List[DecisionStrategy],
                             context: AnalyzedContext) -> List[StrategyConflict]:
        """
        检测策略冲突

        Args:
            matched_strategies: 匹配的策略列表
            context: 分析后的上下文

        Returns:
            List[StrategyConflict]: 检测到的冲突列表
        """
        pass

    @abstractmethod
    def detect_static_conflicts(self) -> List[StrategyConflict]:
        """检测静态冲突（配置时冲突）"""
        pass

    @abstractmethod
    async def detect_runtime_conflicts(self,
                                     strategies: List[DecisionStrategy],
                                     context: AnalyzedContext) -> List[StrategyConflict]:
        """检测运行时冲突"""
        pass


class ConflictResolverInterface(ABC):
    """冲突解决器接口"""

    @abstractmethod
    async def resolve_conflict(self,
                             conflict: StrategyConflict) -> ConflictResolution:
        """
        解决策略冲突

        Args:
            conflict: 策略冲突

        Returns:
            ConflictResolution: 冲突解决结果
        """
        pass

    @abstractmethod
    async def resolve_multiple_conflicts(self,
                                       conflicts: List[StrategyConflict]) -> List[ConflictResolution]:
        """解决多个冲突"""
        pass

    @abstractmethod
    def get_resolution_strategies(self) -> List[str]:
        """获取可用的解决策略"""
        pass


# ==================== 监控和调试接口 ====================

class DecisionMonitorInterface(ABC):
    """决策监控接口"""

    @abstractmethod
    def record_decision(self,
                       context: DecisionContext,
                       result: DecisionResult,
                          execution_time: float) -> None:
        """记录决策信息"""
        pass

    @abstractmethod
    def record_conflict(self,
                       conflict: StrategyConflict,
                          resolution: Optional[ConflictResolution] = None) -> None:
        """记录冲突信息"""
        pass

    @abstractmethod
    def get_metrics(self) -> Dict[str, Any]:
        """获取监控指标"""
        pass

    @abstractmethod
    def get_decision_history(self,
                           session_id: Optional[str] = None,
                              limit: int = 100) -> List[Dict[str, Any]]:
        """获取决策历史"""
        pass


class DecisionDebuggerInterface(ABC):
    """决策调试器接口"""

    @abstractmethod
    async def debug_decision(self,
                           context: DecisionContext) -> Dict[str, Any]:
        """调试决策过程"""
        pass

    @abstractmethod
    def enable_debug_mode(self) -> None:
        """启用调试模式"""
        pass

    @abstractmethod
    def disable_debug_mode(self) -> None:
        """禁用调试模式"""
        pass

    @abstractmethod
    def is_debug_enabled(self) -> bool:
        """检查是否启用调试模式"""
        pass


# ==================== 配置管理接口 ====================

class DecisionConfigInterface(ABC):
    """决策配置接口"""

    @abstractmethod
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置"""
        pass

    @abstractmethod
    def save_config(self, config: Dict[str, Any], config_path: str) -> None:
        """保存配置"""
        pass

    @abstractmethod
    def get_strategy_config(self, strategy_name: str) -> Dict[str, Any]:
        """获取策略配置"""
        pass

    @abstractmethod
    def update_strategy_config(self,
                             strategy_name: str,
                                config: Dict[str, Any]) -> None:
        """更新策略配置"""
        pass

    @abstractmethod
    def validate_config(self, config: Dict[str, Any]) -> List[str]:
        """验证配置"""
        pass


# ==================== 导出接口 ====================

__all__ = [
    # 核心接口
    'DecisionEngineInterface',
    'StrategyRegistryInterface',
    'ContextAnalyzerInterface',

    # 冲突处理接口
    'ConflictDetectorInterface',
    'ConflictResolverInterface',

    # 监控调试接口
    'DecisionMonitorInterface',
    'DecisionDebuggerInterface',

    # 配置管理接口
    'DecisionConfigInterface'
]
