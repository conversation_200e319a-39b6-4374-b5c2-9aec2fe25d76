"""
决策引擎兼容性适配器

为现有代码提供无缝迁移支持，将SimplifiedDecisionEngine的调用
透明地转换为UnifiedDecisionEngine的调用，保持100%向后兼容。

主要功能：
1. 接口适配 - 保持原有的analyze方法签名
2. 数据格式转换 - 自动转换输入输出格式
3. 性能监控 - 记录迁移过程中的性能指标
4. 渐进式替换 - 支持逐步替换现有调用
"""

import logging
import time
from typing import Any, Dict, List, Optional

from .unified_decision_engine import get_unified_decision_engine
from .decision_types import create_decision_context
from .unified_state_manager import ConversationState


class DecisionEngineAdapter:
    """决策引擎适配器 - 提供SimplifiedDecisionEngine兼容接口"""

    def __init__(self, use_unified_engine: bool = True):
        """
        初始化适配器

        Args:
            use_unified_engine: 是否使用统一决策引擎（True）还是原有引擎（False）
        """
        self.logger = logging.getLogger(__name__)
        self.use_unified_engine = use_unified_engine

        # 性能监控
        self.call_count = 0
        self.total_time = 0.0
        self.error_count = 0

        # 初始化引擎
        if use_unified_engine:
            self.unified_engine = get_unified_decision_engine()
            self.logger.info("DecisionEngineAdapter: 使用统一决策引擎")
        else:
            from .simplified_decision_engine import get_simplified_decision_engine
            self.legacy_engine = get_simplified_decision_engine()
            self.logger.info("DecisionEngineAdapter: 使用传统决策引擎")

    async def analyze(self, message: str, context: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        分析消息并做出决策 - 兼容SimplifiedDecisionEngine.analyze接口

        Args:
            message: 用户消息
            context: 上下文信息列表（SimplifiedDecisionEngine格式）

        Returns:
            Dict[str, Any]: 决策结果（SimplifiedDecisionEngine格式）
        """
        start_time = time.time()
        self.call_count += 1

        try:
            if self.use_unified_engine:
                # 使用统一决策引擎
                result = await self._analyze_with_unified_engine(message, context)
            else:
                # 使用传统决策引擎
                result = await self._analyze_with_legacy_engine(message, context)

            # 记录性能指标
            execution_time = time.time() - start_time
            self.total_time += execution_time

            self.logger.debug(f"决策完成: {execution_time:.3f}s, 引擎={'统一' if self.use_unified_engine else '传统'}")

            return result

        except Exception as e:
            self.error_count += 1
            execution_time = time.time() - start_time
            self.total_time += execution_time

            self.logger.error(f"决策失败: {e}", exc_info=True)

            # 返回兼容的错误结果
            return {
                "decision": {
                    "action": "handle_unknown_situation",
                    "priority": 0,
                    "response_template": "system.error",
                    "next_state": None
                },
                "intent": "unknown",
                "emotion": "neutral",
                "confidence": 0.0,
                "metadata": {
                    "error": str(e),
                    "adapter_error": True,
                    "engine_type": "unified" if self.use_unified_engine else "legacy"
                }
            }

    async def _analyze_with_unified_engine(self, message: str, context: Optional[List[Dict[str, Any]]]) -> Dict[str, Any]:
        """使用统一决策引擎进行分析"""
        # 1. 转换输入格式
        decision_context = self._convert_to_unified_context(message, context)

        # 2. 调用统一决策引擎
        decision_result = await self.unified_engine.make_decision(decision_context)

        # 3. 转换输出格式为SimplifiedDecisionEngine兼容格式
        legacy_result = self._convert_to_legacy_format(decision_result, message, context)

        return legacy_result

    async def _analyze_with_legacy_engine(self, message: str, context: Optional[List[Dict[str, Any]]]) -> Dict[str, Any]:
        """使用传统决策引擎进行分析"""
        return await self.legacy_engine.analyze(message, context)

    def _convert_to_unified_context(self, message: str, context: Optional[List[Dict[str, Any]]]):
        """将SimplifiedDecisionEngine的上下文格式转换为UnifiedDecisionEngine格式"""
        # 提取基础信息
        session_id = "unknown"
        user_id = "unknown"
        current_state = ConversationState.IDLE
        conversation_history = []

        if context and len(context) > 0:
            ctx = context[0]
            session_id = ctx.get("session_id", "unknown")
            user_id = ctx.get("user_id", session_id)  # 如果没有user_id，使用session_id

            # 转换状态
            state_name = ctx.get("current_state", "IDLE")
            try:
                current_state = ConversationState[state_name]
            except (KeyError, TypeError):
                current_state = ConversationState.IDLE

            # 提取对话历史
            conversation_history = ctx.get("conversation_history", ctx.get("history", []))

        # 创建统一决策上下文
        decision_context = create_decision_context(
            session_id=session_id,
            user_id=user_id,
            message=message,
            conversation_history=conversation_history,
            current_state=current_state,
            user_profile=context[0].get("user_profile", {}) if context else {},
            business_context=context[0].get("business_context", {}) if context else {},
            metadata={
                "adapter_conversion": True,
                "original_context": context
            }
        )

        return decision_context

    def _convert_to_legacy_format(self, decision_result, original_message: str, original_context: Optional[List[Dict[str, Any]]]) -> Dict[str, Any]:
        """将UnifiedDecisionEngine的结果转换为SimplifiedDecisionEngine兼容格式"""

        # 转换为传统格式
        legacy_result = {
            "decision": {
                "action": decision_result.action,
                "priority": int(decision_result.confidence * 10),  # 将置信度转换为优先级
                "response_template": decision_result.response_template,
                "next_state": decision_result.next_state.value if decision_result.next_state else None
            },
            "intent": decision_result.intent,
            "emotion": decision_result.emotion,
            "confidence": decision_result.confidence,
            "metadata": {
                **decision_result.metadata,
                "unified_engine_result": True,
                "strategy_name": decision_result.strategy_name,
                "execution_time": decision_result.execution_time,
                "adapter_conversion": True
            }
        }

        return legacy_result

    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取适配器性能指标"""
        avg_time = self.total_time / self.call_count if self.call_count > 0 else 0

        return {
            "total_calls": self.call_count,
            "total_time": self.total_time,
            "average_time": avg_time,
            "error_count": self.error_count,
            "error_rate": self.error_count / self.call_count if self.call_count > 0 else 0,
            "engine_type": "unified" if self.use_unified_engine else "legacy"
        }

    def switch_engine(self, use_unified: bool) -> None:
        """切换引擎类型"""
        if use_unified != self.use_unified_engine:
            self.use_unified_engine = use_unified

            if use_unified:
                if not hasattr(self, 'unified_engine'):
                    self.unified_engine = get_unified_decision_engine()
                self.logger.info("切换到统一决策引擎")
            else:
                if not hasattr(self, 'legacy_engine'):
                    from .simplified_decision_engine import get_simplified_decision_engine
                    self.legacy_engine = get_simplified_decision_engine()
                self.logger.info("切换到传统决策引擎")


# 全局适配器实例
_global_adapter: Optional[DecisionEngineAdapter] = None


def get_decision_engine_adapter(use_unified_engine: bool = True) -> DecisionEngineAdapter:
    """
    获取全局决策引擎适配器实例

    Args:
        use_unified_engine: 是否使用统一决策引擎

    Returns:
        DecisionEngineAdapter: 适配器实例
    """
    global _global_adapter
    if _global_adapter is None:
        _global_adapter = DecisionEngineAdapter(use_unified_engine)
    elif _global_adapter.use_unified_engine != use_unified_engine:
        _global_adapter.switch_engine(use_unified_engine)

    return _global_adapter


def reset_decision_engine_adapter() -> None:
    """重置全局适配器实例（主要用于测试）"""
    global _global_adapter
    _global_adapter = None


# 兼容性函数 - 提供与SimplifiedDecisionEngine相同的接口
def get_simplified_decision_engine_compatible():
    """
    获取兼容SimplifiedDecisionEngine接口的适配器

    这个函数可以直接替换原有的get_simplified_decision_engine()调用
    """
    return get_decision_engine_adapter(use_unified_engine=True)


# 导出公共接口
__all__ = [
    'DecisionEngineAdapter',
    'get_decision_engine_adapter',
    'reset_decision_engine_adapter',
    'get_simplified_decision_engine_compatible'
]
