#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Action执行器接口定义

为Action执行器定义抽象接口，实现执行器解耦，便于测试和替换不同的执行器实现。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List
from .base_action_handler import ActionContext, ActionResult


class ActionExecutorInterface(ABC):
    """Action执行器接口"""

    @abstractmethod
    async def execute_action(self, action: str, context: ActionContext) -> ActionResult:
        """
        执行指定的Action

        Args:
            action: 要执行的action名称
            context: action执行上下文

        Returns:
            ActionResult: 执行结果
        """
        pass

    @abstractmethod
    def get_supported_actions(self) -> List[str]:
        """
        获取支持的action列表

        Returns:
            List[str]: 支持的action名称列表
        """
        pass

    @abstractmethod
    def get_handler_info(self) -> Dict[str, Any]:
        """
        获取处理器信息

        Returns:
            Dict[str, Any]: 处理器信息字典
        """
        pass

    @abstractmethod
    def reload_handlers(self) -> None:
        """
        重新加载处理器
        """
        pass


class ConversationFlowInterface(ABC):
    """对话流程接口"""

    @abstractmethod
    async def process_message(self,
                            message: str,
                            session_id: str,
                            user_id: str,
                            **kwargs) -> Dict[str, Any]:
        """
        处理用户消息

        Args:
            message: 用户消息
            session_id: 会话ID
            user_id: 用户ID
            **kwargs: 其他参数

        Returns:
            Dict[str, Any]: 处理结果
        """
        pass

    @abstractmethod
    def get_session_context(self, session_id: str, user_id: str) -> Any:
        """
        获取会话上下文

        Args:
            session_id: 会话ID
            user_id: 用户ID

        Returns:
            Any: 会话上下文对象
        """
        pass


class MessageProcessorInterface(ABC):
    """消息处理器接口"""

    @abstractmethod
    async def process_message(self,
                            message: str,
                            session_context: Any,
                            **kwargs) -> Dict[str, Any]:
        """
        处理消息

        Args:
            message: 用户消息
            session_context: 会话上下文
            **kwargs: 其他参数

        Returns:
            Dict[str, Any]: 处理结果
        """
        pass

    @abstractmethod
    async def _load_session_context(self,
                                  session_id: str,
                                  user_id: str) -> Any:
        """
        加载会话上下文

        Args:
            session_id: 会话ID
            user_id: 用户ID

        Returns:
            Any: 会话上下文对象
        """
        pass
