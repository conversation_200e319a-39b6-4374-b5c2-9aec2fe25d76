#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置监控API

提供配置监控相关的REST API接口
"""

from fastapi import APIRouter, HTTPException, Query
from typing import Dict, Any, List, Optional
import logging

from ..config.monitoring.dashboard import get_config_dashboard
from ..config.monitoring.prometheus_exporter import generate_prometheus_metrics
from ..config.metrics_collector import get_metrics_collector
from ..config.manager import get_config_manager

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/monitoring", tags=["monitoring"])


@router.get("/health")
async def health_check():
    """健康检查接口"""
    try:
        config_manager = get_config_manager()
        metrics_collector = get_metrics_collector()
        
        # 基本健康检查
        health_status = {
            "status": "healthy",
            "timestamp": metrics_collector.get_metrics()["timestamp"],
            "config_loaded": config_manager._config_cache is not None,
            "metrics_available": True
        }
        
        return health_status
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@router.get("/metrics")
async def get_metrics():
    """获取配置相关指标"""
    try:
        metrics_collector = get_metrics_collector()
        metrics = metrics_collector.get_metrics()
        
        return {
            "success": True,
            "data": metrics
        }
        
    except Exception as e:
        logger.error(f"获取指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取指标失败: {str(e)}")


@router.get("/metrics/prometheus")
async def get_prometheus_metrics():
    """获取Prometheus格式的指标"""
    try:
        prometheus_data = generate_prometheus_metrics()
        
        return {
            "success": True,
            "data": prometheus_data,
            "content_type": "text/plain"
        }
        
    except Exception as e:
        logger.error(f"获取Prometheus指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取Prometheus指标失败: {str(e)}")


@router.get("/dashboard")
async def get_dashboard():
    """获取监控面板数据"""
    try:
        dashboard = get_config_dashboard()
        dashboard_data = dashboard.get_dashboard_data()
        
        return {
            "success": True,
            "data": dashboard_data
        }
        
    except Exception as e:
        logger.error(f"获取监控面板数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取监控面板数据失败: {str(e)}")


@router.get("/alerts")
async def get_alerts(limit: int = Query(50, ge=1, le=1000)):
    """获取告警列表"""
    try:
        dashboard = get_config_dashboard()
        alerts = dashboard.get_alerts(limit)
        
        return {
            "success": True,
            "data": alerts,
            "total": len(alerts)
        }
        
    except Exception as e:
        logger.error(f"获取告警列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取告警列表失败: {str(e)}")


@router.get("/alert-rules")
async def get_alert_rules():
    """获取告警规则"""
    try:
        dashboard = get_config_dashboard()
        rules = dashboard.get_alert_rules()
        
        return {
            "success": True,
            "data": rules,
            "total": len(rules)
        }
        
    except Exception as e:
        logger.error(f"获取告警规则失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取告警规则失败: {str(e)}")


@router.post("/alert-rules/{rule_name}/disable")
async def disable_alert_rule(rule_name: str):
    """禁用告警规则"""
    try:
        dashboard = get_config_dashboard()
        dashboard.disable_alert_rule(rule_name)
        
        return {
            "success": True,
            "message": f"告警规则 '{rule_name}' 已禁用"
        }
        
    except Exception as e:
        logger.error(f"禁用告警规则失败: {e}")
        raise HTTPException(status_code=500, detail=f"禁用告警规则失败: {str(e)}")


@router.post("/alert-rules/{rule_name}/enable")
async def enable_alert_rule(rule_name: str):
    """启用告警规则"""
    try:
        dashboard = get_config_dashboard()
        dashboard.enable_alert_rule(rule_name)
        
        return {
            "success": True,
            "message": f"告警规则 '{rule_name}' 已启用"
        }
        
    except Exception as e:
        logger.error(f"启用告警规则失败: {e}")
        raise HTTPException(status_code=500, detail=f"启用告警规则失败: {str(e)}")


@router.post("/alerts/{rule_name}/resolve")
async def resolve_alert(rule_name: str):
    """解决告警"""
    try:
        dashboard = get_config_dashboard()
        dashboard.resolve_alert(rule_name)
        
        return {
            "success": True,
            "message": f"告警 '{rule_name}' 已解决"
        }
        
    except Exception as e:
        logger.error(f"解决告警失败: {e}")
        raise HTTPException(status_code=500, detail=f"解决告警失败: {str(e)}")


@router.get("/config/summary")
async def get_config_summary():
    """获取配置摘要"""
    try:
        config_manager = get_config_manager()
        summary = config_manager.get_config_summary()
        
        return {
            "success": True,
            "data": summary
        }
        
    except Exception as e:
        logger.error(f"获取配置摘要失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置摘要失败: {str(e)}")


@router.get("/config/sources")
async def get_config_sources():
    """获取配置来源信息"""
    try:
        config_manager = get_config_manager()
        config_with_sources = config_manager.dump_config(include_sources=True)
        
        return {
            "success": True,
            "data": {
                "sources": config_with_sources.get("sources", {}),
                "source_summary": config_manager.source_tracker.get_source_summary()
            }
        }
        
    except Exception as e:
        logger.error(f"获取配置来源信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置来源信息失败: {str(e)}")


@router.get("/performance")
async def get_performance_metrics():
    """获取性能指标"""
    try:
        metrics_collector = get_metrics_collector()
        metrics = metrics_collector.get_metrics()
        
        # 提取性能相关指标
        performance_data = {
            "load_performance": {
                "total_loads": metrics.get("config_load_count", 0),
                "average_load_time": metrics.get("average_load_time", 0),
                "total_load_time": metrics.get("config_load_time_total", 0),
                "load_errors": metrics.get("config_load_errors", 0)
            },
            "validation_performance": {
                "total_validations": metrics.get("config_validation_count", 0),
                "average_validation_time": metrics.get("average_validation_time", 0),
                "validation_errors": metrics.get("config_validation_errors", 0)
            },
            "override_stats": {
                "env_overrides": metrics.get("env_override_count", 0),
                "env_override_rejections": metrics.get("env_override_rejected_count", 0),
                "fallback_count": metrics.get("config_fallback_count", 0)
            },
            "quality_stats": {
                "unknown_keys": metrics.get("unknown_key_count", 0)
            }
        }
        
        return {
            "success": True,
            "data": performance_data
        }
        
    except Exception as e:
        logger.error(f"获取性能指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取性能指标失败: {str(e)}")


@router.get("/export/dashboard")
async def export_dashboard(format: str = Query("json", regex="^(json)$")):
    """导出监控面板数据"""
    try:
        dashboard = get_config_dashboard()
        exported_data = dashboard.export_dashboard(format)
        
        return {
            "success": True,
            "data": exported_data,
            "format": format
        }
        
    except Exception as e:
        logger.error(f"导出监控面板数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出监控面板数据失败: {str(e)}")


@router.post("/metrics/reset")
async def reset_metrics():
    """重置指标数据（仅用于测试）"""
    try:
        metrics_collector = get_metrics_collector()
        metrics_collector.reset_metrics()
        
        return {
            "success": True,
            "message": "指标数据已重置"
        }
        
    except Exception as e:
        logger.error(f"重置指标数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"重置指标数据失败: {str(e)}")


# 添加到主应用的路由中
def setup_monitoring_routes(app):
    """设置监控路由"""
    app.include_router(router)
    logger.info("配置监控API路由已设置")
