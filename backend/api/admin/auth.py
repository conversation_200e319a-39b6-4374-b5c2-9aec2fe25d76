"""
后台管理认证API
"""
from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from backend.models.admin import (
    AdminLoginRequest, AdminLoginResponse, TokenRefreshResponse,
)
from backend.data.db.admin_manager import AdminDatabaseManager
from backend.data.db.database_manager import DatabaseManager
from backend.utils.jwt_auth import jwt_manager, create_auth_error
from backend.config.settings import get_database_path
import logging

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/auth", tags=["认证"])
security = HTTPBearer()

# 初始化数据库管理器
db_manager = DatabaseManager(get_database_path())
admin_db = AdminDatabaseManager(db_manager)


@router.post("/login", response_model=ApiResponse)
async def admin_login(request: AdminLoginRequest):
    """管理员登录"""
    try:
        # 认证管理员
        admin_user = await admin_db.authenticate_admin(request.username, request.password)
        if not admin_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )

        # 生成JWT令牌
        access_token = jwt_manager.create_access_token(
            admin_id=admin_user.id,
            username=admin_user.username,
            role=admin_user.role
        )

        # 记录登录日志
        await admin_db.log_admin_operation(
            admin_id=admin_user.id,
            action="login",
            details="管理员登录成功"
        )

        response_data = AdminLoginResponse(
            token=access_token,
            user=admin_user,
            expires_in=jwt_manager.access_token_expire_minutes * 60
        )

        return ApiResponse(
            success=True,
            message="登录成功",
            data=response_data.dict()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"管理员登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试"
        )


@router.post("/refresh", response_model=ApiResponse)
async def refresh_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """刷新访问令牌"""
    try:
        token = credentials.credentials

        # 验证当前令牌
        payload = jwt_manager.verify_token(token)
        admin_id = int(payload["sub"])

        # 获取管理员信息
        admin_user = await admin_db.get_admin_by_id(admin_id)
        if not admin_user:
            raise create_auth_error("用户不存在")

        # 生成新的访问令牌
        new_token = jwt_manager.create_access_token(
            admin_id=admin_user.id,
            username=admin_user.username,
            role=admin_user.role
        )

        response_data = TokenRefreshResponse(
            token=new_token,
            expires_in=jwt_manager.access_token_expire_minutes * 60
        )

        return ApiResponse(
            success=True,
            message="令牌刷新成功",
            data=response_data.dict()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"令牌刷新失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="令牌刷新失败"
        )


@router.post("/logout", response_model=ApiResponse)
async def admin_logout(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """管理员退出登录"""
    try:
        token = credentials.credentials

        # 验证令牌并获取管理员信息
        payload = jwt_manager.verify_token(token)
        admin_id = int(payload["sub"])

        # 记录退出日志
        await admin_db.log_admin_operation(
            admin_id=admin_id,
            action="logout",
            details="管理员退出登录"
        )

        return ApiResponse(
            success=True,
            message="退出登录成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"管理员退出登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="退出登录失败"
        )


@router.get("/me", response_model=ApiResponse)
async def get_current_admin(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前管理员信息"""
    try:
        token = credentials.credentials

        # 验证令牌并获取管理员信息
        payload = jwt_manager.verify_token(token)
        admin_id = int(payload["sub"])

        admin_user = await admin_db.get_admin_by_id(admin_id)
        if not admin_user:
            raise create_auth_error("用户不存在")

        return ApiResponse(
            success=True,
            data=admin_user.dict()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取管理员信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )
