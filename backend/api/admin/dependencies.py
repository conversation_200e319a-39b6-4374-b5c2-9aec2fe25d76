"""
后台管理API依赖注入
"""
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional
from backend.models.admin import AdminRole, AdminUserInfo
from backend.data.db.admin_manager import AdminDatabaseManager
from backend.data.db.database_manager import DatabaseManager
from backend.utils.jwt_auth import jwt_manager, Permission<PERSON>he<PERSON>, create_auth_error, create_permission_error
from backend.config.settings import get_database_path
import logging

logger = logging.getLogger(__name__)
security = HTTPBearer()

# 初始化数据库管理器
db_manager = DatabaseManager(get_database_path())
admin_db = AdminDatabaseManager(db_manager)


async def get_current_admin(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> AdminUserInfo:
    """获取当前登录的管理员"""
    try:
        token = credentials.credentials

        # 验证令牌
        payload = jwt_manager.verify_token(token)
        admin_id = int(payload["sub"])

        # 获取管理员信息
        admin_user = await admin_db.get_admin_by_id(admin_id)
        if not admin_user:
            raise create_auth_error("用户不存在")

        return admin_user

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取当前管理员失败: {e}")
        raise create_auth_error("认证失败")


async def get_current_admin_id(
    current_admin: AdminUserInfo = Depends(get_current_admin)
) -> int:
    """获取当前管理员ID"""
    return current_admin.id


async def get_current_admin_role(
    current_admin: AdminUserInfo = Depends(get_current_admin)
) -> AdminRole:
    """获取当前管理员角色"""
    return current_admin.role


def require_permission(permission: str):
    """权限检查依赖"""
    async def permission_checker(
        current_admin: AdminUserInfo = Depends(get_current_admin)
    ) -> AdminUserInfo:
        if not PermissionChecker.check_permission(current_admin.role, permission):
            raise create_permission_error(permission)
        return current_admin

    return permission_checker


def require_role(allowed_roles: list[AdminRole]):
    """角色检查依赖"""
    async def role_checker(
        current_admin: AdminUserInfo = Depends(get_current_admin)
    ) -> AdminUserInfo:
        if current_admin.role not in allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足，需要以下角色之一: {[role.value for role in allowed_roles]}"
            )
        return current_admin

    return role_checker


async def get_client_ip(request: Request) -> str:
    """获取客户端IP地址"""
    # 优先从X-Forwarded-For头获取（适用于代理环境）
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()

    # 从X-Real-IP头获取
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip

    # 最后从客户端地址获取
    return request.client.host if request.client else "unknown"


async def log_admin_operation(
    admin_id: int,
    action: str,
    resource: Optional[str] = None,
    details: Optional[str] = None,
    ip_address: Optional[str] = None
):
    """记录管理员操作日志的辅助函数"""
    try:
        await admin_db.log_admin_operation(
            admin_id=admin_id,
            action=action,
            resource=resource,
            details=details,
            ip_address=ip_address
        )
    except Exception as e:
        logger.error(f"记录操作日志失败: {e}")


# 权限检查的便捷依赖
require_user_management = require_permission("user_management")
require_system_config = require_permission("system_config")
require_data_stats = require_permission("data_stats")
require_system_monitor = require_permission("system_monitor")
require_conversation_management = require_permission("conversation_management")
require_operation_logs = require_permission("operation_logs")

# 角色检查的便捷依赖
require_super_admin = require_role([AdminRole.SUPER_ADMIN])
require_admin = require_role([AdminRole.SUPER_ADMIN, AdminRole.SYSTEM_ADMIN])
require_analyst = require_role([AdminRole.SUPER_ADMIN, AdminRole.SYSTEM_ADMIN, AdminRole.DATA_ANALYST])


class AdminOperationLogger:
    """管理员操作日志记录器"""

    def __init__(self, action: str, resource: Optional[str] = None):
        self.action = action
        self.resource = resource

    async def __call__(
        self,
        request: Request,
        current_admin: AdminUserInfo = Depends(get_current_admin),
        client_ip: str = Depends(get_client_ip)
    ):
        """记录操作日志"""
        await log_admin_operation(
            admin_id=current_admin.id,
            action=self.action,
            resource=self.resource,
            ip_address=client_ip
        )
        return current_admin


def log_operation(action: str, resource: Optional[str] = None):
    """操作日志记录装饰器"""
    return AdminOperationLogger(action, resource)
