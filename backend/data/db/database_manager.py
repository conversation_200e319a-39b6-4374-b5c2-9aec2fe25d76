"""
数据库管理类，用于处理数据库连接和操作
"""
import sqlite3
import logging
import asyncio
from typing import Any, Dict, List, Optional, Tuple, Union
from contextlib import asynccontextmanager
from pathlib import Path
from backend.utils.performance_monitor import performance_monitor

# 数据库连接管理类，负责与数据库的连接和操作。
class DatabaseManager:
    """数据库连接管理类"""

    # 初始化数据库连接管理器
    def __init__(self, db_path: str):
        """
        Args:
            db_path: 数据库文件路径。
        """
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.lock = asyncio.Lock()

        # 确保数据库目录存在
        db_dir = Path(db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)

    # 获取数据库连接的异步上下文管理器
    @asynccontextmanager
    async def get_connection(self):
        """
        Yields:
            sqlite3.Connection: 数据库连接。
        """
        async with self.lock:  # 确保同一时间只有一个连接在操作数据库
            conn = None
            try:
                conn = sqlite3.connect(self.db_path)
                conn.row_factory = sqlite3.Row  # 使查询结果可以通过列名访问
                yield conn
            except sqlite3.Error as e:
                # 对于session_states表不存在的错误，不记录到错误日志（这是预期的）
                if "no such table: session_states" not in str(e):
                    self.logger.error(f"数据库连接错误: {e}")
                if conn:
                    conn.rollback()
                raise
            finally:
                if conn:
                    conn.close()

    # 执行SQL查询并返回结果
    async def execute_query(self, query: str, params: Optional[Union[Dict[str, Any], Tuple[Any, ...], List[Any]]] = None) -> List[Dict[str, Any]]:
        """
        Args:
            query (str): SQL查询语句。
            params (tuple, optional): 查询参数。Defaults to None.

        Returns:
            list: 查询结果列表，每个元素是一个字典。
        """
        self.logger.debug(f"执行SQL: {query}")
        self.logger.debug(f"参数: {params}")

        # 确定查询类型
        query_type = query.strip().upper().split()[0] if query.strip() else "UNKNOWN"

        # 使用性能监控器跟踪数据库查询
        with performance_monitor.track_db_query(query_type):
            async with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)

                if query.strip().upper().startswith("SELECT"):
                    column_names = [description[0] for description in cursor.description] if cursor.description else []
                    rows = cursor.fetchall()
                    return [{column_names[i]: row[i] for i in range(len(column_names))} for row in rows]
                else:
                    conn.commit()
                    return []

    # 执行更新操作并返回受影响的行数
    async def execute_update(self, query: str, params: Optional[Union[Dict[str, Any], Tuple[Any, ...], List[Any]]] = None) -> int:
        """
        Args:
            query: SQL更新语句。
            params: 更新参数。

        Returns:
            int: 受影响的行数。
        """
        # 确定查询类型
        query_type = query.strip().upper().split()[0] if query.strip() else "UNKNOWN"

        # 使用性能监控器跟踪数据库更新
        with performance_monitor.track_db_query(query_type):
            async with self.get_connection() as conn:
                cursor = conn.cursor()
                try:
                    if params:
                        cursor.execute(query, params)
                    else:
                        cursor.execute(query)
                    conn.commit()
                    return cursor.rowcount
                except sqlite3.Error as e:
                    # 对于session_states表不存在的错误，不记录到错误日志（这是预期的）
                    if "no such table: session_states" not in str(e):
                        self.logger.error(f"执行更新失败: {query}, 错误: {e}")
                    conn.rollback()
                    raise

    # 执行批量更新操作并返回受影响的行数
    async def execute_batch(self, query: str, params_list: List[Union[Dict[str, Any], Tuple[Any, ...], List[Any]]]) -> int:
        """
        Args:
            query: SQL更新语句。
            params_list: 参数列表。

        Returns:
            int: 受影响的行数。
        """
        # 确定查询类型
        query_type = f"BATCH_{query.strip().upper().split()[0]}" if query.strip() else "BATCH_UNKNOWN"

        # 使用性能监控器跟踪批量数据库操作
        with performance_monitor.track_db_query(query_type):
            async with self.get_connection() as conn:
                cursor = conn.cursor()
                try:
                    cursor.executemany(query, params_list)
                    conn.commit()
                    return cursor.rowcount
                except sqlite3.Error as e:
                    self.logger.error(f"执行批量更新失败: {query}, 错误: {e}")
                    conn.rollback()
                    raise

    # 在一个事务中执行多个查询
    async def execute_transaction(self, queries: List[Tuple[str, Optional[Union[Dict[str, Any], Tuple[Any, ...], List[Any]]]]]) -> bool:
        """
            Args:
                queries: 查询列表，每个元素是(query, params)元组。

            Returns:
                bool: 事务是否成功。
        """
        # 使用性能监控器跟踪事务操作
        with performance_monitor.track_db_query("TRANSACTION"):
            async with self.get_connection() as conn:
                cursor = conn.cursor()
                try:
                    for query, params in queries:
                        if params:
                            cursor.execute(query, params)
                        else:
                            cursor.execute(query)
                    conn.commit()
                    return True
                except sqlite3.Error as e:
                    self.logger.error(f"执行事务失败: {e}")
                    conn.rollback()
                    return False

    # 获取单条记录
    async def get_record(self, query: str, params: Optional[Union[Dict[str, Any], Tuple[Any, ...], List[Any]]] = None) -> Optional[Dict[str, Any]]:
        """
        Args:
            query: SQL查询语句。
            params: 查询参数。

        Returns:
            Optional[Dict[str, Any]]: 查询结果，如果没有找到则返回None。
        """
        results = await self.execute_query(query, params)
        return results[0] if results else None

    # 检查记录是否存在
    async def record_exists(self, query: str, params: Optional[Union[Dict[str, Any], Tuple[Any, ...], List[Any]]] = None) -> bool:
        """
        Args:
            query: SQL查询语句。
            params: 查询参数。

        Returns:
            bool: 记录是否存在。
        """
        return bool(await self.get_record(query, params))

    # 获取多条记录（execute_query的别名，用于保持API一致性）
    async def get_records(self, query: str, params: Optional[Union[Dict[str, Any], Tuple[Any, ...], List[Any]]] = None) -> List[Dict[str, Any]]:
        """
        获取多条记录（execute_query的别名）

        Args:
            query: SQL查询语句。
            params: 查询参数。

        Returns:
            List[Dict[str, Any]]: 查询结果列表。
        """
        return await self.execute_query(query, params)

