<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置监控面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .header .subtitle {
            opacity: 0.9;
            margin-top: 0.5rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e1e5e9;
        }
        
        .card h2 {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .metric-item:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            color: #666;
        }
        
        .metric-value {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-healthy { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-error { background-color: #e74c3c; }
        
        .alert-item {
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border-radius: 4px;
            border-left: 4px solid;
        }
        
        .alert-critical {
            background-color: #fdf2f2;
            border-left-color: #e74c3c;
        }
        
        .alert-high {
            background-color: #fef9e7;
            border-left-color: #f39c12;
        }
        
        .alert-medium {
            background-color: #f0f9ff;
            border-left-color: #3498db;
        }
        
        .alert-low {
            background-color: #f8f9fa;
            border-left-color: #95a5a6;
        }
        
        .alert-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .alert-description {
            font-size: 0.9rem;
            color: #666;
        }
        
        .alert-meta {
            font-size: 0.8rem;
            color: #999;
            margin-top: 0.25rem;
        }
        
        .refresh-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.2s;
        }
        
        .refresh-button:hover {
            background: #5a6fd8;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
        
        .error {
            background-color: #fdf2f2;
            color: #e74c3c;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }
        
        .timestamp {
            text-align: right;
            color: #999;
            font-size: 0.9rem;
            margin-top: 1rem;
        }
        
        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .auto-refresh input[type="checkbox"] {
            margin: 0;
        }
        
        .auto-refresh select {
            padding: 0.25rem;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 配置监控面板</h1>
        <div class="subtitle">实时监控配置系统的健康状态和性能指标</div>
    </div>
    
    <div class="container">
        <div class="auto-refresh">
            <input type="checkbox" id="autoRefresh" checked>
            <label for="autoRefresh">自动刷新</label>
            <select id="refreshInterval">
                <option value="5">5秒</option>
                <option value="10" selected>10秒</option>
                <option value="30">30秒</option>
                <option value="60">60秒</option>
            </select>
            <button class="refresh-button" onclick="loadDashboard()">🔄 立即刷新</button>
        </div>
        
        <div id="error-container"></div>
        
        <div class="grid">
            <!-- 系统状态卡片 -->
            <div class="card">
                <h2>🏥 系统状态</h2>
                <div id="system-status">
                    <div class="loading">加载中...</div>
                </div>
            </div>
            
            <!-- 性能指标卡片 -->
            <div class="card">
                <h2>📊 性能指标</h2>
                <div id="performance-metrics">
                    <div class="loading">加载中...</div>
                </div>
            </div>
            
            <!-- 配置统计卡片 -->
            <div class="card">
                <h2>⚙️ 配置统计</h2>
                <div id="config-stats">
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>
        
        <!-- 告警信息 -->
        <div class="card">
            <h2>🚨 活跃告警</h2>
            <div id="alerts-container">
                <div class="loading">加载中...</div>
            </div>
        </div>
        
        <div class="timestamp" id="last-update"></div>
    </div>
    
    <script>
        let autoRefreshTimer = null;
        
        // 加载监控面板数据
        async function loadDashboard() {
            try {
                const response = await fetch('/api/monitoring/dashboard');
                const result = await response.json();
                
                if (!result.success) {
                    throw new Error(result.message || '获取数据失败');
                }
                
                const data = result.data;
                updateSystemStatus(data);
                updatePerformanceMetrics(data.metrics);
                updateConfigStats(data.config_summary);
                updateAlerts(data.active_alerts);
                updateTimestamp(data.timestamp);
                clearError();
                
            } catch (error) {
                showError('加载监控数据失败: ' + error.message);
            }
        }
        
        // 更新系统状态
        function updateSystemStatus(data) {
            const container = document.getElementById('system-status');
            const configLoaded = data.config_summary?.config_loaded || false;
            const envOverrideEnabled = data.config_summary?.env_override_enabled || false;
            const totalAlerts = data.total_alerts || 0;
            
            let overallStatus = 'healthy';
            if (totalAlerts > 0) {
                overallStatus = totalAlerts > 5 ? 'error' : 'warning';
            }
            
            container.innerHTML = `
                <div class="metric-item">
                    <span class="metric-label">
                        <span class="status-indicator status-${overallStatus}"></span>
                        整体状态
                    </span>
                    <span class="metric-value">${overallStatus === 'healthy' ? '健康' : overallStatus === 'warning' ? '警告' : '错误'}</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">配置已加载</span>
                    <span class="metric-value">${configLoaded ? '✅ 是' : '❌ 否'}</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">环境变量覆盖</span>
                    <span class="metric-value">${envOverrideEnabled ? '✅ 启用' : '❌ 禁用'}</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">活跃告警</span>
                    <span class="metric-value">${totalAlerts}</span>
                </div>
            `;
        }
        
        // 更新性能指标
        function updatePerformanceMetrics(metrics) {
            const container = document.getElementById('performance-metrics');
            
            container.innerHTML = `
                <div class="metric-item">
                    <span class="metric-label">配置加载次数</span>
                    <span class="metric-value">${metrics.config_load_count || 0}</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">平均加载时间</span>
                    <span class="metric-value">${(metrics.average_load_time || 0).toFixed(4)}s</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">加载错误</span>
                    <span class="metric-value">${metrics.config_load_errors || 0}</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">验证错误</span>
                    <span class="metric-value">${metrics.config_validation_errors || 0}</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">环境覆盖次数</span>
                    <span class="metric-value">${metrics.env_override_count || 0}</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">覆盖拒绝次数</span>
                    <span class="metric-value">${metrics.env_override_rejected_count || 0}</span>
                </div>
            `;
        }
        
        // 更新配置统计
        function updateConfigStats(configSummary) {
            const container = document.getElementById('config-stats');
            const keysBySource = configSummary?.keys_by_source || {};
            
            let statsHtml = '';
            for (const [source, count] of Object.entries(keysBySource)) {
                const sourceNames = {
                    'config_files': '📄 配置文件',
                    'environment': '🌍 环境变量',
                    'runtime': '⚡ 运行时',
                    'defaults': '🔧 默认值'
                };
                
                statsHtml += `
                    <div class="metric-item">
                        <span class="metric-label">${sourceNames[source] || source}</span>
                        <span class="metric-value">${count}</span>
                    </div>
                `;
            }
            
            if (!statsHtml) {
                statsHtml = '<div class="metric-item"><span class="metric-label">暂无数据</span></div>';
            }
            
            container.innerHTML = statsHtml;
        }
        
        // 更新告警信息
        function updateAlerts(alerts) {
            const container = document.getElementById('alerts-container');
            
            if (!alerts || alerts.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #27ae60; padding: 1rem;">✅ 当前无活跃告警</div>';
                return;
            }
            
            const alertsHtml = alerts.map(alert => {
                const severityIcons = {
                    'critical': '🔴',
                    'high': '🟠',
                    'medium': '🟡',
                    'low': '🔵'
                };
                
                return `
                    <div class="alert-item alert-${alert.severity}">
                        <div class="alert-title">
                            ${severityIcons[alert.severity] || '⚪'} ${alert.rule_name}
                        </div>
                        <div class="alert-description">${alert.description}</div>
                        <div class="alert-meta">
                            当前值: ${alert.value}, 阈值: ${alert.threshold} | ${new Date(alert.timestamp).toLocaleString()}
                        </div>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = alertsHtml;
        }
        
        // 更新时间戳
        function updateTimestamp(timestamp) {
            const container = document.getElementById('last-update');
            container.textContent = `最后更新: ${new Date(timestamp).toLocaleString()}`;
        }
        
        // 显示错误信息
        function showError(message) {
            const container = document.getElementById('error-container');
            container.innerHTML = `<div class="error">❌ ${message}</div>`;
        }
        
        // 清除错误信息
        function clearError() {
            const container = document.getElementById('error-container');
            container.innerHTML = '';
        }
        
        // 设置自动刷新
        function setupAutoRefresh() {
            const checkbox = document.getElementById('autoRefresh');
            const interval = document.getElementById('refreshInterval');
            
            function updateAutoRefresh() {
                if (autoRefreshTimer) {
                    clearInterval(autoRefreshTimer);
                    autoRefreshTimer = null;
                }
                
                if (checkbox.checked) {
                    const intervalSeconds = parseInt(interval.value);
                    autoRefreshTimer = setInterval(loadDashboard, intervalSeconds * 1000);
                }
            }
            
            checkbox.addEventListener('change', updateAutoRefresh);
            interval.addEventListener('change', updateAutoRefresh);
            
            // 初始设置
            updateAutoRefresh();
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboard();
            setupAutoRefresh();
        });
        
        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (autoRefreshTimer) {
                clearInterval(autoRefreshTimer);
            }
        });
    </script>
</body>
</html>
