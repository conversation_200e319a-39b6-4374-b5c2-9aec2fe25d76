#!/usr/bin/env python3
"""
硬编码兜底配置 - 代码层最后防线

当所有配置文件都无法加载时，这些硬编码值确保系统能够启动。
这是配置系统的最后一道防线，只包含系统启动的绝对必需项。

优先级：硬编码兜底 < defaults文件 < 环境变量 < 运行时配置

⚠️ 重要：
1. 只包含启动阻断级别（P0）的配置
2. 值应该是最保守、最安全的
3. 修改需要充分测试
4. 不应该包含敏感信息
"""

import logging
from typing import Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)

# ============================================================================
# P0级别硬编码兜底配置 - 系统启动绝对必需
# ============================================================================

# 应用基础信息
HARDCODED_APP_CONFIG = {
    "name": "需求采集系统",
    "version": "3.0",
    "environment": "development"
}

# 数据库配置 - 启动阻断级别
HARDCODED_DATABASE_CONFIG = {
    "path": "backend/data/aidatabase.db",
    "timeout": 30,
    "check_same_thread": False,
    "max_retries": 3,
    "retry_delay": 1
}

# LLM服务配置 - 启动阻断级别
HARDCODED_LLM_CONFIG = {
    "default_model": "deepseek-chat",
    "default_params": {
        "temperature": 0.7,
        "max_tokens": 2000,
        "timeout": 30,
        "max_retries": 3
    },
    "scenario_mapping": {
        "intent_recognition": "deepseek-chat",
        "domain_classifier": "deepseek-chat", 
        "conversation_flow": "deepseek-chat",
        "document_generator": "deepseek-chat"
    }
}

# 系统性能配置 - 功能降级级别
HARDCODED_PERFORMANCE_CONFIG = {
    "llm_timeout": 30,
    "database_timeout": 10,
    "max_concurrent_requests": 50,
    "max_memory_usage": 512  # MB
}

# 业务阈值配置 - 功能降级级别
HARDCODED_THRESHOLDS_CONFIG = {
    "completion_threshold": 0.7,
    "confidence_threshold": 0.8,
    "performance": {
        "retry": {
            "database_operation": 3,
            "llm_request": 3,
            "max_total_attempts": 5
        }
    }
}

# 日志配置 - 可选级别
HARDCODED_LOGGING_CONFIG = {
    "level": "INFO",
    "format": "json",
    "max_file_size": "10MB",
    "backup_count": 5
}

# 安全配置 - 可选级别
HARDCODED_SECURITY_CONFIG = {
    "input_validation": {
        "max_input_length": 10000,
        "max_file_size": 10485760  # 10MB
    },
    "rate_limiting": {
        "requests_per_minute": 60,
        "requests_per_hour": 1000
    },
    "session": {
        "timeout": 3600,  # 1小时
        "max_sessions_per_user": 5
    }
}

# ============================================================================
# 硬编码兜底配置管理器
# ============================================================================

class HardcodedFallbackManager:
    """硬编码兜底配置管理器
    
    提供系统启动时的最后兜底配置，确保在所有配置文件都无法加载时
    系统仍能以最基本的功能启动。
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._config = self._build_fallback_config()
        self.logger.info("硬编码兜底配置管理器初始化完成")
    
    def _build_fallback_config(self) -> Dict[str, Any]:
        """构建完整的兜底配置"""
        return {
            "app": HARDCODED_APP_CONFIG,
            "data": {
                "database": HARDCODED_DATABASE_CONFIG
            },
            "llm": HARDCODED_LLM_CONFIG,
            "system": {
                "performance": HARDCODED_PERFORMANCE_CONFIG
            },
            "thresholds": HARDCODED_THRESHOLDS_CONFIG,
            "logging": HARDCODED_LOGGING_CONFIG,
            "security": HARDCODED_SECURITY_CONFIG,
            # 知识库默认禁用，避免启动依赖
            "knowledge_base": {
                "enabled": False
            }
        }
    
    def get_config(self, key_path: str, default: Any = None) -> Any:
        """获取兜底配置值
        
        Args:
            key_path: 配置键路径，如 "data.database.path"
            default: 默认值
            
        Returns:
            配置值或默认值
        """
        try:
            keys = key_path.split('.')
            value = self._config
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default
                    
            return value
        except Exception as e:
            self.logger.error(f"获取硬编码兜底配置失败: {key_path}, 错误: {e}")
            return default
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self._config["data"]["database"].copy()
    
    def get_llm_config(self) -> Dict[str, Any]:
        """获取LLM配置"""
        return self._config["llm"].copy()
    
    def get_app_config(self) -> Dict[str, Any]:
        """获取应用配置"""
        return self._config["app"].copy()
    
    def get_performance_config(self) -> Dict[str, Any]:
        """获取性能配置"""
        return self._config["system"]["performance"].copy()
    
    def is_critical_config_available(self) -> bool:
        """检查关键配置是否可用
        
        Returns:
            bool: 关键配置是否完整
        """
        critical_paths = [
            "app.name",
            "data.database.path", 
            "llm.default_model",
            "system.performance.llm_timeout"
        ]
        
        for path in critical_paths:
            if self.get_config(path) is None:
                self.logger.error(f"关键配置缺失: {path}")
                return False
                
        return True
    
    def log_fallback_usage(self, config_key: str, reason: str = "配置文件加载失败"):
        """记录兜底配置使用情况
        
        Args:
            config_key: 使用的配置键
            reason: 使用原因
        """
        self.logger.warning(
            f"使用硬编码兜底配置: {config_key}, 原因: {reason}",
            extra={
                "config_key": config_key,
                "fallback_reason": reason,
                "fallback_type": "hardcoded"
            }
        )

# ============================================================================
# 全局实例
# ============================================================================

# 创建全局硬编码兜底管理器实例
hardcoded_fallback_manager = HardcodedFallbackManager()

def get_hardcoded_fallback_manager() -> HardcodedFallbackManager:
    """获取硬编码兜底配置管理器实例"""
    return hardcoded_fallback_manager

# ============================================================================
# 便捷函数
# ============================================================================

def get_critical_database_path() -> str:
    """获取关键数据库路径（硬编码兜底）"""
    return hardcoded_fallback_manager.get_config("data.database.path", "backend/data/aidatabase.db")

def get_critical_llm_model() -> str:
    """获取关键LLM模型（硬编码兜底）"""
    return hardcoded_fallback_manager.get_config("llm.default_model", "deepseek-chat")

def get_critical_app_name() -> str:
    """获取关键应用名称（硬编码兜底）"""
    return hardcoded_fallback_manager.get_config("app.name", "需求采集系统")

def get_critical_llm_timeout() -> int:
    """获取关键LLM超时时间（硬编码兜底）"""
    return hardcoded_fallback_manager.get_config("system.performance.llm_timeout", 30)
