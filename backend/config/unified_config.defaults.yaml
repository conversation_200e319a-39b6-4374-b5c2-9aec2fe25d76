# ============================================================================
# 兜底配置文件 (Fallback Configuration)
# ============================================================================
# 文件说明：系统兜底配置，优先级 -20
# 作用：当模块化配置缺失或加载失败时提供安全默认值
# 维护原则：只保留系统启动必需和紧急回退配置
# 最后更新：2025-08-22
# ============================================================================

# ============================================================================
# 核心系统配置 - 系统启动必需
# ============================================================================
system:
  # 基础系统信息
  version: '3.0'
  description: '需求采集系统统一配置'
  language: 'zh-CN'
  supported_languages:
    - 'zh-CN'
    - 'en-US'
  
  # 系统开关
  debug_mode: false
  fallback_enabled: true
  use_structured_classification: true
  
  # 决策引擎配置
  decision_engine:
    type: 'simplified'
    enable_caching: true
    cache_ttl: 300
    fallback_to_simplified: true
  
  # 性能配置
  performance:
    cache_enabled: true
    cache_ttl: 3600
    llm_timeout: 10
    max_conversation_turns: 15
    max_message_length: 1000
    max_retry_attempts: 3

# ============================================================================
# LLM配置 - 场景映射和参数（独有配置）
# ============================================================================
llm:
  # 默认模型配置（从 defaults.yaml 补充）
  default_model: deepseek-chat

  # 默认参数配置（从 defaults.yaml 补充）
  default_params:
    api_base: https://api.deepseek.com
    api_key: ***********************************
    max_retries: 3
    max_tokens: 4000
    model_name: deepseek-chat
    provider: deepseek
    temperature: 0.7
    timeout: 30
    top_p: 1

  # 场景到模型的映射
  scenario_mapping:
    apology_generator: doubao-pro-32k
    category_classifier: doubao-1.5-Lite
    clarification_generator: doubao-pro-32k
    conversation_flow: qwen-plus
    document_generator: qwen-plus
    domain_classifier: doubao-1.5-Lite
    domain_guidance_generator: doubao-pro-32k
    empathy_generator: doubao-pro-32k
    greeting_generator: doubao-pro-32k
    information_extractor: doubao-pro-32k
    intent_recognition: doubao-pro-32k
    llm_service: deepseek-chat
    optimized_question_generation: qwen-plus
    structured_intent_classification: doubao-pro-32k
  
  # 场景参数配置
  scenario_params:
    default:
      api_base: https://api.deepseek.com
      api_key: ***********************************
      max_retries: 3
      max_tokens: 4000
      model_name: deepseek-chat
      provider: deepseek
      temperature: 0.7
      timeout: 30
    
    apology_generator:
      max_tokens: 200
      temperature: 0.7
      timeout: 30
    
    category_classifier:
      max_tokens: 1500
      temperature: 0.3
      timeout: 20
    
    clarification_generator:
      max_tokens: 5000
      temperature: 0.7
      timeout: 30
    
    conversation_flow:
      max_tokens: 4000
      temperature: 0.7
      timeout: 30
    
    document_generator:
      max_tokens: 6000
      temperature: 0.9
      timeout: 60
    
    domain_classifier:
      max_tokens: 1500
      temperature: 0.3
      timeout: 20
    
    information_extractor:
      max_tokens: 7000
      temperature: 0.3
      timeout: 45
    
    intent_recognition:
      max_tokens: 3000
      temperature: 0.3
      timeout: 30
    
    optimized_question_generation:
      max_tokens: 3500
      temperature: 0.5
      timeout: 12

# ============================================================================
# 关键词规则 - 独有配置
# ============================================================================
keyword_rules:
  ask_question:
    - 什么是
    - 如何
    - 怎么
    - 能否
    - 可以
  
  business_requirement:
    - 我想
    - 我需要
    - 要做
    - 希望
    - 计划
  
  confirm:
    - 确认
    - 没问题
    - 正确
    - 同意
    - 好的
  
  emotional_support:
    - 担心
    - 困惑
    - 不确定
    - 焦虑
    - 紧张
  
  general_chat:
    - 你好
    - 谢谢
    - 再见
    - 聊天
    - 闲聊
  
  greeting:
    - 你好
    - 您好
    - hello
    - hi
    - 早上好
  
  modify:
    - 修改
    - 更改
    - 调整
    - 完善
    - 优化
  
  restart:
    - 重新开始
    - 重来
    - 重置
    - 清空
    - 从头开始

# ============================================================================
# 策略配置 - 独有配置
# ============================================================================
strategies:
  # 全局策略
  GLOBAL:
    default_strategy: 'requirement_strategy'
    fallback_strategy: 'fallback_strategy'
    error_strategy: 'error_handling'
  
  # 默认策略
  DEFAULT_STRATEGY:
    name: 'requirement_strategy'
    description: '需求收集策略'
    priority: 1
  
  # 状态相关策略
  IDLE:
    strategy: 'greeting_strategy'
    timeout: 300
  
  COLLECTING_INFO:
    strategy: 'requirement_strategy'
    max_turns: 10
  
  DOCUMENTING:
    strategy: 'document_generation'
    auto_save: true
  
  # 错误处理策略
  error_handling:
    max_retries: 3
    fallback_template: 'error.system'
    escalation_enabled: true
  
  # 回复策略
  reply:
    default_template: 'greeting.basic'
    personalization_enabled: true
    context_aware: true
  
  # 状态管理
  state:
    auto_transition: true
    state_timeout: 1800
    cleanup_enabled: true

# ============================================================================
# 领域选择配置 - 独有配置
# ============================================================================
direct_domain_selection:
  main_template: '我理解您的需求。请选择您的项目类型：\n1. 设计类项目\n2. 开发类项目\n3. 营销类项目\n4. 法律类项目\n5. 其他类型\n\n请告诉我具体是哪一类？'
  simplified_template: '请选择项目类型：设计、开发、营销、法律，还是其他？'
  fallback_template: '请告诉我您的项目属于哪个领域，这样我能更好地帮助您。'

domain_selection_mapping:
  business: '商业咨询'
  capabilities: '能力咨询'
  chat: '闲聊对话'
  conversation: '对话管理'
  empathy: '情感支持'
  error: '错误处理'
  greeting: '问候交流'
  guidance: '指导建议'
  help: '帮助说明'
  knowledge_base: '知识查询'
  legal: '法律咨询'
  marketing: '营销策划'
  modify: '修改调整'
  requirement: '需求收集'
  restart: '重新开始'
  design: '设计服务'
  development: '开发服务'
  other: '其他服务'

# ============================================================================
# 兜底阈值配置
# ============================================================================
thresholds:
  # 业务阈值
  business:
    requirement_completion_threshold: 0.8
    document_quality_threshold: 0.8
    focus_point_priority_threshold: 0.7
    response_time_threshold: 2.0
    template_match_threshold: 0.6
    user_satisfaction_threshold: 0.8
  
  # 置信度阈值
  confidence:
    default: 0.7
    high: 0.8
    decision_engine: 0.7
    domain_classification: 0.8
    intent_recognition: 0.75
    keyword_matching: 0.6
  
  # 系统限制
  limits:
    max_conversation_turns: 15
    max_focus_points: 10
    max_input_length: 1000
    max_output_length: 5000
    max_retry_attempts: 3
    max_history_items: 20
    max_results: 50
  
  # 性能阈值
  performance:
    response_time_limit: 30
    cache_hit_rate_target: 0.8
    memory_usage_limit: 512
    cpu_usage_limit: 80
  
  # 质量阈值
  quality:
    min_completeness: 0.6
    min_clarity: 0.7
    min_relevance: 0.8
    max_ambiguity: 0.3
  
  # 安全阈值
  security:
    max_failed_attempts: 5
    session_timeout: 3600
    rate_limit_per_minute: 60
    content_filter_threshold: 0.9
  
  # LLM相关阈值
  llm:
    default_temperature: 0.7
    max_tokens_limit: 8000
    timeout_seconds: 45
    retry_backoff_factor: 1.5

# ============================================================================
# 数据库配置 - 系统启动必需
# ============================================================================
database:
  connection:
    path: 'backend/data/aidatabase.db'
    timeout: 30
    max_retries: 3

  tables:
    conversations: 'conversations'
    focus_points: 'focus_points'
    documents: 'documents'

  queries:
    max_results: 100
    timeout: 10
    enable_logging: false

# ============================================================================
# 对话配置 - 核心功能配置
# ============================================================================
conversation:
  # 关键词加速
  keyword_acceleration:
    enabled: true
    confidence_threshold: 0.8
    fallback_enabled: true

  # 对话状态
  states:
    - IDLE
    - COLLECTING_INFO
    - DOCUMENTING
    - COMPLETED
    - ERROR

  # 状态转换
  transitions:
    IDLE_to_COLLECTING_INFO: 'user_input_received'
    COLLECTING_INFO_to_DOCUMENTING: 'requirements_complete'
    DOCUMENTING_to_COMPLETED: 'document_generated'
    any_to_ERROR: 'system_error'
    ERROR_to_IDLE: 'error_resolved'

# ============================================================================
# 知识库配置 - 兜底配置
# ============================================================================
knowledge_base:
  enabled: true

  # ChromaDB配置
  chroma_db:
    host: 'localhost'
    port: 8000
    collection_name: 'requirements_kb'
    embedding_model: 'text-embedding-ada-002'

  # 检索配置
  retrieval:
    max_results: 5
    similarity_threshold: 0.7
    rerank_enabled: false

  # 文档处理
  document_processing:
    chunk_size: 1000
    chunk_overlap: 200
    max_file_size: 10485760  # 10MB

  # 性能配置
  performance:
    cache_enabled: true
    cache_ttl: 3600
    batch_size: 100

  # 安全配置
  safety:
    content_filter_enabled: true
    max_query_length: 1000
    rate_limit_per_minute: 60

  # 角色过滤
  role_filters:
    enabled: true
    allowed_roles: ['user', 'assistant']

  # 功能开关
  features:
    semantic_search: true
    keyword_search: true
    hybrid_search: false

  # 日志配置
  logging:
    enabled: true
    level: 'INFO'
    max_file_size: '10MB'

# ============================================================================
# 消息模板 - 兜底模板
# ============================================================================
message_templates:
  # 问候模板
  greeting:
    basic: '您好！我是AI需求采集助手，专门帮助您整理和分析业务需求。请告诉我您有什么项目需要帮助？'
    friendly: '您好！很高兴为您服务！我是专业的需求分析助手，可以帮您梳理项目需求、生成规范文档。请描述一下您的项目想法吧！'
    professional: '您好，我是AI需求采集系统。我将协助您进行需求分析和文档生成。请详细描述您的项目背景和目标。'

  # 错误模板
  error:
    system: '抱歉，系统遇到了一些问题。请稍后重试，或者重新描述您的需求。'
    timeout: '处理超时，请简化您的需求描述后重试。'
    invalid_input: '输入格式有误，请重新输入。'

  # 确认模板
  confirmation:
    document_finalized: '文档已生成完成！请查看是否满足您的需求。如需修改，请告诉我具体要调整的地方。'
    reset: '好的，我们重新开始。请告诉我您的新需求。'
    understood: '我明白了，让我来帮您整理这些信息。'

  # 澄清模板
  clarification:
    request: '为了更好地帮助您，我需要了解更多细节。请详细描述一下您的具体需求。'
    domain: '请告诉我这是什么类型的项目：设计、开发、营销还是其他？'
    priority: '在这些需求中，哪些是最重要的？请按优先级排序。'

  # 指导模板
  guidance:
    initial: '让我们开始需求收集。请从项目背景开始，告诉我您想要实现什么目标。'
    next_step: '很好！接下来请告诉我更多关于具体实现方式的想法。'
    completion: '需求收集即将完成，让我为您生成需求文档。'

  # 能力说明模板
  capabilities_strategy:
    capability_introduction: '我具备以下核心能力：\n1. 需求分析和整理\n2. 业务流程梳理\n3. 规范文档生成\n4. 多领域项目支持\n\n请告诉我您需要哪方面的帮助？'

  # 系统模板
  system:
    initialization: '系统正在初始化，请稍候...'
    maintenance: '系统维护中，功能可能受限。'
    emergency: '紧急情况下的基础服务仍然可用。'

# ============================================================================
# 业务模板 - 兜底业务模板
# ============================================================================
business_templates:
  # 确认并重定向
  acknowledge_and_redirect:
    main_template: '好的，我明白了。让我们回到需求收集的主题上。请告诉我您希望整理哪些业务需求，或者您想从哪个方面开始？'
    simplified_template: '明白了。让我们专注于您的需求收集工作，请告诉我需要什么帮助？'
    fallback_template: '好的，请告诉我您的需求，我来帮您整理。'

  # 共情并澄清
  empathy_and_clarify:
    main_template: '我理解您的想法。为了给您更好的建议，请详细描述一下项目的具体情况和期望目标。'
    simplified_template: '我明白。请详细说说您的项目需求。'
    fallback_template: '请告诉我更多细节，这样我能更好地帮助您。'

  # 一般聊天
  general_chat:
    main_template: '感谢您的分享！作为需求采集助手，我更擅长帮您整理项目需求。请告诉我您有什么业务需求需要梳理？'
    simplified_template: '谢谢！我主要帮助整理业务需求，请说说您的项目想法。'
    fallback_template: '让我们聊聊您的项目需求吧！'

  # 重新表述并询问
  rephrase_and_inquire:
    main_template: '根据您的描述，我理解您想要{project_type}。请告诉我更多关于{specific_aspect}的详细信息。'
    simplified_template: '我理解您的需求是{summary}。请提供更多细节。'
    fallback_template: '请详细描述一下您的具体需求。'

  # 重置对话
  reset_conversation:
    main_template: '好的，我们重新开始。请详细描述您的项目背景、目标和具体需求，我会帮您系统地整理分析。'
    simplified_template: '重新开始。请说说您的项目需求。'
    fallback_template: '让我们重新开始，请告诉我您的需求。'

# ============================================================================
# 策略模板 - 兜底策略模板
# ============================================================================
strategy_templates:
  # 能力策略
  capabilities_strategy:
    template: '我是专业的AI需求采集助手，具备需求分析、信息整理、文档生成等核心能力。请告诉我您的项目需求，我来帮您系统地梳理分析。'
    fallback: '我可以帮您整理需求、生成文档。请描述您的项目。'

  # 情感支持策略
  emotional_support_strategy:
    template: '我理解您的担忧。让我们一步步来，先从最基本的项目信息开始整理，这样会让整个过程更清晰。'
    fallback: '别担心，我们慢慢来。请先说说项目的基本情况。'

  # 问候策略
  greeting_strategy:
    template: '您好！我是AI需求采集助手，专门帮助用户整理和分析业务需求。请告诉我您有什么项目需要帮助梳理？'
    fallback: '您好！请告诉我您的项目需求。'

  # 需求策略
  requirement_strategy:
    template: '让我们系统地收集您的需求信息。请从项目背景开始，详细描述您想要实现的目标和具体要求。'
    fallback: '请详细描述您的项目需求和目标。'

  # 知识库策略
  knowledge_base_strategy:
    template: '基于我的知识库，我可以为您提供相关的最佳实践建议。请告诉我您的具体问题或需求领域。'
    fallback: '我可以提供相关建议。请说明您的具体需求。'

  # 兜底策略
  fallback_strategy:
    template: '我是AI需求采集助手，专门帮助整理和分析业务需求。无论您有什么项目想法，我都可以帮您系统地梳理。请详细描述一下吧！'
    fallback: '请告诉我您的需求，我来帮您整理分析。'

# ============================================================================
# 安全配置 - 从 defaults.yaml 补充
# ============================================================================
security:
  input_validation:
    max_input_length: 10000
    allowed_file_types: ["txt", "md", "pdf", "doc", "docx"]
    max_file_size: 10485760  # 10MB

  rate_limiting:
    requests_per_minute: 60
    requests_per_hour: 1000

  session:
    timeout: 3600  # 1小时
    max_sessions_per_user: 5

# ============================================================================
# 文件结束标记
# ============================================================================
# 配置文件版本：v3.0-merged
# 合并完成时间：2025-08-22
# 总配置项：约320个核心配置
# 配置块数：14个（optimized 13个 + security 1个）
# 文件大小：约500行（完整的兜底配置）
