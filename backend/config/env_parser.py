#!/usr/bin/env python3
"""
环境变量解析器

实现环境变量到配置键的映射和类型解析
"""

import os
import json
import logging
from typing import Dict, Any, Optional, Tuple, Union, List
import re


logger = logging.getLogger(__name__)


class EnvironmentVariableParser:
    """环境变量解析器"""
    
    # 环境变量前缀
    ENV_PREFIX = "AID_CONF__"
    
    # 布尔值映射
    BOOLEAN_TRUE_VALUES = {
        'true', 'True', 'TRUE', 
        'yes', 'Yes', 'YES',
        '1', 'on', 'On', 'ON'
    }
    
    BOOLEAN_FALSE_VALUES = {
        'false', 'False', 'FALSE',
        'no', 'No', 'NO', 
        '0', 'off', 'Off', 'OFF'
    }
    
    def __init__(self):
        self.parsed_vars = {}
        self.parsing_errors = []
        self.ignored_vars = []
    
    def parse_all_env_vars(self) -> Dict[str, Any]:
        """解析所有AID_CONF__开头的环境变量"""
        config_vars = {}
        
        for env_name, env_value in os.environ.items():
            if env_name.startswith(self.ENV_PREFIX):
                try:
                    config_key = self._env_name_to_config_key(env_name)
                    parsed_value = self._parse_value(env_value, env_name)
                    
                    # 将配置键转换为嵌套字典
                    self._set_nested_value(config_vars, config_key, parsed_value)
                    
                    self.parsed_vars[env_name] = {
                        'config_key': config_key,
                        'raw_value': env_value,
                        'parsed_value': parsed_value,
                        'type': type(parsed_value).__name__
                    }
                    
                    logger.debug(f"环境变量解析: {env_name} -> {config_key} = {self._safe_log_value(parsed_value)}")
                    
                except Exception as e:
                    error_msg = f"解析环境变量 {env_name} 失败: {e}"
                    logger.warning(error_msg)
                    self.parsing_errors.append({
                        'env_name': env_name,
                        'env_value': env_value,
                        'error': str(e)
                    })
        
        return config_vars
    
    def _env_name_to_config_key(self, env_name: str) -> str:
        """将环境变量名转换为配置键路径"""
        # 移除前缀
        key_part = env_name[len(self.ENV_PREFIX):]
        
        # 转换为小写并用点分隔
        config_key = key_part.lower().replace('__', '.')
        
        return config_key
    
    def _parse_value(self, value: str, env_name: str) -> Any:
        """解析环境变量值，自动推断类型"""
        if not value:
            return ""
        
        # 1. 尝试JSON解析（对象和数组）
        if value.strip().startswith(('{', '[')):
            try:
                return json.loads(value)
            except json.JSONDecodeError as e:
                logger.warning(f"环境变量 {env_name} JSON解析失败: {e}，将作为字符串处理")
                return value
        
        # 2. 布尔值解析
        if value in self.BOOLEAN_TRUE_VALUES:
            return True
        elif value in self.BOOLEAN_FALSE_VALUES:
            return False
        
        # 3. 数字解析
        # 尝试整数
        try:
            # 检查是否为整数格式（避免将浮点数解析为整数）
            if '.' not in value and 'e' not in value.lower():
                return int(value)
        except ValueError:
            pass
        
        # 尝试浮点数
        try:
            return float(value)
        except ValueError:
            pass
        
        # 4. 默认为字符串
        return value
    
    def _set_nested_value(self, config_dict: Dict[str, Any], key_path: str, value: Any):
        """在嵌套字典中设置值"""
        keys = key_path.split('.')
        current = config_dict
        
        # 遍历到倒数第二层
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            elif not isinstance(current[key], dict):
                # 如果已存在的值不是字典，需要转换
                logger.warning(f"配置键冲突: {key_path} 与现有配置冲突")
                current[key] = {}
            current = current[key]
        
        # 设置最终值
        final_key = keys[-1]
        current[final_key] = value
    
    def _safe_log_value(self, value: Any) -> str:
        """安全地记录值（敏感信息脱敏）"""
        if isinstance(value, str):
            # 检查是否为敏感信息
            if self._is_sensitive_value(value):
                return "[REDACTED]"
        
        # 对于复杂对象，转换为字符串并检查敏感信息
        value_str = str(value)
        if len(value_str) > 100:  # 长字符串可能包含敏感信息
            return f"[{type(value).__name__}:{len(value_str)} chars]"
        
        return value_str
    
    def _is_sensitive_value(self, value: str) -> bool:
        """检查是否为敏感值"""
        sensitive_patterns = [
            r'sk-[a-zA-Z0-9]{32,}',  # OpenAI API key
            r'[a-zA-Z0-9]{32,}',     # 长随机字符串
            r'.*password.*',         # 包含password的值
            r'.*secret.*',           # 包含secret的值
            r'.*token.*',            # 包含token的值
            r'.*key.*',              # 包含key的值
        ]
        
        for pattern in sensitive_patterns:
            if re.search(pattern, value, re.IGNORECASE):
                return True
        
        return False
    
    def get_parsing_summary(self) -> Dict[str, Any]:
        """获取解析摘要"""
        return {
            'total_parsed': len(self.parsed_vars),
            'parsing_errors': len(self.parsing_errors),
            'ignored_vars': len(self.ignored_vars),
            'parsed_vars': list(self.parsed_vars.keys()),
            'errors': self.parsing_errors,
            'ignored': self.ignored_vars
        }
    
    def validate_against_schema(self, config_vars: Dict[str, Any], allowed_keys: set) -> Tuple[Dict[str, Any], List[str]]:
        """根据Schema验证环境变量配置"""
        validated_config = {}
        rejected_keys = []
        
        def _validate_nested(current_config: Dict[str, Any], current_path: str = ""):
            for key, value in current_config.items():
                full_path = f"{current_path}.{key}" if current_path else key
                
                if isinstance(value, dict):
                    # 递归验证嵌套配置
                    validated_nested = {}
                    _validate_nested(value, full_path)
                    if validated_nested:  # 只有在有有效子配置时才添加
                        if full_path not in validated_config:
                            validated_config[full_path] = {}
                        validated_config.update(validated_nested)
                else:
                    # 检查键是否在允许列表中
                    if full_path in allowed_keys:
                        self._set_nested_value(validated_config, full_path, value)
                        logger.info(f"环境变量覆盖: {full_path} = {self._safe_log_value(value)} (来源: environment)")
                    else:
                        rejected_keys.append(full_path)
                        logger.warning(f"环境变量 '{full_path}' 未在配置Schema中声明，已忽略")
        
        _validate_nested(config_vars)
        
        return validated_config, rejected_keys


class EnvironmentConfigLoader:
    """环境配置加载器"""
    
    def __init__(self, allowed_keys: Optional[set] = None):
        self.parser = EnvironmentVariableParser()
        self.allowed_keys = allowed_keys or set()
    
    def load_env_config(self) -> Dict[str, Any]:
        """加载环境变量配置"""
        # 解析所有环境变量
        raw_config = self.parser.parse_all_env_vars()
        
        # 如果有允许键列表，进行验证
        if self.allowed_keys:
            validated_config, rejected_keys = self.parser.validate_against_schema(
                raw_config, self.allowed_keys
            )
            
            if rejected_keys:
                logger.warning(f"拒绝了 {len(rejected_keys)} 个未声明的环境变量配置")
            
            return validated_config
        
        return raw_config
    
    def get_env_override_info(self) -> Dict[str, Any]:
        """获取环境变量覆盖信息"""
        return {
            'parser_summary': self.parser.get_parsing_summary(),
            'allowed_keys_count': len(self.allowed_keys),
            'env_vars_with_prefix': len([k for k in os.environ.keys() 
                                       if k.startswith(self.parser.ENV_PREFIX)])
        }


def create_env_config_loader(schema_keys: Optional[set] = None) -> EnvironmentConfigLoader:
    """创建环境配置加载器的工厂函数"""
    return EnvironmentConfigLoader(allowed_keys=schema_keys)


# 便捷函数
def parse_env_var(env_name: str, default: Any = None) -> Any:
    """解析单个环境变量"""
    parser = EnvironmentVariableParser()
    value = os.environ.get(env_name)
    
    if value is None:
        return default
    
    try:
        return parser._parse_value(value, env_name)
    except Exception as e:
        logger.warning(f"解析环境变量 {env_name} 失败: {e}，使用默认值")
        return default


def get_config_from_env(config_key: str, default: Any = None) -> Any:
    """从环境变量获取配置值"""
    env_name = f"AID_CONF__{config_key.upper().replace('.', '__')}"
    return parse_env_var(env_name, default)


# 示例用法
if __name__ == "__main__":
    # 设置一些测试环境变量
    os.environ["AID_CONF__APP__DEBUG"] = "true"
    os.environ["AID_CONF__LLM__TEMPERATURE"] = "0.7"
    os.environ["AID_CONF__DATABASE__TIMEOUT"] = "30"
    os.environ["AID_CONF__LLM__MODELS"] = '{"gpt-4": {"max_tokens": 4000}}'
    os.environ["AID_CONF__UNKNOWN__KEY"] = "should_be_ignored"
    
    # 模拟Schema允许的键
    allowed_keys = {
        "app.debug",
        "llm.temperature", 
        "database.timeout",
        "llm.models"
    }
    
    # 创建加载器并解析
    loader = create_env_config_loader(allowed_keys)
    config = loader.load_env_config()
    
    print("解析的配置:")
    print(json.dumps(config, indent=2))
    
    print("\n解析摘要:")
    info = loader.get_env_override_info()
    print(json.dumps(info, indent=2))
