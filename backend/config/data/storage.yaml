# ============================================================================
# 存储配置
# ============================================================================
# 文件说明：文件存储、缓存存储等相关配置
# 维护责任：基础设施团队
# 更新频率：低频（存储架构变更时）
# 依赖关系：被文件处理、缓存管理等模块依赖
# ============================================================================

# 文件存储配置
file_storage:
  # 本地存储配置
  local:
    enabled: true
    base_path: "backend/data"        # 建议按环境分离：backend/data_{env}；避免跨环境污染；变更后需确认读写权限
    
    # 目录结构
    directories:
      uploads: "uploads"
      documents: "documents"
      exports: "exports"
      temp: "temp"
      logs: "logs"
      backups: "backups"
      
    # 文件权限
    permissions:
      file_mode: "0644"
      dir_mode: "0755"
      
    # 存储限制
    limits:
      max_file_size: 10485760  # 10MB
      max_total_size: **********  # 1GB
      allowed_extensions: [".txt", ".pdf", ".doc", ".docx", ".json", ".yaml"]
      
  # 云存储配置（可选）
  cloud:
    enabled: false
    provider: "aws_s3"  # aws_s3, azure_blob, google_cloud
    
    # AWS S3配置
    aws_s3:
      bucket_name: "requirement-collection"
      region: "us-east-1"
      # access_key: 通过环境变量 AWS_ACCESS_KEY_ID 设置
      # secret_key: 通过环境变量 AWS_SECRET_ACCESS_KEY 设置
      
    # 上传配置
    upload:
      multipart_threshold: 8388608  # 8MB
      max_concurrency: 10
      use_accelerate: false

# 缓存存储配置
cache_storage:
  # Redis配置
  redis:
    enabled: false
    host: "localhost"
    port: 6379
    database: 0
    # password: 通过环境变量 REDIS_PASSWORD 设置
    
    # 连接池配置
    connection_pool:
      max_connections: 20
      retry_on_timeout: true
      socket_timeout: 5
      socket_connect_timeout: 5
      
    # 集群配置（可选）
    cluster:
      enabled: false
      nodes: []
      
  # 内存缓存配置
  memory:
    enabled: true
    max_size: 104857600  # 100MB
    ttl: 3600  # 默认TTL（秒）
    
    # 缓存策略
    eviction_policy: "LRU"  # LRU, LFU, FIFO
    cleanup_interval: 300   # 清理间隔（秒）

# 临时存储配置
temp_storage:
  # 临时文件配置
  temp_files:
    base_path: "backend/data/temp"
    max_age: 3600  # 1小时
    cleanup_interval: 1800  # 30分钟
    max_size: 52428800  # 50MB
    
  # 会话存储
  session_storage:
    type: "file"  # file, redis, database
    path: "backend/data/sessions"
    ttl: 7200  # 2小时
    
  # 上传临时存储
  upload_temp:
    path: "backend/data/temp/uploads"
    max_age: 1800  # 30分钟
    chunk_size: 1048576  # 1MB

# 日志存储配置
log_storage:
  # 日志文件配置
  files:
    base_path: "logs"
    
    # 日志轮转
    rotation:
      enabled: true
      max_size: 10485760  # 10MB
      backup_count: 5
      
    # 日志压缩
    compression:
      enabled: true
      algorithm: "gzip"
      
  # 日志归档
  archival:
    enabled: true
    archive_path: "logs/archive"
    retention_days: 90
    
  # 远程日志存储（可选）
  remote:
    enabled: false
    type: "elasticsearch"  # elasticsearch, splunk, cloudwatch
    endpoint: ""

# 备份存储配置
backup_storage:
  # 本地备份
  local:
    enabled: true
    path: "backend/data/backups"
    retention_days: 30
    compression: true
    
  # 远程备份
  remote:
    enabled: false
    type: "s3"
    schedule: "0 2 * * *"  # 每天凌晨2点
    encryption: true

# 存储监控配置
monitoring:
  # 磁盘使用监控
  disk_usage:
    enabled: true
    warning_threshold: 0.8   # 80%
    critical_threshold: 0.9  # 90%
    check_interval: 300      # 5分钟
    
  # 存储性能监控
  performance:
    enabled: true
    track_io_operations: true
    track_response_time: true
    
  # 存储健康检查
  health_check:
    enabled: true
    check_interval: 60  # 1分钟
    timeout: 10

# 存储安全配置
security:
  # 文件加密
  encryption:
    enabled: false
    algorithm: "AES-256"
    # key: 通过环境变量 STORAGE_ENCRYPTION_KEY 设置
    
  # 访问控制
  access_control:
    enabled: true
    default_permissions: "0644"
    restrict_path_traversal: true
    
  # 文件扫描
  scanning:
    virus_scan: false
    malware_scan: false
    content_scan: true

# 存储优化配置
optimization:
  # 文件压缩
  compression:
    enabled: true
    algorithms: ["gzip", "brotli"]
    min_size: 1024  # 1KB
    
  # 重复文件检测
  deduplication:
    enabled: false
    hash_algorithm: "sha256"
    
  # 存储分层
  tiering:
    enabled: false
    hot_storage_days: 30
    cold_storage_days: 90

# 开发环境配置
development:
  # 调试模式
  debug_storage: true
  log_operations: true
  
  # 测试数据
  create_test_files: false
  cleanup_on_exit: true

# 生产环境配置
production:
  # 性能优化
  enable_caching: true
  use_compression: true
  
  # 安全加固
  encryption:
    enabled: true
  access_control:
    strict_mode: true

# ============================================================================
# 环境变量配置说明
# ============================================================================
# 以下环境变量需要在部署时设置：
# - AWS_ACCESS_KEY_ID: AWS访问密钥ID（如使用S3）
# - AWS_SECRET_ACCESS_KEY: AWS访问密钥（如使用S3）
# - REDIS_PASSWORD: Redis密码（如使用Redis）
# - STORAGE_ENCRYPTION_KEY: 存储加密密钥（如启用加密）
# ============================================================================

# ============================================================================
# 配置元数据
# ============================================================================
_metadata:
  file_version: "1.0"
  created_date: "2025-08-18"
  last_modified: "2025-08-18"
  schema_version: "1.0"
  config_type: "data_storage"
  priority: 8  # 在优先级配置中的位置
