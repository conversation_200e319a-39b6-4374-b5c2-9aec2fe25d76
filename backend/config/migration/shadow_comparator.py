#!/usr/bin/env python3
"""
影子对比框架

在迁移过程中并行运行新旧配置系统，对比配置值差异，确保迁移安全性
"""

import logging
import time
import threading
from typing import Dict, Any, List, Optional, Tuple, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict
import json
import hashlib


logger = logging.getLogger(__name__)


@dataclass
class ConfigDifference:
    """配置差异记录"""
    key: str
    old_value: Any
    new_value: Any
    old_source: str
    new_source: str
    difference_type: str  # 'missing', 'added', 'changed', 'type_mismatch'
    impact_level: str     # 'low', 'medium', 'high', 'critical'
    timestamp: float
    context: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ComparisonMetrics:
    """对比指标"""
    total_keys: int = 0
    matching_keys: int = 0
    different_keys: int = 0
    missing_keys: int = 0
    added_keys: int = 0
    consistency_rate: float = 0.0
    comparison_duration: float = 0.0
    timestamp: float = field(default_factory=time.time)


class ShadowComparator:
    """影子对比器"""
    
    def __init__(self, 
                 old_config_getter: Callable[[str], Any],
                 new_config_getter: Callable[[str], Any],
                 old_source_tracker: Optional[Callable[[str], str]] = None,
                 new_source_tracker: Optional[Callable[[str], str]] = None):
        """
        初始化影子对比器
        
        Args:
            old_config_getter: 旧配置获取函数
            new_config_getter: 新配置获取函数
            old_source_tracker: 旧配置来源追踪函数
            new_source_tracker: 新配置来源追踪函数
        """
        self.old_config_getter = old_config_getter
        self.new_config_getter = new_config_getter
        self.old_source_tracker = old_source_tracker
        self.new_source_tracker = new_source_tracker
        
        # 差异记录
        self.differences: List[ConfigDifference] = []
        self.metrics_history: List[ComparisonMetrics] = []
        
        # 配置
        self.max_differences = 10000  # 最大差异记录数
        self.comparison_interval = 60  # 对比间隔（秒）
        self.alert_thresholds = {
            'consistency_rate': 0.95,  # 一致性率阈值
            'critical_differences': 5,  # 关键差异数阈值
            'high_differences': 20      # 高影响差异数阈值
        }
        
        # 状态
        self.is_running = False
        self.comparison_thread: Optional[threading.Thread] = None
        self.lock = threading.Lock()
        
        # 回调函数
        self.difference_callbacks: List[Callable[[ConfigDifference], None]] = []
        self.metrics_callbacks: List[Callable[[ComparisonMetrics], None]] = []
    
    def add_difference_callback(self, callback: Callable[[ConfigDifference], None]):
        """添加差异回调函数"""
        self.difference_callbacks.append(callback)
    
    def add_metrics_callback(self, callback: Callable[[ComparisonMetrics], None]):
        """添加指标回调函数"""
        self.metrics_callbacks.append(callback)
    
    def start_continuous_comparison(self, config_keys: List[str]):
        """开始持续对比"""
        if self.is_running:
            logger.warning("影子对比已在运行中")
            return
        
        self.is_running = True
        self.comparison_thread = threading.Thread(
            target=self._continuous_comparison_loop,
            args=(config_keys,),
            daemon=True
        )
        self.comparison_thread.start()
        logger.info(f"开始影子对比，监控 {len(config_keys)} 个配置键")
    
    def stop_continuous_comparison(self):
        """停止持续对比"""
        self.is_running = False
        if self.comparison_thread:
            self.comparison_thread.join(timeout=5)
        logger.info("影子对比已停止")
    
    def _continuous_comparison_loop(self, config_keys: List[str]):
        """持续对比循环"""
        while self.is_running:
            try:
                metrics = self.compare_configs(config_keys)
                self._check_alerts(metrics)
                time.sleep(self.comparison_interval)
            except Exception as e:
                logger.error(f"影子对比循环出错: {e}")
                time.sleep(self.comparison_interval)
    
    def compare_configs(self, config_keys: List[str]) -> ComparisonMetrics:
        """对比配置"""
        start_time = time.time()
        current_differences = []
        
        matching_keys = 0
        different_keys = 0
        missing_keys = 0
        added_keys = 0
        
        # 获取所有配置键的值
        old_configs = {}
        new_configs = {}
        
        for key in config_keys:
            try:
                # 获取旧配置值
                old_value = self.old_config_getter(key)
                old_source = self.old_source_tracker(key) if self.old_source_tracker else "unknown"
                old_configs[key] = (old_value, old_source)
                
                # 获取新配置值
                new_value = self.new_config_getter(key)
                new_source = self.new_source_tracker(key) if self.new_source_tracker else "unknown"
                new_configs[key] = (new_value, new_source)
                
            except Exception as e:
                logger.warning(f"获取配置键 {key} 时出错: {e}")
                continue
        
        # 对比配置值
        all_keys = set(old_configs.keys()) | set(new_configs.keys())
        
        for key in all_keys:
            old_data = old_configs.get(key)
            new_data = new_configs.get(key)
            
            if old_data is None:
                # 新增的键
                added_keys += 1
                difference = ConfigDifference(
                    key=key,
                    old_value=None,
                    new_value=new_data[0] if new_data else None,
                    old_source="missing",
                    new_source=new_data[1] if new_data else "unknown",
                    difference_type="added",
                    impact_level=self._assess_impact_level(key, None, new_data[0] if new_data else None),
                    timestamp=time.time()
                )
                current_differences.append(difference)
                
            elif new_data is None:
                # 缺失的键
                missing_keys += 1
                difference = ConfigDifference(
                    key=key,
                    old_value=old_data[0],
                    new_value=None,
                    old_source=old_data[1],
                    new_source="missing",
                    difference_type="missing",
                    impact_level=self._assess_impact_level(key, old_data[0], None),
                    timestamp=time.time()
                )
                current_differences.append(difference)
                
            else:
                # 对比值
                old_value, old_source = old_data
                new_value, new_source = new_data
                
                if self._values_equal(old_value, new_value):
                    matching_keys += 1
                else:
                    different_keys += 1
                    difference_type = "type_mismatch" if type(old_value) != type(new_value) else "changed"
                    difference = ConfigDifference(
                        key=key,
                        old_value=old_value,
                        new_value=new_value,
                        old_source=old_source,
                        new_source=new_source,
                        difference_type=difference_type,
                        impact_level=self._assess_impact_level(key, old_value, new_value),
                        timestamp=time.time(),
                        context={
                            'old_type': type(old_value).__name__,
                            'new_type': type(new_value).__name__
                        }
                    )
                    current_differences.append(difference)
        
        # 记录差异
        with self.lock:
            self.differences.extend(current_differences)
            # 限制差异记录数量
            if len(self.differences) > self.max_differences:
                self.differences = self.differences[-self.max_differences:]
        
        # 计算指标
        total_keys = len(all_keys)
        consistency_rate = matching_keys / total_keys if total_keys > 0 else 1.0
        comparison_duration = time.time() - start_time
        
        metrics = ComparisonMetrics(
            total_keys=total_keys,
            matching_keys=matching_keys,
            different_keys=different_keys,
            missing_keys=missing_keys,
            added_keys=added_keys,
            consistency_rate=consistency_rate,
            comparison_duration=comparison_duration
        )
        
        # 记录指标历史
        with self.lock:
            self.metrics_history.append(metrics)
            # 保留最近100次对比记录
            if len(self.metrics_history) > 100:
                self.metrics_history = self.metrics_history[-100:]
        
        # 触发回调
        for difference in current_differences:
            for callback in self.difference_callbacks:
                try:
                    callback(difference)
                except Exception as e:
                    logger.error(f"差异回调执行失败: {e}")
        
        for callback in self.metrics_callbacks:
            try:
                callback(metrics)
            except Exception as e:
                logger.error(f"指标回调执行失败: {e}")
        
        logger.debug(f"配置对比完成: 总键数={total_keys}, 一致={matching_keys}, "
                    f"差异={different_keys}, 缺失={missing_keys}, 新增={added_keys}, "
                    f"一致性率={consistency_rate:.3f}")
        
        return metrics
    
    def _values_equal(self, old_value: Any, new_value: Any) -> bool:
        """判断两个值是否相等"""
        try:
            # 处理None值
            if old_value is None and new_value is None:
                return True
            if old_value is None or new_value is None:
                return False
            
            # 处理数字类型的比较
            if isinstance(old_value, (int, float)) and isinstance(new_value, (int, float)):
                return abs(old_value - new_value) < 1e-10
            
            # 处理字符串类型
            if isinstance(old_value, str) and isinstance(new_value, str):
                return old_value.strip() == new_value.strip()
            
            # 处理复杂对象
            if isinstance(old_value, (dict, list)):
                return self._deep_equal(old_value, new_value)
            
            # 默认比较
            return old_value == new_value
            
        except Exception as e:
            logger.warning(f"值比较出错: {e}")
            return False
    
    def _deep_equal(self, obj1: Any, obj2: Any) -> bool:
        """深度比较复杂对象"""
        try:
            # 使用JSON序列化进行比较
            json1 = json.dumps(obj1, sort_keys=True, default=str)
            json2 = json.dumps(obj2, sort_keys=True, default=str)
            return json1 == json2
        except Exception:
            return obj1 == obj2
    
    def _assess_impact_level(self, key: str, old_value: Any, new_value: Any) -> str:
        """评估影响级别"""
        # 关键配置键
        critical_keys = [
            'llm.default_model', 'database.connection.path', 'app.debug',
            'system.security.enabled', 'api.base_url'
        ]
        
        # 高影响配置键
        high_impact_keys = [
            'llm.temperature', 'llm.max_tokens', 'database.connection.timeout',
            'performance.cache.enabled', 'logging.level'
        ]
        
        if any(key.startswith(critical_key) for critical_key in critical_keys):
            return 'critical'
        elif any(key.startswith(high_key) for high_key in high_impact_keys):
            return 'high'
        elif old_value is None or new_value is None:
            return 'high'  # 缺失或新增的键通常影响较大
        elif type(old_value) != type(new_value):
            return 'medium'  # 类型不匹配
        else:
            return 'low'
    
    def _check_alerts(self, metrics: ComparisonMetrics):
        """检查告警条件"""
        # 检查一致性率
        if metrics.consistency_rate < self.alert_thresholds['consistency_rate']:
            logger.warning(f"配置一致性率过低: {metrics.consistency_rate:.3f} < {self.alert_thresholds['consistency_rate']}")
        
        # 检查关键差异数量
        with self.lock:
            recent_differences = [d for d in self.differences 
                                if d.timestamp > time.time() - 3600]  # 最近1小时
            critical_count = len([d for d in recent_differences if d.impact_level == 'critical'])
            high_count = len([d for d in recent_differences if d.impact_level == 'high'])
        
        if critical_count > self.alert_thresholds['critical_differences']:
            logger.error(f"关键配置差异过多: {critical_count} > {self.alert_thresholds['critical_differences']}")
        
        if high_count > self.alert_thresholds['high_differences']:
            logger.warning(f"高影响配置差异过多: {high_count} > {self.alert_thresholds['high_differences']}")
    
    def get_differences(self, 
                       since: Optional[datetime] = None,
                       impact_levels: Optional[List[str]] = None) -> List[ConfigDifference]:
        """获取差异记录"""
        with self.lock:
            differences = self.differences.copy()
        
        if since:
            since_timestamp = since.timestamp()
            differences = [d for d in differences if d.timestamp >= since_timestamp]
        
        if impact_levels:
            differences = [d for d in differences if d.impact_level in impact_levels]
        
        return differences
    
    def get_metrics_summary(self, hours: int = 24) -> Dict[str, Any]:
        """获取指标摘要"""
        cutoff_time = time.time() - (hours * 3600)
        
        with self.lock:
            recent_metrics = [m for m in self.metrics_history if m.timestamp >= cutoff_time]
            recent_differences = [d for d in self.differences if d.timestamp >= cutoff_time]
        
        if not recent_metrics:
            return {"error": "无最近指标数据"}
        
        # 计算平均指标
        avg_consistency = sum(m.consistency_rate for m in recent_metrics) / len(recent_metrics)
        avg_duration = sum(m.comparison_duration for m in recent_metrics) / len(recent_metrics)
        
        # 统计差异类型
        difference_types = defaultdict(int)
        impact_levels = defaultdict(int)
        
        for diff in recent_differences:
            difference_types[diff.difference_type] += 1
            impact_levels[diff.impact_level] += 1
        
        return {
            "time_range_hours": hours,
            "total_comparisons": len(recent_metrics),
            "average_consistency_rate": avg_consistency,
            "average_comparison_duration": avg_duration,
            "total_differences": len(recent_differences),
            "difference_types": dict(difference_types),
            "impact_levels": dict(impact_levels),
            "latest_metrics": recent_metrics[-1] if recent_metrics else None
        }
    
    def export_report(self, output_file: str, hours: int = 24):
        """导出对比报告"""
        summary = self.get_metrics_summary(hours)
        differences = self.get_differences(
            since=datetime.now() - timedelta(hours=hours)
        )
        
        report = {
            "generated_at": datetime.now().isoformat(),
            "summary": summary,
            "differences": [
                {
                    "key": d.key,
                    "old_value": str(d.old_value),
                    "new_value": str(d.new_value),
                    "old_source": d.old_source,
                    "new_source": d.new_source,
                    "difference_type": d.difference_type,
                    "impact_level": d.impact_level,
                    "timestamp": datetime.fromtimestamp(d.timestamp).isoformat(),
                    "context": d.context
                }
                for d in differences
            ]
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"对比报告已导出到: {output_file}")


# 便捷函数
def create_shadow_comparator(old_config_manager, new_config_manager) -> ShadowComparator:
    """创建影子对比器的便捷函数"""
    return ShadowComparator(
        old_config_getter=old_config_manager.get,
        new_config_getter=new_config_manager.get,
        old_source_tracker=lambda key: old_config_manager.get_with_source(key)[1],
        new_source_tracker=lambda key: new_config_manager.get_with_source(key)[1]
    )


# 示例用法
if __name__ == "__main__":
    # 模拟配置管理器
    class MockConfigManager:
        def __init__(self, config_data):
            self.config_data = config_data
        
        def get(self, key, default=None):
            keys = key.split('.')
            current = self.config_data
            for k in keys:
                if isinstance(current, dict) and k in current:
                    current = current[k]
                else:
                    return default
            return current
        
        def get_with_source(self, key):
            return self.get(key), "mock_source"
    
    # 创建模拟配置
    old_config = MockConfigManager({
        "app": {"debug": True, "name": "Old App"},
        "llm": {"temperature": 0.7, "model": "gpt-3.5"}
    })
    
    new_config = MockConfigManager({
        "app": {"debug": False, "name": "New App"},  # 值不同
        "llm": {"temperature": 0.8, "model": "gpt-4"}  # 值不同
    })
    
    # 创建影子对比器
    comparator = create_shadow_comparator(old_config, new_config)
    
    # 添加回调
    def on_difference(diff: ConfigDifference):
        print(f"发现差异: {diff.key} - {diff.old_value} -> {diff.new_value} ({diff.impact_level})")
    
    comparator.add_difference_callback(on_difference)
    
    # 执行对比
    test_keys = ["app.debug", "app.name", "llm.temperature", "llm.model"]
    metrics = comparator.compare_configs(test_keys)
    
    print(f"对比结果: 一致性率={metrics.consistency_rate:.3f}, 差异数={metrics.different_keys}")
    
    # 获取摘要
    summary = comparator.get_metrics_summary(1)
    print(f"摘要: {summary}")
