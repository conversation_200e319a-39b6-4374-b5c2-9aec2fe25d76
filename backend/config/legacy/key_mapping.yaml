# 旧配置键到新配置键的映射表
# 用于兼容层和迁移工具

version: "1.0"
description: "旧配置系统到新配置系统的键映射表"
last_updated: "2025-08-21"

# 映射规则说明:
# - old_key: 旧配置键路径
# - new_key: 新配置键路径  
# - status: 映射状态 (direct|deprecated|removed|complex)
# - migration_type: 迁移类型 (rename|restructure|split|merge|custom)
# - description: 映射说明
# - replacement_guide: 替换指导
# - deprecation_version: 废弃版本
# - removal_version: 移除版本

mappings:
  # ============================================================================
  # 系统基础配置映射
  # ============================================================================
  - old_key: "system.version"
    new_key: "app.version"
    status: "direct"
    migration_type: "rename"
    description: "系统版本号"
    replacement_guide: "直接替换键名"
    
  - old_key: "system.description"
    new_key: "app.description"
    status: "direct"
    migration_type: "rename"
    description: "系统描述"
    replacement_guide: "直接替换键名"
    
  - old_key: "system.language"
    new_key: "app.language"
    status: "direct"
    migration_type: "rename"
    description: "系统语言"
    replacement_guide: "直接替换键名"
    
  - old_key: "system.debug_mode"
    new_key: "app.debug"
    status: "direct"
    migration_type: "rename"
    description: "调试模式"
    replacement_guide: "直接替换键名"

  # ============================================================================
  # 日志配置映射
  # ============================================================================
  - old_key: "system.logging.level"
    new_key: "logging.level"
    status: "direct"
    migration_type: "restructure"
    description: "日志级别"
    replacement_guide: "从system.logging移动到顶级logging"
    
  - old_key: "system.logging.format"
    new_key: "logging.format"
    status: "direct"
    migration_type: "restructure"
    description: "日志格式"
    replacement_guide: "从system.logging移动到顶级logging"
    
  - old_key: "system.logging.max_file_size"
    new_key: "logging.max_file_size"
    status: "direct"
    migration_type: "restructure"
    description: "日志文件最大大小"
    replacement_guide: "从system.logging移动到顶级logging"

  # ============================================================================
  # LLM配置映射
  # ============================================================================
  - old_key: "llm.default.model"
    new_key: "llm.default_model"
    status: "direct"
    migration_type: "restructure"
    description: "默认LLM模型"
    replacement_guide: "简化路径结构"
    
  - old_key: "llm.default.temperature"
    new_key: "llm.temperature"
    status: "direct"
    migration_type: "restructure"
    description: "LLM温度参数"
    replacement_guide: "简化路径结构"
    
  - old_key: "llm.default.max_tokens"
    new_key: "llm.max_tokens"
    status: "direct"
    migration_type: "restructure"
    description: "最大令牌数"
    replacement_guide: "简化路径结构"
    
  - old_key: "llm.default.timeout"
    new_key: "llm.timeout"
    status: "direct"
    migration_type: "restructure"
    description: "LLM请求超时"
    replacement_guide: "简化路径结构"

  # LLM模型配置
  - old_key: "llm.models.openai.api_key"
    new_key: "llm.openai.api_key"
    status: "direct"
    migration_type: "restructure"
    description: "OpenAI API密钥"
    replacement_guide: "简化模型配置结构"
    
  - old_key: "llm.models.deepseek.api_key"
    new_key: "llm.deepseek.api_key"
    status: "direct"
    migration_type: "restructure"
    description: "DeepSeek API密钥"
    replacement_guide: "简化模型配置结构"
    
  - old_key: "llm.models.doubao.api_key"
    new_key: "llm.doubao.api_key"
    status: "direct"
    migration_type: "restructure"
    description: "豆包 API密钥"
    replacement_guide: "简化模型配置结构"

  # ============================================================================
  # 数据库配置映射
  # ============================================================================
  - old_key: "database.path"
    new_key: "database.connection.path"
    status: "direct"
    migration_type: "restructure"
    description: "数据库路径"
    replacement_guide: "添加connection层级"
    
  - old_key: "database.timeout"
    new_key: "database.connection.timeout"
    status: "direct"
    migration_type: "restructure"
    description: "数据库超时"
    replacement_guide: "添加connection层级"
    
  - old_key: "database.check_same_thread"
    new_key: "database.connection.check_same_thread"
    status: "direct"
    migration_type: "restructure"
    description: "SQLite同线程检查"
    replacement_guide: "添加connection层级"

  # ============================================================================
  # 性能配置映射
  # ============================================================================
  - old_key: "system.performance.llm_timeout"
    new_key: "performance.timeout.llm"
    status: "direct"
    migration_type: "restructure"
    description: "LLM超时时间"
    replacement_guide: "重新组织性能配置结构"
    
  - old_key: "system.performance.max_retry_attempts"
    new_key: "performance.retry.max_attempts"
    status: "direct"
    migration_type: "restructure"
    description: "最大重试次数"
    replacement_guide: "重新组织性能配置结构"
    
  - old_key: "system.performance.cache_enabled"
    new_key: "performance.cache.enabled"
    status: "direct"
    migration_type: "restructure"
    description: "缓存开关"
    replacement_guide: "重新组织性能配置结构"
    
  - old_key: "system.performance.cache_ttl"
    new_key: "performance.cache.ttl"
    status: "direct"
    migration_type: "restructure"
    description: "缓存TTL"
    replacement_guide: "重新组织性能配置结构"

  # ============================================================================
  # 业务规则配置映射
  # ============================================================================
  - old_key: "business_rules.retry.max_attempts"
    new_key: "business.retry.max_attempts"
    status: "direct"
    migration_type: "rename"
    description: "业务重试最大次数"
    replacement_guide: "简化业务规则键名"
    
  - old_key: "business_rules.retry.delay"
    new_key: "business.retry.delay"
    status: "direct"
    migration_type: "rename"
    description: "业务重试延迟"
    replacement_guide: "简化业务规则键名"
    
  - old_key: "business_rules.quality_control.enabled"
    new_key: "business.quality_control.enabled"
    status: "direct"
    migration_type: "rename"
    description: "质量控制开关"
    replacement_guide: "简化业务规则键名"

  # ============================================================================
  # 阈值配置映射
  # ============================================================================
  - old_key: "thresholds.completion_threshold"
    new_key: "business.thresholds.completion"
    status: "direct"
    migration_type: "restructure"
    description: "完成度阈值"
    replacement_guide: "移动到业务配置下"
    
  - old_key: "thresholds.confidence_threshold"
    new_key: "business.thresholds.confidence"
    status: "direct"
    migration_type: "restructure"
    description: "置信度阈值"
    replacement_guide: "移动到业务配置下"
    
  - old_key: "thresholds.similarity_threshold"
    new_key: "business.thresholds.similarity"
    status: "direct"
    migration_type: "restructure"
    description: "相似度阈值"
    replacement_guide: "移动到业务配置下"

  # ============================================================================
  # 消息模板配置映射
  # ============================================================================
  - old_key: "message_templates.system.greeting"
    new_key: "business.templates.system.greeting"
    status: "direct"
    migration_type: "restructure"
    description: "系统问候模板"
    replacement_guide: "移动到业务模板配置下"
    
  - old_key: "message_templates.error.general"
    new_key: "business.templates.error.general"
    status: "direct"
    migration_type: "restructure"
    description: "通用错误模板"
    replacement_guide: "移动到业务模板配置下"
    
  - old_key: "message_templates.success.completion"
    new_key: "business.templates.success.completion"
    status: "direct"
    migration_type: "restructure"
    description: "完成成功模板"
    replacement_guide: "移动到业务模板配置下"

  # ============================================================================
  # 知识库配置映射
  # ============================================================================
  - old_key: "knowledge_base.enabled"
    new_key: "knowledge_base.enabled"
    status: "direct"
    migration_type: "none"
    description: "知识库开关"
    replacement_guide: "无需更改"
    
  - old_key: "knowledge_base.chroma_db.path"
    new_key: "knowledge_base.storage.path"
    status: "direct"
    migration_type: "restructure"
    description: "知识库存储路径"
    replacement_guide: "重新组织存储配置"
    
  - old_key: "knowledge_base.retrieval.top_k"
    new_key: "knowledge_base.retrieval.top_k"
    status: "direct"
    migration_type: "none"
    description: "检索Top-K"
    replacement_guide: "无需更改"

  # ============================================================================
  # 安全配置映射
  # ============================================================================
  - old_key: "security.enabled"
    new_key: "system.security.enabled"
    status: "direct"
    migration_type: "restructure"
    description: "安全功能开关"
    replacement_guide: "移动到系统安全配置下"
    
  - old_key: "security.authentication.enabled"
    new_key: "system.security.authentication.enabled"
    status: "direct"
    migration_type: "restructure"
    description: "认证功能开关"
    replacement_guide: "移动到系统安全配置下"

  # ============================================================================
  # 废弃的配置键
  # ============================================================================
  - old_key: "system.decision_engine.type"
    new_key: null
    status: "deprecated"
    migration_type: "removed"
    description: "决策引擎类型（已废弃）"
    replacement_guide: "该配置已废弃，使用新的决策引擎架构"
    deprecation_version: "3.0"
    removal_version: "4.0"
    
  - old_key: "system.fallback_enabled"
    new_key: null
    status: "deprecated"
    migration_type: "removed"
    description: "回退机制开关（已废弃）"
    replacement_guide: "新架构中回退机制已内置"
    deprecation_version: "3.0"
    removal_version: "4.0"

  # ============================================================================
  # 复杂映射（需要自定义处理）
  # ============================================================================
  - old_key: "llm.scenarios.*"
    new_key: "llm.scenarios.*"
    status: "complex"
    migration_type: "custom"
    description: "LLM场景配置（复杂映射）"
    replacement_guide: "需要根据具体场景进行映射，参考场景配置迁移指南"
    custom_handler: "migrate_llm_scenarios"
    
  - old_key: "message_config.generators.*"
    new_key: "business.generators.*"
    status: "complex"
    migration_type: "custom"
    description: "消息生成器配置（复杂映射）"
    replacement_guide: "需要重新组织生成器配置结构"
    custom_handler: "migrate_message_generators"

# 特殊处理规则
special_rules:
  # 环境变量映射
  env_var_mappings:
    - old_pattern: "UNIFIED_CONFIG_*"
      new_pattern: "AID_CONF__*"
      description: "环境变量前缀更改"
      
  # 文件路径映射
  file_path_mappings:
    - old_path: "unified_config.defaults.yaml"
      new_path: "backend/config/defaults/"
      description: "默认配置文件拆分为模块化配置"
      
    - old_path: "unified_config.yaml"
      new_path: "backend/config/"
      description: "环境配置文件位置不变"

# 迁移统计
migration_stats:
  total_mappings: 45
  direct_mappings: 35
  deprecated_mappings: 2
  complex_mappings: 2
  removed_mappings: 6

# 验证规则
validation_rules:
  - rule: "required_keys"
    description: "必需的配置键必须有映射"
    keys: ["app.debug", "llm.default_model", "database.connection.path"]
    
  - rule: "no_circular_mapping"
    description: "不允许循环映射"
    
  - rule: "deprecated_warning"
    description: "废弃键访问时必须发出警告"
