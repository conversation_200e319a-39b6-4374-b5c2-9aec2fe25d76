#!/usr/bin/env python3
"""
旧配置访问追踪器

追踪旧配置键的访问频次和来源，用于生成迁移热力图
"""

import logging
import time
import threading
import inspect
from typing import Dict, Any, List, Optional, Tuple
from collections import defaultdict
from dataclasses import dataclass, field
from datetime import datetime, timedelta


logger = logging.getLogger(__name__)


@dataclass
class AccessRecord:
    """访问记录"""
    key: str
    method: str
    timestamp: float
    caller_file: str
    caller_line: int
    caller_function: str
    count: int = 1


@dataclass
class AccessStats:
    """访问统计"""
    key: str
    total_count: int = 0
    first_access: Optional[float] = None
    last_access: Optional[float] = None
    methods: Dict[str, int] = field(default_factory=dict)
    callers: Dict[str, int] = field(default_factory=dict)
    hourly_distribution: Dict[int, int] = field(default_factory=dict)


class LegacyAccessTracker:
    """旧配置访问追踪器"""
    
    def __init__(self, max_records: int = 10000):
        """
        初始化访问追踪器
        
        Args:
            max_records: 最大记录数，超过后会清理旧记录
        """
        self.max_records = max_records
        self.access_records: List[AccessRecord] = []
        self.access_stats: Dict[str, AccessStats] = {}
        self.lock = threading.Lock()
        
        # 统计信息
        self.total_accesses = 0
        self.start_time = time.time()
    
    def record_access(self, key: str, method: str):
        """记录配置访问"""
        # 获取调用者信息
        caller_info = self._get_caller_info()
        
        with self.lock:
            # 创建访问记录
            record = AccessRecord(
                key=key,
                method=method,
                timestamp=time.time(),
                caller_file=caller_info['file'],
                caller_line=caller_info['line'],
                caller_function=caller_info['function']
            )
            
            # 添加到记录列表
            self.access_records.append(record)
            
            # 更新统计信息
            self._update_stats(record)
            
            # 清理旧记录
            if len(self.access_records) > self.max_records:
                self._cleanup_old_records()
            
            self.total_accesses += 1
    
    def _get_caller_info(self) -> Dict[str, Any]:
        """获取调用者信息"""
        try:
            # 获取调用栈
            frame = inspect.currentframe()
            
            # 跳过当前方法和record_access方法
            caller_frame = frame.f_back.f_back.f_back
            
            if caller_frame:
                return {
                    'file': caller_frame.f_code.co_filename,
                    'line': caller_frame.f_lineno,
                    'function': caller_frame.f_code.co_name
                }
        except Exception as e:
            logger.debug(f"获取调用者信息失败: {e}")
        
        return {
            'file': 'unknown',
            'line': 0,
            'function': 'unknown'
        }
    
    def _update_stats(self, record: AccessRecord):
        """更新统计信息"""
        key = record.key
        
        if key not in self.access_stats:
            self.access_stats[key] = AccessStats(key=key)
        
        stats = self.access_stats[key]
        
        # 更新基本统计
        stats.total_count += 1
        if stats.first_access is None:
            stats.first_access = record.timestamp
        stats.last_access = record.timestamp
        
        # 更新方法统计
        stats.methods[record.method] = stats.methods.get(record.method, 0) + 1
        
        # 更新调用者统计
        caller_key = f"{record.caller_file}:{record.caller_line}"
        stats.callers[caller_key] = stats.callers.get(caller_key, 0) + 1
        
        # 更新小时分布
        hour = datetime.fromtimestamp(record.timestamp).hour
        stats.hourly_distribution[hour] = stats.hourly_distribution.get(hour, 0) + 1
    
    def _cleanup_old_records(self):
        """清理旧记录"""
        # 保留最近的记录
        keep_count = int(self.max_records * 0.8)  # 保留80%
        self.access_records = self.access_records[-keep_count:]
        
        logger.debug(f"清理旧访问记录，保留最近 {keep_count} 条记录")
    
    def get_access_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取访问统计"""
        with self.lock:
            result = {}
            for key, stats in self.access_stats.items():
                result[key] = {
                    'count': stats.total_count,
                    'first_access': stats.first_access,
                    'last_access': stats.last_access,
                    'methods': dict(stats.methods),
                    'top_callers': sorted(
                        stats.callers.items(),
                        key=lambda x: x[1],
                        reverse=True
                    )[:5],  # 前5个调用者
                    'hourly_distribution': dict(stats.hourly_distribution)
                }
            return result
    
    def get_hottest_keys(self, limit: int = 20) -> List[Tuple[str, int]]:
        """获取最热门的配置键"""
        with self.lock:
            return sorted(
                [(key, stats.total_count) for key, stats in self.access_stats.items()],
                key=lambda x: x[1],
                reverse=True
            )[:limit]
    
    def get_recent_accesses(self, hours: int = 24) -> List[AccessRecord]:
        """获取最近的访问记录"""
        cutoff_time = time.time() - (hours * 3600)
        
        with self.lock:
            return [
                record for record in self.access_records
                if record.timestamp >= cutoff_time
            ]
    
    def get_caller_analysis(self) -> Dict[str, Any]:
        """获取调用者分析"""
        caller_stats = defaultdict(lambda: {'count': 0, 'keys': set()})
        
        with self.lock:
            for key, stats in self.access_stats.items():
                for caller, count in stats.callers.items():
                    caller_stats[caller]['count'] += count
                    caller_stats[caller]['keys'].add(key)
        
        # 转换为可序列化的格式
        result = {}
        for caller, data in caller_stats.items():
            result[caller] = {
                'count': data['count'],
                'unique_keys': len(data['keys']),
                'keys': list(data['keys'])
            }
        
        return result
    
    def generate_heatmap_data(self) -> Dict[str, Any]:
        """生成热力图数据"""
        with self.lock:
            heatmap_data = {
                'keys': [],
                'access_counts': [],
                'last_access_times': [],
                'caller_diversity': []
            }
            
            for key, stats in self.access_stats.items():
                heatmap_data['keys'].append(key)
                heatmap_data['access_counts'].append(stats.total_count)
                heatmap_data['last_access_times'].append(stats.last_access or 0)
                heatmap_data['caller_diversity'].append(len(stats.callers))
            
            return heatmap_data
    
    def get_migration_priority(self) -> List[Dict[str, Any]]:
        """获取迁移优先级列表"""
        priority_list = []
        
        with self.lock:
            for key, stats in self.access_stats.items():
                # 计算优先级分数
                score = self._calculate_priority_score(stats)
                
                priority_item = {
                    'key': key,
                    'priority_score': score,
                    'access_count': stats.total_count,
                    'caller_count': len(stats.callers),
                    'method_diversity': len(stats.methods),
                    'last_access': stats.last_access,
                    'priority_level': self._get_priority_level(score)
                }
                
                priority_list.append(priority_item)
        
        # 按优先级分数排序
        return sorted(priority_list, key=lambda x: x['priority_score'], reverse=True)
    
    def _calculate_priority_score(self, stats: AccessStats) -> float:
        """计算优先级分数"""
        # 基础分数：访问频次
        base_score = stats.total_count
        
        # 调用者多样性加分
        caller_bonus = len(stats.callers) * 2
        
        # 方法多样性加分
        method_bonus = len(stats.methods) * 1.5
        
        # 最近访问加分
        if stats.last_access:
            hours_since_last = (time.time() - stats.last_access) / 3600
            recency_bonus = max(0, 24 - hours_since_last) / 24 * 10
        else:
            recency_bonus = 0
        
        return base_score + caller_bonus + method_bonus + recency_bonus
    
    def _get_priority_level(self, score: float) -> str:
        """获取优先级等级"""
        if score >= 100:
            return "P0"  # 最高优先级
        elif score >= 50:
            return "P1"  # 高优先级
        elif score >= 20:
            return "P2"  # 中优先级
        else:
            return "P3"  # 低优先级
    
    def export_access_data(self, format: str = 'json') -> str:
        """导出访问数据"""
        data = {
            'summary': {
                'total_accesses': self.total_accesses,
                'unique_keys': len(self.access_stats),
                'tracking_duration': time.time() - self.start_time,
                'export_time': datetime.now().isoformat()
            },
            'access_stats': self.get_access_stats(),
            'hottest_keys': self.get_hottest_keys(),
            'migration_priority': self.get_migration_priority(),
            'caller_analysis': self.get_caller_analysis(),
            'heatmap_data': self.generate_heatmap_data()
        }
        
        if format == 'json':
            import json
            return json.dumps(data, indent=2, default=str)
        elif format == 'yaml':
            import yaml
            return yaml.dump(data, default_flow_style=False)
        else:
            raise ValueError(f"不支持的导出格式: {format}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取追踪器统计信息"""
        with self.lock:
            return {
                'total_accesses': self.total_accesses,
                'unique_keys': len(self.access_stats),
                'total_records': len(self.access_records),
                'tracking_duration': time.time() - self.start_time,
                'max_records': self.max_records
            }
    
    def clear_stats(self):
        """清空统计信息"""
        with self.lock:
            self.access_records.clear()
            self.access_stats.clear()
            self.total_accesses = 0
            self.start_time = time.time()
            
        logger.info("访问追踪统计已清空")


# 示例用法
if __name__ == "__main__":
    # 创建追踪器
    tracker = LegacyAccessTracker()
    
    # 模拟一些访问
    tracker.record_access('system.debug_mode', 'get_config_value')
    tracker.record_access('llm.default.model', 'get_config_value')
    tracker.record_access('system.debug_mode', 'get_config_value')  # 重复访问
    tracker.record_access('database.path', 'get_config_value')
    
    # 获取统计信息
    print("访问统计:")
    stats = tracker.get_access_stats()
    for key, data in stats.items():
        print(f"  {key}: {data['count']} 次访问")
    
    print("\n热门配置键:")
    hottest = tracker.get_hottest_keys(5)
    for key, count in hottest:
        print(f"  {key}: {count}")
    
    print("\n迁移优先级:")
    priority = tracker.get_migration_priority()
    for item in priority[:5]:
        print(f"  [{item['priority_level']}] {item['key']} (分数: {item['priority_score']:.1f})")
    
    # 导出数据
    print("\n导出JSON数据:")
    json_data = tracker.export_access_data('json')
    print(json_data[:500] + "..." if len(json_data) > 500 else json_data)
