#!/usr/bin/env python3
"""
旧配置系统兼容层包装器

提供旧配置接口的兼容性，同时代理到新的配置管理系统
"""

import logging
import warnings
import yaml
from typing import Dict, Any, Optional, List, Callable
from pathlib import Path
from collections import defaultdict
import time
import threading

from ..manager import ConfigManager
from .access_tracker import LegacyAccessTracker


logger = logging.getLogger(__name__)


class DeprecationWarningManager:
    """废弃警告管理器"""
    
    def __init__(self, warning_interval: int = 300):  # 5分钟间隔
        self.warning_interval = warning_interval
        self.last_warnings: Dict[str, float] = {}
        self.warning_counts: Dict[str, int] = defaultdict(int)
        self.lock = threading.Lock()
    
    def should_warn(self, key: str) -> bool:
        """检查是否应该发出警告"""
        with self.lock:
            current_time = time.time()
            last_warn_time = self.last_warnings.get(key, 0)
            
            if current_time - last_warn_time >= self.warning_interval:
                self.last_warnings[key] = current_time
                self.warning_counts[key] += 1
                return True
            
            return False
    
    def get_warning_stats(self) -> Dict[str, Any]:
        """获取警告统计"""
        with self.lock:
            return {
                'total_warnings': sum(self.warning_counts.values()),
                'unique_keys': len(self.warning_counts),
                'warning_counts': dict(self.warning_counts),
                'warning_interval': self.warning_interval
            }


class LegacyConfigWrapper:
    """旧配置系统兼容层包装器"""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None,
                 key_mapping_path: Optional[str] = None,
                 enable_warnings: bool = True):
        """
        初始化兼容层包装器
        
        Args:
            config_manager: 新的配置管理器实例
            key_mapping_path: 键映射文件路径
            enable_warnings: 是否启用废弃警告
        """
        self.config_manager = config_manager or ConfigManager()
        self.enable_warnings = enable_warnings
        self.warning_manager = DeprecationWarningManager()
        self.access_tracker = LegacyAccessTracker()
        
        # 加载键映射
        self.key_mappings = self._load_key_mappings(key_mapping_path)
        
        # 统计信息
        self.access_count = 0
        self.mapping_hits = 0
        self.mapping_misses = 0
        
        logger.info("旧配置兼容层已初始化")
    
    def _load_key_mappings(self, mapping_path: Optional[str]) -> Dict[str, Any]:
        """加载键映射配置"""
        if not mapping_path:
            mapping_path = Path(__file__).parent / "key_mapping.yaml"
        
        try:
            with open(mapping_path, 'r', encoding='utf-8') as f:
                mapping_config = yaml.safe_load(f)
            
            # 构建快速查找字典
            mappings = {}
            for mapping in mapping_config.get('mappings', []):
                old_key = mapping['old_key']
                mappings[old_key] = mapping
            
            logger.info(f"加载了 {len(mappings)} 个键映射规则")
            return mappings
            
        except Exception as e:
            logger.error(f"加载键映射失败: {e}")
            return {}
    
    def _map_key(self, old_key: str) -> Optional[str]:
        """映射旧键到新键"""
        # 精确匹配
        if old_key in self.key_mappings:
            mapping = self.key_mappings[old_key]
            if mapping['status'] == 'direct':
                return mapping['new_key']
            elif mapping['status'] == 'deprecated':
                self._emit_deprecation_warning(old_key, mapping)
                return mapping.get('new_key')
            elif mapping['status'] == 'removed':
                self._emit_removal_warning(old_key, mapping)
                return None
        
        # 通配符匹配
        for pattern, mapping in self.key_mappings.items():
            if '*' in pattern:
                if self._match_wildcard(old_key, pattern):
                    new_key = self._apply_wildcard_mapping(old_key, pattern, mapping['new_key'])
                    if mapping['status'] == 'deprecated':
                        self._emit_deprecation_warning(old_key, mapping)
                    return new_key
        
        return None
    
    def _match_wildcard(self, key: str, pattern: str) -> bool:
        """匹配通配符模式"""
        if not pattern.endswith('*'):
            return False
        
        prefix = pattern[:-1]
        return key.startswith(prefix)
    
    def _apply_wildcard_mapping(self, old_key: str, old_pattern: str, new_pattern: str) -> str:
        """应用通配符映射"""
        if not old_pattern.endswith('*') or not new_pattern:
            return old_key
        
        old_prefix = old_pattern[:-1]
        if new_pattern.endswith('*'):
            new_prefix = new_pattern[:-1]
            suffix = old_key[len(old_prefix):]
            return new_prefix + suffix
        else:
            return new_pattern
    
    def _emit_deprecation_warning(self, old_key: str, mapping: Dict[str, Any]):
        """发出废弃警告"""
        if not self.enable_warnings:
            return
        
        if self.warning_manager.should_warn(old_key):
            message = f"配置键 '{old_key}' 已废弃"
            if mapping.get('new_key'):
                message += f"，请使用 '{mapping['new_key']}'"
            if mapping.get('replacement_guide'):
                message += f"。{mapping['replacement_guide']}"
            
            warnings.warn(message, DeprecationWarning, stacklevel=3)
            logger.warning(f"DEPRECATED: {message}")
    
    def _emit_removal_warning(self, old_key: str, mapping: Dict[str, Any]):
        """发出移除警告"""
        if not self.enable_warnings:
            return
        
        if self.warning_manager.should_warn(old_key):
            message = f"配置键 '{old_key}' 已移除"
            if mapping.get('replacement_guide'):
                message += f"。{mapping['replacement_guide']}"
            
            warnings.warn(message, DeprecationWarning, stacklevel=3)
            logger.error(f"REMOVED: {message}")
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """获取配置值（兼容旧接口）"""
        self.access_count += 1
        
        # 记录访问
        self.access_tracker.record_access(key, 'get_config_value')
        
        # 尝试映射键
        new_key = self._map_key(key)
        
        if new_key:
            self.mapping_hits += 1
            # 使用新键获取配置
            value = self.config_manager.get(new_key, default)
            logger.debug(f"旧键映射: {key} -> {new_key} = {value}")
            return value
        else:
            self.mapping_misses += 1
            # 直接尝试获取（可能是新键或未映射的键）
            value = self.config_manager.get(key, default)
            if value != default:
                logger.debug(f"直接访问: {key} = {value}")
            else:
                logger.debug(f"配置键未找到: {key}，使用默认值: {default}")
            return value
    
    def get_llm_config(self, config_type: str = 'default') -> Dict[str, Any]:
        """获取LLM配置（兼容旧接口）"""
        self.access_count += 1
        self.access_tracker.record_access(f'llm.{config_type}', 'get_llm_config')
        
        # 发出废弃警告
        if self.enable_warnings and self.warning_manager.should_warn('get_llm_config'):
            warnings.warn(
                "get_llm_config() 方法已废弃，请使用 config.get_section('llm') 获取LLM配置",
                DeprecationWarning,
                stacklevel=2
            )
        
        # 获取LLM配置段
        llm_config = self.config_manager.get_section('llm', {})
        
        # 如果指定了特定类型，尝试获取
        if config_type != 'default' and config_type in llm_config:
            return llm_config[config_type]
        
        return llm_config
    
    def get_message_template(self, template_path: str, default: str = None, **kwargs) -> str:
        """获取消息模板（兼容旧接口）"""
        self.access_count += 1
        self.access_tracker.record_access(template_path, 'get_message_template')
        
        # 发出废弃警告
        if self.enable_warnings and self.warning_manager.should_warn('get_message_template'):
            warnings.warn(
                "get_message_template() 方法已废弃，请使用 config.get() 获取模板配置",
                DeprecationWarning,
                stacklevel=2
            )
        
        # 尝试映射模板路径
        mapped_path = self._map_key(f"message_templates.{template_path}")
        if mapped_path:
            template = self.config_manager.get(mapped_path, default or "")
        else:
            # 直接尝试获取
            template = self.config_manager.get(f"business.templates.{template_path}", default or "")
        
        # 如果有参数，尝试格式化模板
        if kwargs and template:
            try:
                return template.format(**kwargs)
            except (KeyError, ValueError) as e:
                logger.warning(f"模板格式化失败: {e}")
                return template
        
        return template
    
    def get_threshold(self, path: str, default: Any = None) -> Any:
        """获取阈值配置（兼容旧接口）"""
        self.access_count += 1
        self.access_tracker.record_access(path, 'get_threshold')
        
        # 发出废弃警告
        if self.enable_warnings and self.warning_manager.should_warn('get_threshold'):
            warnings.warn(
                "get_threshold() 方法已废弃，请使用 config.get() 获取阈值配置",
                DeprecationWarning,
                stacklevel=2
            )
        
        # 尝试映射阈值路径
        mapped_path = self._map_key(f"thresholds.{path}")
        if mapped_path:
            return self.config_manager.get(mapped_path, default)
        else:
            # 直接尝试获取
            return self.config_manager.get(f"business.thresholds.{path}", default)
    
    def get_config_section(self, section: str) -> Dict[str, Any]:
        """获取配置段（兼容旧接口）"""
        self.access_count += 1
        self.access_tracker.record_access(section, 'get_config_section')
        
        # 尝试映射段名
        mapped_section = self._map_key(section)
        if mapped_section:
            return self.config_manager.get_section(mapped_section, {})
        else:
            return self.config_manager.get_section(section, {})
    
    def add_change_listener(self, listener: Callable):
        """添加配置变更监听器（兼容性方法）"""
        # 发出废弃警告
        if self.enable_warnings and self.warning_manager.should_warn('add_change_listener'):
            warnings.warn(
                "add_change_listener() 方法已废弃，新配置系统不支持动态监听",
                DeprecationWarning,
                stacklevel=2
            )
        
        # 暂时不实现，只是为了兼容性
        logger.warning("add_change_listener() 方法已废弃，不执行任何操作")
    
    def get_compatibility_stats(self) -> Dict[str, Any]:
        """获取兼容性统计信息"""
        return {
            'access_count': self.access_count,
            'mapping_hits': self.mapping_hits,
            'mapping_misses': self.mapping_misses,
            'mapping_hit_rate': self.mapping_hits / max(self.access_count, 1),
            'loaded_mappings': len(self.key_mappings),
            'warning_stats': self.warning_manager.get_warning_stats(),
            'access_tracker_stats': self.access_tracker.get_stats()
        }
    
    def generate_migration_report(self) -> Dict[str, Any]:
        """生成迁移报告"""
        access_stats = self.access_tracker.get_access_stats()
        
        return {
            'summary': {
                'total_accesses': self.access_count,
                'unique_keys_accessed': len(access_stats),
                'mapping_coverage': self.mapping_hits / max(self.access_count, 1)
            },
            'top_accessed_keys': sorted(
                access_stats.items(),
                key=lambda x: x[1]['count'],
                reverse=True
            )[:20],
            'unmapped_keys': [
                key for key, stats in access_stats.items()
                if key not in self.key_mappings and stats['count'] > 0
            ],
            'deprecated_usage': self.warning_manager.get_warning_stats(),
            'recommendations': self._generate_recommendations(access_stats)
        }
    
    def _generate_recommendations(self, access_stats: Dict[str, Any]) -> List[str]:
        """生成迁移建议"""
        recommendations = []
        
        # 高频访问的未映射键
        high_frequency_unmapped = [
            key for key, stats in access_stats.items()
            if key not in self.key_mappings and stats['count'] > 10
        ]
        
        if high_frequency_unmapped:
            recommendations.append(
                f"发现 {len(high_frequency_unmapped)} 个高频访问的未映射键，建议添加映射规则"
            )
        
        # 废弃键使用情况
        deprecated_count = sum(
            1 for key in self.warning_manager.warning_counts.keys()
            if key in self.key_mappings and self.key_mappings[key]['status'] == 'deprecated'
        )
        
        if deprecated_count > 0:
            recommendations.append(
                f"发现 {deprecated_count} 个废弃键仍在使用，建议尽快迁移"
            )
        
        return recommendations


# 全局兼容层实例
_global_legacy_wrapper: Optional[LegacyConfigWrapper] = None


def get_legacy_config_wrapper() -> LegacyConfigWrapper:
    """获取全局兼容层实例"""
    global _global_legacy_wrapper
    if _global_legacy_wrapper is None:
        _global_legacy_wrapper = LegacyConfigWrapper()
    return _global_legacy_wrapper


def create_legacy_wrapper(config_manager: Optional[ConfigManager] = None,
                         enable_warnings: bool = True) -> LegacyConfigWrapper:
    """创建兼容层实例"""
    return LegacyConfigWrapper(
        config_manager=config_manager,
        enable_warnings=enable_warnings
    )


# 便捷函数（保持旧接口兼容性）
def get_config_value(key: str, default: Any = None) -> Any:
    """获取配置值的便捷函数（兼容旧接口）"""
    return get_legacy_config_wrapper().get_config_value(key, default)


def get_llm_config(config_type: str = 'default') -> Dict[str, Any]:
    """获取LLM配置的便捷函数（兼容旧接口）"""
    return get_legacy_config_wrapper().get_llm_config(config_type)


# 示例用法
if __name__ == "__main__":
    # 创建兼容层
    wrapper = create_legacy_wrapper()
    
    # 测试旧接口
    print("测试旧配置接口兼容性:")
    
    # 测试配置值获取
    debug_mode = wrapper.get_config_value('system.debug_mode', False)
    print(f"system.debug_mode = {debug_mode}")
    
    # 测试LLM配置
    llm_config = wrapper.get_llm_config()
    print(f"LLM配置: {llm_config}")
    
    # 测试消息模板
    template = wrapper.get_message_template('system.greeting', 'Hello!')
    print(f"问候模板: {template}")
    
    # 获取统计信息
    stats = wrapper.get_compatibility_stats()
    print(f"兼容性统计: {stats}")
    
    # 生成迁移报告
    report = wrapper.generate_migration_report()
    print(f"迁移报告: {report}")
