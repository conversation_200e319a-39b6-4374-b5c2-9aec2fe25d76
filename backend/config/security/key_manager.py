#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
密钥管理器集成

提供与外部密钥管理系统（如Vault、AWS Secrets Manager等）的集成
"""

import os
import json
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime, timedelta
import base64
import hashlib

logger = logging.getLogger(__name__)


@dataclass
class SecretMetadata:
    """密钥元数据"""
    key: str
    version: str
    created_at: datetime
    expires_at: Optional[datetime] = None
    tags: Dict[str, str] = None
    description: str = ""


class KeyManagerInterface(ABC):
    """密钥管理器接口"""
    
    @abstractmethod
    def get_secret(self, key: str, version: str = "latest") -> Optional[str]:
        """获取密钥"""
        pass
    
    @abstractmethod
    def set_secret(self, key: str, value: str, metadata: Optional[SecretMetadata] = None) -> bool:
        """设置密钥"""
        pass
    
    @abstractmethod
    def delete_secret(self, key: str) -> bool:
        """删除密钥"""
        pass
    
    @abstractmethod
    def list_secrets(self, prefix: str = "") -> List[str]:
        """列出密钥"""
        pass
    
    @abstractmethod
    def rotate_secret(self, key: str) -> bool:
        """轮换密钥"""
        pass


class EnvironmentKeyManager(KeyManagerInterface):
    """环境变量密钥管理器"""
    
    def __init__(self, prefix: str = "AID_SECRET_"):
        self.prefix = prefix
    
    def get_secret(self, key: str, version: str = "latest") -> Optional[str]:
        """从环境变量获取密钥"""
        env_key = f"{self.prefix}{key.upper().replace('.', '_')}"
        return os.getenv(env_key)
    
    def set_secret(self, key: str, value: str, metadata: Optional[SecretMetadata] = None) -> bool:
        """设置环境变量密钥"""
        env_key = f"{self.prefix}{key.upper().replace('.', '_')}"
        os.environ[env_key] = value
        return True
    
    def delete_secret(self, key: str) -> bool:
        """删除环境变量密钥"""
        env_key = f"{self.prefix}{key.upper().replace('.', '_')}"
        if env_key in os.environ:
            del os.environ[env_key]
            return True
        return False
    
    def list_secrets(self, prefix: str = "") -> List[str]:
        """列出环境变量密钥"""
        full_prefix = f"{self.prefix}{prefix.upper().replace('.', '_')}"
        keys = []
        
        for env_key in os.environ:
            if env_key.startswith(full_prefix):
                # 转换回配置键格式
                config_key = env_key[len(self.prefix):].lower().replace('_', '.')
                keys.append(config_key)
        
        return keys
    
    def rotate_secret(self, key: str) -> bool:
        """环境变量不支持自动轮换"""
        logger.warning(f"环境变量密钥管理器不支持自动轮换: {key}")
        return False


class FileKeyManager(KeyManagerInterface):
    """文件密钥管理器（用于开发和测试）"""
    
    def __init__(self, secrets_file: str = "secrets.json"):
        self.secrets_file = secrets_file
        self._secrets_cache = {}
        self._load_secrets()
    
    def _load_secrets(self):
        """加载密钥文件"""
        try:
            if os.path.exists(self.secrets_file):
                with open(self.secrets_file, 'r', encoding='utf-8') as f:
                    self._secrets_cache = json.load(f)
        except Exception as e:
            logger.error(f"加载密钥文件失败: {e}")
            self._secrets_cache = {}
    
    def _save_secrets(self):
        """保存密钥文件"""
        try:
            with open(self.secrets_file, 'w', encoding='utf-8') as f:
                json.dump(self._secrets_cache, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存密钥文件失败: {e}")
            return False
        return True
    
    def get_secret(self, key: str, version: str = "latest") -> Optional[str]:
        """获取密钥"""
        return self._secrets_cache.get(key)
    
    def set_secret(self, key: str, value: str, metadata: Optional[SecretMetadata] = None) -> bool:
        """设置密钥"""
        self._secrets_cache[key] = value
        return self._save_secrets()
    
    def delete_secret(self, key: str) -> bool:
        """删除密钥"""
        if key in self._secrets_cache:
            del self._secrets_cache[key]
            return self._save_secrets()
        return False
    
    def list_secrets(self, prefix: str = "") -> List[str]:
        """列出密钥"""
        if not prefix:
            return list(self._secrets_cache.keys())
        
        return [key for key in self._secrets_cache.keys() if key.startswith(prefix)]
    
    def rotate_secret(self, key: str) -> bool:
        """文件密钥管理器不支持自动轮换"""
        logger.warning(f"文件密钥管理器不支持自动轮换: {key}")
        return False


class VaultKeyManager(KeyManagerInterface):
    """HashiCorp Vault密钥管理器"""
    
    def __init__(self, vault_url: str, vault_token: str, mount_path: str = "secret"):
        self.vault_url = vault_url.rstrip('/')
        self.vault_token = vault_token
        self.mount_path = mount_path
        
        # 这里应该导入hvac库，但为了避免依赖，使用模拟实现
        self._vault_client = None
        logger.warning("Vault集成需要安装hvac库")
    
    def get_secret(self, key: str, version: str = "latest") -> Optional[str]:
        """从Vault获取密钥"""
        try:
            # 模拟Vault API调用
            # response = self._vault_client.secrets.kv.v2.read_secret_version(
            #     path=key, mount_point=self.mount_path
            # )
            # return response['data']['data'].get('value')
            
            logger.warning(f"Vault集成未实现，无法获取密钥: {key}")
            return None
            
        except Exception as e:
            logger.error(f"从Vault获取密钥失败: {key} - {e}")
            return None
    
    def set_secret(self, key: str, value: str, metadata: Optional[SecretMetadata] = None) -> bool:
        """向Vault设置密钥"""
        try:
            # 模拟Vault API调用
            # self._vault_client.secrets.kv.v2.create_or_update_secret(
            #     path=key,
            #     secret={'value': value},
            #     mount_point=self.mount_path
            # )
            
            logger.warning(f"Vault集成未实现，无法设置密钥: {key}")
            return False
            
        except Exception as e:
            logger.error(f"向Vault设置密钥失败: {key} - {e}")
            return False
    
    def delete_secret(self, key: str) -> bool:
        """从Vault删除密钥"""
        try:
            # 模拟Vault API调用
            # self._vault_client.secrets.kv.v2.delete_metadata_and_all_versions(
            #     path=key, mount_point=self.mount_path
            # )
            
            logger.warning(f"Vault集成未实现，无法删除密钥: {key}")
            return False
            
        except Exception as e:
            logger.error(f"从Vault删除密钥失败: {key} - {e}")
            return False
    
    def list_secrets(self, prefix: str = "") -> List[str]:
        """列出Vault中的密钥"""
        try:
            # 模拟Vault API调用
            # response = self._vault_client.secrets.kv.v2.list_secrets(
            #     path=prefix, mount_point=self.mount_path
            # )
            # return response['data']['keys']
            
            logger.warning("Vault集成未实现，无法列出密钥")
            return []
            
        except Exception as e:
            logger.error(f"列出Vault密钥失败: {e}")
            return []
    
    def rotate_secret(self, key: str) -> bool:
        """轮换Vault密钥"""
        try:
            # 实现密钥轮换逻辑
            logger.warning(f"Vault密钥轮换未实现: {key}")
            return False
            
        except Exception as e:
            logger.error(f"轮换Vault密钥失败: {key} - {e}")
            return False


class CompositeKeyManager(KeyManagerInterface):
    """复合密钥管理器，支持多个后端"""
    
    def __init__(self, managers: List[KeyManagerInterface], fallback_order: bool = True):
        self.managers = managers
        self.fallback_order = fallback_order  # 是否按顺序回退
    
    def get_secret(self, key: str, version: str = "latest") -> Optional[str]:
        """从多个管理器获取密钥"""
        for manager in self.managers:
            try:
                value = manager.get_secret(key, version)
                if value is not None:
                    return value
            except Exception as e:
                logger.debug(f"密钥管理器 {type(manager).__name__} 获取失败: {e}")
                if not self.fallback_order:
                    break
        
        return None
    
    def set_secret(self, key: str, value: str, metadata: Optional[SecretMetadata] = None) -> bool:
        """向第一个管理器设置密钥"""
        if self.managers:
            return self.managers[0].set_secret(key, value, metadata)
        return False
    
    def delete_secret(self, key: str) -> bool:
        """从所有管理器删除密钥"""
        success = False
        for manager in self.managers:
            try:
                if manager.delete_secret(key):
                    success = True
            except Exception as e:
                logger.error(f"删除密钥失败 {type(manager).__name__}: {e}")
        
        return success
    
    def list_secrets(self, prefix: str = "") -> List[str]:
        """列出所有管理器的密钥"""
        all_keys = set()
        
        for manager in self.managers:
            try:
                keys = manager.list_secrets(prefix)
                all_keys.update(keys)
            except Exception as e:
                logger.error(f"列出密钥失败 {type(manager).__name__}: {e}")
        
        return list(all_keys)
    
    def rotate_secret(self, key: str) -> bool:
        """在第一个支持轮换的管理器中轮换密钥"""
        for manager in self.managers:
            try:
                if manager.rotate_secret(key):
                    return True
            except Exception as e:
                logger.error(f"轮换密钥失败 {type(manager).__name__}: {e}")
        
        return False


class KeyManagerFactory:
    """密钥管理器工厂"""
    
    @staticmethod
    def create_key_manager(config: Dict[str, Any]) -> KeyManagerInterface:
        """根据配置创建密钥管理器"""
        manager_type = config.get('type', 'environment')
        
        if manager_type == 'environment':
            prefix = config.get('prefix', 'AID_SECRET_')
            return EnvironmentKeyManager(prefix)
        
        elif manager_type == 'file':
            secrets_file = config.get('secrets_file', 'secrets.json')
            return FileKeyManager(secrets_file)
        
        elif manager_type == 'vault':
            vault_url = config.get('vault_url')
            vault_token = config.get('vault_token')
            mount_path = config.get('mount_path', 'secret')
            
            if not vault_url or not vault_token:
                raise ValueError("Vault配置缺少必要参数: vault_url, vault_token")
            
            return VaultKeyManager(vault_url, vault_token, mount_path)
        
        elif manager_type == 'composite':
            managers_config = config.get('managers', [])
            managers = []
            
            for manager_config in managers_config:
                manager = KeyManagerFactory.create_key_manager(manager_config)
                managers.append(manager)
            
            fallback_order = config.get('fallback_order', True)
            return CompositeKeyManager(managers, fallback_order)
        
        else:
            raise ValueError(f"不支持的密钥管理器类型: {manager_type}")


class SecretRotationScheduler:
    """密钥轮换调度器"""
    
    def __init__(self, key_manager: KeyManagerInterface):
        self.key_manager = key_manager
        self.rotation_schedule = {}  # key -> rotation_interval_days
    
    def schedule_rotation(self, key: str, interval_days: int):
        """安排密钥轮换"""
        self.rotation_schedule[key] = interval_days
        logger.info(f"安排密钥轮换: {key} 每 {interval_days} 天")
    
    def check_and_rotate(self) -> Dict[str, bool]:
        """检查并执行需要轮换的密钥"""
        results = {}
        
        for key, interval_days in self.rotation_schedule.items():
            try:
                # 这里应该检查密钥的最后轮换时间
                # 简化实现，假设需要轮换
                if self._should_rotate(key, interval_days):
                    success = self.key_manager.rotate_secret(key)
                    results[key] = success
                    
                    if success:
                        logger.info(f"密钥轮换成功: {key}")
                    else:
                        logger.error(f"密钥轮换失败: {key}")
                        
            except Exception as e:
                logger.error(f"检查密钥轮换失败: {key} - {e}")
                results[key] = False
        
        return results
    
    def _should_rotate(self, key: str, interval_days: int) -> bool:
        """检查是否需要轮换密钥"""
        # 简化实现，实际应该检查密钥的创建/更新时间
        return True


# 全局实例
_key_manager: Optional[KeyManagerInterface] = None


def get_key_manager() -> KeyManagerInterface:
    """获取全局密钥管理器实例"""
    global _key_manager
    if _key_manager is None:
        # 默认使用环境变量管理器
        _key_manager = EnvironmentKeyManager()
    return _key_manager


def configure_key_manager(config: Dict[str, Any]):
    """配置全局密钥管理器"""
    global _key_manager
    _key_manager = KeyManagerFactory.create_key_manager(config)


def get_secret(key: str, version: str = "latest") -> Optional[str]:
    """获取密钥的便捷函数"""
    manager = get_key_manager()
    return manager.get_secret(key, version)


def set_secret(key: str, value: str, metadata: Optional[SecretMetadata] = None) -> bool:
    """设置密钥的便捷函数"""
    manager = get_key_manager()
    return manager.set_secret(key, value, metadata)


if __name__ == "__main__":
    # 测试密钥管理器
    print("=== 环境变量密钥管理器测试 ===")
    env_manager = EnvironmentKeyManager()
    
    # 设置测试密钥
    env_manager.set_secret("test.api_key", "test_secret_value")
    
    # 获取密钥
    value = env_manager.get_secret("test.api_key")
    print(f"获取密钥: {value}")
    
    # 列出密钥
    keys = env_manager.list_secrets("test")
    print(f"密钥列表: {keys}")
    
    print("\n=== 复合密钥管理器测试 ===")
    file_manager = FileKeyManager("test_secrets.json")
    composite_manager = CompositeKeyManager([env_manager, file_manager])
    
    # 测试回退机制
    value = composite_manager.get_secret("test.api_key")
    print(f"复合管理器获取密钥: {value}")
    
    # 清理测试文件
    import os
    if os.path.exists("test_secrets.json"):
        os.remove("test_secrets.json")
