#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置访问审计器

记录和审计配置访问行为，特别是敏感配置的访问
"""

import os
import json
import logging
import sqlite3
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import inspect

from .sensitive_key_manager import get_sensitive_key_manager, SensitivityLevel

logger = logging.getLogger(__name__)


@dataclass
class ConfigAccessRecord:
    """配置访问记录"""
    timestamp: str
    config_key: str
    access_type: str  # get, set, delete
    sensitivity_level: str
    caller_module: str
    caller_function: str
    caller_line: int
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    success: bool = True
    error_message: Optional[str] = None


class ConfigAccessAuditor:
    """配置访问审计器"""
    
    def __init__(self, db_path: str = "logs/config_audit.db", enable_file_log: bool = True):
        self.db_path = db_path
        self.enable_file_log = enable_file_log
        self.sensitive_key_manager = get_sensitive_key_manager()
        
        # 确保日志目录存在
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        self._init_database()
        
        # 设置文件日志
        if self.enable_file_log:
            self._setup_file_logger()
    
    def _init_database(self):
        """初始化审计数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS config_access_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    config_key TEXT NOT NULL,
                    access_type TEXT NOT NULL,
                    sensitivity_level TEXT NOT NULL,
                    caller_module TEXT,
                    caller_function TEXT,
                    caller_line INTEGER,
                    user_id TEXT,
                    session_id TEXT,
                    success BOOLEAN DEFAULT TRUE,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建索引
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_config_key 
                ON config_access_log(config_key)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_timestamp 
                ON config_access_log(timestamp)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_sensitivity 
                ON config_access_log(sensitivity_level)
            """)
    
    def _setup_file_logger(self):
        """设置文件日志记录器"""
        self.audit_logger = logging.getLogger("config_audit")
        self.audit_logger.setLevel(logging.INFO)
        
        # 避免重复添加handler
        if not self.audit_logger.handlers:
            handler = logging.FileHandler("logs/config_access_audit.log")
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.audit_logger.addHandler(handler)
    
    def _get_caller_info(self, skip_frames: int = 2) -> tuple:
        """获取调用者信息"""
        try:
            frame = inspect.currentframe()
            
            # 跳过指定数量的帧
            for _ in range(skip_frames):
                if frame is not None:
                    frame = frame.f_back
            
            if frame is not None:
                module = frame.f_globals.get('__name__', 'unknown')
                function = frame.f_code.co_name
                line = frame.f_lineno
                return module, function, line
            
        except Exception as e:
            logger.debug(f"获取调用者信息失败: {e}")
        
        return "unknown", "unknown", 0
    
    def record_access(self, 
                     config_key: str, 
                     access_type: str,
                     success: bool = True,
                     error_message: Optional[str] = None,
                     user_id: Optional[str] = None,
                     session_id: Optional[str] = None):
        """记录配置访问"""
        
        # 获取敏感级别
        sensitivity_level = self.sensitive_key_manager.get_sensitivity_level(config_key)
        
        # 获取调用者信息
        caller_module, caller_function, caller_line = self._get_caller_info()
        
        # 创建访问记录
        record = ConfigAccessRecord(
            timestamp=datetime.now().isoformat(),
            config_key=config_key,
            access_type=access_type,
            sensitivity_level=sensitivity_level.value,
            caller_module=caller_module,
            caller_function=caller_function,
            caller_line=caller_line,
            user_id=user_id,
            session_id=session_id,
            success=success,
            error_message=error_message
        )
        
        # 记录到数据库
        self._record_to_database(record)
        
        # 记录到文件日志
        if self.enable_file_log:
            self._record_to_file_log(record)
        
        # 敏感配置访问特殊处理
        if sensitivity_level in [SensitivityLevel.SECRET, SensitivityLevel.CONFIDENTIAL]:
            self._handle_sensitive_access(record)
    
    def _record_to_database(self, record: ConfigAccessRecord):
        """记录到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO config_access_log 
                    (timestamp, config_key, access_type, sensitivity_level,
                     caller_module, caller_function, caller_line,
                     user_id, session_id, success, error_message)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    record.timestamp,
                    record.config_key,
                    record.access_type,
                    record.sensitivity_level,
                    record.caller_module,
                    record.caller_function,
                    record.caller_line,
                    record.user_id,
                    record.session_id,
                    record.success,
                    record.error_message
                ))
        except Exception as e:
            logger.error(f"记录配置访问到数据库失败: {e}")
    
    def _record_to_file_log(self, record: ConfigAccessRecord):
        """记录到文件日志"""
        try:
            log_message = (
                f"CONFIG_ACCESS: {record.access_type.upper()} "
                f"key={record.config_key} "
                f"level={record.sensitivity_level} "
                f"caller={record.caller_module}.{record.caller_function}:{record.caller_line} "
                f"success={record.success}"
            )
            
            if record.user_id:
                log_message += f" user={record.user_id}"
            
            if record.session_id:
                log_message += f" session={record.session_id}"
            
            if record.error_message:
                log_message += f" error={record.error_message}"
            
            self.audit_logger.info(log_message)
            
        except Exception as e:
            logger.error(f"记录配置访问到文件失败: {e}")
    
    def _handle_sensitive_access(self, record: ConfigAccessRecord):
        """处理敏感配置访问"""
        # 敏感配置访问的特殊处理
        if record.sensitivity_level == SensitivityLevel.SECRET.value:
            # 记录到安全日志
            security_logger = logging.getLogger("security_audit")
            security_logger.warning(
                f"SECRET配置访问: {record.config_key} "
                f"by {record.caller_module}.{record.caller_function}"
            )
        
        # 可以在这里添加更多安全处理逻辑
        # 例如：发送告警、限制访问频率等
    
    def get_access_history(self, 
                          config_key: Optional[str] = None,
                          hours: int = 24,
                          limit: int = 100) -> List[Dict[str, Any]]:
        """获取访问历史"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            if config_key:
                cursor = conn.execute("""
                    SELECT * FROM config_access_log
                    WHERE config_key = ? AND timestamp >= ?
                    ORDER BY timestamp DESC
                    LIMIT ?
                """, (config_key, cutoff_time.isoformat(), limit))
            else:
                cursor = conn.execute("""
                    SELECT * FROM config_access_log
                    WHERE timestamp >= ?
                    ORDER BY timestamp DESC
                    LIMIT ?
                """, (cutoff_time.isoformat(), limit))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def get_sensitive_access_report(self, days: int = 7) -> Dict[str, Any]:
        """获取敏感配置访问报告"""
        cutoff_time = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            # 敏感配置访问统计
            sensitive_stats = conn.execute("""
                SELECT 
                    sensitivity_level,
                    COUNT(*) as access_count,
                    COUNT(DISTINCT config_key) as unique_keys,
                    COUNT(DISTINCT caller_module) as unique_callers
                FROM config_access_log
                WHERE timestamp >= ? 
                AND sensitivity_level IN ('secret', 'confidential')
                GROUP BY sensitivity_level
            """, (cutoff_time.isoformat(),)).fetchall()
            
            # 最常访问的敏感配置
            top_sensitive_keys = conn.execute("""
                SELECT 
                    config_key,
                    sensitivity_level,
                    COUNT(*) as access_count,
                    COUNT(DISTINCT caller_module) as unique_callers
                FROM config_access_log
                WHERE timestamp >= ?
                AND sensitivity_level IN ('secret', 'confidential')
                GROUP BY config_key, sensitivity_level
                ORDER BY access_count DESC
                LIMIT 20
            """, (cutoff_time.isoformat(),)).fetchall()
            
            # 访问失败统计
            failed_access = conn.execute("""
                SELECT 
                    config_key,
                    COUNT(*) as failed_count,
                    MAX(timestamp) as last_failure
                FROM config_access_log
                WHERE timestamp >= ?
                AND success = FALSE
                GROUP BY config_key
                ORDER BY failed_count DESC
                LIMIT 10
            """, (cutoff_time.isoformat(),)).fetchall()
            
            return {
                "report_period_days": days,
                "generated_at": datetime.now().isoformat(),
                "sensitive_stats": [dict(row) for row in sensitive_stats],
                "top_sensitive_keys": [dict(row) for row in top_sensitive_keys],
                "failed_access": [dict(row) for row in failed_access]
            }
    
    def get_access_patterns(self, days: int = 30) -> Dict[str, Any]:
        """分析访问模式"""
        cutoff_time = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            # 按小时统计访问量
            hourly_stats = conn.execute("""
                SELECT 
                    strftime('%H', timestamp) as hour,
                    COUNT(*) as access_count
                FROM config_access_log
                WHERE timestamp >= ?
                GROUP BY hour
                ORDER BY hour
            """, (cutoff_time.isoformat(),)).fetchall()
            
            # 按调用者模块统计
            caller_stats = conn.execute("""
                SELECT 
                    caller_module,
                    COUNT(*) as access_count,
                    COUNT(DISTINCT config_key) as unique_keys
                FROM config_access_log
                WHERE timestamp >= ?
                GROUP BY caller_module
                ORDER BY access_count DESC
                LIMIT 20
            """, (cutoff_time.isoformat(),)).fetchall()
            
            # 异常访问模式（频率过高）
            high_frequency_access = conn.execute("""
                SELECT 
                    config_key,
                    caller_module,
                    COUNT(*) as access_count,
                    COUNT(*) * 1.0 / ? as daily_average
                FROM config_access_log
                WHERE timestamp >= ?
                GROUP BY config_key, caller_module
                HAVING access_count > 1000
                ORDER BY access_count DESC
            """, (days, cutoff_time.isoformat())).fetchall()
            
            return {
                "analysis_period_days": days,
                "hourly_distribution": [dict(row) for row in hourly_stats],
                "top_callers": [dict(row) for row in caller_stats],
                "high_frequency_access": [dict(row) for row in high_frequency_access]
            }
    
    def cleanup_old_records(self, days: int = 90):
        """清理旧的审计记录"""
        cutoff_time = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                DELETE FROM config_access_log
                WHERE timestamp < ?
            """, (cutoff_time.isoformat(),))
            
            deleted_count = cursor.rowcount
            logger.info(f"清理了 {deleted_count} 条超过 {days} 天的审计记录")
            
            return deleted_count


# 全局实例
_config_access_auditor: Optional[ConfigAccessAuditor] = None


def get_config_access_auditor() -> ConfigAccessAuditor:
    """获取全局配置访问审计器实例"""
    global _config_access_auditor
    if _config_access_auditor is None:
        _config_access_auditor = ConfigAccessAuditor()
    return _config_access_auditor


def audit_config_access(config_key: str, 
                       access_type: str,
                       success: bool = True,
                       error_message: Optional[str] = None,
                       user_id: Optional[str] = None,
                       session_id: Optional[str] = None):
    """审计配置访问的便捷函数"""
    auditor = get_config_access_auditor()
    auditor.record_access(
        config_key=config_key,
        access_type=access_type,
        success=success,
        error_message=error_message,
        user_id=user_id,
        session_id=session_id
    )


if __name__ == "__main__":
    # 测试审计器
    auditor = ConfigAccessAuditor()
    
    # 模拟一些访问记录
    test_accesses = [
        ("app.debug", "get"),
        ("database.password", "get"),
        ("llm.api_key", "get"),
        ("auth.token", "set"),
        ("app.secret", "get"),
    ]
    
    print("=== 记录测试访问 ===")
    for key, access_type in test_accesses:
        auditor.record_access(key, access_type, user_id="test_user")
        print(f"记录访问: {key} ({access_type})")
    
    print("\n=== 访问历史 ===")
    history = auditor.get_access_history(hours=1)
    for record in history:
        print(f"{record['timestamp']}: {record['config_key']} ({record['access_type']}) - {record['sensitivity_level']}")
    
    print("\n=== 敏感配置访问报告 ===")
    report = auditor.get_sensitive_access_report(days=1)
    print(json.dumps(report, indent=2, ensure_ascii=False))
