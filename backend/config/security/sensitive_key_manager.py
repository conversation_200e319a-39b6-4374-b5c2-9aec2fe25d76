#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
敏感键管理器

管理敏感配置键的识别、脱敏和访问控制
"""

import re
import logging
from typing import Dict, List, Set, Any, Optional, Pattern
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class SensitivityLevel(Enum):
    """敏感级别"""
    PUBLIC = "public"           # 公开信息
    INTERNAL = "internal"       # 内部信息
    CONFIDENTIAL = "confidential"  # 机密信息
    SECRET = "secret"           # 秘密信息


@dataclass
class SensitiveKeyRule:
    """敏感键规则"""
    pattern: str                    # 匹配模式
    level: SensitivityLevel        # 敏感级别
    mask_strategy: str             # 脱敏策略
    description: str               # 描述
    examples: List[str]            # 示例键


class SensitiveKeyManager:
    """敏感键管理器"""
    
    def __init__(self):
        self.rules: List[SensitiveKeyRule] = []
        self.compiled_patterns: List[tuple] = []  # (compiled_pattern, rule)
        self._init_default_rules()
        self._compile_patterns()
    
    def _init_default_rules(self):
        """初始化默认敏感键规则"""
        self.rules = [
            # 密码相关
            SensitiveKeyRule(
                pattern=r".*password.*",
                level=SensitivityLevel.SECRET,
                mask_strategy="full",
                description="密码字段",
                examples=["database.password", "admin.password", "user.password"]
            ),
            
            # API密钥
            SensitiveKeyRule(
                pattern=r".*api[_-]?key.*",
                level=SensitivityLevel.SECRET,
                mask_strategy="partial",
                description="API密钥",
                examples=["llm.api_key", "service.api_key", "external.api_key"]
            ),
            
            # 访问令牌
            SensitiveKeyRule(
                pattern=r".*token.*",
                level=SensitivityLevel.SECRET,
                mask_strategy="partial",
                description="访问令牌",
                examples=["auth.token", "access_token", "refresh_token"]
            ),
            
            # 密钥
            SensitiveKeyRule(
                pattern=r".*secret.*",
                level=SensitivityLevel.SECRET,
                mask_strategy="full",
                description="密钥信息",
                examples=["app.secret", "jwt.secret", "encryption.secret"]
            ),
            
            # 私钥
            SensitiveKeyRule(
                pattern=r".*private[_-]?key.*",
                level=SensitivityLevel.SECRET,
                mask_strategy="full",
                description="私钥",
                examples=["ssl.private_key", "rsa.private_key"]
            ),
            
            # 证书
            SensitiveKeyRule(
                pattern=r".*cert.*",
                level=SensitivityLevel.CONFIDENTIAL,
                mask_strategy="partial",
                description="证书信息",
                examples=["ssl.cert", "tls.certificate"]
            ),
            
            # 数据库连接字符串
            SensitiveKeyRule(
                pattern=r".*connection[_-]?string.*",
                level=SensitivityLevel.CONFIDENTIAL,
                mask_strategy="partial",
                description="数据库连接字符串",
                examples=["database.connection_string", "db.connection_string"]
            ),
            
            # 用户凭据
            SensitiveKeyRule(
                pattern=r".*credential.*",
                level=SensitivityLevel.SECRET,
                mask_strategy="full",
                description="用户凭据",
                examples=["service.credentials", "auth.credentials"]
            ),
            
            # 邮箱密码
            SensitiveKeyRule(
                pattern=r".*smtp[_-]?password.*",
                level=SensitivityLevel.SECRET,
                mask_strategy="full",
                description="SMTP密码",
                examples=["email.smtp_password", "mail.smtp_password"]
            ),
            
            # 第三方服务密钥
            SensitiveKeyRule(
                pattern=r".*(openai|anthropic|google|aws|azure)[_-]?key.*",
                level=SensitivityLevel.SECRET,
                mask_strategy="partial",
                description="第三方服务密钥",
                examples=["llm.openai_key", "cloud.aws_key", "ai.anthropic_key"]
            ),
            
            # 内部主机信息（部分敏感）
            SensitiveKeyRule(
                pattern=r".*internal[_-]?host.*",
                level=SensitivityLevel.INTERNAL,
                mask_strategy="domain",
                description="内部主机地址",
                examples=["database.internal_host", "cache.internal_host"]
            ),
            
            # 生产环境配置
            SensitiveKeyRule(
                pattern=r".*prod[_-]?.*[_-]?(host|url|endpoint).*",
                level=SensitivityLevel.CONFIDENTIAL,
                mask_strategy="domain",
                description="生产环境地址",
                examples=["api.prod_host", "service.prod_url"]
            )
        ]
    
    def _compile_patterns(self):
        """编译正则表达式模式"""
        self.compiled_patterns = []
        for rule in self.rules:
            try:
                compiled = re.compile(rule.pattern, re.IGNORECASE)
                self.compiled_patterns.append((compiled, rule))
            except re.error as e:
                logger.error(f"编译敏感键模式失败: {rule.pattern} - {e}")
    
    def is_sensitive(self, key: str) -> bool:
        """检查键是否敏感"""
        return self.get_sensitivity_level(key) != SensitivityLevel.PUBLIC
    
    def get_sensitivity_level(self, key: str) -> SensitivityLevel:
        """获取键的敏感级别"""
        for pattern, rule in self.compiled_patterns:
            if pattern.match(key):
                return rule.level
        return SensitivityLevel.PUBLIC
    
    def get_mask_strategy(self, key: str) -> str:
        """获取脱敏策略"""
        for pattern, rule in self.compiled_patterns:
            if pattern.match(key):
                return rule.mask_strategy
        return "none"
    
    def mask_value(self, key: str, value: Any) -> str:
        """脱敏值"""
        if not self.is_sensitive(key):
            return str(value)
        
        if value is None:
            return "None"
        
        value_str = str(value)
        strategy = self.get_mask_strategy(key)
        
        return self._apply_mask_strategy(value_str, strategy)
    
    def _apply_mask_strategy(self, value: str, strategy: str) -> str:
        """应用脱敏策略"""
        if not value:
            return value
        
        if strategy == "full":
            # 完全脱敏
            return "*" * min(len(value), 8)
        
        elif strategy == "partial":
            # 部分脱敏，保留前后几位
            if len(value) <= 6:
                return "*" * len(value)
            else:
                return value[:2] + "*" * (len(value) - 4) + value[-2:]
        
        elif strategy == "domain":
            # 域名脱敏，保留顶级域名
            if "." in value:
                parts = value.split(".")
                if len(parts) > 2:
                    return "***." + ".".join(parts[-2:])
                else:
                    return "***." + parts[-1]
            else:
                return self._apply_mask_strategy(value, "partial")
        
        elif strategy == "email":
            # 邮箱脱敏
            if "@" in value:
                local, domain = value.split("@", 1)
                masked_local = local[0] + "*" * (len(local) - 1) if local else "*"
                return f"{masked_local}@{domain}"
            else:
                return self._apply_mask_strategy(value, "partial")
        
        else:
            # 默认部分脱敏
            return self._apply_mask_strategy(value, "partial")
    
    def get_sensitive_keys_report(self) -> Dict[str, Any]:
        """生成敏感键报告"""
        report = {
            "total_rules": len(self.rules),
            "rules_by_level": {},
            "rules_detail": []
        }
        
        # 按级别统计
        for rule in self.rules:
            level = rule.level.value
            if level not in report["rules_by_level"]:
                report["rules_by_level"][level] = 0
            report["rules_by_level"][level] += 1
        
        # 规则详情
        for rule in self.rules:
            report["rules_detail"].append({
                "pattern": rule.pattern,
                "level": rule.level.value,
                "mask_strategy": rule.mask_strategy,
                "description": rule.description,
                "examples": rule.examples
            })
        
        return report
    
    def add_custom_rule(self, rule: SensitiveKeyRule):
        """添加自定义规则"""
        self.rules.append(rule)
        self._compile_patterns()
        logger.info(f"添加自定义敏感键规则: {rule.pattern}")
    
    def validate_config_keys(self, config_keys: List[str]) -> Dict[str, Any]:
        """验证配置键的敏感性"""
        results = {
            "total_keys": len(config_keys),
            "sensitive_keys": [],
            "public_keys": [],
            "by_level": {}
        }
        
        for key in config_keys:
            level = self.get_sensitivity_level(key)
            
            if level == SensitivityLevel.PUBLIC:
                results["public_keys"].append(key)
            else:
                results["sensitive_keys"].append({
                    "key": key,
                    "level": level.value,
                    "mask_strategy": self.get_mask_strategy(key)
                })
                
                if level.value not in results["by_level"]:
                    results["by_level"][level.value] = 0
                results["by_level"][level.value] += 1
        
        return results


class SensitiveDataMasker:
    """敏感数据脱敏器"""
    
    def __init__(self, key_manager: SensitiveKeyManager = None):
        self.key_manager = key_manager or SensitiveKeyManager()
    
    def mask_dict(self, data: Dict[str, Any], key_prefix: str = "") -> Dict[str, Any]:
        """脱敏字典数据"""
        masked_data = {}
        
        for key, value in data.items():
            full_key = f"{key_prefix}.{key}" if key_prefix else key
            
            if isinstance(value, dict):
                masked_data[key] = self.mask_dict(value, full_key)
            elif isinstance(value, list):
                masked_data[key] = self.mask_list(value, full_key)
            else:
                if self.key_manager.is_sensitive(full_key):
                    masked_data[key] = self.key_manager.mask_value(full_key, value)
                else:
                    masked_data[key] = value
        
        return masked_data
    
    def mask_list(self, data: List[Any], key_prefix: str = "") -> List[Any]:
        """脱敏列表数据"""
        masked_list = []
        
        for i, item in enumerate(data):
            item_key = f"{key_prefix}[{i}]"
            
            if isinstance(item, dict):
                masked_list.append(self.mask_dict(item, item_key))
            elif isinstance(item, list):
                masked_list.append(self.mask_list(item, item_key))
            else:
                if self.key_manager.is_sensitive(key_prefix):
                    masked_list.append(self.key_manager.mask_value(key_prefix, item))
                else:
                    masked_list.append(item)
        
        return masked_list
    
    def mask_log_message(self, message: str) -> str:
        """脱敏日志消息"""
        # 简单的日志脱敏，查找可能的敏感信息模式
        patterns = [
            (r'password["\s]*[:=]["\s]*([^"\s,}]+)', r'password: "***"'),
            (r'token["\s]*[:=]["\s]*([^"\s,}]+)', r'token: "***"'),
            (r'key["\s]*[:=]["\s]*([^"\s,}]+)', r'key: "***"'),
            (r'secret["\s]*[:=]["\s]*([^"\s,}]+)', r'secret: "***"'),
        ]
        
        masked_message = message
        for pattern, replacement in patterns:
            masked_message = re.sub(pattern, replacement, masked_message, flags=re.IGNORECASE)
        
        return masked_message


# 全局实例
_sensitive_key_manager: Optional[SensitiveKeyManager] = None
_sensitive_data_masker: Optional[SensitiveDataMasker] = None


def get_sensitive_key_manager() -> SensitiveKeyManager:
    """获取全局敏感键管理器实例"""
    global _sensitive_key_manager
    if _sensitive_key_manager is None:
        _sensitive_key_manager = SensitiveKeyManager()
    return _sensitive_key_manager


def get_sensitive_data_masker() -> SensitiveDataMasker:
    """获取全局敏感数据脱敏器实例"""
    global _sensitive_data_masker
    if _sensitive_data_masker is None:
        _sensitive_data_masker = SensitiveDataMasker(get_sensitive_key_manager())
    return _sensitive_data_masker


def mask_sensitive_config(config_data: Dict[str, Any]) -> Dict[str, Any]:
    """脱敏配置数据的便捷函数"""
    masker = get_sensitive_data_masker()
    return masker.mask_dict(config_data)


def is_sensitive_key(key: str) -> bool:
    """检查键是否敏感的便捷函数"""
    manager = get_sensitive_key_manager()
    return manager.is_sensitive(key)


if __name__ == "__main__":
    # 测试敏感键管理器
    manager = SensitiveKeyManager()
    
    # 测试键
    test_keys = [
        "app.debug",
        "database.password",
        "llm.api_key",
        "auth.token",
        "app.secret",
        "ssl.private_key",
        "email.smtp_password",
        "service.host"
    ]
    
    print("=== 敏感键检测测试 ===")
    for key in test_keys:
        is_sensitive = manager.is_sensitive(key)
        level = manager.get_sensitivity_level(key)
        masked = manager.mask_value(key, "test_secret_value_123456")
        
        print(f"{key}: 敏感={is_sensitive}, 级别={level.value}, 脱敏='{masked}'")
    
    print("\n=== 配置脱敏测试 ===")
    test_config = {
        "app": {
            "debug": True,
            "name": "Test App"
        },
        "database": {
            "host": "localhost",
            "password": "super_secret_password"
        },
        "llm": {
            "api_key": "sk-1234567890abcdef",
            "model": "gpt-4"
        }
    }
    
    masked_config = mask_sensitive_config(test_config)
    print("原始配置:", test_config)
    print("脱敏配置:", masked_config)
