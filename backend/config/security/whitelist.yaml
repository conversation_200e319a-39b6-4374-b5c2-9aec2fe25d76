# 环境变量覆盖白名单配置
# 定义哪些配置键允许通过环境变量覆盖，以及相应的安全级别

whitelist_rules:
  # 应用基础配置
  - key_pattern: "app.name"
    sensitivity_level: "public"
    allow_env_override: true
    description: "应用名称"
    
  - key_pattern: "app.version"
    sensitivity_level: "public"
    allow_env_override: true
    description: "应用版本号"
    
  - key_pattern: "app.debug"
    sensitivity_level: "internal"
    allow_env_override: true
    description: "调试模式开关"
    
  - key_pattern: "app.environment"
    sensitivity_level: "internal"
    allow_env_override: true
    description: "运行环境 (development/staging/production)"
    validation_pattern: '^(development|staging|production)$'
    
  - key_pattern: "app.log_level"
    sensitivity_level: "internal"
    allow_env_override: true
    description: "日志级别"
    validation_pattern: '^(DEBUG|INFO|WARNING|ERROR|CRITICAL)$'

  # LLM配置
  - key_pattern: "llm.default_model"
    sensitivity_level: "public"
    allow_env_override: true
    description: "默认LLM模型名称"
    
  - key_pattern: "llm.temperature"
    sensitivity_level: "public"
    allow_env_override: true
    description: "LLM温度参数"
    validation_pattern: '^[0-2](\.[0-9]+)?$'
    
  - key_pattern: "llm.max_tokens"
    sensitivity_level: "public"
    allow_env_override: true
    description: "最大令牌数"
    validation_pattern: '^[1-9][0-9]*$'
    
  - key_pattern: "llm.timeout"
    sensitivity_level: "public"
    allow_env_override: true
    description: "LLM请求超时时间（秒）"
    validation_pattern: '^[1-9][0-9]*$'
    
  - key_pattern: "llm.*.api_key"
    sensitivity_level: "secret"
    allow_env_override: true
    description: "LLM提供商API密钥"
    
  - key_pattern: "llm.*.api_base"
    sensitivity_level: "internal"
    allow_env_override: true
    description: "LLM API基础URL"
    validation_pattern: '^https?://.*'
    
  - key_pattern: "llm.*.model_name"
    sensitivity_level: "public"
    allow_env_override: true
    description: "LLM模型名称"
    
  - key_pattern: "llm.models"
    sensitivity_level: "public"
    allow_env_override: true
    description: "LLM模型配置"

  # 数据库配置
  - key_pattern: "database.connection.path"
    sensitivity_level: "internal"
    allow_env_override: true
    description: "数据库文件路径"
    
  - key_pattern: "database.connection.timeout"
    sensitivity_level: "public"
    allow_env_override: true
    description: "数据库连接超时时间"
    validation_pattern: '^[1-9][0-9]*$'
    
  - key_pattern: "database.connection.check_same_thread"
    sensitivity_level: "internal"
    allow_env_override: true
    description: "SQLite同线程检查"
    
  - key_pattern: "database.*.password"
    sensitivity_level: "secret"
    allow_env_override: true
    description: "数据库密码"
    
  - key_pattern: "database.*.username"
    sensitivity_level: "confidential"
    allow_env_override: true
    description: "数据库用户名"
    
  - key_pattern: "database.backup.*"
    sensitivity_level: "internal"
    allow_env_override: true
    description: "数据库备份配置"

  # 系统安全配置
  - key_pattern: "system.security.enabled"
    sensitivity_level: "internal"
    allow_env_override: true
    description: "安全功能总开关"
    
  - key_pattern: "system.security.authentication.enabled"
    sensitivity_level: "internal"
    allow_env_override: true
    description: "认证功能开关"
    
  - key_pattern: "system.security.*.secret"
    sensitivity_level: "secret"
    allow_env_override: true
    description: "安全密钥"
    
  - key_pattern: "system.security.*.token"
    sensitivity_level: "secret"
    allow_env_override: true
    description: "安全令牌"

  # 性能配置
  - key_pattern: "performance.cache.enabled"
    sensitivity_level: "public"
    allow_env_override: true
    description: "缓存功能开关"
    
  - key_pattern: "performance.cache.ttl"
    sensitivity_level: "public"
    allow_env_override: true
    description: "缓存TTL（秒）"
    validation_pattern: '^[1-9][0-9]*$'
    
  - key_pattern: "performance.cache.max_size"
    sensitivity_level: "public"
    allow_env_override: true
    description: "缓存最大大小"
    validation_pattern: '^[1-9][0-9]*$'
    
  - key_pattern: "performance.timeout.*"
    sensitivity_level: "public"
    allow_env_override: true
    description: "各种超时配置"
    
  - key_pattern: "performance.limits.*"
    sensitivity_level: "internal"
    allow_env_override: true
    description: "性能限制配置"
    
  - key_pattern: "performance.concurrency.*"
    sensitivity_level: "internal"
    allow_env_override: true
    description: "并发配置"

  # 监控和日志配置
  - key_pattern: "monitoring.*"
    sensitivity_level: "internal"
    allow_env_override: true
    description: "监控相关配置"
    
  - key_pattern: "logging.*"
    sensitivity_level: "internal"
    allow_env_override: true
    description: "日志相关配置"

  # 业务配置
  - key_pattern: "business.*.enabled"
    sensitivity_level: "internal"
    allow_env_override: true
    description: "业务功能开关"
    
  - key_pattern: "business.retry.*"
    sensitivity_level: "public"
    allow_env_override: true
    description: "重试策略配置"
    
  - key_pattern: "business.quality_control.*"
    sensitivity_level: "internal"
    allow_env_override: true
    description: "质量控制配置"

  # 知识库配置
  - key_pattern: "knowledge_base.*"
    sensitivity_level: "internal"
    allow_env_override: true
    description: "知识库配置"

# 明确禁止的配置键（即使匹配上面的模式也不允许）
blocked_keys:
  - "system.security.master_key"
  - "database.encryption_key"
  - "*.private_key"
  - "*.certificate"

# 敏感信息检测配置
sensitive_detection:
  # 自定义敏感模式
  custom_patterns:
    - pattern: "sk-[a-zA-Z0-9]{32,}"
      level: "secret"
      description: "OpenAI API密钥"
      
    - pattern: "[a-zA-Z0-9]{40,}"
      level: "confidential"
      description: "长随机字符串（可能是密钥）"
      
    - pattern: "-----BEGIN.*PRIVATE KEY-----"
      level: "secret"
      description: "私钥"
      
    - pattern: "mongodb://.*:.*@"
      level: "confidential"
      description: "MongoDB连接字符串"
      
    - pattern: "mysql://.*:.*@"
      level: "confidential"
      description: "MySQL连接字符串"

  # 敏感键名模式
  sensitive_key_patterns:
    - ".*api_key.*"
    - ".*secret.*"
    - ".*password.*"
    - ".*passwd.*"
    - ".*token.*"
    - ".*private_key.*"
    - ".*encryption_key.*"
    - ".*auth.*"
    - ".*credential.*"
    - ".*cert.*"
    - ".*key.*"

# 日志配置
logging:
  # 是否记录环境变量覆盖
  log_env_overrides: true
  
  # 是否记录被拒绝的覆盖尝试
  log_rejected_overrides: true
  
  # 是否记录敏感信息检测结果
  log_sensitive_detection: true
  
  # 日志级别
  log_level: "INFO"

# 验证配置
validation:
  # 是否启用严格模式（拒绝所有未明确允许的键）
  strict_mode: true
  
  # 是否允许通配符匹配
  allow_wildcard_matching: true
  
  # 最大配置键长度
  max_key_length: 100
  
  # 最大配置值长度
  max_value_length: 10000
