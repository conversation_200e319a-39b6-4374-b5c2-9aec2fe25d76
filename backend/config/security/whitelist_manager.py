#!/usr/bin/env python3
"""
配置安全白名单管理器

管理环境变量覆盖的安全白名单和敏感信息检测
"""

import re
import logging
from typing import Dict, List, Set, Any, Optional, Tuple
from pathlib import Path
import yaml
from dataclasses import dataclass
from enum import Enum


logger = logging.getLogger(__name__)


class SensitivityLevel(Enum):
    """敏感度级别"""
    PUBLIC = "public"           # 公开信息，可以记录
    INTERNAL = "internal"       # 内部信息，限制记录
    CONFIDENTIAL = "confidential"  # 机密信息，不能记录
    SECRET = "secret"           # 秘密信息，严格保护


@dataclass
class ConfigKeyRule:
    """配置键规则"""
    key_pattern: str
    sensitivity_level: SensitivityLevel
    allow_env_override: bool
    description: str
    validation_pattern: Optional[str] = None


class SensitiveInfoDetector:
    """敏感信息检测器"""
    
    def __init__(self):
        self.sensitive_patterns = {
            # API密钥模式
            'openai_api_key': {
                'pattern': r'sk-[a-zA-Z0-9]{32,}',
                'level': SensitivityLevel.SECRET,
                'description': 'OpenAI API密钥'
            },
            'generic_api_key': {
                'pattern': r'[a-zA-Z0-9]{32,}',
                'level': SensitivityLevel.CONFIDENTIAL,
                'description': '通用API密钥'
            },
            
            # 密码模式
            'password': {
                'pattern': r'.{8,}',  # 8位以上可能是密码
                'level': SensitivityLevel.SECRET,
                'description': '密码'
            },
            
            # JWT令牌
            'jwt_token': {
                'pattern': r'eyJ[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+',
                'level': SensitivityLevel.SECRET,
                'description': 'JWT令牌'
            },
            
            # 数据库连接字符串
            'db_connection': {
                'pattern': r'(mysql|postgresql|mongodb)://.*:.*@',
                'level': SensitivityLevel.CONFIDENTIAL,
                'description': '数据库连接字符串'
            },
            
            # 私钥
            'private_key': {
                'pattern': r'-----BEGIN (RSA |EC |)PRIVATE KEY-----',
                'level': SensitivityLevel.SECRET,
                'description': '私钥'
            }
        }
        
        self.sensitive_key_patterns = [
            r'.*api_key.*',
            r'.*secret.*',
            r'.*password.*',
            r'.*passwd.*',
            r'.*token.*',
            r'.*private_key.*',
            r'.*encryption_key.*',
            r'.*auth.*',
            r'.*credential.*'
        ]
    
    def detect_sensitive_key(self, key: str) -> Tuple[bool, SensitivityLevel, str]:
        """检测配置键是否敏感"""
        key_lower = key.lower()
        
        for pattern in self.sensitive_key_patterns:
            if re.match(pattern, key_lower):
                # 根据关键词确定敏感度级别
                if any(word in key_lower for word in ['secret', 'private_key', 'password']):
                    return True, SensitivityLevel.SECRET, f"敏感配置键: {key}"
                elif any(word in key_lower for word in ['api_key', 'token', 'credential']):
                    return True, SensitivityLevel.CONFIDENTIAL, f"机密配置键: {key}"
                else:
                    return True, SensitivityLevel.INTERNAL, f"内部配置键: {key}"
        
        return False, SensitivityLevel.PUBLIC, ""
    
    def detect_sensitive_value(self, value: Any, key: str = "") -> Tuple[bool, SensitivityLevel, str]:
        """检测配置值是否敏感"""
        if not isinstance(value, str):
            return False, SensitivityLevel.PUBLIC, ""
        
        # 首先检查键是否敏感
        is_sensitive_key, key_level, key_reason = self.detect_sensitive_key(key)
        if is_sensitive_key:
            return True, key_level, key_reason
        
        # 检查值的模式
        for pattern_name, pattern_info in self.sensitive_patterns.items():
            if re.search(pattern_info['pattern'], value):
                return True, pattern_info['level'], pattern_info['description']
        
        return False, SensitivityLevel.PUBLIC, ""
    
    def mask_sensitive_value(self, value: Any, sensitivity_level: SensitivityLevel) -> str:
        """根据敏感度级别遮蔽敏感值"""
        if sensitivity_level == SensitivityLevel.PUBLIC:
            return str(value)
        elif sensitivity_level == SensitivityLevel.INTERNAL:
            return f"[INTERNAL:{len(str(value))} chars]"
        elif sensitivity_level == SensitivityLevel.CONFIDENTIAL:
            return "[CONFIDENTIAL]"
        elif sensitivity_level == SensitivityLevel.SECRET:
            return "[SECRET]"
        else:
            return "[REDACTED]"


class ConfigWhitelistManager:
    """配置白名单管理器"""
    
    def __init__(self, whitelist_config_path: Optional[str] = None):
        self.detector = SensitiveInfoDetector()
        self.whitelist_rules: Dict[str, ConfigKeyRule] = {}
        self.allowed_keys: Set[str] = set()
        self.blocked_keys: Set[str] = set()
        
        if whitelist_config_path:
            self.load_whitelist_config(whitelist_config_path)
        else:
            self._load_default_rules()
    
    def _load_default_rules(self):
        """加载默认的白名单规则"""
        default_rules = [
            # 应用配置
            ConfigKeyRule("app.name", SensitivityLevel.PUBLIC, True, "应用名称"),
            ConfigKeyRule("app.version", SensitivityLevel.PUBLIC, True, "应用版本"),
            ConfigKeyRule("app.debug", SensitivityLevel.INTERNAL, True, "调试模式"),
            ConfigKeyRule("app.environment", SensitivityLevel.INTERNAL, True, "运行环境"),
            ConfigKeyRule("app.log_level", SensitivityLevel.INTERNAL, True, "日志级别"),
            
            # LLM配置
            ConfigKeyRule("llm.default_model", SensitivityLevel.PUBLIC, True, "默认LLM模型"),
            ConfigKeyRule("llm.temperature", SensitivityLevel.PUBLIC, True, "LLM温度参数"),
            ConfigKeyRule("llm.max_tokens", SensitivityLevel.PUBLIC, True, "最大令牌数"),
            ConfigKeyRule("llm.timeout", SensitivityLevel.PUBLIC, True, "LLM请求超时"),
            ConfigKeyRule("llm.*.api_key", SensitivityLevel.SECRET, True, "LLM API密钥"),
            ConfigKeyRule("llm.*.api_base", SensitivityLevel.INTERNAL, True, "LLM API基础URL"),
            
            # 数据库配置
            ConfigKeyRule("database.connection.path", SensitivityLevel.INTERNAL, True, "数据库路径"),
            ConfigKeyRule("database.connection.timeout", SensitivityLevel.PUBLIC, True, "数据库超时"),
            ConfigKeyRule("database.*.password", SensitivityLevel.SECRET, True, "数据库密码"),
            
            # 系统配置
            ConfigKeyRule("system.security.enabled", SensitivityLevel.INTERNAL, True, "安全功能开关"),
            ConfigKeyRule("system.performance.*", SensitivityLevel.PUBLIC, True, "性能配置"),
            
            # 缓存配置
            ConfigKeyRule("cache.enabled", SensitivityLevel.PUBLIC, True, "缓存开关"),
            ConfigKeyRule("cache.ttl", SensitivityLevel.PUBLIC, True, "缓存TTL"),
            ConfigKeyRule("cache.max_size", SensitivityLevel.PUBLIC, True, "缓存最大大小"),
            
            # 监控配置
            ConfigKeyRule("monitoring.*", SensitivityLevel.INTERNAL, True, "监控配置"),
            ConfigKeyRule("logging.*", SensitivityLevel.INTERNAL, True, "日志配置"),
        ]
        
        for rule in default_rules:
            self.add_rule(rule)
    
    def load_whitelist_config(self, config_path: str):
        """从配置文件加载白名单规则"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            rules_config = config.get('whitelist_rules', [])
            for rule_config in rules_config:
                rule = ConfigKeyRule(
                    key_pattern=rule_config['key_pattern'],
                    sensitivity_level=SensitivityLevel(rule_config['sensitivity_level']),
                    allow_env_override=rule_config.get('allow_env_override', True),
                    description=rule_config.get('description', ''),
                    validation_pattern=rule_config.get('validation_pattern')
                )
                self.add_rule(rule)
            
            logger.info(f"从 {config_path} 加载了 {len(rules_config)} 个白名单规则")
            
        except Exception as e:
            logger.error(f"加载白名单配置失败: {e}")
            self._load_default_rules()
    
    def add_rule(self, rule: ConfigKeyRule):
        """添加白名单规则"""
        self.whitelist_rules[rule.key_pattern] = rule
        
        if rule.allow_env_override:
            # 处理通配符模式
            if '*' in rule.key_pattern:
                # 对于通配符模式，我们需要在实际检查时匹配
                pass
            else:
                self.allowed_keys.add(rule.key_pattern)
        else:
            self.blocked_keys.add(rule.key_pattern)
    
    def is_key_allowed(self, config_key: str) -> Tuple[bool, Optional[ConfigKeyRule]]:
        """检查配置键是否允许环境变量覆盖"""
        # 精确匹配
        if config_key in self.allowed_keys:
            rule = self.whitelist_rules.get(config_key)
            return True, rule
        
        if config_key in self.blocked_keys:
            rule = self.whitelist_rules.get(config_key)
            return False, rule
        
        # 通配符匹配
        for pattern, rule in self.whitelist_rules.items():
            if self._match_pattern(config_key, pattern):
                return rule.allow_env_override, rule
        
        # 默认拒绝未知键
        return False, None
    
    def _match_pattern(self, key: str, pattern: str) -> bool:
        """匹配通配符模式"""
        # 将通配符模式转换为正则表达式
        regex_pattern = pattern.replace('.', r'\.').replace('*', r'[^.]*')
        regex_pattern = f"^{regex_pattern}$"
        
        return bool(re.match(regex_pattern, key))
    
    def validate_env_override(self, config_key: str, value: Any) -> Tuple[bool, str, Dict[str, Any]]:
        """验证环境变量覆盖"""
        # 检查键是否允许
        is_allowed, rule = self.is_key_allowed(config_key)
        
        if not is_allowed:
            return False, f"配置键 '{config_key}' 不允许环境变量覆盖", {}
        
        # 检查敏感信息
        is_sensitive, sensitivity_level, reason = self.detector.detect_sensitive_value(value, config_key)
        
        # 验证值格式（如果有验证模式）
        validation_error = None
        if rule and rule.validation_pattern:
            if not re.match(rule.validation_pattern, str(value)):
                validation_error = f"值不符合验证模式: {rule.validation_pattern}"
        
        # 生成安全日志信息
        safe_value = self.detector.mask_sensitive_value(value, sensitivity_level)
        log_info = {
            'config_key': config_key,
            'value': safe_value,
            'sensitivity_level': sensitivity_level.value,
            'is_sensitive': is_sensitive,
            'rule_description': rule.description if rule else "未知规则",
            'validation_error': validation_error
        }
        
        if validation_error:
            return False, validation_error, log_info
        
        return True, "验证通过", log_info
    
    def get_allowed_keys(self) -> Set[str]:
        """获取所有允许的配置键"""
        allowed = set(self.allowed_keys)
        
        # 添加通配符匹配的键（这需要根据实际Schema动态生成）
        for pattern, rule in self.whitelist_rules.items():
            if rule.allow_env_override and '*' in pattern:
                # 这里可以根据实际的Schema键来扩展
                pass
        
        return allowed
    
    def get_security_summary(self) -> Dict[str, Any]:
        """获取安全配置摘要"""
        return {
            'total_rules': len(self.whitelist_rules),
            'allowed_keys': len(self.allowed_keys),
            'blocked_keys': len(self.blocked_keys),
            'sensitive_patterns': len(self.detector.sensitive_patterns),
            'rules_by_sensitivity': {
                level.value: len([r for r in self.whitelist_rules.values() 
                                if r.sensitivity_level == level])
                for level in SensitivityLevel
            }
        }


def create_whitelist_manager(config_path: Optional[str] = None) -> ConfigWhitelistManager:
    """创建白名单管理器的工厂函数"""
    return ConfigWhitelistManager(config_path)


# 示例用法
if __name__ == "__main__":
    # 创建白名单管理器
    manager = create_whitelist_manager()
    
    # 测试一些配置键
    test_cases = [
        ("app.debug", "true"),
        ("llm.openai.api_key", "sk-1234567890abcdef1234567890abcdef"),
        ("database.password", "secret_password"),
        ("unknown.key", "some_value"),
        ("llm.temperature", "0.7"),
    ]
    
    print("环境变量覆盖验证测试:")
    print("=" * 50)
    
    for key, value in test_cases:
        is_valid, message, log_info = manager.validate_env_override(key, value)
        status = "✅ 允许" if is_valid else "❌ 拒绝"
        print(f"{status} {key} = {log_info['value']}")
        print(f"   原因: {message}")
        print(f"   敏感度: {log_info['sensitivity_level']}")
        print()
    
    print("安全配置摘要:")
    print("=" * 50)
    summary = manager.get_security_summary()
    for key, value in summary.items():
        print(f"{key}: {value}")
