# ============================================================================
# LLM场景参数配置
# ============================================================================
# 文件说明：不同业务场景下的LLM参数配置，针对特定任务优化模型表现
# 维护责任：AI算法团队 + 业务开发团队
# 更新频率：中频（场景优化和新场景添加时）
# 依赖关系：被各种Agent和处理器依赖
# ============================================================================

# 场景参数配置
scenario_params:
  # 默认场景
  default:
    temperature: 0.7          # 0.1-1.0；默认场景的通用配置
    max_tokens: 4000          # 结合UI/接口限制设置
    timeout: 30               # 秒；超时策略与重试配合
    top_p: 0.9                # 与temperature联动
    frequency_penalty: 0.0    # 0-2；抑制重复
    presence_penalty: 0.0     # 0-2；鼓励新主题
    
  # 意图识别场景
  intent_recognition:
    temperature: 0.3  # 低温度，提高准确性（成本更低、稳定性更好）
    max_tokens: 500   # 任务型短输出
    timeout: 20       # 响应要求快
    top_p: 0.8
    frequency_penalty: 0.0
    presence_penalty: 0.0
    description: "用于识别用户意图，需要高准确性"
    
  # 领域分类场景
  domain_classifier:
    temperature: 0.2  # 极低温度，确保分类准确
    max_tokens: 200
    timeout: 15
    top_p: 0.7
    frequency_penalty: 0.0
    presence_penalty: 0.0
    description: "用于领域分类，需要极高准确性"
    
  # 需求澄清场景
  clarification_generator:
    temperature: 0.7  # 中等温度，平衡准确性和创造性
    max_tokens: 1000
    timeout: 25
    top_p: 0.9
    frequency_penalty: 0.1
    presence_penalty: 0.1
    description: "生成澄清问题，需要一定创造性"
    
  # 文档生成场景
  document_generator:
    temperature: 0.5  # 中低温度，确保文档结构化（更稳）
    max_tokens: 6000  # 长输出；注意模型上下文上限
    timeout: 60       # 允许更长生成时间
    top_p: 0.9
    frequency_penalty: 0.0
    presence_penalty: 0.0
    description: "生成需求文档，需要结构化和完整性"
    
  # 问候生成场景
  greeting_generator:
    temperature: 0.7  # 中等温度，保持友好自然
    max_tokens: 150
    timeout: 15
    top_p: 0.9
    frequency_penalty: 0.2
    presence_penalty: 0.1
    description: "生成问候语，需要自然友好"
    
  # 错误处理场景
  error_handler:
    temperature: 0.6  # 中低温度，确保错误处理准确
    max_tokens: 300
    timeout: 20
    top_p: 0.8
    frequency_penalty: 0.0
    presence_penalty: 0.0
    description: "处理错误情况，需要准确和有帮助"
    
  # 对话流程场景
  conversation_flow:
    temperature: 0.7  # 中等温度，保持对话自然
    max_tokens: 2000
    timeout: 30
    top_p: 0.9
    frequency_penalty: 0.1
    presence_penalty: 0.1
    description: "管理对话流程，需要上下文理解"
    
  # 信息提取场景
  information_extractor:
    temperature: 0.3  # 低温度，确保提取准确
    max_tokens: 1500
    timeout: 25
    top_p: 0.8
    frequency_penalty: 0.0
    presence_penalty: 0.0
    description: "提取关键信息，需要高准确性"
    
  # 质量评估场景
  quality_assessor:
    temperature: 0.4  # 中低温度，确保评估客观
    max_tokens: 800
    timeout: 20
    top_p: 0.8
    frequency_penalty: 0.0
    presence_penalty: 0.0
    description: "评估内容质量，需要客观准确"
    
  # 建议生成场景
  suggestion_generator:
    temperature: 0.8  # 较高温度，鼓励创造性建议
    max_tokens: 1200
    timeout: 30
    top_p: 0.9
    frequency_penalty: 0.1
    presence_penalty: 0.2
    description: "生成改进建议，需要创造性"

# 场景模型映射
scenario_models:
  # 高精度场景使用最佳模型
  high_precision:
    scenarios:
      - "intent_recognition"
      - "domain_classifier"
      - "information_extractor"
    preferred_models: ["deepseek-chat", "gpt-3.5-turbo"]

  # 创造性场景使用平衡模型
  creative:
    scenarios:
      - "suggestion_generator"
      - "clarification_generator"
      - "greeting_generator"
    preferred_models: ["deepseek-chat", "doubao-1.5-Lite"]

  # 长文本场景使用大上下文模型
  long_context:
    scenarios:
      - "document_generator"
      - "conversation_flow"
    preferred_models: ["deepseek-chat", "gpt-3.5-turbo"]

  # 快速响应场景使用轻量模型
  fast_response:
    scenarios:
      - "greeting_generator"
      - "error_handler"
    preferred_models: ["doubao-1.5-Lite", "qwen-turbo"]

# 场景优化配置
scenario_optimization:
  # 自动参数调优
  auto_tuning: false
  tuning_metrics: ["accuracy", "response_time", "user_satisfaction"]
  
  # 场景切换策略
  switching_strategy: "confidence_based"
  confidence_threshold: 0.8
  
  # 场景组合策略
  combination_enabled: false
  combination_scenarios: []

# 场景监控配置
scenario_monitoring:
  # 性能监控
  track_performance: true
  track_accuracy: true
  track_user_feedback: true
  
  # 使用统计
  track_usage: true
  track_token_consumption: true
  
  # 质量监控
  quality_checks: true
  output_validation: true

# 实验性场景配置
experimental_scenarios:
  # 多轮对话优化
  multi_turn_optimization:
    enabled: false
    temperature: 0.6
    max_tokens: 2000
    context_window: 5
    
  # 个性化响应
  personalized_response:
    enabled: false
    temperature: 0.7
    max_tokens: 1500
    personalization_weight: 0.3
    
  # 情感分析增强
  emotion_aware:
    enabled: false
    temperature: 0.6
    max_tokens: 1000
    emotion_weight: 0.2

# ============================================================================
# 场景使用指南
# ============================================================================
# 1. 选择合适的场景：
#    - 根据任务类型选择对应场景
#    - 考虑准确性vs创造性的平衡
#    - 注意响应时间要求
#
# 2. 参数调优建议：
#    - temperature: 准确性要求高时使用低值(0.1-0.4)
#    - max_tokens: 根据预期输出长度设置
#    - timeout: 考虑用户体验和系统负载
#
# 3. 模型选择：
#    - 高精度任务优先使用最佳模型
#    - 快速响应任务使用轻量模型
#    - 长文本任务使用大上下文模型
# ============================================================================

# ============================================================================
# 配置元数据
# ============================================================================
_metadata:
  file_version: "1.0"
  created_date: "2025-08-18"
  last_modified: "2025-08-18"
  schema_version: "1.0"
  config_type: "llm_scenarios"
  priority: 5  # 在优先级配置中的位置
