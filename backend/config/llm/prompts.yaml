# ============================================================================
# LLM提示词配置
# ============================================================================
# 文件说明：各种场景下的LLM提示词模板，用于指导模型生成期望的输出
# 维护责任：AI算法团队 + 产品团队
# 更新频率：高频（提示词优化和新场景添加时）
# 依赖关系：被各种Agent和LLM客户端依赖
# ============================================================================

# 系统提示词
system_prompts:
  # 默认系统提示词（通用型；如无特定场景覆盖将使用该模板。修改后建议回归关键流程）
  default: |
    你是一个专业的需求采集助手，擅长帮助用户整理和记录各种业务需求。
    你的任务是：
    1. 理解用户的需求描述
    2. 提出合适的澄清问题
    3. 整理和结构化需求信息
    4. 生成清晰的需求文档
    
    请始终保持专业、友好、耐心的态度。
    
  # 意图识别系统提示词
  intent_recognition: |  # 输出必须仅为意图标签；避免多余文本影响解析
    你是一个意图识别专家，需要准确识别用户输入的意图类型。
    
    可能的意图类型包括：
    - greeting: 问候
    - requirement_collection: 需求收集
    - clarification: 澄清问题
    - confirmation: 确认信息
    - modification: 修改需求
    - system_query: 系统功能查询
    - general: 一般对话
    
    请只返回最匹配的意图类型，不要添加额外解释。
    
  # 领域分类系统提示词
  domain_classification: |  # 仅返回领域名称；请确保映射到系统预期的枚举
    你是一个业务领域分类专家，需要将用户需求分类到合适的业务领域。
    
    主要业务领域包括：
    - 产品设计: 产品功能、用户体验、界面设计等
    - 技术开发: 系统架构、技术实现、性能优化等
    - 业务流程: 工作流程、业务规则、操作规范等
    - 数据分析: 数据收集、分析报告、指标监控等
    - 市场营销: 推广策略、用户获取、品牌建设等
    - 其他: 不属于以上类别的需求
    
    请只返回最匹配的领域名称。

# 任务提示词模板
task_prompts:
  # 需求澄清提示词
  clarification: |
    基于用户提供的需求信息：
    {user_input}
    
    当前已收集的信息：
    {collected_info}
    
    请生成1-2个具体的澄清问题，帮助更好地理解用户需求。
    问题应该：
    1. 针对缺失或不清楚的关键信息
    2. 使用简洁明了的语言
    3. 避免过于技术性的术语
    
    请直接返回问题，不要添加额外的解释。
    
  # 信息提取提示词
  information_extraction: |
    从以下用户输入中提取关键信息：
    {user_input}
    
    需要提取的信息类型：
    - 需求目标: 用户希望达到什么目标
    - 功能描述: 具体需要什么功能
    - 约束条件: 有什么限制或要求
    - 优先级: 重要程度如何
    - 时间要求: 是否有时间限制
    - 相关人员: 涉及哪些角色或部门
    
    请以JSON格式返回提取的信息，如果某项信息不存在则标记为null。
    
  # 文档生成提示词
  document_generation: |
    基于收集的需求信息生成结构化的需求文档：
    
    需求信息：
    {requirement_data}
    
    请生成包含以下部分的需求文档：
    1. 需求概述
    2. 详细描述
    3. 功能要求
    4. 非功能要求
    5. 约束条件
    6. 验收标准
    
    文档应该：
    - 结构清晰，层次分明
    - 语言准确，表达清楚
    - 内容完整，覆盖所有要点
    - 格式规范，便于阅读
    
  # 质量评估提示词
  quality_assessment: |
    请评估以下需求信息的质量：
    {requirement_info}
    
    评估维度：
    1. 完整性 (0-10分): 信息是否完整
    2. 清晰性 (0-10分): 描述是否清楚
    3. 可行性 (0-10分): 需求是否可实现
    4. 一致性 (0-10分): 内容是否一致
    5. 可测试性 (0-10分): 是否可验证
    
    请为每个维度打分并给出简要说明，最后给出总体评分和改进建议。
    
  # 建议生成提示词
  suggestion_generation: |
    基于当前的需求信息：
    {current_requirements}
    
    请提供3-5个改进建议，帮助完善需求。建议应该：
    1. 针对具体的改进点
    2. 提供可操作的建议
    3. 考虑实际可行性
    4. 有助于提高需求质量
    
    请按重要性排序，并简要说明每个建议的理由。

# 对话提示词模板
conversation_prompts:
  # 问候回复
  greeting_response: |
    用户说：{user_input}
    
    请生成一个友好、专业的问候回复，并引导用户开始需求收集。
    回复应该：
    - 表达欢迎
    - 简要介绍你的能力
    - 询问用户的需求
    
  # 确认回复
  confirmation_response: |
    用户确认了以下信息：
    {confirmed_info}
    
    请生成一个确认回复，表示已收到确认，并说明下一步行动。
    
  # 错误处理回复
  error_handling: |
    发生了以下错误：{error_type}
    用户输入：{user_input}
    
    请生成一个友好的错误处理回复，包括：
    1. 表达歉意
    2. 简要说明问题
    3. 提供解决建议
    4. 引导继续对话

# 特殊场景提示词
special_prompts:
  # 多轮对话上下文
  multi_turn_context: |
    对话历史：
    {conversation_history}
    
    当前用户输入：{current_input}
    
    请基于对话上下文生成合适的回复，保持对话的连贯性和一致性。
    
  # 个性化回复
  personalized_response: |
    用户画像：{user_profile}
    用户偏好：{user_preferences}
    当前输入：{user_input}
    
    请生成个性化的回复，考虑用户的特点和偏好。
    
  # 情感感知回复
  emotion_aware_response: |
    用户情感状态：{emotion_state}
    用户输入：{user_input}
    
    请生成情感感知的回复，适当调整语气和内容以匹配用户的情感状态。

# 提示词优化配置
prompt_optimization:
  # 版本控制
  version_control: true
  current_version: "1.0"
  
  # A/B测试
  ab_testing: false
  test_variants: []
  
  # 动态调整
  dynamic_adjustment: false
  adjustment_rules: []

# 提示词验证规则
validation_rules:
  # 长度限制
  max_prompt_length: 4000
  min_prompt_length: 50
  
  # 内容检查
  required_elements: ["任务描述", "输出格式"]
  forbidden_elements: ["敏感信息", "不当内容"]
  
  # 格式检查
  format_validation: true
  variable_validation: true

# ============================================================================
# 提示词设计原则
# ============================================================================
# 1. 清晰性：提示词应该清楚地描述任务和期望输出
# 2. 具体性：提供具体的指导和示例
# 3. 一致性：保持风格和格式的一致性
# 4. 可测试性：提示词效果应该可以测量和验证
# 5. 可维护性：便于更新和优化
# ============================================================================

# ============================================================================
# 配置元数据
# ============================================================================
_metadata:
  file_version: "1.0"
  created_date: "2025-08-18"
  last_modified: "2025-08-18"
  schema_version: "1.0"
  config_type: "llm_prompts"
  priority: 6  # 在优先级配置中的位置
