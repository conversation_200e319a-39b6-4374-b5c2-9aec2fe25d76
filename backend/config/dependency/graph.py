#!/usr/bin/env python3
"""
系统依赖图

管理组件与配置键之间的依赖关系，支持影响分析和变更评估
"""

import logging
from typing import Dict, List, Set, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json
import threading
from pathlib import Path


logger = logging.getLogger(__name__)


class ComponentType(Enum):
    """组件类型"""
    AGENT = "agent"
    HANDLER = "handler"
    SERVICE = "service"
    UTILITY = "utility"
    CONFIG = "config"
    DATABASE = "database"
    API = "api"
    STRATEGY = "strategy"


class DependencyType(Enum):
    """依赖类型"""
    REQUIRED = "required"      # 必需依赖，缺失会导致组件无法工作
    OPTIONAL = "optional"      # 可选依赖，缺失会降级功能
    FALLBACK = "fallback"      # 回退依赖，用于降级场景
    PERFORMANCE = "performance" # 性能依赖，影响性能但不影响功能


class ImpactLevel(Enum):
    """影响级别"""
    CRITICAL = "critical"      # 关键影响，可能导致系统无法启动
    HIGH = "high"             # 高影响，影响核心功能
    MEDIUM = "medium"         # 中等影响，影响部分功能
    LOW = "low"               # 低影响，轻微功能影响


@dataclass
class ComponentInfo:
    """组件信息"""
    name: str
    component_type: ComponentType
    description: str
    file_path: str
    maintainer: Optional[str] = None
    test_files: List[str] = field(default_factory=list)
    documentation: Optional[str] = None


@dataclass
class ConfigDependency:
    """配置依赖"""
    config_key: str
    dependency_type: DependencyType
    impact_level: ImpactLevel
    description: str
    default_value: Any = None
    validation_rules: Optional[str] = None
    fallback_strategy: Optional[str] = None


@dataclass
class ImpactAnalysis:
    """影响分析结果"""
    config_key: str
    affected_components: List[str]
    impact_level: ImpactLevel
    risk_assessment: str
    test_requirements: List[str]
    rollback_strategy: str
    deployment_notes: List[str]


class SystemDependencyGraph:
    """系统依赖图"""
    
    def __init__(self):
        self.components: Dict[str, ComponentInfo] = {}
        self.dependencies: Dict[str, List[ConfigDependency]] = {}  # component_name -> dependencies
        self.config_to_components: Dict[str, Set[str]] = {}  # config_key -> component_names
        self.lock = threading.Lock()
        
        logger.info("系统依赖图已初始化")
    
    def register_component(self, component_info: ComponentInfo):
        """注册组件"""
        with self.lock:
            self.components[component_info.name] = component_info
            if component_info.name not in self.dependencies:
                self.dependencies[component_info.name] = []
            
            logger.debug(f"注册组件: {component_info.name} ({component_info.component_type.value})")
    
    def add_config_dependency(self, component_name: str, dependency: ConfigDependency):
        """添加配置依赖"""
        with self.lock:
            if component_name not in self.components:
                raise ValueError(f"组件 '{component_name}' 未注册")
            
            # 添加依赖
            self.dependencies[component_name].append(dependency)
            
            # 更新反向索引
            config_key = dependency.config_key
            if config_key not in self.config_to_components:
                self.config_to_components[config_key] = set()
            self.config_to_components[config_key].add(component_name)
            
            logger.debug(f"添加配置依赖: {component_name} -> {config_key}")
    
    def remove_config_dependency(self, component_name: str, config_key: str):
        """移除配置依赖"""
        with self.lock:
            if component_name in self.dependencies:
                self.dependencies[component_name] = [
                    dep for dep in self.dependencies[component_name]
                    if dep.config_key != config_key
                ]
            
            # 更新反向索引
            if config_key in self.config_to_components:
                self.config_to_components[config_key].discard(component_name)
                if not self.config_to_components[config_key]:
                    del self.config_to_components[config_key]
            
            logger.debug(f"移除配置依赖: {component_name} -> {config_key}")
    
    def get_affected_components(self, config_key: str) -> List[str]:
        """获取受配置键影响的组件"""
        with self.lock:
            return list(self.config_to_components.get(config_key, set()))
    
    def get_component_dependencies(self, component_name: str) -> List[ConfigDependency]:
        """获取组件的配置依赖"""
        with self.lock:
            return self.dependencies.get(component_name, []).copy()
    
    def analyze_config_impact(self, config_key: str) -> ImpactAnalysis:
        """分析配置键变更的影响"""
        with self.lock:
            affected_components = self.get_affected_components(config_key)
            
            if not affected_components:
                return ImpactAnalysis(
                    config_key=config_key,
                    affected_components=[],
                    impact_level=ImpactLevel.LOW,
                    risk_assessment="无已知组件依赖此配置键",
                    test_requirements=[],
                    rollback_strategy="直接回滚配置值",
                    deployment_notes=["无特殊部署要求"]
                )
            
            # 分析影响级别
            max_impact = ImpactLevel.LOW
            critical_components = []
            test_files = set()
            deployment_notes = []
            
            for component_name in affected_components:
                component = self.components.get(component_name)
                if not component:
                    continue
                
                # 查找该组件对此配置键的依赖
                for dep in self.dependencies.get(component_name, []):
                    if dep.config_key == config_key:
                        # 更新最大影响级别
                        if self._compare_impact_level(dep.impact_level, max_impact) > 0:
                            max_impact = dep.impact_level
                        
                        # 收集关键组件
                        if dep.impact_level in [ImpactLevel.CRITICAL, ImpactLevel.HIGH]:
                            critical_components.append(component_name)
                        
                        # 收集测试文件
                        test_files.update(component.test_files)
                        
                        # 收集部署注意事项
                        if dep.fallback_strategy:
                            deployment_notes.append(f"{component_name}: {dep.fallback_strategy}")
            
            # 生成风险评估
            risk_assessment = self._generate_risk_assessment(
                config_key, affected_components, max_impact, critical_components
            )
            
            # 生成回滚策略
            rollback_strategy = self._generate_rollback_strategy(
                config_key, affected_components, max_impact
            )
            
            return ImpactAnalysis(
                config_key=config_key,
                affected_components=affected_components,
                impact_level=max_impact,
                risk_assessment=risk_assessment,
                test_requirements=list(test_files),
                rollback_strategy=rollback_strategy,
                deployment_notes=deployment_notes or ["无特殊部署要求"]
            )
    
    def _compare_impact_level(self, level1: ImpactLevel, level2: ImpactLevel) -> int:
        """比较影响级别"""
        level_order = {
            ImpactLevel.LOW: 0,
            ImpactLevel.MEDIUM: 1,
            ImpactLevel.HIGH: 2,
            ImpactLevel.CRITICAL: 3
        }
        return level_order[level1] - level_order[level2]
    
    def _generate_risk_assessment(self, config_key: str, affected_components: List[str],
                                 max_impact: ImpactLevel, critical_components: List[str]) -> str:
        """生成风险评估"""
        if max_impact == ImpactLevel.CRITICAL:
            return f"高风险：配置键 '{config_key}' 影响 {len(critical_components)} 个关键组件，" \
                   f"变更可能导致系统无法启动或核心功能失效"
        elif max_impact == ImpactLevel.HIGH:
            return f"中高风险：配置键 '{config_key}' 影响 {len(affected_components)} 个组件，" \
                   f"其中 {len(critical_components)} 个为关键组件"
        elif max_impact == ImpactLevel.MEDIUM:
            return f"中等风险：配置键 '{config_key}' 影响 {len(affected_components)} 个组件，" \
                   f"可能导致部分功能异常"
        else:
            return f"低风险：配置键 '{config_key}' 影响 {len(affected_components)} 个组件，" \
                   f"影响较小"
    
    def _generate_rollback_strategy(self, config_key: str, affected_components: List[str],
                                   max_impact: ImpactLevel) -> str:
        """生成回滚策略"""
        if max_impact == ImpactLevel.CRITICAL:
            return f"立即回滚：监控关键指标，发现异常立即回滚配置值并重启相关服务"
        elif max_impact == ImpactLevel.HIGH:
            return f"快速回滚：准备回滚脚本，监控业务指标，异常时5分钟内完成回滚"
        elif max_impact == ImpactLevel.MEDIUM:
            return f"计划回滚：监控相关功能，如有异常可在下个发布窗口回滚"
        else:
            return f"按需回滚：直接修改配置值即可，无需特殊回滚流程"
    
    def get_component_graph(self) -> Dict[str, Any]:
        """获取组件依赖图"""
        with self.lock:
            graph = {
                "components": {},
                "dependencies": {},
                "config_usage": {}
            }
            
            # 组件信息
            for name, component in self.components.items():
                graph["components"][name] = {
                    "type": component.component_type.value,
                    "description": component.description,
                    "file_path": component.file_path,
                    "maintainer": component.maintainer,
                    "test_files": component.test_files
                }
            
            # 依赖关系
            for component_name, deps in self.dependencies.items():
                graph["dependencies"][component_name] = [
                    {
                        "config_key": dep.config_key,
                        "type": dep.dependency_type.value,
                        "impact": dep.impact_level.value,
                        "description": dep.description
                    }
                    for dep in deps
                ]
            
            # 配置使用情况
            for config_key, components in self.config_to_components.items():
                graph["config_usage"][config_key] = list(components)
            
            return graph
    
    def export_to_file(self, file_path: str, format: str = "json"):
        """导出依赖图到文件"""
        graph = self.get_component_graph()
        
        if format == "json":
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(graph, f, indent=2, ensure_ascii=False)
        elif format == "yaml":
            import yaml
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(graph, f, default_flow_style=False, allow_unicode=True)
        else:
            raise ValueError(f"不支持的格式: {format}")
        
        logger.info(f"依赖图已导出到: {file_path}")
    
    def load_from_file(self, file_path: str, format: str = "json"):
        """从文件加载依赖图"""
        if format == "json":
            with open(file_path, 'r', encoding='utf-8') as f:
                graph = json.load(f)
        elif format == "yaml":
            import yaml
            with open(file_path, 'r', encoding='utf-8') as f:
                graph = yaml.safe_load(f)
        else:
            raise ValueError(f"不支持的格式: {format}")
        
        with self.lock:
            # 清空现有数据
            self.components.clear()
            self.dependencies.clear()
            self.config_to_components.clear()
            
            # 加载组件
            for name, component_data in graph.get("components", {}).items():
                component = ComponentInfo(
                    name=name,
                    component_type=ComponentType(component_data["type"]),
                    description=component_data["description"],
                    file_path=component_data["file_path"],
                    maintainer=component_data.get("maintainer"),
                    test_files=component_data.get("test_files", []),
                    documentation=component_data.get("documentation")
                )
                self.components[name] = component
            
            # 加载依赖
            for component_name, deps_data in graph.get("dependencies", {}).items():
                self.dependencies[component_name] = []
                for dep_data in deps_data:
                    dependency = ConfigDependency(
                        config_key=dep_data["config_key"],
                        dependency_type=DependencyType(dep_data["type"]),
                        impact_level=ImpactLevel(dep_data["impact"]),
                        description=dep_data["description"]
                    )
                    self.dependencies[component_name].append(dependency)
                    
                    # 更新反向索引
                    config_key = dependency.config_key
                    if config_key not in self.config_to_components:
                        self.config_to_components[config_key] = set()
                    self.config_to_components[config_key].add(component_name)
        
        logger.info(f"依赖图已从 {file_path} 加载")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.lock:
            component_types = {}
            dependency_types = {}
            impact_levels = {}
            
            for component in self.components.values():
                component_type = component.component_type.value
                component_types[component_type] = component_types.get(component_type, 0) + 1
            
            for deps in self.dependencies.values():
                for dep in deps:
                    dep_type = dep.dependency_type.value
                    dependency_types[dep_type] = dependency_types.get(dep_type, 0) + 1
                    
                    impact_level = dep.impact_level.value
                    impact_levels[impact_level] = impact_levels.get(impact_level, 0) + 1
            
            return {
                "total_components": len(self.components),
                "total_config_keys": len(self.config_to_components),
                "total_dependencies": sum(len(deps) for deps in self.dependencies.values()),
                "component_types": component_types,
                "dependency_types": dependency_types,
                "impact_levels": impact_levels
            }


# 全局依赖图实例
_global_dependency_graph: Optional[SystemDependencyGraph] = None


def get_dependency_graph() -> SystemDependencyGraph:
    """获取全局依赖图实例"""
    global _global_dependency_graph
    if _global_dependency_graph is None:
        _global_dependency_graph = SystemDependencyGraph()
    return _global_dependency_graph


def create_dependency_graph() -> SystemDependencyGraph:
    """创建新的依赖图实例"""
    return SystemDependencyGraph()


# 便捷函数
def register_component(name: str, component_type: ComponentType, description: str,
                      file_path: str, **kwargs) -> ComponentInfo:
    """注册组件的便捷函数"""
    component = ComponentInfo(
        name=name,
        component_type=component_type,
        description=description,
        file_path=file_path,
        **kwargs
    )
    get_dependency_graph().register_component(component)
    return component


def add_config_dependency(component_name: str, config_key: str,
                         dependency_type: DependencyType, impact_level: ImpactLevel,
                         description: str, **kwargs):
    """添加配置依赖的便捷函数"""
    dependency = ConfigDependency(
        config_key=config_key,
        dependency_type=dependency_type,
        impact_level=impact_level,
        description=description,
        **kwargs
    )
    get_dependency_graph().add_config_dependency(component_name, dependency)


def analyze_impact(config_key: str) -> ImpactAnalysis:
    """分析配置影响的便捷函数"""
    return get_dependency_graph().analyze_config_impact(config_key)


# 示例用法
if __name__ == "__main__":
    # 创建依赖图
    graph = create_dependency_graph()
    
    # 注册组件
    agent_component = ComponentInfo(
        name="conversation_flow_agent",
        component_type=ComponentType.AGENT,
        description="对话流程代理",
        file_path="backend/agents/conversation_flow/core_refactored.py",
        maintainer="AI Team",
        test_files=["tests/agents/test_conversation_flow.py"]
    )
    graph.register_component(agent_component)
    
    # 添加配置依赖
    llm_dependency = ConfigDependency(
        config_key="llm.default_model",
        dependency_type=DependencyType.REQUIRED,
        impact_level=ImpactLevel.HIGH,
        description="对话流程需要LLM模型进行推理",
        fallback_strategy="使用硬编码的默认模型 gpt-3.5-turbo"
    )
    graph.add_config_dependency("conversation_flow_agent", llm_dependency)
    
    # 分析影响
    impact = graph.analyze_config_impact("llm.default_model")
    print(f"配置键 'llm.default_model' 的影响分析:")
    print(f"  影响组件: {impact.affected_components}")
    print(f"  影响级别: {impact.impact_level.value}")
    print(f"  风险评估: {impact.risk_assessment}")
    print(f"  回滚策略: {impact.rollback_strategy}")
    
    # 获取统计信息
    stats = graph.get_statistics()
    print(f"\n依赖图统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
