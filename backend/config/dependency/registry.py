#!/usr/bin/env python3
"""
组件依赖注册器

登记系统中关键组件的配置依赖关系
"""

import logging
from pathlib import Path
from .graph import (
    SystemDependencyGraph, ComponentInfo, ConfigDependency,
    ComponentType, DependencyType, ImpactLevel,
    get_dependency_graph
)


logger = logging.getLogger(__name__)


class ComponentDependencyRegistry:
    """组件依赖注册器"""
    
    def __init__(self, dependency_graph: SystemDependencyGraph = None):
        self.graph = dependency_graph or get_dependency_graph()
        self.registered_components = set()
    
    def register_all_components(self):
        """注册所有关键组件"""
        logger.info("开始注册关键组件依赖...")
        
        # 注册各类组件
        self._register_agent_components()
        self._register_handler_components()
        self._register_service_components()
        self._register_config_components()
        self._register_utility_components()
        self._register_strategy_components()
        
        logger.info(f"完成注册 {len(self.registered_components)} 个组件")
    
    def _register_agent_components(self):
        """注册Agent组件"""
        logger.debug("注册Agent组件...")
        
        # 对话流程核心Agent
        self._register_component_with_dependencies(
            name="conversation_flow_core",
            component_type=ComponentType.AGENT,
            description="对话流程核心处理器",
            file_path="backend/agents/conversation_flow/core_refactored.py",
            maintainer="AI Team",
            test_files=["tests/agents/test_conversation_flow.py"],
            dependencies=[
                ("llm.default_model", DependencyType.REQUIRED, ImpactLevel.CRITICAL, 
                 "对话流程必需的LLM模型", "使用硬编码默认模型 gpt-3.5-turbo"),
                ("llm.temperature", DependencyType.OPTIONAL, ImpactLevel.MEDIUM,
                 "LLM推理温度参数", "使用默认值 0.7"),
                ("llm.max_tokens", DependencyType.OPTIONAL, ImpactLevel.MEDIUM,
                 "LLM最大令牌数", "使用默认值 2000"),
                ("business.retry.max_attempts", DependencyType.OPTIONAL, ImpactLevel.LOW,
                 "重试最大次数", "使用默认值 3"),
                ("performance.timeout.llm", DependencyType.OPTIONAL, ImpactLevel.MEDIUM,
                 "LLM请求超时时间", "使用默认值 30秒")
            ]
        )
        
        # 动态回复生成器
        self._register_component_with_dependencies(
            name="dynamic_reply_generator",
            component_type=ComponentType.AGENT,
            description="动态回复生成器",
            file_path="backend/agents/dynamic_reply_generator.py",
            maintainer="AI Team",
            test_files=["tests/agents/test_dynamic_reply.py"],
            dependencies=[
                ("llm.default_model", DependencyType.REQUIRED, ImpactLevel.HIGH,
                 "回复生成需要LLM模型", "使用备用模型"),
                ("business.templates.system.greeting", DependencyType.OPTIONAL, ImpactLevel.LOW,
                 "系统问候模板", "使用硬编码模板"),
                ("business.quality_control.enabled", DependencyType.OPTIONAL, ImpactLevel.MEDIUM,
                 "质量控制开关", "默认启用质量控制")
            ]
        )
        
        # Agent实例池
        self._register_component_with_dependencies(
            name="agent_instance_pool",
            component_type=ComponentType.AGENT,
            description="Agent实例池管理器",
            file_path="backend/agents/agent_instance_pool.py",
            maintainer="AI Team",
            test_files=["tests/agents/test_instance_pool.py"],
            dependencies=[
                ("performance.concurrency.max_agents", DependencyType.REQUIRED, ImpactLevel.HIGH,
                 "最大Agent实例数", "使用默认值 10"),
                ("performance.cache.enabled", DependencyType.OPTIONAL, ImpactLevel.MEDIUM,
                 "实例缓存开关", "禁用缓存"),
                ("system.security.enabled", DependencyType.OPTIONAL, ImpactLevel.MEDIUM,
                 "安全功能开关", "禁用安全检查")
            ]
        )
        
        # 消息回复管理器
        self._register_component_with_dependencies(
            name="message_reply_manager",
            component_type=ComponentType.AGENT,
            description="消息回复管理器",
            file_path="backend/agents/message_reply_manager.py",
            maintainer="AI Team",
            dependencies=[
                ("business.retry.delay", DependencyType.OPTIONAL, ImpactLevel.LOW,
                 "重试延迟时间", "使用默认值 1秒"),
                ("logging.level", DependencyType.OPTIONAL, ImpactLevel.LOW,
                 "日志级别", "使用INFO级别")
            ]
        )
    
    def _register_handler_components(self):
        """注册Handler组件"""
        logger.debug("注册Handler组件...")
        
        # 文档处理器
        self._register_component_with_dependencies(
            name="document_handler",
            component_type=ComponentType.HANDLER,
            description="文档处理器",
            file_path="backend/handlers/document_handler.py",
            maintainer="Document Team",
            test_files=["tests/handlers/test_document.py"],
            dependencies=[
                ("knowledge_base.enabled", DependencyType.REQUIRED, ImpactLevel.HIGH,
                 "知识库功能开关", "禁用文档处理功能"),
                ("knowledge_base.storage.path", DependencyType.REQUIRED, ImpactLevel.CRITICAL,
                 "知识库存储路径", "使用临时目录"),
                ("performance.limits.max_file_size", DependencyType.OPTIONAL, ImpactLevel.MEDIUM,
                 "最大文件大小限制", "使用默认值 10MB")
            ]
        )
        
        # 对话处理器
        self._register_component_with_dependencies(
            name="conversation_handler",
            component_type=ComponentType.HANDLER,
            description="对话处理器",
            file_path="backend/handlers/conversation_handler.py",
            maintainer="AI Team",
            dependencies=[
                ("database.connection.path", DependencyType.REQUIRED, ImpactLevel.CRITICAL,
                 "数据库连接路径", "使用内存数据库"),
                ("database.connection.timeout", DependencyType.OPTIONAL, ImpactLevel.MEDIUM,
                 "数据库连接超时", "使用默认值 30秒"),
                ("business.thresholds.completion", DependencyType.OPTIONAL, ImpactLevel.MEDIUM,
                 "完成度阈值", "使用默认值 0.8")
            ]
        )
        
        # 复合处理器
        self._register_component_with_dependencies(
            name="composite_handler",
            component_type=ComponentType.HANDLER,
            description="复合处理器",
            file_path="backend/handlers/composite_handler.py",
            maintainer="AI Team",
            dependencies=[
                ("system.performance.enabled", DependencyType.OPTIONAL, ImpactLevel.MEDIUM,
                 "性能优化开关", "禁用性能优化"),
                ("monitoring.metrics.enabled", DependencyType.OPTIONAL, ImpactLevel.LOW,
                 "指标监控开关", "禁用指标收集")
            ]
        )
    
    def _register_service_components(self):
        """注册Service组件"""
        logger.debug("注册Service组件...")
        
        # 资源管理器
        self._register_component_with_dependencies(
            name="resource_manager",
            component_type=ComponentType.SERVICE,
            description="资源管理服务",
            file_path="backend/services/resource_manager.py",
            maintainer="Infrastructure Team",
            dependencies=[
                ("performance.limits.max_memory", DependencyType.REQUIRED, ImpactLevel.HIGH,
                 "最大内存限制", "使用系统默认值"),
                ("performance.limits.max_cpu", DependencyType.OPTIONAL, ImpactLevel.MEDIUM,
                 "最大CPU使用率", "不限制CPU使用"),
                ("monitoring.alerts.enabled", DependencyType.OPTIONAL, ImpactLevel.LOW,
                 "告警功能开关", "禁用告警")
            ]
        )
    
    def _register_config_components(self):
        """注册Config组件"""
        logger.debug("注册Config组件...")
        
        # 配置管理器
        self._register_component_with_dependencies(
            name="config_manager",
            component_type=ComponentType.CONFIG,
            description="配置管理器",
            file_path="backend/config/manager.py",
            maintainer="Config Team",
            test_files=["tests/config/test_manager.py"],
            dependencies=[
                ("app.debug", DependencyType.OPTIONAL, ImpactLevel.LOW,
                 "调试模式开关", "默认关闭调试模式"),
                ("logging.level", DependencyType.OPTIONAL, ImpactLevel.LOW,
                 "日志级别", "使用INFO级别"),
                ("system.security.enabled", DependencyType.OPTIONAL, ImpactLevel.MEDIUM,
                 "安全功能开关", "启用基本安全检查")
            ]
        )
        
        # 模块化加载器
        self._register_component_with_dependencies(
            name="modular_config_loader",
            component_type=ComponentType.CONFIG,
            description="模块化配置加载器",
            file_path="backend/config/modular_loader.py",
            maintainer="Config Team",
            dependencies=[
                ("app.environment", DependencyType.OPTIONAL, ImpactLevel.MEDIUM,
                 "运行环境", "使用development环境"),
                ("performance.cache.enabled", DependencyType.OPTIONAL, ImpactLevel.LOW,
                 "配置缓存开关", "禁用缓存")
            ]
        )
    
    def _register_utility_components(self):
        """注册Utility组件"""
        logger.debug("注册Utility组件...")
        
        # 安全管理器
        self._register_component_with_dependencies(
            name="safety_manager",
            component_type=ComponentType.UTILITY,
            description="安全管理器",
            file_path="backend/utils/safety_manager.py",
            maintainer="Security Team",
            dependencies=[
                ("system.security.enabled", DependencyType.REQUIRED, ImpactLevel.CRITICAL,
                 "安全功能总开关", "禁用所有安全检查"),
                ("system.security.authentication.enabled", DependencyType.OPTIONAL, ImpactLevel.HIGH,
                 "认证功能开关", "跳过认证检查"),
                ("logging.security.enabled", DependencyType.OPTIONAL, ImpactLevel.MEDIUM,
                 "安全日志开关", "禁用安全日志")
            ]
        )
        
        # 性能监控器
        self._register_component_with_dependencies(
            name="performance_monitor",
            component_type=ComponentType.UTILITY,
            description="性能监控器",
            file_path="backend/utils/performance_monitor.py",
            maintainer="Infrastructure Team",
            dependencies=[
                ("monitoring.enabled", DependencyType.REQUIRED, ImpactLevel.MEDIUM,
                 "监控功能开关", "禁用性能监控"),
                ("monitoring.interval", DependencyType.OPTIONAL, ImpactLevel.LOW,
                 "监控间隔", "使用默认值 60秒"),
                ("performance.thresholds.cpu", DependencyType.OPTIONAL, ImpactLevel.MEDIUM,
                 "CPU阈值", "使用默认值 80%")
            ]
        )
        
        # 意图管理器
        self._register_component_with_dependencies(
            name="intent_manager",
            component_type=ComponentType.UTILITY,
            description="意图管理器",
            file_path="backend/utils/intent_manager.py",
            maintainer="AI Team",
            dependencies=[
                ("business.thresholds.confidence", DependencyType.REQUIRED, ImpactLevel.HIGH,
                 "置信度阈值", "使用默认值 0.7"),
                ("business.thresholds.similarity", DependencyType.OPTIONAL, ImpactLevel.MEDIUM,
                 "相似度阈值", "使用默认值 0.8")
            ]
        )
    
    def _register_strategy_components(self):
        """注册Strategy组件"""
        logger.debug("注册Strategy组件...")
        
        # 能力策略
        self._register_component_with_dependencies(
            name="capabilities_strategy",
            component_type=ComponentType.STRATEGY,
            description="能力策略",
            file_path="backend/agents/strategies/capabilities_strategy.py",
            maintainer="AI Team",
            dependencies=[
                ("business.capabilities.enabled", DependencyType.REQUIRED, ImpactLevel.HIGH,
                 "能力功能开关", "禁用能力展示"),
                ("business.templates.capabilities.intro", DependencyType.OPTIONAL, ImpactLevel.LOW,
                 "能力介绍模板", "使用默认模板")
            ]
        )
        
        # 需求策略
        self._register_component_with_dependencies(
            name="requirement_strategy",
            component_type=ComponentType.STRATEGY,
            description="需求策略",
            file_path="backend/agents/strategies/requirement_strategy.py",
            maintainer="AI Team",
            dependencies=[
                ("business.requirements.enabled", DependencyType.REQUIRED, ImpactLevel.HIGH,
                 "需求收集开关", "禁用需求收集"),
                ("business.thresholds.completion", DependencyType.REQUIRED, ImpactLevel.HIGH,
                 "需求完成度阈值", "使用默认值 0.8")
            ]
        )
        
        # 情感支持策略
        self._register_component_with_dependencies(
            name="emotional_support_strategy",
            component_type=ComponentType.STRATEGY,
            description="情感支持策略",
            file_path="backend/agents/strategies/emotional_support_strategy.py",
            maintainer="AI Team",
            dependencies=[
                ("business.emotional_support.enabled", DependencyType.OPTIONAL, ImpactLevel.MEDIUM,
                 "情感支持开关", "禁用情感支持"),
                ("business.templates.emotional.comfort", DependencyType.OPTIONAL, ImpactLevel.LOW,
                 "安慰模板", "使用默认安慰语")
            ]
        )
    
    def _register_component_with_dependencies(self, name: str, component_type: ComponentType,
                                           description: str, file_path: str,
                                           dependencies: list, maintainer: str = None,
                                           test_files: list = None):
        """注册组件及其依赖"""
        # 注册组件
        component = ComponentInfo(
            name=name,
            component_type=component_type,
            description=description,
            file_path=file_path,
            maintainer=maintainer,
            test_files=test_files or []
        )
        self.graph.register_component(component)
        
        # 添加依赖
        for config_key, dep_type, impact_level, desc, fallback in dependencies:
            dependency = ConfigDependency(
                config_key=config_key,
                dependency_type=dep_type,
                impact_level=impact_level,
                description=desc,
                fallback_strategy=fallback
            )
            self.graph.add_config_dependency(name, dependency)
        
        self.registered_components.add(name)
        logger.debug(f"注册组件: {name} (依赖数: {len(dependencies)})")
    
    def get_registration_summary(self) -> dict:
        """获取注册摘要"""
        stats = self.graph.get_statistics()
        return {
            "registered_components": len(self.registered_components),
            "component_list": list(self.registered_components),
            "dependency_graph_stats": stats
        }


def register_all_system_dependencies():
    """注册所有系统依赖的便捷函数"""
    registry = ComponentDependencyRegistry()
    registry.register_all_components()
    return registry


# 示例用法
if __name__ == "__main__":
    # 注册所有组件依赖
    registry = register_all_system_dependencies()
    
    # 获取注册摘要
    summary = registry.get_registration_summary()
    print("组件依赖注册摘要:")
    print(f"  注册组件数: {summary['registered_components']}")
    print(f"  总配置键数: {summary['dependency_graph_stats']['total_config_keys']}")
    print(f"  总依赖数: {summary['dependency_graph_stats']['total_dependencies']}")
    
    # 测试影响分析
    graph = get_dependency_graph()
    impact = graph.analyze_config_impact("llm.default_model")
    print(f"\n配置键 'llm.default_model' 影响分析:")
    print(f"  影响组件: {impact.affected_components}")
    print(f"  影响级别: {impact.impact_level.value}")
    print(f"  风险评估: {impact.risk_assessment}")
    
    # 导出依赖图
    graph.export_to_file("system_dependencies.json", "json")
    print(f"\n依赖图已导出到: system_dependencies.json")
