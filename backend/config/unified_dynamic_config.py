#!/usr/bin/env python3
"""
统一动态配置管理器
整合了通用动态配置管理和专用关键词配置管理功能

功能：
1. 运行时配置更新：支持不停机的配置热更新
2. 配置版本管理：配置变更历史和版本回滚
3. 配置验证：配置更新前的合法性验证
4. 配置监控：配置变更的监控和告警
5. 灰度发布：支持配置的灰度发布和A/B测试
6. 关键词专用管理：针对关键词配置的专用操作

设计原则：
- 线程安全：支持多线程环境下的配置更新
- 原子操作：配置更新的原子性保证
- 向后兼容：配置格式的向后兼容性
- 故障恢复：配置错误时的自动回滚机制
- 职责单一：通用框架+专用扩展的设计模式
"""

import json
import yaml
import logging
import threading
import time
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
from copy import deepcopy
import hashlib
from enum import Enum


class ConfigChangeType(Enum):
    """配置变更类型"""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    ROLLBACK = "rollback"


class ConfigValidationResult(Enum):
    """配置验证结果"""
    VALID = "valid"
    INVALID = "invalid"
    WARNING = "warning"


@dataclass
class ConfigVersion:
    """配置版本信息"""
    version: str
    timestamp: datetime
    config_data: Dict[str, Any]
    change_type: ConfigChangeType
    description: str
    author: str
    checksum: str


@dataclass
class ConfigValidationError:
    """配置验证错误"""
    field_path: str
    error_type: str
    message: str
    severity: str = "error"


@dataclass
class ConfigChangeEvent:
    """配置变更事件"""
    config_name: str
    change_type: ConfigChangeType
    old_version: Optional[str]
    new_version: str
    timestamp: datetime
    author: str
    description: str


class ConfigValidator:
    """配置验证器"""

    def __init__(self):
        self.validation_rules = {}
        self.logger = logging.getLogger(__name__)

    def register_validator(self, config_name: str, validator_func: Callable):
        """注册配置验证器"""
        self.validation_rules[config_name] = validator_func

    def validate_config(self, config_name: str, config_data: Dict[str, Any]) -> tuple:
        """
        验证配置数据

        Returns:
            (is_valid: bool, errors: List[ConfigValidationError])
        """
        if config_name not in self.validation_rules:
            return True, []

        try:
            validator = self.validation_rules[config_name]
            return validator(config_data)
        except Exception as e:
            self.logger.error(f"配置验证异常: {e}", exc_info=True)
            return False, [ConfigValidationError(
                field_path="root",
                error_type="validation_exception",
                message=f"验证器执行异常: {str(e)}"
            )]


class ConfigStorage:
    """配置存储管理器"""

    def __init__(self, storage_dir: str = "backend/data/config_storage"):
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger(__name__)

    def save_config_version(self, config_name: str, version: ConfigVersion):
        """保存配置版本"""
        version_file = self.storage_dir / f"{config_name}_v{version.version}.json"

        version_data = {
            "version": version.version,
            "timestamp": version.timestamp.isoformat(),
            "config_data": version.config_data,
            "change_type": version.change_type.value,
            "description": version.description,
            "author": version.author,
            "checksum": version.checksum
        }

        with open(version_file, 'w', encoding='utf-8') as f:
            json.dump(version_data, f, ensure_ascii=False, indent=2)

        # 更新最新版本链接
        latest_file = self.storage_dir / f"{config_name}_latest.json"
        with open(latest_file, 'w', encoding='utf-8') as f:
            json.dump(version_data, f, ensure_ascii=False, indent=2)

    def load_config_version(self, config_name: str, version: str = "latest") -> Optional[ConfigVersion]:
        """加载配置版本"""
        if version == "latest":
            version_file = self.storage_dir / f"{config_name}_latest.json"
        else:
            version_file = self.storage_dir / f"{config_name}_v{version}.json"

        if not version_file.exists():
            return None

        try:
            with open(version_file, 'r', encoding='utf-8') as f:
                version_data = json.load(f)

            return ConfigVersion(
                version=version_data["version"],
                timestamp=datetime.fromisoformat(version_data["timestamp"]),
                config_data=version_data["config_data"],
                change_type=ConfigChangeType(version_data["change_type"]),
                description=version_data["description"],
                author=version_data["author"],
                checksum=version_data["checksum"]
            )
        except Exception as e:
            self.logger.error(f"加载配置版本失败: {e}", exc_info=True)
            return None

    def list_config_versions(self, config_name: str) -> List[str]:
        """列出配置的所有版本"""
        pattern = f"{config_name}_v*.json"
        version_files = list(self.storage_dir.glob(pattern))

        versions = []
        for file in version_files:
            try:
                version = file.stem.split('_v')[1]
                versions.append(version)
            except IndexError:
                continue

        try:
            versions.sort(key=lambda x: float(x.split('.')[-1]) if '.' in x else 0)
        except (ValueError, IndexError):
            versions.sort()
        return versions

    def delete_old_versions(self, config_name: str, keep_count: int = 10):
        """删除旧版本，保留最近的几个版本"""
        versions = self.list_config_versions(config_name)

        if len(versions) <= keep_count:
            return

        versions_to_delete = versions[:-keep_count]
        for version in versions_to_delete:
            version_file = self.storage_dir / f"{config_name}_v{version}.json"
            try:
                version_file.unlink()
                self.logger.info(f"删除旧配置版本: {config_name} v{version}")
            except Exception as e:
                self.logger.error(f"删除配置版本失败: {e}")


class UnifiedDynamicConfigManager:
    """统一动态配置管理器"""

    def __init__(self, storage_dir: str = "backend/data/config_storage"):
        self.configs = {}
        self.config_versions = {}
        self.config_locks = {}
        self.change_listeners = {}
        self.validator = ConfigValidator()
        self.storage = ConfigStorage(storage_dir)
        self.logger = logging.getLogger(__name__)

        # 配置变更历史
        self.change_history = []
        self.max_history_size = 1000

        # 自动保存配置
        self.auto_save_enabled = True
        self.auto_cleanup_enabled = True

        self.logger.info("统一动态配置管理器初始化完成")

    def register_config(self, config_name: str, initial_config: Dict[str, Any],
                       validator_func: Optional[Callable] = None):
        """注册配置"""
        if validator_func:
            self.validator.register_validator(config_name, validator_func)

        self.config_locks[config_name] = threading.RLock()

        # 尝试从存储加载最新配置
        latest_version = self.storage.load_config_version(config_name)

        if latest_version:
            self.configs[config_name] = deepcopy(latest_version.config_data)
            self.config_versions[config_name] = latest_version.version
            self.logger.info(f"从存储加载配置: {config_name} v{latest_version.version}")
        else:
            self.configs[config_name] = deepcopy(initial_config)

            version = self._generate_version()
            config_version = ConfigVersion(
                version=version,
                timestamp=datetime.now(),
                config_data=deepcopy(initial_config),
                change_type=ConfigChangeType.CREATE,
                description="初始配置",
                author="system",
                checksum=self._calculate_checksum(initial_config)
            )

            self.config_versions[config_name] = version

            if self.auto_save_enabled:
                self.storage.save_config_version(config_name, config_version)

            self.logger.info(f"注册新配置: {config_name} v{version}")

        self.change_listeners[config_name] = []

    def get_config(self, config_name: str) -> Optional[Dict[str, Any]]:
        """获取配置"""
        if config_name not in self.configs:
            return None

        with self.config_locks[config_name]:
            return deepcopy(self.configs[config_name])

    def update_config(self, config_name: str, new_config: Dict[str, Any],
                     author: str = "system", description: str = "配置更新") -> bool:
        """更新配置"""
        if config_name not in self.configs:
            self.logger.error(f"配置不存在: {config_name}")
            return False

        # 验证新配置
        is_valid, errors = self.validator.validate_config(config_name, new_config)
        if not is_valid:
            self.logger.error(f"配置验证失败: {config_name}")
            for error in errors:
                self.logger.error(f"  {error.field_path}: {error.message}")
            return False

        with self.config_locks[config_name]:
            old_config = deepcopy(self.configs[config_name])
            old_version = self.config_versions[config_name]

            try:
                self.configs[config_name] = deepcopy(new_config)

                new_version = self._generate_version()
                config_version = ConfigVersion(
                    version=new_version,
                    timestamp=datetime.now(),
                    config_data=deepcopy(new_config),
                    change_type=ConfigChangeType.UPDATE,
                    description=description,
                    author=author,
                    checksum=self._calculate_checksum(new_config)
                )

                self.config_versions[config_name] = new_version

                if self.auto_save_enabled:
                    self.storage.save_config_version(config_name, config_version)

                change_event = ConfigChangeEvent(
                    config_name=config_name,
                    change_type=ConfigChangeType.UPDATE,
                    old_version=old_version,
                    new_version=new_version,
                    timestamp=datetime.now(),
                    author=author,
                    description=description
                )
                self._record_change_event(change_event)

                self._notify_change_listeners(config_name, old_config, new_config)

                self.logger.info(f"配置更新成功: {config_name} {old_version} -> {new_version}")
                return True

            except Exception as e:
                self.configs[config_name] = old_config
                self.config_versions[config_name] = old_version
                self.logger.error(f"配置更新失败，已回滚: {config_name}, 错误: {e}", exc_info=True)
                return False

    def rollback_config(self, config_name: str, target_version: str,
                       author: str = "system") -> bool:
        """回滚配置到指定版本"""
        if config_name not in self.configs:
            self.logger.error(f"配置不存在: {config_name}")
            return False

        target_config_version = self.storage.load_config_version(config_name, target_version)
        if not target_config_version:
            self.logger.error(f"目标版本不存在: {config_name} v{target_version}")
            return False

        is_valid, errors = self.validator.validate_config(
            config_name, target_config_version.config_data
        )
        if not is_valid:
            self.logger.error(f"目标配置验证失败: {config_name} v{target_version}")
            return False

        with self.config_locks[config_name]:
            old_config = deepcopy(self.configs[config_name])
            old_version = self.config_versions[config_name]

            try:
                self.configs[config_name] = deepcopy(target_config_version.config_data)

                rollback_version = self._generate_version()
                config_version = ConfigVersion(
                    version=rollback_version,
                    timestamp=datetime.now(),
                    config_data=deepcopy(target_config_version.config_data),
                    change_type=ConfigChangeType.ROLLBACK,
                    description=f"回滚到版本 {target_version}",
                    author=author,
                    checksum=target_config_version.checksum
                )

                self.config_versions[config_name] = rollback_version

                if self.auto_save_enabled:
                    self.storage.save_config_version(config_name, config_version)

                change_event = ConfigChangeEvent(
                    config_name=config_name,
                    change_type=ConfigChangeType.ROLLBACK,
                    old_version=old_version,
                    new_version=rollback_version,
                    timestamp=datetime.now(),
                    author=author,
                    description=f"回滚到版本 {target_version}"
                )
                self._record_change_event(change_event)

                self._notify_change_listeners(config_name, old_config, target_config_version.config_data)

                self.logger.info(f"配置回滚成功: {config_name} {old_version} -> {rollback_version} (回滚到 {target_version})")
                return True

            except Exception as e:
                self.configs[config_name] = old_config
                self.config_versions[config_name] = old_version
                self.logger.error(f"配置回滚失败: {config_name}, 错误: {e}", exc_info=True)
                return False

    def add_change_listener(self, config_name: str, listener_func: Callable):
        """添加配置变更监听器"""
        if config_name not in self.change_listeners:
            self.change_listeners[config_name] = []

        self.change_listeners[config_name].append(listener_func)
        self.logger.info(f"添加配置变更监听器: {config_name}")

    def remove_change_listener(self, config_name: str, listener_func: Callable):
        """移除配置变更监听器"""
        if config_name in self.change_listeners:
            try:
                self.change_listeners[config_name].remove(listener_func)
                self.logger.info(f"移除配置变更监听器: {config_name}")
            except ValueError:
                pass

    def get_config_info(self, config_name: str) -> Optional[Dict[str, Any]]:
        """获取配置信息"""
        if config_name not in self.configs:
            return None

        versions = self.storage.list_config_versions(config_name)
        current_version = self.config_versions.get(config_name)

        return {
            "config_name": config_name,
            "current_version": current_version,
            "available_versions": versions,
            "total_versions": len(versions),
            "has_listeners": len(self.change_listeners.get(config_name, [])) > 0,
            "last_updated": datetime.now().isoformat()
        }

    def get_change_history(self, config_name: Optional[str] = None,
                          limit: int = 100) -> List[Dict[str, Any]]:
        """获取配置变更历史"""
        history = self.change_history

        if config_name:
            history = [event for event in history if event.config_name == config_name]

        history = sorted(history, key=lambda x: x.timestamp, reverse=True)[:limit]

        return [asdict(event) for event in history]

    def cleanup_old_versions(self, keep_count: int = 10):
        """清理旧版本"""
        if not self.auto_cleanup_enabled:
            return

        for config_name in self.configs.keys():
            self.storage.delete_old_versions(config_name, keep_count)

    def _generate_version(self) -> str:
        """生成版本号"""
        timestamp = int(time.time() * 1000)
        return f"1.0.{timestamp}"

    def _calculate_checksum(self, config_data: Dict[str, Any]) -> str:
        """计算配置数据的校验和"""
        config_str = json.dumps(config_data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(config_str.encode('utf-8')).hexdigest()

    def _record_change_event(self, event: ConfigChangeEvent):
        """记录变更事件"""
        self.change_history.append(event)

        if len(self.change_history) > self.max_history_size:
            self.change_history = self.change_history[-self.max_history_size:]

    def _notify_change_listeners(self, config_name: str, old_config: Dict[str, Any],
                               new_config: Dict[str, Any]):
        """通知配置变更监听器"""
        listeners = self.change_listeners.get(config_name, [])

        for listener in listeners:
            try:
                listener(config_name, old_config, new_config)
            except Exception as e:
                self.logger.error(f"配置变更监听器执行失败: {e}", exc_info=True)


class DynamicKeywordConfig:
    """动态关键词配置管理器

    基于统一动态配置管理器的关键词专用管理层
    """

    def __init__(self, config_file: str = "backend/config/business_rules.yaml",
                 manager: Optional[UnifiedDynamicConfigManager] = None):
        self.config_file = Path(config_file)
        self.logger = logging.getLogger(__name__)
        self.config_name = "keyword_accelerator"
        self.manager = manager or get_unified_config_manager()

        # 配置变更回调
        self.change_callbacks = []

        # 初始化配置
        self._initialize_config()

        # 注册配置变更监听器
        self.manager.add_change_listener(
            self.config_name, self._on_config_changed
        )

        self.logger.info("动态关键词配置管理器初始化完成")

    def _initialize_config(self):
        """初始化配置"""
        try:
            # 从文件加载初始配置
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = yaml.safe_load(f)
                self.logger.info(f"从文件加载初始配置: {self.config_file}")

                # 转换文件配置为标准格式
                initial_config = self._convert_file_config_to_standard(file_config)
            else:
                # 使用默认配置
                initial_config = self._get_default_config()
                self.logger.info("使用默认关键词配置")

            # 注册到动态配置管理器（如果支持的话）
            if hasattr(self.manager, 'register_config'):
                self.manager.register_config(
                    self.config_name,
                    initial_config,
                    validate_keyword_config
                )
            else:
                # 直接设置配置
                self._config = initial_config

        except Exception as e:
            self.logger.error(f"初始化关键词配置失败: {e}", exc_info=True)
            # 使用默认配置作为后备
            default_config = self._get_default_config()
            self.manager.register_config(
                self.config_name,
                default_config,
                validate_keyword_config
            )

    def _convert_file_config_to_standard(self, file_config: Dict[str, Any]) -> Dict[str, Any]:
        """将文件配置转换为标准格式"""
        standard_config = self._get_default_config()

        # 从quick_intent_rules部分提取关键词配置（新版格式）
        if "quick_intent_rules" in file_config:
            quick_rules = file_config["quick_intent_rules"]
            if isinstance(quick_rules, list):
                for rule in quick_rules:
                    if isinstance(rule, dict) and "intent" in rule and "keywords_config_key" in rule:
                        intent = rule["intent"]
                        config_key = rule["keywords_config_key"]

                        # 解析配置键路径，例如："simple_commands.greeting_keywords"
                        config_path = config_key.split('.')
                        keywords = self._get_nested_value(file_config, config_path)

                        if keywords and isinstance(keywords, list):
                            standard_config["keywords"][intent] = keywords

                            # 设置优先级
                            if "priority" in rule:
                                standard_config["priority_rules"][intent] = rule["priority"]

                            # 设置状态限制
                            if "allowed_states" in rule:
                                standard_config["state_restrictions"][intent] = rule["allowed_states"]

        # 从document_confirmation部分提取关键词（向后兼容）
        if "document_confirmation" in file_config:
            doc_config = file_config["document_confirmation"]

            if "confirmation_keywords" in doc_config:
                standard_config["keywords"]["confirm"] = doc_config["confirmation_keywords"]

            if "negation_keywords" in doc_config:
                standard_config["keywords"]["modify"] = doc_config["negation_keywords"]

        # 从new_chat_request部分提取重启关键词（向后兼容）
        if "new_chat_request" in file_config and "keywords" in file_config["new_chat_request"]:
            standard_config["keywords"]["restart"] = file_config["new_chat_request"]["keywords"]

        return standard_config

    def _get_nested_value(self, data: Dict[str, Any], path: List[str]) -> Any:
        """获取嵌套字典中的值"""
        current = data
        for key in path:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        return current

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "keywords": {
                "confirm": [
                    "确认", "没问题", "正确", "对的", "是的", "好的", "可以",
                    "同意", "接受", "通过", "批准", "确定", "肯定", "赞成",
                    "ok", "yes", "fine", "good", "确认无误", "确认正确"
                ],
                "modify": [
                    "修改", "更改", "调整", "改正", "纠正", "编辑", "变更",
                    "重新", "再次", "重做", "返回", "回退", "撤销", "取消",
                    "不同意", "不接受", "拒绝", "否定", "反对", "modify", "change", "update"
                ],
                "restart": [
                    "新聊天", "新的聊天", "开始新聊天", "重新开始", "重新来",
                    "重新开始聊天", "开始新的对话", "新对话", "重置", "清空",
                    "从头开始", "重新开始对话", "开始全新对话", "restart", "new chat"
                ],
                "greeting": [
                    "你好", "hello", "hi", "您好", "早上好", "下午好", "晚上好", "嗨"
                ],
                "system_capability_query": [
                    "你能做什么", "你会什么", "你的功能", "能力介绍", "功能说明",
                    "帮助", "help", "怎么用", "使用说明", "操作指南"
                ]
            },
            "priority_rules": {
                "modify": 10,
                "restart": 8,
                "confirm": 6,
                "greeting": 5,
                "system_capability_query": 5
            },
            "state_restrictions": {
                "confirm": ["DOCUMENTING"],
                "modify": ["DOCUMENTING"],
                "restart": [],
                "greeting": [],
                "system_capability_query": []
            }
        }

    def get_config(self) -> Optional[Dict[str, Any]]:
        """获取当前配置"""
        return self.manager.get_config(self.config_name)

    def update_config(self, new_config: Dict[str, Any], author: str = "system",
                     description: str = "关键词配置更新") -> bool:
        """更新配置"""
        success = self.manager.update_config(
            self.config_name, new_config, author, description
        )

        if success:
            self.logger.info(f"关键词配置更新成功: {description}")
        else:
            self.logger.error(f"关键词配置更新失败: {description}")

        return success

    def add_keywords(self, intent: str, keywords: list, author: str = "system") -> bool:
        """添加关键词"""
        current_config = self.get_config()
        if not current_config:
            return False

        new_config = deepcopy(current_config)

        if intent not in new_config["keywords"]:
            new_config["keywords"][intent] = []

        existing_keywords = set(new_config["keywords"][intent])
        new_keywords = [kw for kw in keywords if kw not in existing_keywords]

        if new_keywords:
            new_config["keywords"][intent].extend(new_keywords)

            return self.update_config(
                new_config,
                author,
                f"为意图 {intent} 添加关键词: {', '.join(new_keywords)}"
            )
        else:
            self.logger.info(f"关键词已存在，无需添加: {intent}")
            return True

    def remove_keywords(self, intent: str, keywords: list, author: str = "system") -> bool:
        """移除关键词"""
        current_config = self.get_config()
        if not current_config:
            return False

        new_config = deepcopy(current_config)

        if intent not in new_config["keywords"]:
            self.logger.warning(f"意图不存在: {intent}")
            return False

        original_keywords = new_config["keywords"][intent]
        new_config["keywords"][intent] = [
            kw for kw in original_keywords if kw not in keywords
        ]

        removed_keywords = [kw for kw in keywords if kw in original_keywords]

        if removed_keywords:
            return self.update_config(
                new_config,
                author,
                f"从意图 {intent} 移除关键词: {', '.join(removed_keywords)}"
            )
        else:
            self.logger.info(f"关键词不存在，无需移除: {intent}")
            return True

    def update_priority(self, intent: str, priority: int, author: str = "system") -> bool:
        """更新优先级"""
        current_config = self.get_config()
        if not current_config:
            return False

        new_config = deepcopy(current_config)

        old_priority = new_config["priority_rules"].get(intent, 0)
        new_config["priority_rules"][intent] = priority

        return self.update_config(
            new_config,
            author,
            f"更新意图 {intent} 优先级: {old_priority} -> {priority}"
        )

    def rollback_config(self, target_version: str, author: str = "system") -> bool:
        """回滚配置到指定版本"""
        success = self.manager.rollback_config(
            self.config_name, target_version, author
        )

        if success:
            self.logger.info(f"关键词配置回滚成功: 回滚到版本 {target_version}")
        else:
            self.logger.error(f"关键词配置回滚失败: 目标版本 {target_version}")

        return success

    def get_config_info(self) -> Optional[Dict[str, Any]]:
        """获取配置信息"""
        return self.manager.get_config_info(self.config_name)

    def get_change_history(self, limit: int = 50) -> list:
        """获取配置变更历史"""
        return self.manager.get_change_history(self.config_name, limit)

    def add_change_callback(self, callback):
        """添加配置变更回调"""
        self.change_callbacks.append(callback)

    def remove_change_callback(self, callback):
        """移除配置变更回调"""
        if callback in self.change_callbacks:
            self.change_callbacks.remove(callback)

    def _on_config_changed(self, config_name: str, old_config: Dict[str, Any],
                          new_config: Dict[str, Any]):
        """配置变更回调"""
        self.logger.info(f"关键词配置已更新: {config_name}")

        # 分析配置变更
        changes = self._analyze_config_changes(old_config, new_config)

        # 记录变更详情
        for change in changes:
            self.logger.info(f"  {change}")

        # 通知所有回调
        for callback in self.change_callbacks:
            try:
                callback(config_name, old_config, new_config, changes)
            except Exception as e:
                self.logger.error(f"配置变更回调执行失败: {e}", exc_info=True)

    def _analyze_config_changes(self, old_config: Dict[str, Any],
                              new_config: Dict[str, Any]) -> list:
        """分析配置变更"""
        changes = []

        # 分析关键词变更
        old_keywords = old_config.get("keywords", {})
        new_keywords = new_config.get("keywords", {})

        for intent in set(old_keywords.keys()) | set(new_keywords.keys()):
            old_kw_set = set(old_keywords.get(intent, []))
            new_kw_set = set(new_keywords.get(intent, []))

            added = new_kw_set - old_kw_set
            removed = old_kw_set - new_kw_set

            if added:
                changes.append(f"意图 {intent} 添加关键词: {', '.join(added)}")
            if removed:
                changes.append(f"意图 {intent} 移除关键词: {', '.join(removed)}")

        # 分析优先级变更
        old_priorities = old_config.get("priority_rules", {})
        new_priorities = new_config.get("priority_rules", {})

        for intent in set(old_priorities.keys()) | set(new_priorities.keys()):
            old_priority = old_priorities.get(intent, 0)
            new_priority = new_priorities.get(intent, 0)

            if old_priority != new_priority:
                changes.append(f"意图 {intent} 优先级变更: {old_priority} -> {new_priority}")

        # 分析状态限制变更
        old_restrictions = old_config.get("state_restrictions", {})
        new_restrictions = new_config.get("state_restrictions", {})

        for intent in set(old_restrictions.keys()) | set(new_restrictions.keys()):
            old_states = set(old_restrictions.get(intent, []))
            new_states = set(new_restrictions.get(intent, []))

            if old_states != new_states:
                changes.append(f"意图 {intent} 状态限制变更: {list(old_states)} -> {list(new_states)}")

        return changes


# ============================================================================
# 配置验证函数
# ============================================================================

def validate_keyword_config(config_data: Dict[str, Any]) -> tuple:
    """关键词配置验证器"""
    errors = []

    # 检查必需字段
    required_fields = ["keywords", "priority_rules", "state_restrictions"]
    for field in required_fields:
        if field not in config_data:
            errors.append(ConfigValidationError(
                field_path=field,
                error_type="missing_field",
                message=f"缺少必需字段: {field}"
            ))

    if errors:
        return False, errors

    # 验证关键词配置
    keywords = config_data.get("keywords", {})
    if not isinstance(keywords, dict):
        errors.append(ConfigValidationError(
            field_path="keywords",
            error_type="invalid_type",
            message="keywords必须是字典类型"
        ))
    else:
        # 检查关键词重复
        all_keywords = []
        for intent, keyword_list in keywords.items():
            if not isinstance(keyword_list, list):
                errors.append(ConfigValidationError(
                    field_path=f"keywords.{intent}",
                    error_type="invalid_type",
                    message=f"关键词列表必须是数组类型: {intent}"
                ))
                continue

            for keyword in keyword_list:
                if not isinstance(keyword, str):
                    errors.append(ConfigValidationError(
                        field_path=f"keywords.{intent}",
                        error_type="invalid_type",
                        message=f"关键词必须是字符串类型: {keyword}"
                    ))
                    continue

                if keyword in all_keywords:
                    errors.append(ConfigValidationError(
                        field_path=f"keywords.{intent}",
                        error_type="duplicate_keyword",
                        message=f"关键词重复: {keyword}",
                        severity="warning"
                    ))
                else:
                    all_keywords.append(keyword)

    # 验证优先级规则
    priority_rules = config_data.get("priority_rules", {})
    if not isinstance(priority_rules, dict):
        errors.append(ConfigValidationError(
            field_path="priority_rules",
            error_type="invalid_type",
            message="priority_rules必须是字典类型"
        ))
    else:
        for intent, priority in priority_rules.items():
            if not isinstance(priority, (int, float)):
                errors.append(ConfigValidationError(
                    field_path=f"priority_rules.{intent}",
                    error_type="invalid_type",
                    message=f"优先级必须是数字类型: {intent}"
                ))
            elif priority < 0:
                errors.append(ConfigValidationError(
                    field_path=f"priority_rules.{intent}",
                    error_type="invalid_value",
                    message=f"优先级不能为负数: {intent}"
                ))

    # 验证状态限制
    state_restrictions = config_data.get("state_restrictions", {})
    if not isinstance(state_restrictions, dict):
        errors.append(ConfigValidationError(
            field_path="state_restrictions",
            error_type="invalid_type",
            message="state_restrictions必须是字典类型"
        ))
    else:
        valid_states = ["IDLE", "COLLECTING_INFO", "DOCUMENTING", "COMPLETED"]
        for intent, states in state_restrictions.items():
            if not isinstance(states, list):
                errors.append(ConfigValidationError(
                    field_path=f"state_restrictions.{intent}",
                    error_type="invalid_type",
                    message=f"状态限制必须是数组类型: {intent}"
                ))
                continue

            for state in states:
                if state not in valid_states:
                    errors.append(ConfigValidationError(
                        field_path=f"state_restrictions.{intent}",
                        error_type="invalid_value",
                        message=f"无效的状态值: {state}，有效值: {valid_states}"
                    ))

    # 只有严重错误才返回False
    has_errors = any(error.severity == "error" for error in errors)
    return not has_errors, errors


# ============================================================================
# 全局实例
# ============================================================================

# 全局统一动态配置管理器实例
# 避免循环导入，延迟初始化
unified_config_manager = None

def get_unified_config_manager():
    global unified_config_manager
    if unified_config_manager is None:
        from .service import config_service
        unified_config_manager = config_service
    return unified_config_manager

# 全局动态关键词配置实例（延迟初始化以避免循环导入）
dynamic_keyword_config = None

def get_dynamic_keyword_config():
    global dynamic_keyword_config
    if dynamic_keyword_config is None:
        dynamic_keyword_config = DynamicKeywordConfig()
    return dynamic_keyword_config

# 向后兼容别名
dynamic_config_manager = unified_config_manager
