# ============================================================================
# 安全配置默认值 - 生产环境安全兜底
# ============================================================================

security:
  # 输入验证
  input_validation:
    max_input_length: 10000
    allowed_file_types: ["txt", "md", "pdf", "doc", "docx"]
    max_file_size: 10485760  # 10MB
    
  # 速率限制
  rate_limiting:
    requests_per_minute: 60
    requests_per_hour: 1000
    burst_limit: 10
    
  # 会话管理
  session:
    timeout: 3600  # 1小时
    max_sessions_per_user: 5
    session_cleanup_interval: 300  # 5分钟
    
  # 内容过滤
  content_filter:
    enabled: true
    threshold: 0.8
    
  # 敏感数据保护
  sensitive_data:
    # 敏感字段列表（用于日志脱敏）
    sensitive_fields:
      - "password"
      - "api_key" 
      - "secret"
      - "token"
      - "private_key"
    # 脱敏替换字符
    mask_char: "*"
    # 保留字符数（前后各保留的字符数）
    preserve_chars: 2

# 访问控制
access_control:
  # 允许的主机
  allowed_hosts:
    - "localhost"
    - "127.0.0.1"
    - "0.0.0.0"
  
  # CORS配置
  cors:
    allow_origins: ["*"]
    allow_methods: ["GET", "POST", "PUT", "DELETE"]
    allow_headers: ["*"]
    allow_credentials: true

# 审计日志
audit:
  enabled: true
  log_file: "logs/audit.log"
  log_operations:
    - "CREATE"
    - "UPDATE" 
    - "DELETE"
    - "LOGIN"
    - "LOGOUT"
  retention_days: 90
