# ============================================================================
# 关键默认值配置 - 启动阻断级别（P0）兜底
# ============================================================================
#
# 这些是系统启动必需的最小配置集，任何一项缺失都会导致系统无法正常启动
# 优先级：代码硬编码 < 本文件 < 环境变量 < 运行时配置
#
# ⚠️ 重要：修改这些值需要充分测试，确保不会影响系统稳定性
# ============================================================================

# 应用基础信息（P0 - 启动阻断）
app:
  name: "需求采集系统"
  version: "3.0"
  environment: "development"  # development/staging/production

# 数据库配置（P0 - 启动阻断）
data:
  database:
    # 数据库文件路径 - 系统启动必需
    path: "backend/data/aidatabase.db"
    # 连接超时（秒）
    timeout: 30
    # 连接检查
    check_same_thread: false
    # 重试配置
    max_retries: 3
    retry_delay: 1

# LLM服务配置（P0 - 启动阻断）
llm:
  # 默认模型 - 所有LLM调用的最后兜底
  default_model: "deepseek-chat"

  # 关键场景映射（可被具体配置文件覆盖）
  scenario_mapping:
    intent_recognition: "doubao-pro-32k"
    domain_classifier: "doubao-1.5-Lite"
    conversation_flow: "qwen-plus"
    document_generator: "qwen-plus"

  # 默认LLM参数
  default_params:
    temperature: 0.7
    max_tokens: 2000
    timeout: 30
    max_retries: 3

# 系统性能配置（P1 - 功能降级）
system:
  performance:
    # LLM调用超时（秒）- 影响所有AI功能
    llm_timeout: 30
    # 数据库操作超时（秒）
    database_timeout: 10
    # 最大并发请求数
    max_concurrent_requests: 100
    # 内存使用限制（MB）
    max_memory_usage: 1024

# 业务阈值配置（P1 - 功能降级）
thresholds:
  # 需求采集完成度阈值
  completion_threshold: 0.7
  # 置信度阈值
  confidence_threshold: 0.8
  # 性能相关
  performance:
    retry:
      database_operation: 3
      llm_request: 3
      max_total_attempts: 5

# 知识库配置（P2 - 可选功能）
knowledge_base:
  # 默认禁用，避免启动依赖
  enabled: false
  retrieval:
    top_k: 5
    similarity_threshold: 0.7
    max_context_length: 4000

# 日志配置（P2 - 可选功能）
logging:
  level: "INFO"
  format: "json"
  max_file_size: "10MB"
  backup_count: 5

