# ============================================================================
# 业务逻辑配置默认值 - 核心业务流程兜底
# ============================================================================

# 决策策略配置
decision_strategies:
  intent_recognition:
    use_keyword_acceleration: true
    use_semantic_matching: true  
    use_llm_classification: true
    confidence_threshold: 0.7
    
  domain_classification:
    enabled: true
    confidence_threshold: 0.8
    fallback_to_general: true
    
  response_generation:
    use_templates: true
    use_llm_generation: true
    personalization_enabled: false

# 工作流规则
workflow_rules:
  state_transitions:
    INITIAL:
      - "GREETING"
      - "REQUIREMENT_GATHERING"
    GREETING:
      - "REQUIREMENT_GATHERING"
      - "SYSTEM_CAPABILITY_QUERY"
    REQUIREMENT_GATHERING:
      - "DOCUMENTING"
      - "CLARIFICATION"
    CLARIFICATION:
      - "REQUIREMENT_GATHERING"
      - "DOCUMENTING"
    DOCUMENTING:
      - "COMPLETED"
      - "MODIFICATION"
    MODIFICATION:
      - "DOCUMENTING"
      - "COMPLETED"

# 需求采集配置
requirement_collection:
  completion_threshold: 0.8
  max_focus_points: 10
  min_focus_points: 3
  
  # 关注点优先级
  focus_point_priority:
    p0: true  # 必需
    p1: true  # 重要
    p2: true  # 一般
    p3: false # 可选

# 文档确认配置
document_confirmation:
  require_explicit_confirmation: true
  case_sensitive: false
  partial_match_enabled: true
  confirmation_keywords:
    - "确认"
    - "没问题"
    - "正确"
    - "同意"
    - "确认无误"
    - "批准"
    - "好的"
    - "ok"
    - "okay"
    - "confirm"
    - "yes"
    - "good"

# 质量控制
quality_control:
  max_input_length: 1000
  min_input_length: 2
  spam_detection_enabled: true
  abuse_detection_threshold: 0.8
  relevance_threshold: 0.5

# 重试策略
retry:
  max_pending_attempts: 3
  max_total_attempts: 5
  backoff_factor: 1.5
  base_delay: 1.0  # 秒

# 动作处理器映射
action_handlers:
  handler_classes:
    CompositeHandler: "backend.handlers.composite_handler.CompositeHandler"
    ConversationHandler: "backend.handlers.conversation_handler.ConversationHandler"
    DocumentHandler: "backend.handlers.document_handler.DocumentHandler"
    GeneralRequestHandler: "backend.handlers.general_request_handler.GeneralRequestHandler"
    KnowledgeBaseHandler: "backend.handlers.knowledge_base_handler.KnowledgeBaseHandler"
    RequirementHandler: "backend.handlers.requirement_handler.RequirementHandler"
