# 配置监控告警规则
# 定义各种配置相关的告警规则和阈值

# P0级别告警 - 关键系统问题，需要立即响应
critical_alerts:
  - name: "config_load_failure"
    metric: "config_load_errors"
    threshold: 0
    operator: "gt"
    severity: "critical"
    description: "配置加载失败，系统可能无法正常启动"
    enabled: true
    notification:
      - type: "email"
        recipients: ["<EMAIL>"]
      - type: "slack"
        channel: "#alerts-critical"
    
  - name: "config_validation_failure"
    metric: "config_validation_errors"
    threshold: 0
    operator: "gt"
    severity: "critical"
    description: "配置验证失败，可能存在配置结构问题"
    enabled: true
    notification:
      - type: "email"
        recipients: ["<EMAIL>"]
    
  - name: "excessive_config_fallback"
    metric: "config_fallback_count"
    threshold: 50
    operator: "gt"
    severity: "critical"
    description: "配置降级次数过多，可能存在配置文件损坏"
    enabled: true
    notification:
      - type: "pagerduty"
        service_key: "config-service"

# P1级别告警 - 高优先级问题，需要及时处理
high_alerts:
  - name: "high_config_load_time"
    metric: "average_load_time"
    threshold: 5.0
    operator: "gt"
    severity: "high"
    description: "配置加载时间过长，可能影响系统启动性能"
    enabled: true
    notification:
      - type: "slack"
        channel: "#alerts-high"
    
  - name: "frequent_env_override_rejection"
    metric: "env_override_rejected_count"
    threshold: 10
    operator: "gt"
    severity: "high"
    description: "环境变量覆盖频繁被拒绝，可能存在配置问题"
    enabled: true
    
  - name: "config_validation_slow"
    metric: "average_validation_time"
    threshold: 2.0
    operator: "gt"
    severity: "high"
    description: "配置验证时间过长，可能影响系统性能"
    enabled: true

# P2级别告警 - 中等优先级问题，需要关注
medium_alerts:
  - name: "moderate_config_fallback"
    metric: "config_fallback_count"
    threshold: 10
    operator: "gt"
    severity: "medium"
    description: "配置降级次数较多，建议检查配置完整性"
    enabled: true
    
  - name: "unknown_config_keys"
    metric: "unknown_key_count"
    threshold: 5
    operator: "gt"
    severity: "medium"
    description: "发现较多未知配置键，可能需要更新白名单"
    enabled: true
    
  - name: "env_override_rejection"
    metric: "env_override_rejected_count"
    threshold: 3
    operator: "gt"
    severity: "medium"
    description: "环境变量覆盖被拒绝，请检查配置键是否正确"
    enabled: true

# P3级别告警 - 低优先级问题，信息性告警
low_alerts:
  - name: "single_unknown_key"
    metric: "unknown_key_count"
    threshold: 0
    operator: "gt"
    severity: "low"
    description: "发现未知配置键，建议检查配置"
    enabled: true
    
  - name: "env_override_activity"
    metric: "env_override_count"
    threshold: 100
    operator: "gt"
    severity: "low"
    description: "环境变量覆盖活动频繁，请确认是否正常"
    enabled: false  # 默认禁用，避免噪音

# 复合告警规则 - 基于多个指标的复杂告警
composite_alerts:
  - name: "config_system_degraded"
    description: "配置系统整体性能下降"
    severity: "high"
    enabled: true
    conditions:
      - metric: "average_load_time"
        threshold: 3.0
        operator: "gt"
      - metric: "config_fallback_count"
        threshold: 5
        operator: "gt"
    logic: "AND"  # 所有条件都满足时触发
    
  - name: "config_quality_issues"
    description: "配置质量问题集中出现"
    severity: "medium"
    enabled: true
    conditions:
      - metric: "config_validation_errors"
        threshold: 0
        operator: "gt"
      - metric: "unknown_key_count"
        threshold: 3
        operator: "gt"
      - metric: "env_override_rejected_count"
        threshold: 2
        operator: "gt"
    logic: "OR"   # 任一条件满足时触发

# 时间窗口告警 - 基于时间窗口的告警规则
time_window_alerts:
  - name: "config_load_spike"
    metric: "config_load_count"
    threshold: 50
    operator: "gt"
    severity: "medium"
    description: "配置加载次数在短时间内激增"
    time_window: "5m"  # 5分钟窗口
    enabled: true
    
  - name: "validation_error_burst"
    metric: "config_validation_errors"
    threshold: 3
    operator: "gt"
    severity: "high"
    description: "配置验证错误在短时间内集中出现"
    time_window: "1m"  # 1分钟窗口
    enabled: true

# 告警抑制规则 - 避免告警风暴
suppression_rules:
  - name: "config_load_failure_suppression"
    description: "配置加载失败时抑制其他相关告警"
    trigger_alert: "config_load_failure"
    suppress_alerts:
      - "config_validation_failure"
      - "high_config_load_time"
      - "config_system_degraded"
    duration: "10m"  # 抑制10分钟
    
  - name: "maintenance_mode_suppression"
    description: "维护模式下抑制所有告警"
    condition:
      env_var: "MAINTENANCE_MODE"
      value: "true"
    suppress_all: true

# 告警通知配置
notification_config:
  email:
    smtp_server: "smtp.company.com"
    smtp_port: 587
    username: "<EMAIL>"
    # password 应该通过环境变量设置
    from_address: "<EMAIL>"
    
  slack:
    webhook_url: "${SLACK_WEBHOOK_URL}"  # 从环境变量获取
    default_channel: "#alerts"
    
  pagerduty:
    api_key: "${PAGERDUTY_API_KEY}"  # 从环境变量获取
    
  webhook:
    url: "${ALERT_WEBHOOK_URL}"  # 自定义webhook
    timeout: 10
    retry_count: 3

# 告警升级规则
escalation_rules:
  - name: "critical_alert_escalation"
    severity: "critical"
    steps:
      - delay: "0m"
        actions: ["slack", "email"]
      - delay: "5m"
        actions: ["pagerduty"]
        condition: "not_acknowledged"
      - delay: "15m"
        actions: ["phone_call"]
        condition: "not_resolved"
        
  - name: "high_alert_escalation"
    severity: "high"
    steps:
      - delay: "0m"
        actions: ["slack"]
      - delay: "10m"
        actions: ["email"]
        condition: "not_acknowledged"
      - delay: "30m"
        actions: ["pagerduty"]
        condition: "not_resolved"

# 告警恢复通知
recovery_notification:
  enabled: true
  notify_on_recovery: true
  recovery_message_template: |
    ✅ 告警已恢复
    
    告警名称: {alert_name}
    描述: {description}
    恢复时间: {recovery_time}
    持续时间: {duration}
    
    当前指标值: {current_value}
    阈值: {threshold}

# 告警历史保留
history_retention:
  max_alerts: 10000      # 最多保留告警数量
  retention_days: 30     # 保留天数
  cleanup_interval: "24h" # 清理间隔

# 告警测试配置
testing:
  enabled: false  # 生产环境应设为false
  test_alerts:
    - name: "test_critical"
      severity: "critical"
      description: "测试关键告警"
    - name: "test_high"
      severity: "high"
      description: "测试高优先级告警"
