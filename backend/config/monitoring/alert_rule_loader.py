#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
告警规则加载器

从YAML配置文件加载告警规则，支持动态更新和验证
"""

import os
import yaml
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime

from .dashboard import AlertRule

logger = logging.getLogger(__name__)


@dataclass
class NotificationConfig:
    """通知配置"""
    type: str  # email, slack, pagerduty, webhook
    recipients: List[str] = field(default_factory=list)
    channel: Optional[str] = None
    service_key: Optional[str] = None
    webhook_url: Optional[str] = None


@dataclass
class CompositeAlertRule:
    """复合告警规则"""
    name: str
    description: str
    severity: str
    enabled: bool
    conditions: List[Dict[str, Any]]
    logic: str  # AND, OR
    notifications: List[NotificationConfig] = field(default_factory=list)


@dataclass
class TimeWindowAlertRule:
    """时间窗口告警规则"""
    name: str
    metric: str
    threshold: float
    operator: str
    severity: str
    description: str
    time_window: str  # 5m, 1h, etc.
    enabled: bool
    notifications: List[NotificationConfig] = field(default_factory=list)


class AlertRuleLoader:
    """告警规则加载器"""
    
    def __init__(self, config_file: str = None):
        if config_file is None:
            config_file = Path(__file__).parent / "alert_rules.yaml"
        
        self.config_file = Path(config_file)
        self.rules: Dict[str, List[AlertRule]] = {}
        self.composite_rules: List[CompositeAlertRule] = []
        self.time_window_rules: List[TimeWindowAlertRule] = []
        self.notification_config: Dict[str, Any] = {}
        self.last_loaded: Optional[datetime] = None
        
        # 加载规则
        self.load_rules()
    
    def load_rules(self) -> bool:
        """加载告警规则"""
        try:
            if not self.config_file.exists():
                logger.warning(f"告警规则文件不存在: {self.config_file}")
                return False
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            if not config:
                logger.warning("告警规则文件为空")
                return False
            
            # 清空现有规则
            self.rules.clear()
            self.composite_rules.clear()
            self.time_window_rules.clear()
            
            # 加载基本告警规则
            self._load_basic_rules(config)
            
            # 加载复合告警规则
            self._load_composite_rules(config)
            
            # 加载时间窗口告警规则
            self._load_time_window_rules(config)
            
            # 加载通知配置
            self._load_notification_config(config)
            
            self.last_loaded = datetime.now()
            logger.info(f"告警规则加载完成: {self.config_file}")
            
            return True
            
        except Exception as e:
            logger.error(f"加载告警规则失败: {e}")
            return False
    
    def _load_basic_rules(self, config: Dict[str, Any]):
        """加载基本告警规则"""
        severity_groups = [
            'critical_alerts', 'high_alerts', 'medium_alerts', 'low_alerts'
        ]
        
        for group in severity_groups:
            if group not in config:
                continue
            
            severity = group.replace('_alerts', '')
            self.rules[severity] = []
            
            for rule_config in config[group]:
                try:
                    # 解析通知配置
                    notifications = []
                    if 'notification' in rule_config:
                        for notif_config in rule_config['notification']:
                            notifications.append(NotificationConfig(
                                type=notif_config['type'],
                                recipients=notif_config.get('recipients', []),
                                channel=notif_config.get('channel'),
                                service_key=notif_config.get('service_key'),
                                webhook_url=notif_config.get('webhook_url')
                            ))
                    
                    # 创建告警规则
                    rule = AlertRule(
                        name=rule_config['name'],
                        metric=rule_config['metric'],
                        threshold=rule_config['threshold'],
                        operator=rule_config['operator'],
                        severity=rule_config['severity'],
                        description=rule_config['description'],
                        enabled=rule_config.get('enabled', True)
                    )
                    
                    # 添加通知配置（扩展AlertRule以支持通知）
                    rule.notifications = notifications
                    
                    self.rules[severity].append(rule)
                    
                except Exception as e:
                    logger.error(f"解析告警规则失败: {rule_config.get('name', 'unknown')} - {e}")
    
    def _load_composite_rules(self, config: Dict[str, Any]):
        """加载复合告警规则"""
        if 'composite_alerts' not in config:
            return
        
        for rule_config in config['composite_alerts']:
            try:
                # 解析通知配置
                notifications = []
                if 'notification' in rule_config:
                    for notif_config in rule_config['notification']:
                        notifications.append(NotificationConfig(
                            type=notif_config['type'],
                            recipients=notif_config.get('recipients', []),
                            channel=notif_config.get('channel'),
                            service_key=notif_config.get('service_key'),
                            webhook_url=notif_config.get('webhook_url')
                        ))
                
                rule = CompositeAlertRule(
                    name=rule_config['name'],
                    description=rule_config['description'],
                    severity=rule_config['severity'],
                    enabled=rule_config.get('enabled', True),
                    conditions=rule_config['conditions'],
                    logic=rule_config['logic'],
                    notifications=notifications
                )
                
                self.composite_rules.append(rule)
                
            except Exception as e:
                logger.error(f"解析复合告警规则失败: {rule_config.get('name', 'unknown')} - {e}")
    
    def _load_time_window_rules(self, config: Dict[str, Any]):
        """加载时间窗口告警规则"""
        if 'time_window_alerts' not in config:
            return
        
        for rule_config in config['time_window_alerts']:
            try:
                # 解析通知配置
                notifications = []
                if 'notification' in rule_config:
                    for notif_config in rule_config['notification']:
                        notifications.append(NotificationConfig(
                            type=notif_config['type'],
                            recipients=notif_config.get('recipients', []),
                            channel=notif_config.get('channel'),
                            service_key=notif_config.get('service_key'),
                            webhook_url=notif_config.get('webhook_url')
                        ))
                
                rule = TimeWindowAlertRule(
                    name=rule_config['name'],
                    metric=rule_config['metric'],
                    threshold=rule_config['threshold'],
                    operator=rule_config['operator'],
                    severity=rule_config['severity'],
                    description=rule_config['description'],
                    time_window=rule_config['time_window'],
                    enabled=rule_config.get('enabled', True),
                    notifications=notifications
                )
                
                self.time_window_rules.append(rule)
                
            except Exception as e:
                logger.error(f"解析时间窗口告警规则失败: {rule_config.get('name', 'unknown')} - {e}")
    
    def _load_notification_config(self, config: Dict[str, Any]):
        """加载通知配置"""
        if 'notification_config' in config:
            self.notification_config = config['notification_config']
            
            # 处理环境变量替换
            self._process_env_vars(self.notification_config)
    
    def _process_env_vars(self, config: Any):
        """处理配置中的环境变量"""
        if isinstance(config, dict):
            for key, value in config.items():
                if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                    env_var = value[2:-1]
                    config[key] = os.getenv(env_var, value)
                elif isinstance(value, (dict, list)):
                    self._process_env_vars(value)
        elif isinstance(config, list):
            for item in config:
                self._process_env_vars(item)
    
    def get_all_basic_rules(self) -> List[AlertRule]:
        """获取所有基本告警规则"""
        all_rules = []
        for severity_rules in self.rules.values():
            all_rules.extend(severity_rules)
        return all_rules
    
    def get_rules_by_severity(self, severity: str) -> List[AlertRule]:
        """根据严重程度获取告警规则"""
        return self.rules.get(severity, [])
    
    def get_composite_rules(self) -> List[CompositeAlertRule]:
        """获取复合告警规则"""
        return self.composite_rules
    
    def get_time_window_rules(self) -> List[TimeWindowAlertRule]:
        """获取时间窗口告警规则"""
        return self.time_window_rules
    
    def get_notification_config(self) -> Dict[str, Any]:
        """获取通知配置"""
        return self.notification_config
    
    def reload_if_changed(self) -> bool:
        """如果文件有变更则重新加载"""
        try:
            if not self.config_file.exists():
                return False
            
            file_mtime = datetime.fromtimestamp(self.config_file.stat().st_mtime)
            
            if self.last_loaded is None or file_mtime > self.last_loaded:
                logger.info("检测到告警规则文件变更，重新加载...")
                return self.load_rules()
            
            return False
            
        except Exception as e:
            logger.error(f"检查告警规则文件变更失败: {e}")
            return False
    
    def validate_rules(self) -> List[str]:
        """验证告警规则"""
        errors = []
        
        # 验证基本规则
        for severity, rules in self.rules.items():
            for rule in rules:
                if not rule.name:
                    errors.append(f"规则名称不能为空 (severity: {severity})")
                
                if not rule.metric:
                    errors.append(f"规则 {rule.name}: 指标名称不能为空")
                
                if rule.operator not in ['gt', 'lt', 'eq', 'ne']:
                    errors.append(f"规则 {rule.name}: 无效的操作符 {rule.operator}")
                
                if rule.severity not in ['critical', 'high', 'medium', 'low']:
                    errors.append(f"规则 {rule.name}: 无效的严重程度 {rule.severity}")
        
        # 验证复合规则
        for rule in self.composite_rules:
            if not rule.conditions:
                errors.append(f"复合规则 {rule.name}: 条件不能为空")
            
            if rule.logic not in ['AND', 'OR']:
                errors.append(f"复合规则 {rule.name}: 无效的逻辑操作符 {rule.logic}")
        
        # 验证时间窗口规则
        for rule in self.time_window_rules:
            if not rule.time_window:
                errors.append(f"时间窗口规则 {rule.name}: 时间窗口不能为空")
        
        return errors
    
    def get_rule_summary(self) -> Dict[str, Any]:
        """获取规则摘要"""
        total_basic = sum(len(rules) for rules in self.rules.values())
        enabled_basic = sum(len([r for r in rules if r.enabled]) for rules in self.rules.values())
        
        enabled_composite = len([r for r in self.composite_rules if r.enabled])
        enabled_time_window = len([r for r in self.time_window_rules if r.enabled])
        
        return {
            "basic_rules": {
                "total": total_basic,
                "enabled": enabled_basic,
                "by_severity": {
                    severity: len([r for r in rules if r.enabled])
                    for severity, rules in self.rules.items()
                }
            },
            "composite_rules": {
                "total": len(self.composite_rules),
                "enabled": enabled_composite
            },
            "time_window_rules": {
                "total": len(self.time_window_rules),
                "enabled": enabled_time_window
            },
            "last_loaded": self.last_loaded.isoformat() if self.last_loaded else None,
            "config_file": str(self.config_file)
        }


# 全局实例
_rule_loader: Optional[AlertRuleLoader] = None


def get_alert_rule_loader() -> AlertRuleLoader:
    """获取全局告警规则加载器实例"""
    global _rule_loader
    if _rule_loader is None:
        _rule_loader = AlertRuleLoader()
    return _rule_loader


def reload_alert_rules() -> bool:
    """重新加载告警规则"""
    loader = get_alert_rule_loader()
    return loader.load_rules()


if __name__ == "__main__":
    # 测试告警规则加载
    loader = AlertRuleLoader()
    
    print("=== 告警规则摘要 ===")
    summary = loader.get_rule_summary()
    print(f"基本规则: {summary['basic_rules']['total']} 个 (启用: {summary['basic_rules']['enabled']})")
    print(f"复合规则: {summary['composite_rules']['total']} 个 (启用: {summary['composite_rules']['enabled']})")
    print(f"时间窗口规则: {summary['time_window_rules']['total']} 个 (启用: {summary['time_window_rules']['enabled']})")
    
    print("\n=== 规则验证 ===")
    errors = loader.validate_rules()
    if errors:
        print("发现错误:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("所有规则验证通过")
