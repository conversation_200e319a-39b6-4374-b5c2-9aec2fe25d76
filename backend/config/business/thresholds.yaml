# ============================================================================
# 业务阈值配置（从unified_config.yaml拆分）
# ============================================================================
# 文件说明：从unified_config.yaml拆分的业务阈值配置
# 拆分时间：2025-08-18 19:22:20
# 源配置块：thresholds
# 维护责任：配置管理团队
# 更新频率：根据业务需求
# ============================================================================

confidence:
  default: 0.7
  minimum: 0.5
  low: 0.6
  high: 0.8
  very_high: 0.9
  intent_recognition: 0.8
  domain_classification: 0.8
  keyword_matching: 0.7
  structured_classification: 0.6
  decision_engine: 0.7
business:
  requirement_completion_threshold: 0.8
  focus_point_priority_threshold: 0.7
  document_quality_threshold: 0.8
  template_match_threshold: 0.6
  response_time_threshold: 2.0
  user_satisfaction_threshold: 0.8
limits:
  max_focus_points: 10
  min_focus_points: 3
  max_history_items: 100
  max_results: 50
  default_max_items: 10
  max_query_length: 1000
  max_keywords: 20
  cache_max_size: 1000
  log_max_entries: 10000
  max_concurrent: 5
  session_max_duration: 3600
performance:
  timeout:
    default: 5
    short: 3
    medium: 10
    long: 30
    very_long: 60
    api_request: 10
    database: 5
    file_operation: 5
    llm_service: 30
  retry:
    default: 3
    quick_retry: 2
    persistent_retry: 5
    api_call: 2
    database_operation: 3
    file_access: 2
    llm_request: 3
    max_attempts: 3
quality:
  min_input_length: 2
  max_input_length: 1000
  min_word_count: 1
  max_word_count: 200
  completeness_threshold: 0.8
  relevance_threshold: 0.5
  similarity_threshold: 0.3
  spam_detection_threshold: 0.7
  abuse_detection_threshold: 0.8
security:
  max_login_attempts: 5
  session_timeout: 7200
  token_expiry: 3600
  rate_limit_per_minute: 60
  rate_limit_per_hour: 1000
  burst_limit: 10
  content_filter_threshold: 0.8
  sensitive_data_threshold: 0.9
llm:
  default_temperature: 0.5
  low_temperature: 0.1
  high_temperature: 0.8
  default_max_tokens: 3000
  small_max_tokens: 100
  large_max_tokens: 4096
  response_quality_threshold: 0.7
  coherence_threshold: 0.8
  relevance_threshold: 0.7
workflow:
  state_transition_confidence: 0.8
  auto_transition_threshold: 0.9
  task_completion_threshold: 0.85
  partial_completion_threshold: 0.6
  user_engagement_threshold: 0.7
  interaction_quality_threshold: 0.6
monitoring:
  response_time_warning: 1.0
  response_time_critical: 3.0
  error_rate_warning: 0.05
  error_rate_critical: 0.1
  memory_usage_warning: 0.8
  memory_usage_critical: 0.9
  cpu_usage_warning: 0.7
  cpu_usage_critical: 0.85
  user_satisfaction_warning: 0.7
  completion_rate_warning: 0.8
  success_rate_warning: 0.9
adaptive:
  enabled: false
  adjustment_factor: 0.1
  min_samples: 100
  adjustment_interval: 3600
  confidence_range:
    min: 0.5
    max: 0.95
  quality_range:
    min: 0.6
    max: 0.9
experimental:
  ab_test_enabled: false
  test_traffic_ratio: 0.1
  new_feature_confidence: 0.9
  beta_feature_threshold: 0.8
_metadata:
  file_version: '1.0'
  split_date: '2025-08-18T19:22:20.115915'
  source_blocks:
  - thresholds
  config_type: thresholds
  total_items: 79
thresholds:
  business:
    document_quality_threshold: 0.8
    focus_point_priority_threshold: 0.7
    requirement_completion_threshold: 0.8
    response_time_threshold: 2.0
    template_match_threshold: 0.6
    user_satisfaction_threshold: 0.8
  confidence:
    decision_engine: 0.7
    default: 0.7
    domain_classification: 0.8
    high: 0.8
    intent_recognition: 0.8
    keyword_matching: 0.7
    low: 0.6
    minimum: 0.5
    structured_classification: 0.6
    very_high: 0.9
  limits:
    cache_max_size: 1000
    default_max_items: 10
    log_max_entries: 10000
    max_concurrent: 5
    max_focus_points: 10
    max_history_items: 100
    max_keywords: 20
    max_query_length: 1000
    max_results: 50
    min_focus_points: 3
    session_max_duration: 3600
  performance:
    retry:
      api_call: 2
      database_operation: 3
      default: 3
      file_access: 2
      llm_request: 3
      max_attempts: 3
      persistent_retry: 5
      quick_retry: 2
    timeout:
      api_request: 10
      database: 5
      default: 5
      file_operation: 5
      llm_service: 30
      long: 30
      medium: 10
      short: 3
      very_long: 60
  quality:
    abuse_detection_threshold: 0.8
    completeness_threshold: 0.8
    max_input_length: 1000
    max_word_count: 200
    min_input_length: 2
    min_word_count: 1
    relevance_threshold: 0.5
    similarity_threshold: 0.3
    spam_detection_threshold: 0.7
  security:
    burst_limit: 10
    content_filter_threshold: 0.8
    max_login_attempts: 5
    rate_limit_per_hour: 1000
    rate_limit_per_minute: 60
    sensitive_data_threshold: 0.9
    session_timeout: 7200
    token_expiry: 3600
  llm:
    default_temperature: 0.5
    low_temperature: 0.1
    high_temperature: 0.8
    default_max_tokens: 3000
    small_max_tokens: 100
    large_max_tokens: 4096
    default_timeout: 30
    long_timeout: 60
    short_timeout: 10
