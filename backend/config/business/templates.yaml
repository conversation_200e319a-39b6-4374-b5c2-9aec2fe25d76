# ============================================================================
# 业务消息模板配置（从unified_config.yaml拆分）
# ============================================================================
# 文件说明：从unified_config.yaml拆分的业务消息模板配置
# 拆分时间：2025-08-18 19:22:20
# 源配置块：business_templates, error_fallback_templates, message_reply_system, message_templates, strategy_templates, system_fallback_templates
# 维护责任：配置管理团队
# 更新频率：根据业务需求
# ============================================================================

greeting:
  basic: '您好！我是您的需求采集助手。

    我可以帮助您整理和记录各种业务需求。

    请告诉我您想要整理什么类型的需求？

    '
  personalized: '您好，{user_name}！

    欢迎回来，我是您的需求采集助手。

    上次我们讨论了{last_topic}，今天有什么新的需求需要整理吗？

    '
  capability_intro: '我可以帮助您：

    • 收集和整理业务需求

    • 生成需求文档

    • 提供需求分析建议

    • 协助需求评审和确认


    请告诉我您的具体需求！

    '
requirement_collection:
  start_collection: '好的，让我们开始收集您的{domain}需求。

    为了更好地帮助您，我需要了解以下几个方面：

    {focus_points}


    请先告诉我：{first_question}

    '
  continue_collection: '很好！关于{current_topic}的信息我已经记录了。


    接下来，请告诉我：{next_question}

    '
  request_details: '关于{topic}，能否提供更多详细信息？

    比如：{examples}


    这将帮助我更准确地理解您的需求。

    '
confirmation:
  info_confirmation: '让我确认一下您提供的信息：


    {summary}


    以上信息是否正确？如有需要修改的地方，请告诉我。

    '
  completion_confirmation: '太好了！您的{domain}需求已经收集完成。


    我将为您生成详细的需求文档。

    请稍等片刻...

    '
  document_confirmation: '需求文档已生成完成！


    请查看文档内容，如果需要修改或补充，请告诉我具体的修改要求。

    如果确认无误，请回复"确认"。

    '
error:
  general_error: '抱歉，系统遇到了一些问题。

    请稍后重试，或者告诉我您遇到的具体情况。

    '
  invalid_input: '抱歉，我没有理解您的输入。

    请提供更清晰的描述，或者告诉我您需要什么帮助。

    '
  system_busy: '系统当前比较繁忙，请稍等片刻。

    我会尽快为您处理请求。

    '
  feature_unavailable: '抱歉，您请求的功能暂时不可用。

    请尝试其他功能，或稍后再试。

    '
guidance:
  operation_guide: '您可以通过以下方式与我互动：


    • 直接描述您的需求

    • 回答我的问题来完善需求

    • 要求修改或补充已有内容

    • 随时说"重新开始"来重新收集需求


    有什么问题请随时告诉我！

    '
  format_suggestion: '为了更好地理解您的需求，建议您：


    • 描述具体的业务场景

    • 说明期望达到的目标

    • 提及相关的约束条件

    • 指出优先级和重要程度


    请按照这个思路继续描述。

    '
state_transition:
  enter_requirement_gathering: '好的，让我们开始详细的需求收集。

    我会逐步了解您的需求细节。

    '
  enter_documenting: '需求信息收集完成，正在生成文档...

    请稍等片刻。

    '
  enter_modification: '我明白您需要修改需求。

    请告诉我具体要修改的内容。

    '
feedback:
  processing: '正在处理您的请求，请稍等...

    '
  completed: '处理完成！{result_summary}


    还有其他需要帮助的吗？

    '
  need_more_info: '为了更好地帮助您，我需要了解更多信息：

    {required_info}


    请提供这些信息，谢谢！

    '
special_scenarios:
  restart: '好的，让我们重新开始。

    之前的内容已清除，请告诉我您的新需求。

    '
  session_timeout: '会话已超时，为了保护您的信息安全。

    请重新开始我们的对话。

    '
  feature_upgrade: '系统功能已升级！

    现在我可以更好地帮助您处理需求。


    让我们开始吧！

    '
_metadata:
  file_version: '1.0'
  split_date: '2025-08-18T19:22:20.044293'
  source_blocks:
  - business_templates
  - error_fallback_templates
  - message_reply_system
  - message_templates
  - strategy_templates
  - system_fallback_templates
  config_type: templates
  total_items: 489
business_templates:
  acknowledge_and_redirect:
    fallback_template: '好的，请告诉我您的需求，我来帮您整理。

      '
    main_template: '好的，我明白了。让我们回到需求收集的主题上。请告诉我您希望整理哪些业务需求，或者您想从哪个方面开始？

      '
    simplified_template: '明白了。让我们专注于您的需求收集工作，请告诉我需要什么帮助？

      '
  empathy_and_clarify:
    fallback_template: '请告诉我您的具体需求，我会尽力帮助您。

      '
    main_template: '我理解您的情况。为了更好地帮助您，能否请您详细说明一下具体的需求或遇到的问题？这样我就能为您提供更精准的支持。

      '
    simplified_template: '我理解您的需要。请告诉我更多详情，这样我就能更好地协助您。

      '
  general_chat:
    fallback_template: '您好！我可以帮您整理业务需求，请告诉我需要什么帮助？

      '
    main_template: '感谢您与我交流！我是专业的需求采集助手。如果您有任何业务需求需要整理，或者想了解需求分析的相关问题，我很乐意为您提供帮助。

      '
    simplified_template: '很高兴与您交流！有什么我可以帮助您的吗？

      '
  rephrase_and_inquire:
    fallback_template: '让我换个角度理解您的需求，请用您最熟悉的方式描述一下这个项目的核心想法。

      '
    main_template: '让我重新理解一下您的需求：


      您提到的 "{message_preview}" 这个想法很有意思。


      **🔄 让我换个方式问：**

      1. 从用户的角度看，这个项目要解决什么痛点？

      2. 如果项目成功了，最直观的改变是什么？

      3. 您觉得这个项目的核心价值在哪里？


      **🎯 具体场景**

      - 能否举个具体的使用场景？

      - 典型的用户会在什么情况下需要这个？


      这样的表述是否更清楚一些？

      '
    simplified_template: '让我换个角度理解您的需求："{message_preview}"


      请用您最熟悉的方式描述一下这个项目的核心想法。

      '
  reset_conversation:
    error_template: '重置过程中遇到了一些问题，但我们可以重新开始。请告诉我您的新需求。

      '
    main_template: '好的，我们重新开始！


      我是AI需求采集助手，可以帮助您：

      🎯 整理和分析业务需求

      📋 制定详细的需求规格

      📝 生成规范的需求文档


      请告诉我您想要讨论的项目或需求，我们开始吧！

      '
    simplified_template: '好的，我们重新开始！请告诉我您想要讨论的项目或需求。

      '
error_fallback_templates:
  help_guidance: '我很乐意为您提供帮助！请告诉我您在需求收集方面需要什么支持？

    '
  provide_suggestions: '基于您的需求，我建议我们先明确项目的核心目标，然后制定详细的实施计划。您觉得从哪个方面开始最合适？

    '
  request_clarification: '为了给您更好的建议，我需要了解更多细节。请详细描述一下项目的具体需求和期望目标。

    '
  requirement_gathering_start: '我很乐意帮您整理需求！请详细描述一下您的项目背景和希望达到的目标，这样我就能为您提供更精准的指导。

    '
  skip_question: '我们换个话题，请从您最想聊的方面开始，详细描述一下您的项目想法。

    '
message_reply_system:
  a_b_testing:
    enabled: false
    test_groups:
      greeting:
        traffic_split: 0.5
        variant_a: greeting.basic
        variant_b: greeting.friendly
  analytics:
    enabled: true
    export_interval_hours: 24
    track_fallback_usage: true
    track_response_time: true
    track_user_satisfaction: true
  categories:
    clarification:
      description: 澄清类回复
      enabled: true
      fallback_template: clarification.request
      priority: 1
    completion:
      description: 完成类回复
      enabled: true
      fallback_template: confirmation.document_finalized
      priority: 1
    confirmation:
      description: 确认类回复
      enabled: true
      fallback_template: confirmation.reset
      priority: 1
    empathy:
      description: 共情类回复
      enabled: true
      fallback_template: empathy.fallback
      priority: 1
    error:
      description: 错误类回复
      enabled: true
      fallback_template: error.system
      priority: 1
    greeting:
      description: 问候类回复
      enabled: true
      fallback_template: greeting.basic
      priority: 1
    guidance:
      description: 引导类回复
      enabled: true
      fallback_template: guidance.initial
      priority: 1
  description: 统一消息配置文件
  enable_a_b_testing: false
  enable_analytics: true
  fallback_enabled: true
  generators:
    capabilities_generator:
      agent_name: capabilities_generator
      description: 能力说明生成
      enabled: true
      fallback_template: capabilities.explanation
      instruction: 详细介绍你的主要功能和能力，包括需求分析、信息收集、文档生成等核心能力，以及你支持的领域范围。
      max_tokens: 300
      temperature: 0.7
    chat_generator:
      agent_name: chat_generator
      description: 闲聊回复生成
      enabled: true
      fallback_template: chat.general
      instruction: 生成一个友好的闲聊回复，然后自然地引导用户回到系统的核心功能上，询问他们是否有需要帮助的项目需求。
      max_tokens: 150
      temperature: 0.7
    clarification_generator:
      agent_name: clarification_generator
      description: 澄清问题回复
      enabled: true
      fallback_template: clarification.request
      instruction: 请生成一个友好、专业的引导性问题，帮助用户明确需求领域。
      max_tokens: 200
      temperature: 0.7
    default_generator:
      agent_name: default_generator
      description: 通用动态回复生成
      enabled: true
      fallback_template: fallback.default
      instruction: 根据用户输入生成合适的回复。
      max_tokens: 200
      temperature: 0.7
    empathy_generator:
      agent_name: empathy_generator
      description: 共情并澄清问题
      enabled: true
      fallback_template: clarification.request
      instruction: 用户表达了负面情绪，请先共情再引导。
      max_tokens: 200
      temperature: 0.7
    greeting_generator:
      agent_name: greeting_generator
      description: 动态问候回复
      enabled: true
      fallback_template: greeting.basic
      instruction: 用户向你问候，请友好回应并简要介绍你是一个AI需求采集助手，专门帮助用户整理和分析业务需求。然后询问用户有什么需求需要帮助整理。回复要简洁、专业、友好。
      max_tokens: 150
      temperature: 0.7
    introduction_generator:
      agent_name: introduction_generator
      description: 自我介绍生成
      enabled: true
      fallback_template: introduction.full
      instruction: 生成一个简洁、专业的自我介绍，说明你是一个专注于需求分析和文档生成的AI助手，能够帮助用户梳理需求、生成规范文档。
      max_tokens: 200
      temperature: 0.7
  language: zh-CN
  last_updated: '2025-06-28'
  llm_timeout: 10
  max_retry_attempts: 3
  supported_languages:
  - zh-CN
  - en-US
  version: '2.0'
message_templates:
  capabilities_strategy:
    capability_introduction: '我是您的AI助手，具备以下核心能力：


      📋 **需求分析与收集**

      • 深入了解您的项目需求和目标

      • 通过专业问题收集关键信息

      • 帮助您梳理和完善需求细节


      💡 **专业建议与咨询**

      • 基于行业经验提供专业建议

      • 为您的项目提供技术选型指导

      • 协助制定实施策略和风险评估


      📚 **知识查询与解答**

      • 快速查找和整理相关信息

      • 回答各类技术和业务问题

      • 提供准确、及时的专业解答


      📝 **文档生成与整理**

      • 自动生成规范的需求文档

      • 整理项目方案和总结报告

      • 根据反馈调整和完善文档内容


      🎯 **支持的项目类型**

      • 软件开发：网站、APP、系统开发

      • 设计服务：UI设计、平面设计、品牌设计

      • 营销策划：活动策划、品牌推广、内容营销

      • 法律咨询：合同审查、知识产权、风险评估


      请告诉我您的具体需求，我会为您提供专业的分析和建议！

      '
  clarification:
    default: '为了更好地为您服务，我需要了解一下您的具体需求：


      **请告诉我您希望在以下哪个方面得到帮助：**


      🎨 **创意设计** - Logo设计、海报制作、品牌视觉、UI界面设计

      💻 **技术开发** - 网站建设、手机APP、小程序、软件系统

      📢 **营销推广** - 活动策划、广告投放、品牌推广、市场营销

      ⚖️ **法律服务** - 合同起草、知识产权、法律咨询、风险评估


      您可以直接选择类型，也可以用自己的话描述具体需求，我会为您提供专业的需求分析和建议。

      '
    detailed_clarification: 我理解您的问题，但需要更多信息来为您提供准确的帮助。请告诉我更多关于您具体需求的详细信息。
    document_refinement: 非常抱歉文档未能让您满意。为了能更正错误，您能具体指出需要修改的部分吗？
    general_request: 抱歉我的问题可能不够清晰。让我换一种方式问：请您详细描述一下您的具体需求，这样我能更好地为您提供帮助。
    need_more_info: 我理解您的请求，但需要更多信息来为您提供准确的帮助。请告诉我更多关于您具体需求的详细信息。
    request: 抱歉我的问题可能不够清晰。让我换一种方式问：请您详细描述一下您的具体需求，这样我能更好地为您提供帮助。
  confirmation:
    document_finalized: 感谢您的确认！需求文档已最终确定。如果您有新的需求，请随时告诉我。
    reset: 好的，我已经重置了会话状态。现在我们可以开始新的需求采集。请告诉我您的需求是什么？
    restart: 好的，我已经重置了会话状态。现在我们可以开始新的需求采集。请告诉我您的需求是什么？
  error:
    document_generation_failed: '抱歉，文档生成失败，请稍后重试。您可以选择：

      1. 重新尝试生成文档

      2. 修改需求后重试'
    document_generation_not_initialized: 文档生成器未初始化，无法生成文档。
    document_modification: 抱歉，文档修改失败。请重新描述您的修改需求。
    emergency_fallback: 抱歉，我遇到了一些问题。请您重新描述一下需求，我会尽力帮助您。
    general_fallback: 抱歉，我遇到了一些问题。请告诉我您的具体需求，我会尽力帮助您。
    general_request_processing: 处理您的请求时遇到问题，请稍后再试或重新描述您的需求。
    general_unknown: 抱歉，系统遇到了一个未知错误。请稍后再试，或者重新描述您的需求。
    knowledge_base_not_found: 抱歉，我在知识库中没有找到相关信息。
    message_processing: 抱歉，处理消息时遇到了问题。
    modification: 抱歉，修改文档时出现错误，请稍后再试。
    processing: '处理您的请求时出错: {error_msg}'
    request_processing: 抱歉，无法处理您的请求。
    safety:
      blocked_hate_speech: 检测到不当言论，请保持友善和尊重的交流方式。我们致力于为所有用户提供安全的服务环境。
      blocked_profanity: 为了保持文明与安全的沟通环境，请避免使用不当用语。您可以换一种表达方式，我们很乐意继续协助您。
      blocked_sexual_content: 请避免涉及不当内容的表达。让我们专注于您的业务需求，我会为您提供专业的帮助。
      blocked_violence: 请避免使用威胁或暴力性语言。让我们保持友好的交流，专注于解决您的实际需求。
      pii_warning: 检测到可能的个人敏感信息，已为您做脱敏处理以保护隐私。请避免在对话中提供身份证、银行卡等敏感信息。
      self_harm_support: '我注意到您可能遇到了困难。如果您正在经历情绪困扰，建议您：


        🆘 **紧急求助**：

        • 全国心理危机干预热线：400-161-9995

        • 北京危机干预热线：400-161-9995

        • 上海心理援助热线：021-64383562


        💙 **专业帮助**：

        • 寻求专业心理咨询师的帮助

        • 联系当地心理健康服务机构

        • 与信任的朋友或家人交流


        如果您有业务需求需要整理，我也很乐意为您提供专业的需求分析服务。

        '
      warning_jailbreak: 请直接描述您的业务需求，我会为您提供专业的需求分析服务。
      warning_profanity: 检测到不当用语，已为您做适当处理。以下是对您需求的回复：
      warning_violence: 请注意用词，让我们保持友好交流。以下是对您需求的回复：
    system: 系统处理您的请求时遇到问题，请稍后再试。
  greeting:
    ai_assistant: 您好！我是由己AI助手，可以帮助您完成各种任务。请问有什么可以帮您的吗？
    basic: 您好！我是AI需求采集助手，专门帮助您整理和分析业务需求。请问有什么需求需要帮助整理？
    enthusiastic: 您好！很高兴为您服务！我是AI需求分析师，可以帮助您分析和整理项目需求，请告诉我您的想法。
    friendly: 您好！很高兴为您服务！我是AI需求分析师，可以帮助您分析和整理项目需求，请告诉我您的想法。
    general_assistant: 您好！我是由己AI助手，可以帮助您完成各种任务。请问有什么可以帮您的吗？
    new_project: 您好！我是专业的需求分析助手，可以帮您梳理项目需求、生成规范文档。请问您有什么需求需要我协助处理？
    professional: 您好！我是专业的需求分析助手，可以帮您梳理项目需求、生成规范文档。请问您有什么需求需要我协助处理？
    project_focused: 您好！我是专业的需求分析助手，可以帮您梳理项目需求、生成规范文档。请问您有什么需求需要我协助处理？
    requirement_analyst: 您好！我是专业的需求分析助手，可以帮您梳理项目需求、生成规范文档。请问您有什么需求需要我协助处理？
    service_oriented: 您好！我是专业的需求分析助手，可以帮您梳理项目需求、生成规范文档。请问您有什么需求需要我协助处理？
    service_ready: 您好！我是专业的需求分析助手，可以帮您梳理项目需求、生成规范文档。请问您有什么需求需要我协助处理？
    simple: 您好！我是AI需求采集助手，请问有什么需要帮助的？
    standard: 您好！我是AI需求采集助手，专门帮助您整理和分析业务需求。请问有什么需求需要帮助整理？
    welcome_service: 欢迎使用我们的服务！有什么可以帮助您的吗？
  guidance:
    default_requirement_prompt: 请描述您的详细需求。
    initial: 请您先告诉我您想做什么，例如"我想开发一个软件"或"我需要设计一个Logo"。
    proactive_suggestions:
      completion_guidance: '太棒了！我们已经收集了丰富的需求信息。现在我来为您生成专业的需求文档。


        📄 **文档将包含**：

        • 项目概述和目标

        • 详细需求描述

        • 功能规格说明

        • 预估时间和成本

        • 实施建议


        文档生成后，您可以：

        ✅ 确认文档内容

        🔧 指出需要修改的部分

        💬 提出补充意见


        正在为您生成文档，请稍候...

        '
      next_steps_suggestion: '很好！基于您提供的信息，我建议我们按以下步骤进行：


        📋 **接下来的步骤**：

        1. 明确项目的核心目标和预期效果

        2. 确定目标用户群体和使用场景

        3. 梳理具体的功能需求和技术要求

        4. 讨论时间安排和预算考虑


        让我们从第一步开始，请详细说明您希望通过这个项目实现什么目标？

        '
      welcome_with_examples: '您好！我是AI需求分析师，专门帮助您梳理和分析项目需求。


        💡 **常见需求类型**：

        • 📱 软件开发：移动应用、网站、系统开发

        • 🎨 设计服务：Logo设计、UI设计、宣传物料

        • 📊 数据分析：报表制作、数据可视化

        • 🛒 电商项目：网店搭建、营销策划


        请告诉我您想要做什么项目？我会根据您的需求提供专业的分析和建议。

        '
    specific_requirement_help: 请描述您的具体需求，我将为您提供帮助。
  system:
    action_executor:
      failed: 'Action {action} 执行失败: {error}'
      success: 'Action {action} 执行成功，耗时: {duration}s'
    document:
      confirmation_prefix: '根据您提供的信息，我已生成需求文档。请查看并确认：


        '
      content_error: 获取文档内容时出现错误
      content_retrieval: 正在获取文档内容
      finalized: 感谢您的确认！需求文档已最终确定。如果您有新的需求，请随时告诉我。
      generated: 所有必要信息已收集完毕，我已根据您提供的信息生成了需求文档。请查看并确认：
      generation_error: 文档生成过程中出现错误
      generation_start: 开始生成文档
      guidance: '


        ---

        文档操作指引：

        ✅ 输入''确认'' - 确认文档无误并完成

        🔧 指出需要修改的部分 - 例如''修改功能描述部分'''
      project_name_default: 用户项目
      project_name_template: '{category_name}项目'
    error: 抱歉，系统遇到了一些问题，请稍后再试。
    initialization:
      action_executor_success: ActionExecutor 初始化成功
    processing:
      current_state_action: '当前状态 {state} 执行动作: {action}'
      fallback_handling: '未匹配到特定意图，使用兜底处理: {fallback_intent}'
      general_decision_engine: 状态 {state} 使用通用意图决策引擎
      intent_detected: '检测到意图: {intent}'
      message_received: '收到新消息: ''{message}'', session: {session_id}'
      operation_failed: 操作执行失败，请稍后重试。
      special_state_logic: 状态 {state} 使用特殊处理逻辑
    session:
      clear_domain_success: 领域和类别信息已清除
      clear_messages_success: 消息历史已清除
      no_domain_info: 数据库中未找到会话 {session_id} 的领域/类别信息。
      reset_complete: 会话重置完成
      restart_request: 用户 {session_id} 请求重新开始对话
      restore_success: '从数据库恢复会话状态 - 领域: {domain}, 类别: {category}'
    state:
      db_update_success: 数据库状态更新成功
      transition: '状态转换: {from_state} -> {to_state}'
      update_success: '状态更新成功: {new_state}'
    timeout: 处理时间过长，请重新提交您的请求。
    welcome: '您好！我是AI需求分析师，专门帮助您梳理和分析项目需求。


      💡 **我可以帮您**：

      • 📋 需求梳理：将想法整理成清晰的需求文档

      • 🎯 专业分析：提供行业经验和最佳实践建议

      • 📊 成本评估：预估项目时间和预算范围

      • 🔍 风险识别：提前发现潜在问题和解决方案


      🎨 **常见项目类型**：

      • **软件开发**：网站建设、移动App、管理系统

      • **设计服务**：Logo设计、UI界面、宣传海报

      • **营销策划**：品牌推广、活动策划、内容营销

      • **数据分析**：报表制作、数据可视化、业务分析


      ⏱️ **整个流程大约需要5-10分钟**，我会通过几个关键问题了解您的需求，最终生成专业的需求文档。


      请告诉我您想要做什么项目？我会根据您的具体情况提供专业的分析和建议。

      '
strategy_templates:
  capabilities_strategy:
    detailed_explanations:
      consultation: 基于丰富的知识库，我能为您提供专业的建议和方案，涵盖技术选型、实施策略、风险评估等多个方面。
      continuous_support: 在整个项目过程中，我会持续为您提供支持，解答疑问，协助解决问题。
      document_generation: 我可以帮助您生成各类文档，如需求文档、方案书、总结报告等，提高您的工作效率。
      knowledge_search: 我拥有广泛的知识库，可以快速查找和整理各类信息，为您提供准确、及时的答案。
      requirement_collection: 我可以通过对话了解您的具体需求，包括项目类型、功能要求、预算范围、时间计划等，并帮助您梳理和完善需求细节。
    patterns:
      inquiry_patterns:
      - 你.*能.*
      - 你.*会.*
      - 你.*可以.*
      - 有.*功能
      - 支持.*
      - 提供.*
      - 介绍.*
      - 展示.*
      - 说明.*
    templates:
      core_abilities:
      - 我是一个智能AI助手，主要能力包括：
      - 作为您的AI助手，我可以为您提供以下服务：
      - 我具备多种能力，可以在以下方面为您提供帮助：
      interaction_features:
      - ✨ 智能对话 - 理解您的需求并提供针对性回复
      - 🔍 信息检索 - 快速查找和整理相关信息
      - 📊 数据分析 - 帮助分析和解读各类数据
      - 🎨 创意支持 - 为您的创意项目提供灵感和建议
      - ⚡ 快速响应 - 7x24小时随时为您服务
      specific_services:
      - 📋 需求收集与分析 - 帮助您梳理项目需求和想法
      - 💡 方案建议与咨询 - 为您的项目提供专业建议
      - 📚 知识查询与解答 - 回答各类问题，提供信息支持
      - 🎯 任务规划与指导 - 协助您制定计划和执行步骤
      - 📝 文档整理与生成 - 帮助您整理和生成各类文档
      - 🤝 沟通协调与支持 - 在项目过程中提供持续支持
      use_cases:
      - 🏢 企业项目规划 - 帮助企业梳理数字化转型需求
      - 💻 技术方案咨询 - 为开发项目提供技术建议
      - 🎨 创意设计支持 - 协助设计项目的需求分析
      - 📈 业务流程优化 - 分析和改进业务流程
      - 🔧 问题诊断解决 - 协助排查和解决各类问题
  emotional_support_strategy:
    templates:
      positive_guidance:
      - 不过，我相信我们一定能找到解决办法的。
      - 让我们把注意力转向解决方案上。
      - 虽然现在有些困难，但我会全力协助您。
      - 每个问题都有解决的方法，我们一起努力。
      - 相信自己，您比想象中更坚强。
      response_templates:
        confused:
        - 没关系，有不明白的地方很正常。我会用更简单的方式为您说明。
        - 您的困惑我能理解。让我换个角度来解释，看看是否更清楚。
        - 不用担心，每个人都会遇到不理解的地方。让我们一步步来解决。
        negative:
          anger:
          - 感受到您的不满了。有时候遇到困难确实会让人生气，我会尽力帮您找到解决方案。
          - 您的感受完全可以理解。让我们冷静下来，一步步分析问题所在。
          - 我理解您现在的愤怒情绪。让我们一起找到问题的根源和解决方案。
          anxiety:
          - 感受到您的不安了。有时候未知的事情确实会让人紧张，我们可以一步步来处理。
          - 您的担心我能理解。让我们把问题分解一下，这样会更容易应对。
          - 焦虑是很正常的情绪反应。让我们一起制定一个应对计划，这样您会感觉更有控制感。
          frustration:
          - 我能感受到您的挫败感。遇到困难时感到烦躁是很自然的，让我来帮您理清思路。
          - 看得出您现在很烦躁。有时候事情确实会让人抓狂，我们一起想办法解决。
          - 您的感受我完全理解。让我们暂停一下，重新整理思路。
          sadness:
          - 我能感受到您现在的难过。虽然我不能完全理解您的感受，但我会陪伴您度过这个困难时期。
          - 看到您这样难过，我也很心疼。请相信，困难总会过去的，我会尽我所能帮助您。
          - 每个人都会有低落的时候，这很正常。让我们一起想想有什么可以让您感觉好一些的。
        positive:
          gratitude:
          - 不用客气！能够帮助您是我的荣幸。有任何需要随时找我。
          - 您的感谢让我很温暖！这就是我存在的意义，为您提供帮助。
          - 谢谢您的认可！我会继续努力为您提供更好的服务。
          happiness:
          - 太好了！看到您这么开心，我也很高兴。请分享一下是什么让您如此愉快？
          - 您的好心情感染了我！有什么开心的事情想要分享吗？
          - 真为您感到高兴！保持这种积极的心态，一切都会更顺利的。
          satisfaction:
          - 很高兴您对结果满意！您的认可是对我最大的鼓励。
          - 太棒了！看到您满意的样子，我也很有成就感。
          - 您的满意就是我的目标！有其他需要帮助的地方吗？
        tired:
        - 看得出您很疲惫。要不要先休息一下？我随时在这里等您。
        - 感受到您的疲劳了。累的时候效率会下降，适当休息很重要。
        - 您辛苦了！如果现在不方便处理，我们可以稍后再继续。
  fallback_strategy:
    templates:
      error_templates:
      - 系统处理时遇到了一些问题，请您稍后再试或换个方式描述。
      - 系统暂时无法处理您的请求，请您稍等片刻或重新表述。
      - 遇到了一些技术问题，请您稍后再试，或者用其他方式描述您的需求。
      fallback_templates:
        capability_hint:
        - '我可以帮助您：

          • 解答各类问题

          • 提供专业建议

          • 协助需求分析

          • 查找相关信息


          请告诉我您需要哪方面的帮助？'
        - '我的主要能力包括：

          • 问题咨询与解答

          • 项目需求收集

          • 信息查询与整理

          • 方案建议与分析


          有什么具体需要协助的吗？'
        - '我可以在以下方面为您提供帮助：

          • 技术咨询

          • 需求梳理

          • 信息搜索

          • 问题解决


          请说明您的具体需求。'
        clarification:
        - 我想更好地帮助您，请问您具体需要什么帮助？
        - 为了给您提供准确的帮助，请您详细描述一下您的需求。
        - 我需要更多信息才能为您提供合适的帮助，请您补充说明一下。
        - 请您再详细说明一下，这样我就能更好地协助您了。
        encouragement:
        - 虽然我暂时没有理解您的具体需求，但我会努力帮助您。请再试着描述一下？
        - 每个问题都有解决的方法，让我们一起来找到答案。请详细说明您的情况。
        - 我相信我们能够找到解决方案。请您耐心地再解释一下您的需求。
        - 不用担心，我会尽力理解并帮助您。请用不同的方式描述一下您的问题。
        general_help:
        - 我是AI助手，可以帮助您处理各种需求。请告诉我您需要什么帮助？
        - 我可以协助您解决问题、回答疑问或提供建议。有什么可以帮您的吗？
        - 作为您的AI助手，我随时准备为您提供帮助。请说明您的具体需求。
        - 我很乐意为您提供帮助！请告诉我您遇到了什么问题或需要什么服务。
        redirect:
        - 如果您有具体的项目需求，我可以帮您详细分析。
        - 如果您需要查询某些信息，我可以为您搜索。
        - 如果您遇到了技术问题，我可以协助诊断。
        - 如果您需要咨询建议，我可以提供专业意见。
      scenario_guides:
        consultation: 如果您需要咨询建议，请详细说明您的情况和疑问。
        information_search: 如果您需要查找信息，请告诉我您想了解什么内容。
        project_inquiry: 如果您想了解项目开发相关信息，请告诉我项目类型和具体需求。
        technical_support: 如果您遇到技术问题，请描述具体的错误现象或困难。
  greeting_strategy:
    greeting_type_detection:
      casual_indicators:
      - hi
      - hello
      - 嗨
      - hey
      - 哈喽
      formal_indicators:
      - 您好
      - 请问
      - 打扰
      - 麻烦
      time_based_indicators:
      - 早上
      - 下午
      - 晚上
      - 早安
      - 晚安
    parameters:
      confidence_factors:
        conversation_state:
          idle: 0.7
          other: 0.4
        intent_match: 0.9
        keyword_match: 0.8
        message_length:
          long: 0.3
          short: 0.6
          very_short: 0.8
      message_length_thresholds:
        max_greeting_length: 10
        short: 15
        very_short: 5
    response_mapping:
      default: greeting.basic
      emotion_based:
        negative: greeting.professional
        neutral: greeting.basic
        positive: greeting.service_ready
      time_based:
        afternoon: greeting.friendly
        evening: greeting.professional
        morning: greeting.basic
  knowledge_base_strategy:
    patterns:
      question_patterns:
      - .*\?$
      - ^(什么|怎么|如何|为什么|哪里|哪个|多少).*
      - .*(吗|呢)\?*$
      - ^(请问|想问|咨询).*
    templates:
      response_templates:
        faq:
        - 我来为您解答这个常见问题。
        - 关于这个疑问，我可以为您详细说明。
        - 让我为您提供相关的解答。
        features:
        - 我来为您介绍我们的功能和服务。
        - 关于功能特点，我可以为您详细说明。
        - 让我为您展示我们能提供的服务。
        how_to:
        - 我来为您介绍具体的操作方法和步骤。
        - 关于使用方法，我可以为您详细说明。
        - 让我为您提供详细的操作指导。
        pricing:
        - 关于价格和套餐，我来为您详细介绍。
        - 我可以为您说明收费标准和套餐选择。
        - 让我为您介绍相关的价格信息。
        registration:
        - 关于注册流程，我来为您详细介绍。
        - 我可以指导您完成注册相关的操作。
        - 让我为您说明账户注册的具体步骤。
        support:
        - 我很乐意为您提供技术支持和帮助。
        - 关于您的问题，我会尽力协助解决。
        - 让我来帮您解决遇到的问题。
        what_is:
        - 我来为您介绍相关的功能和服务。
        - 关于这个问题，我可以为您详细解释。
        - 让我为您说明相关的概念和功能。
      search_prompts:
        complex: 您的问题比较复杂，我来为您查找详细的资料。
        general: 我来为您搜索相关的知识和解答。
        specific: 我正在为您查找相关信息，请稍等...
  requirement_strategy:
    patterns:
      requirement_patterns:
      - 我想.*
      - 我需要.*
      - 帮我.*
      - 能否.*
      - 可以.*吗
      - 怎么.*
      - 如何.*
    templates:
      collection_questions:
        consulting:
        - 好的，我来为您提供咨询服务。请问您遇到了什么问题？
        - 明白了，关于咨询需求，能详细描述一下您的情况吗？
        - 我可以为您分析问题，请问您希望在哪个方面得到建议？
        content:
        - 好的，我来帮您了解内容需求。请问您需要什么类型的内容？
        - 明白了，关于内容创作，能说说您的目标和要求吗？
        - 我可以协助您规划内容，请问有什么具体的主题或方向？
        design:
        - 好的，我来帮您了解设计需求。请问您需要设计什么类型的作品？
        - 明白了，关于设计项目，能说说您的风格偏好和用途吗？
        - 我可以协助您整理设计需求，请问有什么具体的要求？
        development:
        - 好的，我来帮您了解开发需求。请问您想开发什么类型的项目？
        - 明白了，关于开发项目，能详细说说您的具体需求吗？
        - 我可以协助您梳理开发需求，请问项目的主要功能是什么？
      confirmation_templates:
      - 我已经了解了您的基本需求，让我来帮您进一步梳理详细信息。
      - 根据您的描述，我来协助您完善需求细节。
      - 好的，我会帮您收集更多信息以便提供更好的服务。
system_fallback_templates:
  clarification_generation_fallback: '请告诉我您具体需要什么类型的服务：设计、开发、营销还是法律？

    '
  config_loading_fallback: '配置加载遇到问题，但我仍可以为您提供基础的需求收集服务。请告诉我您的项目需求。

    '
  default_system_message: '我是AI需求采集助手，专门帮助您整理和分析业务需求。

    '
  emergency_fallback: '抱歉，我遇到了一些问题。请您重新描述一下需求，我会尽力帮助您。

    '
  system_initialization_fallback: '系统正在初始化中，请稍后再试。如有紧急需求，请详细描述您的项目情况。

    '
