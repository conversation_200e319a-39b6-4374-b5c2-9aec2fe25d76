# ============================================================================
# 系统安全配置（从unified_config.yaml拆分）
# ============================================================================
# 文件说明：从unified_config.yaml拆分的系统安全配置
# 拆分时间：2025-08-18 19:22:20
# 源配置块：security
# 维护责任：配置管理团队
# 更新频率：根据业务需求
# ============================================================================

authentication:
  enabled: true
  session_timeout: 7200
  max_login_attempts: 5
  lockout_duration: 300
  password_policy:
    min_length: 8
    require_uppercase: true
    require_lowercase: true
    require_numbers: true
    require_special_chars: false
  token:
    expiry: 3600
    refresh_enabled: true
    refresh_threshold: 300
authorization:
  enabled: true
  default_role: user
  roles:
    admin:
      permissions:
      - read
      - write
      - delete
      - admin
    user:
      permissions:
      - read
      - write
    guest:
      permissions:
      - read
  access_control:
    config_read:
    - admin
    - user
    config_write:
    - admin
    system_admin:
    - admin
data_protection:
  encryption_enabled: true
  data_masking: true
  sensitive_fields:
  - api_key
  - password
  - token
  - secret
  - private_key
  masking_rules:
    api_key: sk-****
    password: '****'
    email: '*@*.com'
audit:
  enabled: true
  log_all_requests: false
  log_sensitive_operations: true
  events:
    login: true
    logout: true
    config_change: true
    admin_operation: true
    security_violation: true
  log_file: logs/audit.log
  log_rotation: true
  retention_days: 90
security_limits:
  rate_limiting:
    enabled: true
    requests_per_minute: 60
    requests_per_hour: 1000
    burst_limit: 10
  content_filter:
    enabled: true
    max_input_length: 1000
    blocked_patterns: []
  ip_control:
    whitelist_enabled: false
    blacklist_enabled: true
    allowed_ips: []
    blocked_ips: []
encryption:
  algorithm: AES-256
  key_rotation_enabled: false
  key_rotation_interval: 2592000
  transport:
    tls_enabled: true
    min_tls_version: '1.2'
    cipher_suites:
    - ECDHE-RSA-AES256-GCM-SHA384
security_monitoring:
  enabled: true
  threat_detection:
    brute_force_detection: true
    anomaly_detection: false
    suspicious_activity_detection: true
  alerts:
    enabled: true
    email_notifications: false
    log_alerts: true
  metrics:
    failed_login_attempts: true
    security_violations: true
    blocked_requests: true
_metadata:
  file_version: '1.0'
  split_date: '2025-08-18T19:22:20.109411'
  source_blocks:
  - security
  config_type: security
  total_items: 29
security:
  access_control:
    max_requests_per_minute: 60
    rate_limiting: true
  content_moderation:
    actions:
      hate_speech: BLOCK
      jailbreak: WARN
      pii_patterns: MASK
      profanity: WARN
      self_harm: WARN
      sexual_content: BLOCK
      violence: WARN
    default_action: WARN
    enabled: true
    logging:
      include_original_text: false
      log_level: INFO
      log_violations: true
    masking:
      keep_first_char: true
      min_mask_length: 2
      replacement: '*'
  data_protection:
    encrypt_sensitive: true
    mask_personal_info: true
  input_validation:
    enabled: true
    forbidden_patterns: []
    max_length: 10000
