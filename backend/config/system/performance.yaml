# ============================================================================
# 系统性能配置（从unified_config.yaml拆分）
# ============================================================================
# 文件说明：从unified_config.yaml拆分的系统性能配置
# 拆分时间：2025-08-18 19:22:20
# 源配置块：performance
# 维护责任：配置管理团队
# 更新频率：根据业务需求
# ============================================================================

cache:
  enabled: true
  default_ttl: 3600
  max_size: 1000
  categories:
    config_cache:
      ttl: 7200
      max_size: 100
    llm_response_cache:
      ttl: 1800
      max_size: 500
    template_cache:
      ttl: 3600
      max_size: 200
    business_rule_cache:
      ttl: 3600
      max_size: 100
timeout:
  default: 5
  short: 3
  medium: 10
  long: 30
  very_long: 60
  api_request: 10
  database: 5
  file_operation: 5
  llm_service: 30
retry:
  default: 3
  quick_retry: 2
  persistent_retry: 5
  api_call: 2
  database_operation: 3
  file_access: 2
  llm_request: 3
  max_attempts: 3
  backoff_factor: 1.5
  initial_delay: 1
  max_delay: 30
concurrency:
  max_concurrent: 5
  max_concurrent_queries: 5
  thread_pool_size: 10
  connection_pool_size: 20
monitoring:
  enabled: true
  metrics_collection: true
  performance_logging: true
  slow_query_threshold: 1.0
  metrics:
    response_time: true
    throughput: true
    error_rate: true
    cache_hit_rate: true
    memory_usage: true
    cpu_usage: true
limits:
  max_memory_usage: 512MB
  max_heap_size: 256MB
  max_request_per_minute: 1000
  max_request_per_hour: 10000
  max_concurrent_requests: 50
  max_query_results: 50
  max_batch_size: 100
  max_file_size: 10MB
optimization:
  preload_enabled: true
  preload_critical_configs: true
  preload_templates: true
  response_compression: true
  static_file_compression: true
  keep_alive: true
  connection_reuse: true
_metadata:
  file_version: '1.0'
  split_date: '2025-08-18T19:22:20.103890'
  source_blocks:
  - performance
  config_type: performance
  total_items: 22
performance:
  agent_cache:
    cleanup_interval_minutes: 5
    enable_component_cache: true
    enable_metrics: true
    enable_session_cache: true
    max_cached_sessions: 100
    max_memory_mb: 500
    memory_check_interval: 10
    metrics_report_interval: 50
    session_timeout_minutes: 30
  cache:
    enabled: true
    max_size: 1000
    ttl: 3600
  concurrency:
    max_workers: 4
    queue_size: 100
  monitoring:
    enabled: true
    log_slow_queries: true
    metrics_interval: 60
    slow_query_threshold: 1.0
