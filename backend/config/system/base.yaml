# ============================================================================
# 系统基础配置（从unified_config.yaml拆分）
# ============================================================================
# 文件说明：从unified_config.yaml拆分的系统基础配置
# 拆分时间：2025-08-18 19:22:20
# 源配置块：development, integrations, knowledge_base, system
# 维护责任：配置管理团队
# 更新频率：根据业务需求
# ============================================================================

system:
  debug_mode: false
  decision_engine:
    cache_ttl: 300
    enable_caching: true
    fallback_to_simplified: true
    type: simplified
  description: 需求采集系统统一配置
  fallback_enabled: true
  language: zh-CN
  last_updated: '2025-07-20'
  logging:
    backup_count: 5
    format: json
    level: INFO
    max_file_size: 10MB
  performance:
    cache_enabled: true
    cache_ttl: 3600
    llm_timeout: 10
    max_conversation_turns: 15
    max_message_length: 1000
    max_retry_attempts: 3
  supported_languages:
  - zh-CN
  - en-US
  use_structured_classification: true
  version: '3.0'
logging:
  level: INFO
  format: json
  max_file_size: 10MB
  backup_count: 5
  files:
    app_log: logs/app.log
    error_log: logs/error.log
    session_log: logs/session.log
    performance_log: logs/performance/performance.log
  loggers:
    root: INFO
    backend: INFO
    agents: INFO
    handlers: INFO
    config: DEBUG
development:
  debug:
    log_requests: false
    log_responses: false
    mock_llm: false
  testing:
    mock_external_apis: false
    use_test_db: false
production:
  hot_reload: false
  debug_toolbar: false
  profiling_enabled: true
  mock_external_services: false
_metadata:
  file_version: '1.0'
  split_date: '2025-08-18T19:22:20.070888'
  source_blocks:
  - development
  - integrations
  - knowledge_base
  - system
  config_type: base
  total_items: 79
integrations:
  external_apis:
    openai:
      retry_attempts: 3
      timeout: 30
  knowledge_base:
    enabled: true
    update_interval: 3600
knowledge_base:
  chroma_db:
    collection_name: hybrid_knowledge_base
    embedding_model: moka-ai/m3e-base
    path: backend/data/chroma_db
  document_processing:
    chunk_overlap: 100
    chunk_size: 800
    max_chunks_per_doc: 50
    supported_formats:
    - md
    - txt
  enabled: true
  features:
    document_ingestion: false
    intent_enhancement: false
    mode_switching: false
    rag_query: true
  logging:
    level: INFO
    log_queries: false
    log_results: false
  performance:
    cache_enabled: true
    cache_ttl: 3600
    default_limit: 5
    max_concurrent_queries: 5
    max_results: 50
  retrieval:
    max_context_length: 4000
    similarity_threshold: 0.7
    top_k: 5
  role_filters:
    allowed_roles: []
    enabled: false
  safety:
    enable_content_filter: true
    max_query_length: 1000
    rate_limit_per_minute: 60
