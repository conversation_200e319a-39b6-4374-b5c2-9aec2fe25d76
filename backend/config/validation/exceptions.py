#!/usr/bin/env python3
"""
配置验证异常类

定义配置验证过程中可能出现的各种异常类型
"""

from typing import List, Dict, Any, Optional


class ConfigValidationError(Exception):
    """配置验证基础异常"""
    
    def __init__(self, message: str, config_key: Optional[str] = None, 
                 config_value: Optional[Any] = None):
        super().__init__(message)
        self.config_key = config_key
        self.config_value = config_value
        self.message = message
    
    def __str__(self):
        if self.config_key:
            return f"配置验证错误 [{self.config_key}]: {self.message}"
        return f"配置验证错误: {self.message}"


class SchemaValidationError(ConfigValidationError):
    """模式验证错误"""
    
    def __init__(self, message: str, schema_name: str, 
                 validation_errors: List[Dict[str, Any]], 
                 config_key: Optional[str] = None):
        super().__init__(message, config_key)
        self.schema_name = schema_name
        self.validation_errors = validation_errors
    
    def get_detailed_errors(self) -> List[str]:
        """获取详细错误信息"""
        errors = []
        for error in self.validation_errors:
            loc = " -> ".join(str(x) for x in error.get('loc', []))
            msg = error.get('msg', '未知错误')
            input_val = error.get('input', '')
            
            if loc:
                errors.append(f"字段 '{loc}': {msg} (输入值: {input_val})")
            else:
                errors.append(f"{msg} (输入值: {input_val})")
        
        return errors


class RequiredFieldMissingError(ConfigValidationError):
    """必需字段缺失错误"""
    
    def __init__(self, field_name: str, config_section: str):
        message = f"必需字段 '{field_name}' 在配置节 '{config_section}' 中缺失"
        super().__init__(message, field_name)
        self.field_name = field_name
        self.config_section = config_section


class InvalidTypeError(ConfigValidationError):
    """无效类型错误"""
    
    def __init__(self, field_name: str, expected_type: str, 
                 actual_type: str, actual_value: Any):
        message = f"字段 '{field_name}' 类型错误: 期望 {expected_type}, 实际 {actual_type}"
        super().__init__(message, field_name, actual_value)
        self.field_name = field_name
        self.expected_type = expected_type
        self.actual_type = actual_type


class ValueConstraintError(ConfigValidationError):
    """值约束错误"""
    
    def __init__(self, field_name: str, constraint_description: str, 
                 actual_value: Any):
        message = f"字段 '{field_name}' 值约束错误: {constraint_description} (实际值: {actual_value})"
        super().__init__(message, field_name, actual_value)
        self.field_name = field_name
        self.constraint_description = constraint_description


class CircularReferenceError(ConfigValidationError):
    """循环引用错误"""
    
    def __init__(self, reference_chain: List[str]):
        chain_str = " -> ".join(reference_chain)
        message = f"检测到循环引用: {chain_str}"
        super().__init__(message)
        self.reference_chain = reference_chain


class UnknownConfigKeyError(ConfigValidationError):
    """未知配置键错误"""
    
    def __init__(self, config_key: str, valid_keys: List[str]):
        message = f"未知配置键 '{config_key}'"
        if valid_keys:
            suggestions = [key for key in valid_keys if config_key.lower() in key.lower()]
            if suggestions:
                message += f", 可能的建议: {', '.join(suggestions[:3])}"
        super().__init__(message, config_key)
        self.valid_keys = valid_keys


class ConfigIntegrityError(ConfigValidationError):
    """配置完整性错误"""
    
    def __init__(self, message: str, missing_dependencies: List[str] = None,
                 conflicting_configs: List[str] = None):
        super().__init__(message)
        self.missing_dependencies = missing_dependencies or []
        self.conflicting_configs = conflicting_configs or []


class EnvironmentVariableError(ConfigValidationError):
    """环境变量错误"""
    
    def __init__(self, env_var_name: str, message: str):
        full_message = f"环境变量 '{env_var_name}' 错误: {message}"
        super().__init__(full_message, env_var_name)
        self.env_var_name = env_var_name
