#!/usr/bin/env python3
"""
配置验证框架

基于Pydantic的配置验证系统，提供：
1. 配置模式定义和验证
2. 类型检查和约束验证
3. 人类可读的错误报告
4. 配置完整性检查
5. 环境变量验证支持

使用方式：
```python
from backend.config.validation import ConfigValidator, get_config_validator

validator = get_config_validator()
result = validator.validate_config(config_data)
if not result.is_valid:
    print(result.get_error_report())
```
"""

from .validator import ConfigValidator, ValidationResult, get_config_validator
from .schemas import (
    AppConfigSchema,
    DatabaseConfigSchema, 
    LLMConfigSchema,
    SystemConfigSchema,
    BusinessConfigSchema,
    SecurityConfigSchema,
    KnowledgeBaseConfigSchema
)
from .exceptions import (
    ConfigValidationError,
    SchemaValidationError,
    RequiredFieldMissingError,
    InvalidTypeError,
    ValueConstraintError
)

__all__ = [
    'ConfigValidator',
    'ValidationResult', 
    'get_config_validator',
    'AppConfigSchema',
    'DatabaseConfigSchema',
    'LLMConfigSchema', 
    'SystemConfigSchema',
    'BusinessConfigSchema',
    'SecurityConfigSchema',
    'KnowledgeBaseConfigSchema',
    'ConfigValidationError',
    'SchemaValidationError',
    'RequiredFieldMissingError',
    'InvalidTypeError',
    'ValueConstraintError'
]
