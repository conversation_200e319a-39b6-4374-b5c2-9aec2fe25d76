#!/usr/bin/env python3
"""
配置验证负例用例集

包含常见的配置错误样例和期望的错误信息，用于测试验证系统的错误报告质量
"""

import yaml
import json
from typing import Dict, List, Any, Tuple
from pathlib import Path
import tempfile
import os

from .schemas import (
    BusinessConfigSchema,
    LLMConfigSchema,
    DatabaseConfigSchema,
    PerformanceConfigSchema,
    SecurityConfigSchema,
    RootConfigSchema
)


class NegativeTestCase:
    """负例测试用例"""
    
    def __init__(self, name: str, description: str, config_data: Dict[str, Any], 
                 expected_error_type: str, expected_error_keywords: List[str]):
        self.name = name
        self.description = description
        self.config_data = config_data
        self.expected_error_type = expected_error_type
        self.expected_error_keywords = expected_error_keywords


class ConfigValidationNegativeTests:
    """配置验证负例测试集"""
    
    def __init__(self):
        self.test_cases = self._build_test_cases()
    
    def _build_test_cases(self) -> List[NegativeTestCase]:
        """构建测试用例"""
        cases = []
        
        # YAML语法错误
        cases.extend(self._yaml_syntax_errors())
        
        # Schema验证错误
        cases.extend(self._schema_validation_errors())
        
        # 业务逻辑错误
        cases.extend(self._business_logic_errors())
        
        # 类型错误
        cases.extend(self._type_errors())
        
        # 范围错误
        cases.extend(self._range_errors())
        
        # 依赖错误
        cases.extend(self._dependency_errors())
        
        return cases
    
    def _yaml_syntax_errors(self) -> List[NegativeTestCase]:
        """YAML语法错误用例"""
        return [
            NegativeTestCase(
                name="yaml_invalid_indentation",
                description="YAML缩进错误",
                config_data={"_yaml_content": """
default_model: deepseek-chat
models:
  deepseek-chat:
    provider: deepseek
  model_name: deepseek-chat  # 错误的缩进
                """},
                expected_error_type="YAMLError",
                expected_error_keywords=["缩进", "indentation", "语法错误"]
            ),
            NegativeTestCase(
                name="yaml_invalid_syntax",
                description="YAML语法错误 - 冒号缺失",
                config_data={"_yaml_content": """
default_model deepseek-chat  # 缺少冒号
models:
  deepseek-chat:
    provider: deepseek
                """},
                expected_error_type="YAMLError",
                expected_error_keywords=["语法错误", "冒号", "syntax"]
            )
        ]
    
    def _schema_validation_errors(self) -> List[NegativeTestCase]:
        """Schema验证错误用例"""
        return [
            NegativeTestCase(
                name="missing_required_field",
                description="缺少必需字段",
                config_data={
                    "models": {
                        "test-model": {
                            "provider": "test"
                            # 缺少 model_name 字段
                        }
                    }
                },
                expected_error_type="ValidationError",
                expected_error_keywords=["缺少", "字段", "model_name"]
            ),
            NegativeTestCase(
                name="invalid_field_type",
                description="字段类型错误",
                config_data={
                    "default_model": "test-model",
                    "models": {
                        "test-model": {
                            "provider": "test",
                            "model_name": "test-model",
                            "temperature": "invalid_number"  # 应该是数字
                        }
                    }
                },
                expected_error_type="ValidationError",
                expected_error_keywords=["类型错误", "数字", "invalid_number"]
            ),
            NegativeTestCase(
                name="empty_string_field",
                description="空字符串字段",
                config_data={
                    "default_model": "",  # 空字符串
                    "models": {}
                },
                expected_error_type="ValidationError",
                expected_error_keywords=["长度不足", "字符", "空字符串"]
            )
        ]
    
    def _business_logic_errors(self) -> List[NegativeTestCase]:
        """业务逻辑错误用例"""
        return [
            NegativeTestCase(
                name="nonexistent_default_model",
                description="默认模型不存在",
                config_data={
                    "default_model": "nonexistent-model",
                    "models": {
                        "existing-model": {
                            "provider": "test",
                            "model_name": "existing-model"
                        }
                    }
                },
                expected_error_type="ValidationError",
                expected_error_keywords=["默认模型", "不存在", "未定义", "nonexistent-model"]
            ),
            NegativeTestCase(
                name="invalid_scenario_mapping",
                description="场景映射到不存在的模型",
                config_data={
                    "default_model": "test-model",
                    "models": {
                        "test-model": {
                            "provider": "test",
                            "model_name": "test-model"
                        }
                    },
                    "scenario_mapping": {
                        "test_scenario": "nonexistent-model"
                    }
                },
                expected_error_type="ValidationError",
                expected_error_keywords=["场景", "映射", "模型", "不存在", "nonexistent-model"]
            )
        ]
    
    def _type_errors(self) -> List[NegativeTestCase]:
        """类型错误用例"""
        return [
            NegativeTestCase(
                name="boolean_as_string",
                description="布尔值写成字符串",
                config_data={
                    "cache": {
                        "enabled": "true"  # 应该是布尔值 true
                    }
                },
                expected_error_type="ValidationError",
                expected_error_keywords=["布尔", "boolean", "true", "字符串"]
            ),
            NegativeTestCase(
                name="number_as_string",
                description="数字写成字符串",
                config_data={
                    "timeout": {
                        "default": "30"  # 应该是数字 30
                    }
                },
                expected_error_type="ValidationError",
                expected_error_keywords=["数字", "integer", "字符串", "30"]
            )
        ]
    
    def _range_errors(self) -> List[NegativeTestCase]:
        """范围错误用例"""
        return [
            NegativeTestCase(
                name="temperature_out_of_range",
                description="温度参数超出范围",
                config_data={
                    "default_model": "test-model",
                    "models": {
                        "test-model": {
                            "provider": "test",
                            "model_name": "test-model",
                            "temperature": 3.0  # 超出范围 [0.0, 2.0]
                        }
                    }
                },
                expected_error_type="ValidationError",
                expected_error_keywords=["温度", "temperature", "范围", "3.0", "2.0"]
            ),
            NegativeTestCase(
                name="negative_timeout",
                description="负数超时时间",
                config_data={
                    "timeout": {
                        "default": -10  # 负数
                    }
                },
                expected_error_type="ValidationError",
                expected_error_keywords=["超时", "timeout", "负数", "大于等于", "ge"]
            )
        ]
    
    def _dependency_errors(self) -> List[NegativeTestCase]:
        """依赖错误用例"""
        return [
            NegativeTestCase(
                name="database_path_invalid_extension",
                description="数据库文件扩展名错误",
                config_data={
                    "database": {
                        "connection": {
                            "path": "database.txt",  # 应该是 .db 结尾
                            "timeout": 30
                        }
                    }
                },
                expected_error_type="ValidationError",
                expected_error_keywords=["数据库", "扩展名", ".db", ".txt"]
            ),
            NegativeTestCase(
                name="invalid_api_base_url",
                description="无效的API基础URL",
                config_data={
                    "default_model": "test-model",
                    "models": {
                        "test-model": {
                            "provider": "test",
                            "model_name": "test-model",
                            "api_base": "invalid-url"  # 无效URL
                        }
                    }
                },
                expected_error_type="ValidationError",
                expected_error_keywords=["URL", "http", "https", "invalid-url"]
            )
        ]
    
    def run_test_case(self, test_case: NegativeTestCase, schema_class) -> Tuple[bool, str, str]:
        """运行单个测试用例"""
        try:
            # 如果是YAML内容测试
            if "_yaml_content" in test_case.config_data:
                yaml_content = test_case.config_data["_yaml_content"]
                try:
                    yaml.safe_load(yaml_content)
                    return False, "期望YAML解析失败，但实际成功", ""
                except yaml.YAMLError as e:
                    error_msg = str(e)
                    return True, "YAML解析失败（符合预期）", error_msg
            
            # Schema验证测试
            schema_class(**test_case.config_data)
            return False, "期望Schema验证失败，但实际成功", ""
            
        except Exception as e:
            error_msg = str(e)
            error_type = type(e).__name__
            
            # 检查错误类型是否匹配
            if error_type != test_case.expected_error_type:
                return False, f"错误类型不匹配：期望 {test_case.expected_error_type}，实际 {error_type}", error_msg
            
            # 检查错误信息是否包含期望的关键词
            missing_keywords = []
            for keyword in test_case.expected_error_keywords:
                if keyword.lower() not in error_msg.lower():
                    missing_keywords.append(keyword)
            
            if missing_keywords:
                return False, f"错误信息缺少关键词: {missing_keywords}", error_msg
            
            return True, "测试通过", error_msg
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试用例"""
        results = {
            "total_cases": len(self.test_cases),
            "passed": 0,
            "failed": 0,
            "test_results": []
        }
        
        # 根据测试用例选择合适的Schema
        schema_mapping = {
            "llm": LLMConfigSchema,
            "database": DatabaseConfigSchema,
            "performance": PerformanceConfigSchema,
            "security": SecurityConfigSchema,
            "business": BusinessConfigSchema
        }
        
        for test_case in self.test_cases:
            # 根据测试用例内容选择Schema
            schema_class = LLMConfigSchema  # 默认使用LLM Schema
            
            if "database" in test_case.config_data:
                schema_class = DatabaseConfigSchema
            elif "cache" in test_case.config_data or "timeout" in test_case.config_data:
                schema_class = PerformanceConfigSchema
            elif "authentication" in test_case.config_data:
                schema_class = SecurityConfigSchema
            
            success, message, error_msg = self.run_test_case(test_case, schema_class)
            
            result = {
                "name": test_case.name,
                "description": test_case.description,
                "success": success,
                "message": message,
                "error_message": error_msg,
                "expected_error_type": test_case.expected_error_type,
                "expected_keywords": test_case.expected_error_keywords
            }
            
            results["test_results"].append(result)
            
            if success:
                results["passed"] += 1
            else:
                results["failed"] += 1
        
        return results
    
    def generate_report(self, results: Dict[str, Any], format: str = "text") -> str:
        """生成测试报告"""
        if format == "json":
            return json.dumps(results, indent=2, ensure_ascii=False)
        
        lines = ["配置验证负例测试报告", "=" * 50, ""]
        
        # 汇总信息
        lines.extend([
            f"总测试用例: {results['total_cases']}",
            f"通过: {results['passed']}",
            f"失败: {results['failed']}",
            f"成功率: {results['passed']/results['total_cases']*100:.1f}%",
            ""
        ])
        
        # 详细结果
        for result in results["test_results"]:
            status = "✅" if result["success"] else "❌"
            lines.extend([
                f"{status} {result['name']}",
                f"   描述: {result['description']}",
                f"   结果: {result['message']}"
            ])
            
            if not result["success"]:
                lines.append(f"   错误信息: {result['error_message'][:100]}...")
            
            lines.append("")
        
        return "\n".join(lines)


    def test_error_formatting(self) -> Dict[str, Any]:
        """测试错误格式化质量"""
        from .error_formatter import ConfigErrorFormatter

        formatter = ConfigErrorFormatter()
        formatting_results = {
            "total_cases": 0,
            "readable_errors": 0,
            "formatting_tests": []
        }

        # 测试每个用例的错误格式化
        for test_case in self.test_cases:
            if "_yaml_content" in test_case.config_data:
                continue  # 跳过YAML内容测试

            try:
                # 尝试触发错误
                LLMConfigSchema(**test_case.config_data)
            except Exception as e:
                formatted_error = formatter.format_validation_error(e, f"{test_case.name}.yaml")

                # 评估错误信息的可读性
                readability_score = self._evaluate_error_readability(formatted_error, test_case)

                formatting_test = {
                    "test_case": test_case.name,
                    "formatted_error": formatted_error,
                    "readability_score": readability_score,
                    "is_readable": readability_score >= 0.7
                }

                formatting_results["formatting_tests"].append(formatting_test)
                formatting_results["total_cases"] += 1

                if readability_score >= 0.7:
                    formatting_results["readable_errors"] += 1

        return formatting_results

    def _evaluate_error_readability(self, formatted_error: str, test_case: NegativeTestCase) -> float:
        """评估错误信息的可读性"""
        score = 0.0

        # 检查是否包含中文说明
        if any(ord(char) > 127 for char in formatted_error):
            score += 0.2

        # 检查是否有解决方案
        if "解决方案" in formatted_error or "💡" in formatted_error:
            score += 0.3

        # 检查是否有示例
        if "示例" in formatted_error or "📝" in formatted_error:
            score += 0.2

        # 检查是否包含期望的关键词
        keyword_matches = sum(1 for keyword in test_case.expected_error_keywords
                            if keyword.lower() in formatted_error.lower())
        if keyword_matches > 0:
            score += 0.3 * (keyword_matches / len(test_case.expected_error_keywords))

        return min(score, 1.0)


def main():
    """主函数"""
    print("开始运行配置验证负例测试...")

    test_suite = ConfigValidationNegativeTests()

    # 运行基础测试
    results = test_suite.run_all_tests()

    # 生成基础报告
    report = test_suite.generate_report(results, "text")
    print(report)

    # 测试错误格式化质量
    print("\n" + "=" * 50)
    print("测试错误格式化质量...")

    formatting_results = test_suite.test_error_formatting()

    print(f"错误格式化测试结果:")
    print(f"  总测试用例: {formatting_results['total_cases']}")
    print(f"  可读性良好: {formatting_results['readable_errors']}")
    print(f"  可读性比例: {formatting_results['readable_errors']/formatting_results['total_cases']*100:.1f}%")

    # 显示格式化示例
    print("\n错误格式化示例:")
    for i, test in enumerate(formatting_results["formatting_tests"][:3]):  # 显示前3个
        print(f"\n示例 {i+1}: {test['test_case']}")
        print(f"可读性评分: {test['readability_score']:.2f}")
        print("格式化错误信息:")
        print("-" * 30)
        print(test["formatted_error"][:300] + "..." if len(test["formatted_error"]) > 300 else test["formatted_error"])
        print("-" * 30)

    # 保存详细报告
    all_results = {
        "basic_tests": results,
        "formatting_tests": formatting_results
    }

    json_report = json.dumps(all_results, indent=2, ensure_ascii=False)
    with open("negative_test_results.json", "w", encoding="utf-8") as f:
        f.write(json_report)

    print(f"\n详细报告已保存到: negative_test_results.json")

    # 返回退出码
    failed_basic = results["failed"]
    failed_formatting = formatting_results["total_cases"] - formatting_results["readable_errors"]

    if failed_basic > 0 or failed_formatting > 0:
        print(f"\n⚠️  基础测试失败: {failed_basic}, 格式化测试失败: {failed_formatting}")
        return 1
    else:
        print("\n✅ 所有测试都通过了")
        return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())
