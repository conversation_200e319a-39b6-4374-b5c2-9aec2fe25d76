#!/usr/bin/env python3
"""
配置验证模式定义

使用Pydantic定义各个配置模块的验证模式
"""

from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict
from enum import Enum


class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class Environment(str, Enum):
    """环境类型枚举"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


class AppConfigSchema(BaseModel):
    """应用配置模式"""
    model_config = ConfigDict(extra="forbid")

    name: str = Field(..., description="应用名称", min_length=1, max_length=100)
    version: str = Field("1.0", description="应用版本", pattern=r"^\d+\.\d+(\.\d+)?$")
    environment: Environment = Field(Environment.DEVELOPMENT, description="运行环境")
    debug: bool = Field(False, description="调试模式")


class DatabaseConnectionConfigSchema(BaseModel):
    """数据库连接配置模式"""
    model_config = ConfigDict(extra="forbid")

    path: str = Field(..., description="数据库文件路径", min_length=1)
    timeout: int = Field(30, description="连接超时时间（秒）", ge=1, le=300)
    check_same_thread: bool = Field(False, description="SQLite线程检查")

    @field_validator('path')
    @classmethod
    def validate_path(cls, v):
        if not v.endswith('.db'):
            raise ValueError('数据库文件必须以.db结尾')
        return v


class DatabaseQueriesConfigSchema(BaseModel):
    """数据库查询配置模式"""
    model_config = ConfigDict(extra="forbid")

    batch_size: int = Field(100, description="批量大小", ge=1, le=10000)
    max_results: int = Field(1000, description="最大结果数", ge=1, le=100000)
    timeout: int = Field(30, description="查询超时（秒）", ge=1, le=300)
    # 支持各种查询配置
    conversations: Dict[str, str] = Field(default_factory=dict, description="会话查询")
    messages: Dict[str, str] = Field(default_factory=dict, description="消息查询")
    documents: Dict[str, str] = Field(default_factory=dict, description="文档查询")
    focus_points: Dict[str, str] = Field(default_factory=dict, description="关注点查询")
    sessions: Dict[str, str] = Field(default_factory=dict, description="会话查询")
    summaries: Dict[str, str] = Field(default_factory=dict, description="摘要查询")
    concern_point_coverage: Dict[str, str] = Field(default_factory=dict, description="关注点覆盖查询")
    focus_point_definitions: Dict[str, str] = Field(default_factory=dict, description="关注点定义查询")
    backup: Dict[str, str] = Field(default_factory=dict, description="备份查询")
    pagination: Dict[str, int] = Field(default_factory=dict, description="分页配置")
    optimization: Dict[str, Any] = Field(default_factory=dict, description="优化配置")
    slow_query: Dict[str, Any] = Field(default_factory=dict, description="慢查询配置")


class DatabaseTablesConfigSchema(BaseModel):
    """数据库表配置模式"""
    model_config = ConfigDict(extra="forbid")

    conversations: Dict[str, Any] = Field(default_factory=dict, description="会话表配置")
    documents: Dict[str, Any] = Field(default_factory=dict, description="文档表配置")
    focus_points: Dict[str, Any] = Field(default_factory=dict, description="关注点表配置")
    # 支持通用表配置
    users: Dict[str, Any] = Field(default_factory=dict, description="用户表配置")
    sessions: Dict[str, Any] = Field(default_factory=dict, description="会话表配置")
    conversation_history: Dict[str, Any] = Field(default_factory=dict, description="会话历史表配置")
    requirements: Dict[str, Any] = Field(default_factory=dict, description="需求表配置")


class DatabaseMigrationsConfigSchema(BaseModel):
    """数据库迁移配置模式"""
    model_config = ConfigDict(extra="forbid")

    migration_path: str = Field("backend/database/migrations", description="迁移文件路径")
    strategy: str = Field("incremental", description="迁移策略")
    auto_migrate: bool = Field(False, description="是否自动迁移")
    backup_before_migrate: bool = Field(True, description="迁移前是否备份")
    version_table: str = Field("schema_versions", description="版本表名")
    current_version: str = Field("1.0.0", description="当前版本")

    @field_validator('strategy')
    @classmethod
    def validate_strategy(cls, v):
        """验证迁移策略"""
        allowed_strategies = ["incremental", "full", "rollback"]
        if v not in allowed_strategies:
            raise ValueError(f"迁移策略必须是以下之一: {allowed_strategies}")
        return v


class DatabaseBackupConfigSchema(BaseModel):
    """数据库备份配置模式"""
    model_config = ConfigDict(extra="forbid")

    auto_backup: bool = Field(True, description="是否自动备份")
    backup_interval: int = Field(86400, description="备份间隔（秒）", ge=3600, le=604800)
    backup_path: str = Field("backend/data/backups", description="备份路径")
    retention_days: int = Field(30, description="保留天数", ge=1, le=365)
    max_backup_files: int = Field(10, description="最大备份文件数", ge=1, le=100)
    compression: bool = Field(True, description="是否压缩")
    compression_level: int = Field(6, description="压缩级别", ge=1, le=9)


class DatabaseSecurityConfigSchema(BaseModel):
    """数据库安全配置模式"""
    model_config = ConfigDict(extra="forbid")

    encryption: Dict[str, Any] = Field(default_factory=dict, description="加密配置")
    access_control: Dict[str, Any] = Field(default_factory=dict, description="访问控制配置")
    audit_log: Dict[str, Any] = Field(default_factory=dict, description="审计日志配置")


class DatabaseMonitoringConfigSchema(BaseModel):
    """数据库监控配置模式"""
    model_config = ConfigDict(extra="forbid")

    metrics: Dict[str, bool] = Field(default_factory=dict, description="指标配置")
    monitoring_interval: int = Field(60, description="监控间隔（秒）", ge=10, le=3600)
    alerts: Dict[str, float] = Field(default_factory=dict, description="告警配置")


class DatabaseConfigSchema(BaseModel):
    """数据库配置模式"""
    model_config = ConfigDict(extra="allow")

    database: Dict[str, Any] = Field(default_factory=dict, description="数据库主配置")
    tables: DatabaseTablesConfigSchema = Field(
        default_factory=DatabaseTablesConfigSchema, description="表配置"
    )
    queries: DatabaseQueriesConfigSchema = Field(
        default_factory=DatabaseQueriesConfigSchema, description="查询配置"
    )
    migrations: DatabaseMigrationsConfigSchema = Field(
        default_factory=DatabaseMigrationsConfigSchema, description="迁移配置"
    )
    backup: DatabaseBackupConfigSchema = Field(
        default_factory=DatabaseBackupConfigSchema, description="备份配置"
    )
    security: DatabaseSecurityConfigSchema = Field(
        default_factory=DatabaseSecurityConfigSchema, description="安全配置"
    )
    monitoring: DatabaseMonitoringConfigSchema = Field(
        default_factory=DatabaseMonitoringConfigSchema, description="监控配置"
    )
    cleanup: Dict[str, Any] = Field(default_factory=dict, description="清理配置")
    development: Dict[str, Any] = Field(default_factory=dict, description="开发配置")
    production: Dict[str, Any] = Field(default_factory=dict, description="生产配置")
    # 支持conversation配置（向后兼容）
    conversation: Dict[str, Any] = Field(default_factory=dict, description="会话配置")


class LLMModelConfigSchema(BaseModel):
    """LLM模型配置模式"""
    model_config = ConfigDict(extra="forbid")

    provider: str = Field(..., description="模型提供商", min_length=1)
    model_name: str = Field(..., description="模型名称", min_length=1)
    api_key: Optional[str] = Field(None, description="API密钥")
    api_base: Optional[str] = Field(None, description="API基础URL")
    temperature: float = Field(0.7, description="温度参数", ge=0.0, le=2.0)
    max_tokens: int = Field(2000, description="最大令牌数", ge=1, le=32000)
    timeout: int = Field(30, description="请求超时（秒）", ge=1, le=300)
    max_retries: int = Field(3, description="最大重试次数", ge=0, le=10)
    top_p: float = Field(1.0, description="nucleus sampling参数", ge=0.0, le=1.0)
    frequency_penalty: float = Field(0.0, description="频率惩罚", ge=0.0, le=2.0)
    presence_penalty: float = Field(0.0, description="存在惩罚", ge=0.0, le=2.0)

    @field_validator('api_base')
    @classmethod
    def validate_api_base(cls, v):
        """验证API基础URL格式"""
        if v and not v.startswith(('http://', 'https://')):
            raise ValueError('API基础URL必须以http://或https://开头')
        return v


class LLMProviderConfigSchema(BaseModel):
    """LLM提供商配置模式"""
    model_config = ConfigDict(extra="forbid")

    name: str = Field(..., description="提供商名称", min_length=1)
    base_url: str = Field(..., description="基础URL", min_length=1)
    auth_type: str = Field("bearer_token", description="认证类型")
    rate_limit: int = Field(60, description="速率限制（请求/分钟）", ge=1, le=10000)

    @field_validator('auth_type')
    @classmethod
    def validate_auth_type(cls, v):
        """验证认证类型"""
        allowed_types = ["bearer_token", "api_key", "oauth", "basic_auth"]
        if v not in allowed_types:
            raise ValueError(f"认证类型必须是以下之一: {allowed_types}")
        return v


class ModelCapabilitiesConfigSchema(BaseModel):
    """模型能力配置模式"""
    model_config = ConfigDict(extra="forbid")

    text_generation: bool = Field(True, description="文本生成能力")
    conversation: bool = Field(True, description="对话能力")
    code_generation: bool = Field(False, description="代码生成能力")
    reasoning: bool = Field(True, description="推理能力")
    multilingual: bool = Field(True, description="多语言能力")
    context_length: int = Field(4096, description="上下文长度", ge=1, le=1000000)


class ModelSelectionConfigSchema(BaseModel):
    """模型选择配置模式"""
    model_config = ConfigDict(extra="forbid")

    strategy: str = Field("performance_based", description="选择策略")
    performance_weights: Dict[str, float] = Field(
        default_factory=dict, description="性能权重配置"
    )
    fallback_enabled: bool = Field(True, description="是否启用降级")
    fallback_models: List[str] = Field(
        default_factory=list, description="降级模型列表"
    )

    @field_validator('strategy')
    @classmethod
    def validate_strategy(cls, v):
        """验证选择策略"""
        allowed_strategies = ["performance_based", "cost_based", "quality_first", "random"]
        if v not in allowed_strategies:
            raise ValueError(f"选择策略必须是以下之一: {allowed_strategies}")
        return v

    @field_validator('performance_weights')
    @classmethod
    def validate_performance_weights(cls, v):
        """验证性能权重"""
        for key, weight in v.items():
            if not 0.0 <= weight <= 1.0:
                raise ValueError(f"性能权重 '{key}' 必须在0.0-1.0范围内: {weight}")
        return v


class LLMMonitoringConfigSchema(BaseModel):
    """LLM监控配置模式"""
    model_config = ConfigDict(extra="forbid")

    performance_tracking: bool = Field(True, description="性能跟踪")
    response_time_tracking: bool = Field(True, description="响应时间跟踪")
    error_rate_tracking: bool = Field(True, description="错误率跟踪")
    usage_tracking: bool = Field(True, description="使用量跟踪")
    token_usage_tracking: bool = Field(True, description="Token使用量跟踪")
    cost_tracking: bool = Field(True, description="成本跟踪")
    quality_monitoring: bool = Field(True, description="质量监控")
    output_quality_scoring: bool = Field(False, description="输出质量评分")


class LLMOptimizationConfigSchema(BaseModel):
    """LLM优化配置模式"""
    model_config = ConfigDict(extra="forbid")

    response_caching: bool = Field(True, description="响应缓存")
    cache_ttl: int = Field(3600, description="缓存TTL（秒）", ge=60, le=86400)
    batch_processing: bool = Field(False, description="批量处理")
    batch_size: int = Field(10, description="批量大小", ge=1, le=100)
    batch_timeout: int = Field(5, description="批量超时（秒）", ge=1, le=60)
    model_warmup: bool = Field(True, description="模型预热")
    warmup_requests: int = Field(3, description="预热请求数", ge=1, le=10)


class LLMConfigSchema(BaseModel):
    """LLM服务配置模式"""
    model_config = ConfigDict(extra="allow")

    default_model: str = Field(..., description="默认模型名称", min_length=1)
    models: Dict[str, LLMModelConfigSchema] = Field(
        default_factory=dict, description="模型配置字典"
    )
    providers: Dict[str, LLMProviderConfigSchema] = Field(
        default_factory=dict, description="提供商配置字典"
    )
    model_capabilities: Dict[str, ModelCapabilitiesConfigSchema] = Field(
        default_factory=dict, description="模型能力配置"
    )
    model_selection: ModelSelectionConfigSchema = Field(
        default_factory=ModelSelectionConfigSchema, description="模型选择配置"
    )
    monitoring: LLMMonitoringConfigSchema = Field(
        default_factory=LLMMonitoringConfigSchema, description="监控配置"
    )
    optimization: LLMOptimizationConfigSchema = Field(
        default_factory=LLMOptimizationConfigSchema, description="优化配置"
    )
    scenario_mapping: Dict[str, str] = Field(
        default_factory=dict, description="场景到模型的映射"
    )
    scenario_params: Dict[str, Dict[str, Any]] = Field(
        default_factory=dict, description="场景参数配置"
    )
    default_params: Dict[str, Any] = Field(
        default_factory=dict, description="默认参数"
    )
    experimental: Dict[str, Any] = Field(
        default_factory=dict, description="实验性功能配置"
    )

    @field_validator('scenario_mapping')
    @classmethod
    def validate_scenario_mapping(cls, v, info):
        """验证场景映射中的模型是否存在"""
        if info.data and 'models' in info.data:
            models = info.data['models']
            default_model = info.data.get('default_model')

            for scenario, model_name in v.items():
                if model_name not in models and model_name != default_model:
                    raise ValueError(f"场景 '{scenario}' 映射的模型 '{model_name}' 不存在")
        return v

    @model_validator(mode='after')
    def validate_llm_config_integrity(self):
        """验证LLM配置完整性"""
        # 验证默认模型存在
        if self.default_model and self.default_model not in self.models:
            # 检查是否在场景映射中被引用
            if not any(model == self.default_model for model in self.scenario_mapping.values()):
                raise ValueError(f"默认模型 '{self.default_model}' 未在模型配置中定义")

        # 验证模型能力配置与模型配置的一致性
        for model_name in self.models.keys():
            if model_name not in self.model_capabilities:
                # 可以给出警告，但不强制要求
                pass

        # 验证降级模型存在
        if hasattr(self.model_selection, 'fallback_models'):
            for fallback_model in self.model_selection.fallback_models:
                if fallback_model not in self.models:
                    raise ValueError(f"降级模型 '{fallback_model}' 未在模型配置中定义")

        return self


class CacheConfigSchema(BaseModel):
    """缓存配置模式"""
    model_config = ConfigDict(extra="forbid")

    enabled: bool = Field(True, description="是否启用缓存")
    default_ttl: int = Field(3600, description="默认TTL（秒）", ge=60, le=86400)
    max_size: int = Field(1000, description="最大缓存大小", ge=10, le=100000)
    categories: Dict[str, Dict[str, int]] = Field(
        default_factory=dict, description="分类缓存配置"
    )


class TimeoutConfigSchema(BaseModel):
    """超时配置模式"""
    model_config = ConfigDict(extra="forbid")

    default: int = Field(5, description="默认超时（秒）", ge=1, le=300)
    short: int = Field(3, description="短超时（秒）", ge=1, le=60)
    medium: int = Field(10, description="中等超时（秒）", ge=1, le=120)
    long: int = Field(30, description="长超时（秒）", ge=1, le=600)
    very_long: int = Field(60, description="超长超时（秒）", ge=1, le=1200)
    api_request: int = Field(10, description="API请求超时（秒）", ge=1, le=300)
    database: int = Field(5, description="数据库超时（秒）", ge=1, le=300)
    file_operation: int = Field(5, description="文件操作超时（秒）", ge=1, le=300)
    llm_service: int = Field(30, description="LLM服务超时（秒）", ge=1, le=600)


class RetryPerformanceConfigSchema(BaseModel):
    """重试性能配置模式"""
    model_config = ConfigDict(extra="forbid")

    default: int = Field(3, description="默认重试次数", ge=0, le=10)
    quick_retry: int = Field(2, description="快速重试次数", ge=0, le=5)
    persistent_retry: int = Field(5, description="持久重试次数", ge=0, le=20)
    api_call: int = Field(2, description="API调用重试次数", ge=0, le=10)
    database_operation: int = Field(3, description="数据库操作重试次数", ge=0, le=10)
    file_access: int = Field(2, description="文件访问重试次数", ge=0, le=5)
    llm_request: int = Field(3, description="LLM请求重试次数", ge=0, le=10)
    max_attempts: int = Field(3, description="最大尝试次数", ge=1, le=20)
    backoff_factor: float = Field(1.5, description="退避因子", ge=1.0, le=10.0)
    initial_delay: int = Field(1, description="初始延迟（秒）", ge=0, le=60)
    max_delay: int = Field(30, description="最大延迟（秒）", ge=1, le=300)


class ConcurrencyConfigSchema(BaseModel):
    """并发配置模式"""
    model_config = ConfigDict(extra="forbid")

    max_concurrent: int = Field(5, description="最大并发数", ge=1, le=1000)
    max_concurrent_queries: int = Field(5, description="最大并发查询数", ge=1, le=100)
    thread_pool_size: int = Field(10, description="线程池大小", ge=1, le=100)
    connection_pool_size: int = Field(20, description="连接池大小", ge=1, le=1000)


class MonitoringPerformanceConfigSchema(BaseModel):
    """性能监控配置模式"""
    model_config = ConfigDict(extra="forbid")

    enabled: bool = Field(True, description="是否启用监控")
    metrics_collection: bool = Field(True, description="是否收集指标")
    performance_logging: bool = Field(True, description="是否记录性能日志")
    slow_query_threshold: float = Field(1.0, description="慢查询阈值（秒）", ge=0.1, le=60.0)
    metrics: Dict[str, bool] = Field(default_factory=dict, description="指标配置")


class LimitsConfigSchema(BaseModel):
    """限制配置模式"""
    model_config = ConfigDict(extra="forbid")

    max_memory_usage: str = Field("512MB", description="最大内存使用")
    max_heap_size: str = Field("256MB", description="最大堆大小")
    max_request_per_minute: int = Field(1000, description="每分钟最大请求数", ge=1, le=100000)
    max_request_per_hour: int = Field(10000, description="每小时最大请求数", ge=1, le=1000000)
    max_concurrent_requests: int = Field(50, description="最大并发请求数", ge=1, le=10000)
    max_query_results: int = Field(50, description="最大查询结果数", ge=1, le=10000)
    max_batch_size: int = Field(100, description="最大批量大小", ge=1, le=10000)
    max_file_size: str = Field("10MB", description="最大文件大小")

    @field_validator('max_memory_usage', 'max_heap_size', 'max_file_size')
    @classmethod
    def validate_size_format(cls, v):
        """验证大小格式"""
        import re
        if not re.match(r'^\d+[KMGT]?B$', v.upper()):
            raise ValueError(f'大小格式错误，应为如 "10MB" 的格式: {v}')
        return v


class OptimizationConfigSchema(BaseModel):
    """优化配置模式"""
    model_config = ConfigDict(extra="forbid")

    preload_enabled: bool = Field(True, description="是否启用预加载")
    preload_critical_configs: bool = Field(True, description="是否预加载关键配置")
    preload_templates: bool = Field(True, description="是否预加载模板")
    response_compression: bool = Field(True, description="是否启用响应压缩")
    static_file_compression: bool = Field(True, description="是否启用静态文件压缩")
    keep_alive: bool = Field(True, description="是否启用Keep-Alive")
    connection_reuse: bool = Field(True, description="是否启用连接复用")


class AgentCacheConfigSchema(BaseModel):
    """Agent缓存配置模式"""
    model_config = ConfigDict(extra="forbid")

    cleanup_interval_minutes: int = Field(5, description="清理间隔（分钟）", ge=1, le=60)
    enable_component_cache: bool = Field(True, description="是否启用组件缓存")
    enable_metrics: bool = Field(True, description="是否启用指标")
    enable_session_cache: bool = Field(True, description="是否启用会话缓存")
    max_cached_sessions: int = Field(100, description="最大缓存会话数", ge=1, le=10000)
    max_memory_mb: int = Field(500, description="最大内存（MB）", ge=10, le=10000)
    memory_check_interval: int = Field(10, description="内存检查间隔（秒）", ge=1, le=300)
    metrics_report_interval: int = Field(50, description="指标报告间隔", ge=1, le=1000)
    session_timeout_minutes: int = Field(30, description="会话超时（分钟）", ge=1, le=1440)


class PerformanceConfigSchema(BaseModel):
    """性能配置模式"""
    model_config = ConfigDict(extra="allow")

    cache: CacheConfigSchema = Field(
        default_factory=CacheConfigSchema, description="缓存配置"
    )
    timeout: TimeoutConfigSchema = Field(
        default_factory=TimeoutConfigSchema, description="超时配置"
    )
    retry: RetryPerformanceConfigSchema = Field(
        default_factory=RetryPerformanceConfigSchema, description="重试配置"
    )
    concurrency: ConcurrencyConfigSchema = Field(
        default_factory=ConcurrencyConfigSchema, description="并发配置"
    )
    monitoring: MonitoringPerformanceConfigSchema = Field(
        default_factory=MonitoringPerformanceConfigSchema, description="监控配置"
    )
    limits: LimitsConfigSchema = Field(
        default_factory=LimitsConfigSchema, description="限制配置"
    )
    optimization: OptimizationConfigSchema = Field(
        default_factory=OptimizationConfigSchema, description="优化配置"
    )
    agent_cache: AgentCacheConfigSchema = Field(
        default_factory=AgentCacheConfigSchema, description="Agent缓存配置"
    )
    # 向后兼容的字段
    llm_timeout: int = Field(30, description="LLM超时时间（秒）", ge=1, le=600)
    database_timeout: int = Field(10, description="数据库超时时间（秒）", ge=1, le=300)
    max_concurrent_requests: int = Field(
        100, description="最大并发请求数", ge=1, le=1000
    )
    max_memory_usage: int = Field(
        1024, description="最大内存使用（MB）", ge=128, le=8192
    )
    cache_size: int = Field(1000, description="缓存大小", ge=10, le=10000)


class SystemConfigSchema(BaseModel):
    """系统配置模式"""
    model_config = ConfigDict(extra="forbid")

    performance: PerformanceConfigSchema = Field(
        default_factory=PerformanceConfigSchema, description="性能配置"
    )
    debug_mode: bool = Field(False, description="调试模式")
    maintenance_mode: bool = Field(False, description="维护模式")


class ThresholdsConfigSchema(BaseModel):
    """阈值配置模式"""
    model_config = ConfigDict(extra="forbid")

    completion_threshold: float = Field(
        0.7, description="完成度阈值", ge=0.0, le=1.0
    )
    confidence_threshold: float = Field(
        0.8, description="置信度阈值", ge=0.0, le=1.0
    )
    performance: Dict[str, Any] = Field(
        default_factory=dict, description="性能相关阈值"
    )


class AuthenticationConfigSchema(BaseModel):
    """认证配置模式"""
    model_config = ConfigDict(extra="forbid")

    enabled: bool = Field(True, description="是否启用认证")
    session_timeout: int = Field(7200, description="会话超时（秒）", ge=300, le=86400)
    max_login_attempts: int = Field(5, description="最大登录尝试次数", ge=1, le=20)
    lockout_duration: int = Field(300, description="锁定时长（秒）", ge=60, le=3600)
    password_policy: Dict[str, Any] = Field(default_factory=dict, description="密码策略")
    token: Dict[str, Any] = Field(default_factory=dict, description="令牌配置")


class AuthorizationConfigSchema(BaseModel):
    """授权配置模式"""
    model_config = ConfigDict(extra="forbid")

    enabled: bool = Field(True, description="是否启用授权")
    default_role: str = Field("user", description="默认角色")
    roles: Dict[str, Dict[str, List[str]]] = Field(
        default_factory=dict, description="角色配置"
    )
    access_control: Dict[str, List[str]] = Field(
        default_factory=dict, description="访问控制配置"
    )


class DataProtectionConfigSchema(BaseModel):
    """数据保护配置模式"""
    model_config = ConfigDict(extra="forbid")

    encryption_enabled: bool = Field(True, description="是否启用加密")
    data_masking: bool = Field(True, description="是否启用数据脱敏")
    sensitive_fields: List[str] = Field(
        default_factory=list, description="敏感字段列表"
    )
    masking_rules: Dict[str, str] = Field(
        default_factory=dict, description="脱敏规则"
    )


class AuditConfigSchema(BaseModel):
    """审计配置模式"""
    model_config = ConfigDict(extra="forbid")

    enabled: bool = Field(True, description="是否启用审计")
    log_all_requests: bool = Field(False, description="是否记录所有请求")
    log_sensitive_operations: bool = Field(True, description="是否记录敏感操作")
    events: Dict[str, bool] = Field(default_factory=dict, description="事件配置")
    log_file: str = Field("logs/audit.log", description="日志文件路径")
    log_rotation: bool = Field(True, description="是否启用日志轮转")
    retention_days: int = Field(90, description="保留天数", ge=1, le=3650)


class SecurityLimitsConfigSchema(BaseModel):
    """安全限制配置模式"""
    model_config = ConfigDict(extra="forbid")

    rate_limiting: Dict[str, Any] = Field(default_factory=dict, description="速率限制配置")
    content_filter: Dict[str, Any] = Field(default_factory=dict, description="内容过滤配置")
    ip_control: Dict[str, Any] = Field(default_factory=dict, description="IP控制配置")


class EncryptionConfigSchema(BaseModel):
    """加密配置模式"""
    model_config = ConfigDict(extra="forbid")

    algorithm: str = Field("AES-256", description="加密算法")
    key_rotation_enabled: bool = Field(False, description="是否启用密钥轮转")
    key_rotation_interval: int = Field(2592000, description="密钥轮转间隔（秒）", ge=86400, le=31536000)
    transport: Dict[str, Any] = Field(default_factory=dict, description="传输加密配置")

    @field_validator('algorithm')
    @classmethod
    def validate_algorithm(cls, v):
        """验证加密算法"""
        allowed_algorithms = ["AES-128", "AES-192", "AES-256", "ChaCha20-Poly1305"]
        if v not in allowed_algorithms:
            raise ValueError(f"加密算法必须是以下之一: {allowed_algorithms}")
        return v


class SecurityMonitoringConfigSchema(BaseModel):
    """安全监控配置模式"""
    model_config = ConfigDict(extra="forbid")

    enabled: bool = Field(True, description="是否启用安全监控")
    threat_detection: Dict[str, bool] = Field(
        default_factory=dict, description="威胁检测配置"
    )
    alerts: Dict[str, Any] = Field(default_factory=dict, description="告警配置")
    metrics: Dict[str, bool] = Field(default_factory=dict, description="指标配置")


class ContentModerationConfigSchema(BaseModel):
    """内容审核配置模式"""
    model_config = ConfigDict(extra="forbid")

    enabled: bool = Field(True, description="是否启用内容审核")
    actions: Dict[str, str] = Field(default_factory=dict, description="动作配置")
    default_action: str = Field("WARN", description="默认动作")
    logging: Dict[str, Any] = Field(default_factory=dict, description="日志配置")
    masking: Dict[str, Any] = Field(default_factory=dict, description="脱敏配置")

    @field_validator('default_action')
    @classmethod
    def validate_default_action(cls, v):
        """验证默认动作"""
        allowed_actions = ["ALLOW", "WARN", "BLOCK", "MASK"]
        if v not in allowed_actions:
            raise ValueError(f"默认动作必须是以下之一: {allowed_actions}")
        return v


class AccessControlConfigSchema(BaseModel):
    """访问控制配置模式"""
    model_config = ConfigDict(extra="forbid")

    max_requests_per_minute: int = Field(60, description="每分钟最大请求数", ge=1, le=10000)
    rate_limiting: bool = Field(True, description="是否启用速率限制")


class InputValidationConfigSchema(BaseModel):
    """输入验证配置模式"""
    model_config = ConfigDict(extra="forbid")

    enabled: bool = Field(True, description="是否启用输入验证")
    max_length: int = Field(10000, description="最大长度", ge=1, le=1000000)
    forbidden_patterns: List[str] = Field(
        default_factory=list, description="禁止的模式列表"
    )


class SecurityConfigSchema(BaseModel):
    """安全配置模式"""
    model_config = ConfigDict(extra="allow")

    authentication: AuthenticationConfigSchema = Field(
        default_factory=AuthenticationConfigSchema, description="认证配置"
    )
    authorization: AuthorizationConfigSchema = Field(
        default_factory=AuthorizationConfigSchema, description="授权配置"
    )
    data_protection: DataProtectionConfigSchema = Field(
        default_factory=DataProtectionConfigSchema, description="数据保护配置"
    )
    audit: AuditConfigSchema = Field(
        default_factory=AuditConfigSchema, description="审计配置"
    )
    security_limits: SecurityLimitsConfigSchema = Field(
        default_factory=SecurityLimitsConfigSchema, description="安全限制配置"
    )
    encryption: EncryptionConfigSchema = Field(
        default_factory=EncryptionConfigSchema, description="加密配置"
    )
    security_monitoring: SecurityMonitoringConfigSchema = Field(
        default_factory=SecurityMonitoringConfigSchema, description="安全监控配置"
    )
    content_moderation: ContentModerationConfigSchema = Field(
        default_factory=ContentModerationConfigSchema, description="内容审核配置"
    )
    access_control: AccessControlConfigSchema = Field(
        default_factory=AccessControlConfigSchema, description="访问控制配置"
    )
    input_validation: InputValidationConfigSchema = Field(
        default_factory=InputValidationConfigSchema, description="输入验证配置"
    )
    # 向后兼容的字段
    rate_limiting: Dict[str, Any] = Field(
        default_factory=dict, description="速率限制配置"
    )
    session: Dict[str, Any] = Field(
        default_factory=dict, description="会话配置"
    )
    content_filter: Dict[str, Any] = Field(
        default_factory=dict, description="内容过滤配置"
    )
    sensitive_data: Dict[str, Any] = Field(
        default_factory=dict, description="敏感数据保护配置"
    )


class ActionHandlersConfigSchema(BaseModel):
    """动作处理器配置模式"""
    model_config = ConfigDict(extra="forbid")

    handler_classes: Dict[str, str] = Field(
        default_factory=dict, description="处理器类映射"
    )

    @field_validator('handler_classes')
    @classmethod
    def validate_handler_classes(cls, v):
        """验证处理器类路径格式"""
        for handler_name, class_path in v.items():
            if not class_path or '.' not in class_path:
                raise ValueError(f"处理器 '{handler_name}' 的类路径格式无效: {class_path}")
        return v


class DocumentConfirmationConfigSchema(BaseModel):
    """文档确认配置模式"""
    model_config = ConfigDict(extra="forbid")

    confirmation_keywords: List[str] = Field(
        default_factory=list, description="确认关键词列表"
    )
    require_explicit_confirmation: bool = Field(
        True, description="是否需要明确确认"
    )
    case_sensitive: bool = Field(False, description="是否区分大小写")
    partial_match_enabled: bool = Field(True, description="是否启用部分匹配")


class QualityControlConfigSchema(BaseModel):
    """质量控制配置模式"""
    model_config = ConfigDict(extra="forbid")

    max_input_length: int = Field(1000, description="最大输入长度", ge=1, le=100000)
    min_input_length: int = Field(2, description="最小输入长度", ge=1, le=1000)
    spam_detection_enabled: bool = Field(True, description="是否启用垃圾检测")
    content_filters: Dict[str, bool] = Field(
        default_factory=dict, description="内容过滤器配置"
    )
    quality_thresholds: Dict[str, float] = Field(
        default_factory=dict, description="质量阈值配置"
    )

    @field_validator('quality_thresholds')
    @classmethod
    def validate_quality_thresholds(cls, v):
        """验证质量阈值范围"""
        for key, value in v.items():
            if not 0.0 <= value <= 1.0:
                raise ValueError(f"质量阈值 '{key}' 必须在0.0-1.0范围内: {value}")
        return v


class RequirementCollectionConfigSchema(BaseModel):
    """需求采集配置模式"""
    model_config = ConfigDict(extra="forbid")

    completion_threshold: float = Field(
        0.8, description="完成度阈值", ge=0.0, le=1.0
    )
    max_focus_points: int = Field(10, description="最大关注点数量", ge=1, le=100)
    min_focus_points: int = Field(3, description="最小关注点数量", ge=1, le=50)
    collection_strategy: str = Field("adaptive", description="采集策略")
    auto_completion_enabled: bool = Field(True, description="是否启用自动完成")
    validation_rules: Dict[str, bool] = Field(
        default_factory=dict, description="验证规则配置"
    )

    @field_validator('collection_strategy')
    @classmethod
    def validate_collection_strategy(cls, v):
        """验证采集策略"""
        allowed_strategies = ["adaptive", "sequential", "parallel", "priority_based"]
        if v not in allowed_strategies:
            raise ValueError(f"采集策略必须是以下之一: {allowed_strategies}")
        return v


class RetryConfigSchema(BaseModel):
    """重试配置模式"""
    model_config = ConfigDict(extra="forbid")

    backoff_factor: float = Field(1.5, description="退避因子", ge=1.0, le=10.0)
    max_pending_attempts: int = Field(3, description="最大待处理尝试次数", ge=1, le=10)
    max_total_attempts: int = Field(5, description="最大总尝试次数", ge=1, le=20)
    retry_conditions: List[str] = Field(
        default_factory=list, description="重试条件列表"
    )
    no_retry_conditions: List[str] = Field(
        default_factory=list, description="不重试条件列表"
    )


class DecisionStrategiesConfigSchema(BaseModel):
    """决策策略配置模式"""
    model_config = ConfigDict(extra="forbid")

    intent_recognition: Dict[str, Any] = Field(
        default_factory=dict, description="意图识别配置"
    )
    domain_classification: Dict[str, Any] = Field(
        default_factory=dict, description="领域分类配置"
    )
    response_generation: Dict[str, Any] = Field(
        default_factory=dict, description="响应生成配置"
    )


class WorkflowRulesConfigSchema(BaseModel):
    """工作流规则配置模式"""
    model_config = ConfigDict(extra="forbid")

    state_transitions: Dict[str, List[str]] = Field(
        default_factory=dict, description="状态转换规则"
    )
    state_validation: Dict[str, List[str]] = Field(
        default_factory=dict, description="状态验证规则"
    )


class BusinessConfigSchema(BaseModel):
    """业务配置模式"""
    model_config = ConfigDict(extra="allow")

    action_handlers: ActionHandlersConfigSchema = Field(
        default_factory=ActionHandlersConfigSchema, description="动作处理器配置"
    )
    document_confirmation: DocumentConfirmationConfigSchema = Field(
        default_factory=DocumentConfirmationConfigSchema, description="文档确认配置"
    )
    quality_control: QualityControlConfigSchema = Field(
        default_factory=QualityControlConfigSchema, description="质量控制配置"
    )
    requirement_collection: RequirementCollectionConfigSchema = Field(
        default_factory=RequirementCollectionConfigSchema, description="需求采集配置"
    )
    retry: RetryConfigSchema = Field(
        default_factory=RetryConfigSchema, description="重试策略配置"
    )
    decision_strategies: DecisionStrategiesConfigSchema = Field(
        default_factory=DecisionStrategiesConfigSchema, description="决策策略配置"
    )
    workflow_rules: WorkflowRulesConfigSchema = Field(
        default_factory=WorkflowRulesConfigSchema, description="工作流规则配置"
    )
    focus_point_priority: Dict[str, bool] = Field(
        default_factory=dict, description="关注点优先级配置"
    )
    error_handling: Dict[str, Any] = Field(
        default_factory=dict, description="错误处理配置"
    )
    # 支持strategies配置（复杂的状态机配置）
    strategies: Dict[str, Any] = Field(
        default_factory=dict, description="策略配置（状态机）"
    )
    business_rules: Dict[str, Any] = Field(
        default_factory=dict, description="业务规则配置"
    )


class KnowledgeBaseConfigSchema(BaseModel):
    """知识库配置模式"""
    model_config = ConfigDict(extra="forbid")

    enabled: bool = Field(False, description="是否启用知识库")
    retrieval: Dict[str, Any] = Field(
        default_factory=dict, description="检索配置"
    )
    features: Dict[str, bool] = Field(
        default_factory=dict, description="功能开关"
    )


class LoggingConfigSchema(BaseModel):
    """日志配置模式"""
    model_config = ConfigDict(extra="forbid")

    level: LogLevel = Field(LogLevel.INFO, description="日志级别")
    format: str = Field("json", description="日志格式")
    max_file_size: str = Field("10MB", description="最大文件大小")
    backup_count: int = Field(5, description="备份文件数量", ge=1, le=100)

    @field_validator('max_file_size')
    @classmethod
    def validate_file_size(cls, v):
        """验证文件大小格式"""
        import re
        if not re.match(r'^\d+[KMGT]?B$', v.upper()):
            raise ValueError('文件大小格式错误，应为如 "10MB" 的格式')
        return v


class RootConfigSchema(BaseModel):
    """根配置模式 - 整个配置文件的顶级结构"""
    model_config = ConfigDict(extra="allow")

    app: AppConfigSchema = Field(
        default_factory=AppConfigSchema, description="应用配置"
    )
    data: DatabaseConfigSchema = Field(
        default_factory=DatabaseConfigSchema, description="数据相关配置"
    )
    llm: LLMConfigSchema = Field(
        default_factory=LLMConfigSchema, description="LLM配置"
    )
    system: SystemConfigSchema = Field(
        default_factory=SystemConfigSchema, description="系统配置"
    )
    thresholds: ThresholdsConfigSchema = Field(
        default_factory=ThresholdsConfigSchema, description="阈值配置"
    )
    security: SecurityConfigSchema = Field(
        default_factory=SecurityConfigSchema, description="安全配置"
    )
    business: BusinessConfigSchema = Field(
        default_factory=BusinessConfigSchema, description="业务配置"
    )
    knowledge_base: KnowledgeBaseConfigSchema = Field(
        default_factory=KnowledgeBaseConfigSchema, description="知识库配置"
    )
    logging: LoggingConfigSchema = Field(
        default_factory=LoggingConfigSchema, description="日志配置"
    )
    performance: PerformanceConfigSchema = Field(
        default_factory=PerformanceConfigSchema, description="性能配置"
    )
    # 支持模块化配置文件的元数据
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="配置元数据", alias="_metadata"
    )

    @model_validator(mode='after')
    def validate_config_integrity(self):
        """验证配置完整性"""
        # 验证数据库配置
        if hasattr(self, 'data') and self.data:
            # data字段现在是DatabaseConfigSchema类型，会自动验证
            pass

        # 验证LLM配置完整性
        if hasattr(self, 'llm') and self.llm:
            # LLM配置的完整性验证已在LLMConfigSchema中实现
            pass

        # 验证性能配置与其他配置的一致性
        if hasattr(self, 'performance') and hasattr(self, 'llm'):
            perf_config = self.performance
            llm_config = self.llm

            # 检查LLM超时配置一致性
            if hasattr(perf_config, 'llm_timeout') and hasattr(llm_config, 'default_params'):
                default_params = llm_config.default_params
                if 'timeout' in default_params:
                    if perf_config.llm_timeout != default_params['timeout']:
                        # 这里可以给出警告而不是错误
                        pass

        return self


# 导出所有模式类供外部使用
__all__ = [
    # 基础枚举
    'LogLevel',
    'Environment',

    # 应用配置
    'AppConfigSchema',

    # 数据库配置
    'DatabaseConnectionConfigSchema',
    'DatabaseQueriesConfigSchema',
    'DatabaseTablesConfigSchema',
    'DatabaseMigrationsConfigSchema',
    'DatabaseBackupConfigSchema',
    'DatabaseSecurityConfigSchema',
    'DatabaseMonitoringConfigSchema',
    'DatabaseConfigSchema',

    # LLM配置
    'LLMModelConfigSchema',
    'LLMProviderConfigSchema',
    'ModelCapabilitiesConfigSchema',
    'ModelSelectionConfigSchema',
    'LLMMonitoringConfigSchema',
    'LLMOptimizationConfigSchema',
    'LLMConfigSchema',

    # 业务配置
    'ActionHandlersConfigSchema',
    'DocumentConfirmationConfigSchema',
    'QualityControlConfigSchema',
    'RequirementCollectionConfigSchema',
    'RetryConfigSchema',
    'DecisionStrategiesConfigSchema',
    'WorkflowRulesConfigSchema',
    'BusinessConfigSchema',

    # 性能配置
    'CacheConfigSchema',
    'TimeoutConfigSchema',
    'RetryPerformanceConfigSchema',
    'ConcurrencyConfigSchema',
    'MonitoringPerformanceConfigSchema',
    'LimitsConfigSchema',
    'OptimizationConfigSchema',
    'AgentCacheConfigSchema',
    'PerformanceConfigSchema',

    # 安全配置
    'AuthenticationConfigSchema',
    'AuthorizationConfigSchema',
    'DataProtectionConfigSchema',
    'AuditConfigSchema',
    'SecurityLimitsConfigSchema',
    'EncryptionConfigSchema',
    'SecurityMonitoringConfigSchema',
    'ContentModerationConfigSchema',
    'AccessControlConfigSchema',
    'InputValidationConfigSchema',
    'SecurityConfigSchema',

    # 系统配置
    'SystemConfigSchema',
    'ThresholdsConfigSchema',
    'KnowledgeBaseConfigSchema',
    'LoggingConfigSchema',

    # 根配置
    'RootConfigSchema',
]
