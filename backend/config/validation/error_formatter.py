#!/usr/bin/env python3
"""
人类可读错误报告格式化器

将配置验证错误转换为清晰、可操作的错误信息
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from pydantic import ValidationError
import yaml


class ConfigErrorFormatter:
    """配置错误格式化器"""
    
    def __init__(self):
        self.error_patterns = self._build_error_patterns()
        self.solution_templates = self._build_solution_templates()
    
    def _build_error_patterns(self) -> Dict[str, Dict[str, Any]]:
        """构建错误模式匹配规则"""
        return {
            "missing_required_field": {
                "pattern": r"Field required",
                "template": "缺少必需字段 '{field}'",
                "severity": "error"
            },
            "invalid_type_number": {
                "pattern": r"Input should be a valid (number|integer|float)",
                "template": "字段类型错误：期望数字类型，实际输入 '{input_value}'",
                "severity": "error"
            },
            "invalid_type_boolean": {
                "pattern": r"Input should be a valid (bool|boolean)",
                "template": "字段类型错误：期望布尔值（true/false），实际输入 '{input_value}'",
                "severity": "error"
            },
            "invalid_type_string": {
                "pattern": r"Input should be a valid (string|str)",
                "template": "字段类型错误：期望字符串类型，实际输入 '{input_value}'",
                "severity": "error"
            },
            "string_too_short": {
                "pattern": r"String should have at least (\d+) character",
                "template": "字符串长度不足：至少需要 {min_length} 个字符，当前为空字符串",
                "severity": "error"
            },
            "number_greater_than": {
                "pattern": r"Input should be greater than or equal to (\d+\.?\d*)",
                "template": "数值过小：必须大于等于 {min_value}，当前值为 {input_value}",
                "severity": "error"
            },
            "number_less_than": {
                "pattern": r"Input should be less than or equal to (\d+\.?\d*)",
                "template": "数值过大：必须小于等于 {max_value}，当前值为 {input_value}",
                "severity": "error"
            },
            "invalid_choice": {
                "pattern": r"Input should be one of \[(.*?)\]",
                "template": "无效选择：允许的值为 [{choices}]，当前值为 '{input_value}'",
                "severity": "error"
            },
            "value_error": {
                "pattern": r"Value error, (.*)",
                "template": "业务逻辑错误：{error_detail}",
                "severity": "error"
            },
            "url_validation": {
                "pattern": r"URL.*invalid|invalid.*URL",
                "template": "URL格式错误：'{input_value}' 不是有效的URL格式",
                "severity": "error"
            },
            "yaml_syntax_error": {
                "pattern": r"while parsing.*line (\d+), column (\d+)",
                "template": "YAML语法错误：第 {line} 行，第 {column} 列",
                "severity": "error"
            }
        }
    
    def _build_solution_templates(self) -> Dict[str, Dict[str, Any]]:
        """构建解决方案模板"""
        return {
            "missing_required_field": {
                "solutions": [
                    "在配置文件中添加缺少的字段",
                    "检查字段名是否拼写正确",
                    "参考配置模板或文档中的示例"
                ],
                "examples": {
                    "model_name": "model_name: 'your-model-name'",
                    "provider": "provider: 'openai'",
                    "default_model": "default_model: 'gpt-3.5-turbo'"
                }
            },
            "invalid_type_number": {
                "solutions": [
                    "移除数字值周围的引号",
                    "确保使用正确的数字格式",
                    "检查是否误用了字符串"
                ],
                "examples": {
                    "integer": "timeout: 30  # 不是 '30'",
                    "float": "temperature: 0.7  # 不是 '0.7'"
                }
            },
            "invalid_type_boolean": {
                "solutions": [
                    "使用 true/false 而不是字符串",
                    "移除布尔值周围的引号",
                    "确保大小写正确"
                ],
                "examples": {
                    "boolean": "enabled: true  # 不是 'true' 或 'True'"
                }
            },
            "invalid_type_string": {
                "solutions": [
                    "为字符串值添加引号",
                    "检查特殊字符是否需要转义",
                    "确保字符串格式正确"
                ],
                "examples": {
                    "string": "name: 'my-config'  # 字符串需要引号"
                }
            },
            "string_too_short": {
                "solutions": [
                    "提供非空的字符串值",
                    "检查是否误用了空字符串",
                    "确保字段值有意义且完整"
                ],
                "examples": {
                    "non_empty": "model_name: 'gpt-3.5-turbo'  # 不能为空"
                }
            },
            "number_greater_than": {
                "solutions": [
                    "增大数值到允许的最小值以上",
                    "检查配置文档中的取值范围",
                    "避免使用负数（如果不允许）"
                ],
                "examples": {
                    "positive": "timeout: 1  # 必须大于0"
                }
            },
            "number_less_than": {
                "solutions": [
                    "减小数值到允许的最大值以下",
                    "检查配置文档中的取值范围",
                    "考虑使用推荐的默认值"
                ],
                "examples": {
                    "within_range": "temperature: 1.0  # 不能超过2.0"
                }
            },
            "invalid_choice": {
                "solutions": [
                    "从允许的选项中选择一个值",
                    "检查拼写和大小写",
                    "参考配置文档中的有效选项"
                ]
            },
            "value_error": {
                "solutions": [
                    "检查业务逻辑约束",
                    "确保引用的资源存在",
                    "验证配置项之间的依赖关系"
                ]
            },
            "url_validation": {
                "solutions": [
                    "使用完整的URL格式（包含协议）",
                    "检查URL拼写是否正确",
                    "确保URL可访问"
                ],
                "examples": {
                    "url": "api_base: 'https://api.example.com'  # 完整URL"
                }
            },
            "yaml_syntax_error": {
                "solutions": [
                    "检查YAML缩进是否正确（使用空格，不要使用Tab）",
                    "确保冒号后有空格",
                    "检查引号是否配对",
                    "使用YAML验证工具检查语法"
                ]
            }
        }
    
    def format_validation_error(self, error: ValidationError, config_file: str = "") -> str:
        """格式化Pydantic验证错误"""
        formatted_errors = []
        
        for error_detail in error.errors():
            formatted_error = self._format_single_error(error_detail, config_file)
            formatted_errors.append(formatted_error)
        
        return self._build_error_report(formatted_errors, config_file)
    
    def format_yaml_error(self, error: yaml.YAMLError, config_file: str = "") -> str:
        """格式化YAML解析错误"""
        error_msg = str(error)
        
        # 提取行号和列号
        line_match = re.search(r'line (\d+)', error_msg)
        column_match = re.search(r'column (\d+)', error_msg)
        
        formatted_error = {
            "type": "YAML语法错误",
            "message": "配置文件YAML语法不正确",
            "details": error_msg,
            "location": {
                "line": line_match.group(1) if line_match else "未知",
                "column": column_match.group(1) if column_match else "未知"
            },
            "solutions": self.solution_templates["yaml_syntax_error"]["solutions"],
            "severity": "error"
        }
        
        return self._build_error_report([formatted_error], config_file)
    
    def _format_single_error(self, error_detail: Dict[str, Any], config_file: str) -> Dict[str, Any]:
        """格式化单个错误"""
        error_type = error_detail.get("type", "unknown")
        error_msg = error_detail.get("msg", "")
        field_path = " -> ".join(str(loc) for loc in error_detail.get("loc", []))
        input_value = error_detail.get("input", "")
        
        # 匹配错误模式
        matched_pattern = None
        for pattern_name, pattern_info in self.error_patterns.items():
            if re.search(pattern_info["pattern"], error_msg, re.IGNORECASE):
                matched_pattern = pattern_name
                break
        
        # 生成人类可读的错误信息
        if matched_pattern:
            human_message = self._generate_human_message(matched_pattern, error_msg, field_path, input_value)
            solutions = self.solution_templates.get(matched_pattern, {}).get("solutions", [])
            examples = self.solution_templates.get(matched_pattern, {}).get("examples", {})
        else:
            human_message = f"配置验证失败：{error_msg}"
            solutions = ["请检查配置文件格式和内容", "参考配置文档和示例"]
            examples = {}
        
        return {
            "type": error_type,
            "field": field_path,
            "message": human_message,
            "original_error": error_msg,
            "input_value": str(input_value) if input_value else "未提供",
            "solutions": solutions,
            "examples": examples,
            "severity": self.error_patterns.get(matched_pattern, {}).get("severity", "error")
        }
    
    def _generate_human_message(self, pattern_name: str, error_msg: str, field_path: str, input_value: Any) -> str:
        """生成人类可读的错误信息"""
        pattern_info = self.error_patterns[pattern_name]
        template = pattern_info["template"]

        # 提取模式匹配的参数
        match = re.search(pattern_info["pattern"], error_msg, re.IGNORECASE)

        # 根据不同的模式生成消息
        if pattern_name == "missing_required_field":
            return template.format(field=field_path.split(' -> ')[-1] if field_path else "未知字段")
        elif pattern_name.startswith("invalid_type"):
            return template.format(input_value=str(input_value))
        elif pattern_name == "string_too_short":
            min_length = match.group(1) if match else "1"
            return template.format(min_length=min_length)
        elif pattern_name == "number_greater_than":
            min_value = match.group(1) if match else "0"
            return template.format(min_value=min_value, input_value=str(input_value))
        elif pattern_name == "number_less_than":
            max_value = match.group(1) if match else "未知"
            return template.format(max_value=max_value, input_value=str(input_value))
        elif pattern_name == "invalid_choice":
            choices = match.group(1) if match else "未知选项"
            return template.format(choices=choices, input_value=str(input_value))
        elif pattern_name == "value_error":
            error_detail = match.group(1) if match else error_msg
            return template.format(error_detail=error_detail)
        elif pattern_name == "url_validation":
            return template.format(input_value=str(input_value))
        elif pattern_name == "yaml_syntax_error":
            line = match.group(1) if match else "未知"
            column = match.group(2) if match else "未知"
            return template.format(line=line, column=column)

        return template
    
    def _build_error_report(self, formatted_errors: List[Dict[str, Any]], config_file: str) -> str:
        """构建完整的错误报告"""
        lines = []
        
        # 报告头部
        lines.extend([
            "🚨 配置验证失败",
            "=" * 50
        ])
        
        if config_file:
            lines.append(f"📁 文件: {config_file}")
        
        lines.extend([
            f"❌ 发现 {len(formatted_errors)} 个错误",
            ""
        ])
        
        # 错误详情
        for i, error in enumerate(formatted_errors, 1):
            lines.extend([
                f"错误 {i}: {error['message']}",
                f"   📍 位置: {error['field']}" if error['field'] else "   📍 位置: 根级别",
                f"   🔍 输入值: {error['input_value']}"
            ])
            
            if error.get('solutions'):
                lines.append("   💡 解决方案:")
                for solution in error['solutions']:
                    lines.append(f"      • {solution}")
            
            if error.get('examples'):
                lines.append("   📝 示例:")
                for key, example in error['examples'].items():
                    lines.append(f"      {key}: {example}")
            
            lines.append("")
        
        # 报告尾部
        lines.extend([
            "📚 更多帮助:",
            "   • 查看配置文档: docs/development/配置管理指南.md",
            "   • 运行配置验证: python tools/config_validation_cli.py",
            "   • 查看配置示例: backend/config/defaults/",
            ""
        ])
        
        return "\n".join(lines)
    
    def format_generic_error(self, error: Exception, config_file: str = "", context: str = "") -> str:
        """格式化通用错误"""
        error_type = type(error).__name__
        error_msg = str(error)
        
        lines = [
            "🚨 配置处理失败",
            "=" * 50
        ]
        
        if config_file:
            lines.append(f"📁 文件: {config_file}")
        
        if context:
            lines.append(f"🔍 上下文: {context}")
        
        lines.extend([
            f"❌ 错误类型: {error_type}",
            f"📝 错误信息: {error_msg}",
            "",
            "💡 建议:",
            "   • 检查文件是否存在且可读",
            "   • 验证文件格式是否正确",
            "   • 查看详细的错误信息定位问题",
            "",
            "📚 获取帮助:",
            "   • 运行: python tools/config_validation_cli.py --help",
            "   • 查看文档: docs/development/",
            ""
        ])
        
        return "\n".join(lines)


def format_config_error(error: Exception, config_file: str = "", context: str = "") -> str:
    """格式化配置错误的便捷函数"""
    formatter = ConfigErrorFormatter()
    
    if isinstance(error, ValidationError):
        return formatter.format_validation_error(error, config_file)
    elif isinstance(error, yaml.YAMLError):
        return formatter.format_yaml_error(error, config_file)
    else:
        return formatter.format_generic_error(error, config_file, context)


# 示例用法
if __name__ == "__main__":
    # 测试错误格式化
    formatter = ConfigErrorFormatter()
    
    # 模拟一个ValidationError
    try:
        from .schemas import LLMConfigSchema
        invalid_config = {
            "models": {
                "test-model": {
                    "provider": "test"
                    # 缺少 model_name
                }
            }
        }
        LLMConfigSchema(**invalid_config)
    except ValidationError as e:
        formatted_error = formatter.format_validation_error(e, "test_config.yaml")
        print(formatted_error)
    
    # 模拟一个YAML错误
    try:
        yaml_content = """
default_model: test
models:
  test-model:
    provider: test
  model_name: test  # 错误的缩进
        """
        yaml.safe_load(yaml_content)
    except yaml.YAMLError as e:
        formatted_error = formatter.format_yaml_error(e, "test_config.yaml")
        print(formatted_error)
