#!/usr/bin/env python3
"""
配置验证器

基于Pydantic的配置验证核心实现
"""

import logging
from typing import Dict, Any, List, Optional, Union, Tuple
from dataclasses import dataclass
from pathlib import Path
import yaml

from pydantic import ValidationError

from .schemas import RootConfigSchema, DatabaseConfigSchema
from .exceptions import (
    ConfigValidationError,
    SchemaValidationError,
    RequiredFieldMissingError,
    InvalidTypeError,
    ValueConstraintError,
    UnknownConfigKeyError,
    ConfigIntegrityError
)

logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """验证结果数据类"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    validated_config: Optional[Dict[str, Any]] = None
    schema_errors: List[Dict[str, Any]] = None
    
    def get_error_report(self) -> str:
        """获取人类可读的错误报告"""
        if self.is_valid:
            return "✅ 配置验证通过"
        
        report_lines = ["❌ 配置验证失败\n"]
        
        if self.errors:
            report_lines.append("🔴 错误:")
            for i, error in enumerate(self.errors, 1):
                report_lines.append(f"  {i}. {error}")
            report_lines.append("")
        
        if self.warnings:
            report_lines.append("🟡 警告:")
            for i, warning in enumerate(self.warnings, 1):
                report_lines.append(f"  {i}. {warning}")
            report_lines.append("")
        
        if self.schema_errors:
            report_lines.append("📋 详细验证错误:")
            for error in self.schema_errors:
                loc = " -> ".join(str(x) for x in error.get('loc', []))
                msg = error.get('msg', '未知错误')
                input_val = error.get('input', '')
                
                if loc:
                    report_lines.append(f"  字段 '{loc}': {msg}")
                    if input_val:
                        report_lines.append(f"    输入值: {input_val}")
                else:
                    report_lines.append(f"  {msg}")
                    if input_val:
                        report_lines.append(f"    输入值: {input_val}")
        
        return "\n".join(report_lines)
    
    def get_summary(self) -> str:
        """获取验证结果摘要"""
        if self.is_valid:
            return "配置验证通过"
        
        error_count = len(self.errors)
        warning_count = len(self.warnings)
        
        summary_parts = []
        if error_count > 0:
            summary_parts.append(f"{error_count} 个错误")
        if warning_count > 0:
            summary_parts.append(f"{warning_count} 个警告")
        
        return f"配置验证失败: {', '.join(summary_parts)}"


class ConfigValidator:
    """配置验证器
    
    提供基于Pydantic的配置验证功能
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.root_schema = RootConfigSchema
        
        # 模块级别的schema映射
        self.module_schemas = {
            'app': 'AppConfigSchema',
            'data.database': DatabaseConfigSchema,
            'llm': 'LLMConfigSchema',
            'system': 'SystemConfigSchema',
            'thresholds': 'ThresholdsConfigSchema',
            'security': 'SecurityConfigSchema',
            'business': 'BusinessConfigSchema',
            'knowledge_base': 'KnowledgeBaseConfigSchema',
            'logging': 'LoggingConfigSchema'
        }
    
    def validate_config(self, config_data: Dict[str, Any], 
                       strict: bool = True) -> ValidationResult:
        """验证配置数据
        
        Args:
            config_data: 要验证的配置数据
            strict: 是否严格模式（禁止未知字段）
            
        Returns:
            ValidationResult: 验证结果
        """
        errors = []
        warnings = []
        schema_errors = []
        validated_config = None
        
        try:
            # 使用根schema验证整个配置
            validated_config_obj = self.root_schema(**config_data)
            validated_config = validated_config_obj.model_dump()
            
            self.logger.info("配置验证通过")
            return ValidationResult(
                is_valid=True,
                errors=[],
                warnings=warnings,
                validated_config=validated_config
            )
            
        except ValidationError as e:
            # 处理Pydantic验证错误
            schema_errors = e.errors()
            for error in schema_errors:
                error_msg = self._format_validation_error(error)
                errors.append(error_msg)
            
            self.logger.error(f"配置验证失败: {len(errors)} 个错误")
            
        except Exception as e:
            errors.append(f"配置验证异常: {str(e)}")
            self.logger.error(f"配置验证异常: {e}")
        
        return ValidationResult(
            is_valid=False,
            errors=errors,
            warnings=warnings,
            validated_config=validated_config,
            schema_errors=schema_errors
        )
    
    def validate_config_file(self, file_path: Union[str, Path]) -> ValidationResult:
        """验证配置文件
        
        Args:
            file_path: 配置文件路径
            
        Returns:
            ValidationResult: 验证结果
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            return ValidationResult(
                is_valid=False,
                errors=[f"配置文件不存在: {file_path}"],
                warnings=[]
            )
        
        try:
            # 读取YAML文件
            with open(file_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            if config_data is None:
                return ValidationResult(
                    is_valid=False,
                    errors=[f"配置文件为空: {file_path}"],
                    warnings=[]
                )
            
            # 验证配置数据
            result = self.validate_config(config_data)
            
            # 添加文件路径信息到错误消息
            if not result.is_valid:
                result.errors = [f"文件 {file_path}: {error}" for error in result.errors]
            
            return result
            
        except yaml.YAMLError as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"YAML格式错误 {file_path}: {str(e)}"],
                warnings=[]
            )
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"读取配置文件失败 {file_path}: {str(e)}"],
                warnings=[]
            )
    
    def validate_partial_config(self, config_key: str, 
                               config_value: Any) -> ValidationResult:
        """验证部分配置
        
        Args:
            config_key: 配置键路径，如 "llm.models.gpt4"
            config_value: 配置值
            
        Returns:
            ValidationResult: 验证结果
        """
        # 根据配置键确定使用的schema
        schema_class = self._get_schema_for_key(config_key)
        
        if not schema_class:
            return ValidationResult(
                is_valid=False,
                errors=[f"未找到配置键 '{config_key}' 对应的验证模式"],
                warnings=[]
            )
        
        try:
            # 验证配置值
            validated_obj = schema_class(**config_value)
            
            return ValidationResult(
                is_valid=True,
                errors=[],
                warnings=[],
                validated_config=validated_obj.model_dump()
            )
            
        except ValidationError as e:
            errors = []
            schema_errors = e.errors()
            
            for error in schema_errors:
                error_msg = self._format_validation_error(error, config_key)
                errors.append(error_msg)
            
            return ValidationResult(
                is_valid=False,
                errors=errors,
                warnings=[],
                schema_errors=schema_errors
            )
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"验证配置键 '{config_key}' 时出错: {str(e)}"],
                warnings=[]
            )
    
    def check_required_fields(self, config_data: Dict[str, Any]) -> List[str]:
        """检查必需字段
        
        Args:
            config_data: 配置数据
            
        Returns:
            List[str]: 缺失的必需字段列表
        """
        missing_fields = []
        
        # 检查关键的必需字段
        required_paths = [
            ('app', 'name'),
            ('data', 'database', 'path'),
            ('llm', 'default_model')
        ]
        
        for path in required_paths:
            current = config_data
            field_path = []
            
            for key in path:
                field_path.append(key)
                if not isinstance(current, dict) or key not in current:
                    missing_fields.append('.'.join(field_path))
                    break
                current = current[key]
        
        return missing_fields
    
    def _format_validation_error(self, error: Dict[str, Any], 
                                prefix: str = "") -> str:
        """格式化验证错误消息"""
        loc = error.get('loc', ())
        msg = error.get('msg', '未知错误')
        error_type = error.get('type', 'unknown')
        input_val = error.get('input', '')
        
        # 构建字段路径
        field_path = '.'.join(str(x) for x in loc)
        if prefix:
            field_path = f"{prefix}.{field_path}" if field_path else prefix
        
        # 根据错误类型提供更友好的消息
        if error_type == 'missing':
            return f"必需字段 '{field_path}' 缺失"
        elif error_type == 'type_error':
            return f"字段 '{field_path}' 类型错误: {msg}"
        elif error_type == 'value_error':
            return f"字段 '{field_path}' 值错误: {msg} (输入值: {input_val})"
        else:
            return f"字段 '{field_path}': {msg} (输入值: {input_val})"
    
    def _get_schema_for_key(self, config_key: str):
        """根据配置键获取对应的schema类"""
        # 简单的键到schema的映射
        key_parts = config_key.split('.')
        
        if key_parts[0] in ['app']:
            from .schemas import AppConfigSchema
            return AppConfigSchema
        elif key_parts[0] == 'data' and len(key_parts) > 1 and key_parts[1] == 'database':
            return DatabaseConfigSchema
        elif key_parts[0] == 'llm':
            from .schemas import LLMConfigSchema
            return LLMConfigSchema
        elif key_parts[0] == 'system':
            from .schemas import SystemConfigSchema
            return SystemConfigSchema
        elif key_parts[0] == 'thresholds':
            from .schemas import ThresholdsConfigSchema
            return ThresholdsConfigSchema
        elif key_parts[0] == 'security':
            from .schemas import SecurityConfigSchema
            return SecurityConfigSchema
        elif key_parts[0] == 'business':
            from .schemas import BusinessConfigSchema
            return BusinessConfigSchema
        elif key_parts[0] == 'knowledge_base':
            from .schemas import KnowledgeBaseConfigSchema
            return KnowledgeBaseConfigSchema
        elif key_parts[0] == 'logging':
            from .schemas import LoggingConfigSchema
            return LoggingConfigSchema
        
        return None


# 全局配置验证器实例
_config_validator = None


def get_config_validator() -> ConfigValidator:
    """获取配置验证器实例（单例模式）"""
    global _config_validator
    
    if _config_validator is None:
        _config_validator = ConfigValidator()
    
    return _config_validator
