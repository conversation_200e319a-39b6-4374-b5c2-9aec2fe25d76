#!/usr/bin/env python3
"""
配置Schema测试脚本

用于验证配置Schema的正确性和完整性
"""

import yaml
import json
from pathlib import Path
from typing import Dict, Any
from pydantic import ValidationError

from .schemas import (
    BusinessConfigSchema,
    LLMConfigSchema,
    DatabaseConfigSchema,
    PerformanceConfigSchema,
    SecurityConfigSchema,
    RootConfigSchema
)


def load_yaml_file(file_path: str) -> Dict[str, Any]:
    """加载YAML配置文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"加载文件失败 {file_path}: {e}")
        return {}


def test_business_config():
    """测试业务配置Schema"""
    print("测试业务配置Schema...")
    
    config_path = "backend/config/business/rules.yaml"
    if Path(config_path).exists():
        config_data = load_yaml_file(config_path)
        
        try:
            # 测试完整配置
            business_schema = BusinessConfigSchema(**config_data)
            print("✅ 业务配置Schema验证通过")
            
            # 测试部分配置
            if 'action_handlers' in config_data:
                from .schemas import ActionHandlersConfigSchema
                handlers_schema = ActionHandlersConfigSchema(**config_data['action_handlers'])
                print("✅ 动作处理器配置Schema验证通过")
                
        except ValidationError as e:
            print(f"❌ 业务配置Schema验证失败: {e}")
        except Exception as e:
            print(f"❌ 业务配置Schema测试异常: {e}")
    else:
        print(f"⚠️  配置文件不存在: {config_path}")


def test_llm_config():
    """测试LLM配置Schema"""
    print("测试LLM配置Schema...")
    
    config_path = "backend/config/llm/models.yaml"
    if Path(config_path).exists():
        config_data = load_yaml_file(config_path)
        
        try:
            # 测试LLM配置
            llm_schema = LLMConfigSchema(**config_data)
            print("✅ LLM配置Schema验证通过")
            
            # 测试模型配置
            if 'models' in config_data:
                from .schemas import LLMModelConfigSchema
                for model_name, model_config in config_data['models'].items():
                    model_schema = LLMModelConfigSchema(**model_config)
                print("✅ LLM模型配置Schema验证通过")
                
        except ValidationError as e:
            print(f"❌ LLM配置Schema验证失败: {e}")
        except Exception as e:
            print(f"❌ LLM配置Schema测试异常: {e}")
    else:
        print(f"⚠️  配置文件不存在: {config_path}")


def test_database_config():
    """测试数据库配置Schema"""
    print("测试数据库配置Schema...")
    
    config_path = "backend/config/data/database.yaml"
    if Path(config_path).exists():
        config_data = load_yaml_file(config_path)
        
        try:
            # 测试数据库配置
            db_schema = DatabaseConfigSchema(**config_data)
            print("✅ 数据库配置Schema验证通过")
            
        except ValidationError as e:
            print(f"❌ 数据库配置Schema验证失败: {e}")
        except Exception as e:
            print(f"❌ 数据库配置Schema测试异常: {e}")
    else:
        print(f"⚠️  配置文件不存在: {config_path}")


def test_performance_config():
    """测试性能配置Schema"""
    print("测试性能配置Schema...")
    
    config_path = "backend/config/system/performance.yaml"
    if Path(config_path).exists():
        config_data = load_yaml_file(config_path)
        
        try:
            # 测试性能配置
            perf_schema = PerformanceConfigSchema(**config_data)
            print("✅ 性能配置Schema验证通过")
            
        except ValidationError as e:
            print(f"❌ 性能配置Schema验证失败: {e}")
        except Exception as e:
            print(f"❌ 性能配置Schema测试异常: {e}")
    else:
        print(f"⚠️  配置文件不存在: {config_path}")


def test_security_config():
    """测试安全配置Schema"""
    print("测试安全配置Schema...")
    
    config_path = "backend/config/system/security.yaml"
    if Path(config_path).exists():
        config_data = load_yaml_file(config_path)
        
        try:
            # 测试安全配置
            security_schema = SecurityConfigSchema(**config_data)
            print("✅ 安全配置Schema验证通过")
            
        except ValidationError as e:
            print(f"❌ 安全配置Schema验证失败: {e}")
        except Exception as e:
            print(f"❌ 安全配置Schema测试异常: {e}")
    else:
        print(f"⚠️  配置文件不存在: {config_path}")


def test_schema_examples():
    """测试Schema示例数据"""
    print("测试Schema示例数据...")
    
    # 测试最小配置
    minimal_config = {
        "app": {
            "name": "test-app"
        },
        "llm": {
            "default_model": "test-model",
            "models": {
                "test-model": {
                    "provider": "test",
                    "model_name": "test-model"
                }
            }
        }
    }
    
    try:
        root_schema = RootConfigSchema(**minimal_config)
        print("✅ 最小配置Schema验证通过")
    except ValidationError as e:
        print(f"❌ 最小配置Schema验证失败: {e}")
    except Exception as e:
        print(f"❌ 最小配置Schema测试异常: {e}")


def main():
    """主测试函数"""
    print("开始配置Schema测试...")
    print("=" * 50)
    
    test_business_config()
    print()
    
    test_llm_config()
    print()
    
    test_database_config()
    print()
    
    test_performance_config()
    print()
    
    test_security_config()
    print()
    
    test_schema_examples()
    print()
    
    print("=" * 50)
    print("配置Schema测试完成")


if __name__ == "__main__":
    main()
