# ============================================================================
# 动态关键词配置（从unified_config.yaml拆分）
# ============================================================================
# 文件说明：从unified_config.yaml拆分的动态关键词配置
# 拆分时间：2025-08-18 19:22:20
# 源配置块：direct_domain_selection, domain_selection_mapping, keyword_rules
# 维护责任：配置管理团队
# 更新频率：根据业务需求
# ============================================================================

keyword_categories:
  greeting:
    keywords:
    - 你好
    - 您好
    - hi
    - hello
    - 早上好
    - 下午好
    - 晚上好
    confidence: 0.9
    priority: 5
    enabled: true
  system_capability_query:
    keywords:
    - 能做什么
    - 有什么功能
    - 功能
    - 能力
    - 帮助
    - help
    - 使用方法
    - 怎么用
    confidence: 0.8
    priority: 5
    enabled: true
  requirement_collection:
    keywords:
    - 需求
    - 要求
    - 希望
    - 想要
    - 需要
    - 期望
    - 目标
    - 功能需求
    - 业务需求
    confidence: 0.8
    priority: 7
    enabled: true
  confirmation:
    keywords:
    - 确认
    - 没问题
    - 正确
    - 同意
    - 好的
    - ok
    - okay
    - 是的
    - 对的
    confidence: 0.9
    priority: 6
    enabled: true
  modification:
    keywords:
    - 修改
    - 更改
    - 调整
    - 改正
    - 纠正
    - 重新
    - 不对
    - 错了
    confidence: 0.8
    priority: 8
    enabled: true
  restart:
    keywords:
    - 重新开始
    - 重来
    - 重新
    - 从头开始
    - 清空
    - 重置
    - restart
    - reset
    confidence: 0.9
    priority: 10
    enabled: true
domain_keywords:
  product_design:
    keywords:
    - 产品设计
    - 用户体验
    - 界面设计
    - 交互设计
    - 原型
    - UI
    - UX
    - 设计
    confidence: 0.8
    enabled: true
  technical_development:
    keywords:
    - 技术开发
    - 系统架构
    - 技术实现
    - 编程
    - 开发
    - 代码
    - 系统
    - 架构
    confidence: 0.8
    enabled: true
  business_process:
    keywords:
    - 业务流程
    - 工作流程
    - 业务规则
    - 流程
    - 规则
    - 操作
    - 步骤
    confidence: 0.8
    enabled: true
  data_analysis:
    keywords:
    - 数据分析
    - 数据
    - 分析
    - 报告
    - 统计
    - 指标
    - 监控
    confidence: 0.8
    enabled: true
emotion_keywords:
  positive:
    keywords:
    - 满意
    - 很好
    - 不错
    - 棒
    - 优秀
    - 完美
    - 喜欢
    confidence: 0.7
    enabled: true
  negative:
    keywords:
    - 不满意
    - 不好
    - 差
    - 糟糕
    - 失望
    - 不喜欢
    - 问题
    confidence: 0.7
    enabled: true
  neutral:
    keywords:
    - 一般
    - 还行
    - 普通
    - 可以
    - 凑合
    confidence: 0.6
    enabled: true
matching_config:
  strategy: fuzzy
  fuzzy_matching:
    enabled: true
    similarity_threshold: 0.8
    max_edit_distance: 2
  semantic_matching:
    enabled: false
    model: sentence-transformers
    threshold: 0.7
  optimization:
    case_sensitive: false
    ignore_punctuation: true
    normalize_whitespace: true
    remove_stop_words: false
weight_config:
  base_weights:
    exact_match: 1.0
    fuzzy_match: 0.8
    semantic_match: 0.6
  position_weights:
    beginning: 1.2
    middle: 1.0
    end: 0.8
  frequency_weights:
    high_frequency: 1.1
    medium_frequency: 1.0
    low_frequency: 0.9
dynamic_update:
  hot_reload: true
  check_interval: 300
  update_sources:
  - file_system
  - database
  - api
  validation:
    enabled: true
    schema_validation: true
    conflict_detection: true
performance:
  caching:
    enabled: true
    cache_size: 1000
    ttl: 3600
  indexing:
    enabled: true
    index_type: trie
    rebuild_interval: 86400
  concurrency:
    max_workers: 4
    batch_size: 100
monitoring:
  usage_tracking: true
  performance_monitoring: true
  quality_monitoring: true
  alerts:
    low_match_rate: 0.5
    high_error_rate: 0.1
experimental:
  ml_enhancement:
    enabled: false
    model_path: ''
  auto_discovery:
    enabled: false
    min_frequency: 10
    confidence_threshold: 0.8
  personalization:
    enabled: false
    user_specific_keywords: false
_metadata:
  file_version: '1.0'
  split_date: '2025-08-18T19:22:20.079685'
  source_blocks:
  - direct_domain_selection
  - domain_selection_mapping
  - keyword_rules
  config_type: keywords
  total_items: 316
direct_domain_selection:
  fallback_template: '让我直接帮您收集需求信息。请详细描述一下您的项目需求和期望目标。

    '
  main_template: '我理解您的需求可能比较特殊或复杂。为了确保为您提供最合适的服务，请直接从以下选项中选择最接近的领域：


    **请回复对应的数字或直接说出领域名称：**


    1️⃣ **平面设计** - Logo设计、海报制作、品牌视觉、宣传材料等

    2️⃣ **UI/UX设计** - 界面设计、用户体验、交互设计、原型制作等

    3️⃣ **营销推广** - 活动策划、广告投放、品牌推广、市场营销等

    4️⃣ **法律咨询** - 合同审查、知识产权、法律风险评估、诉讼咨询等

    5️⃣ **软件开发** - 网站建设、APP开发、小程序、系统开发等


    **或者您可以：**

    • 详细描述您的具体需求，我会重新为您分析

    • 说明您的项目目标和期望效果


    请选择最合适的选项，我会立即为您提供专业的需求分析服务。

    '
  simplified_template: '请从以下领域中选择最接近您需求的选项：


    1. 平面设计  2. UI/UX设计  3. 营销推广  4. 法律咨询  5. 软件开发


    请回复数字或领域名称，我会为您提供专业的需求分析。

    '
domain_selection_mapping:
  business:
    extraction:
      point_detail: '  [{index}] {point_name}: {point_value} (完整度: {completeness})'
    focus_points:
      all_completed: 所有关注点已完成采集，开始生成文档
      empty_list: 关注点列表为空
      found_next: '找到下一个待处理的关注点: {point_id} - {point_name}'
      generation_failed: '生成后续问题失败: {error}'
      progress_awareness:
        half_complete: '太棒了！我们已经完成了一半的需求收集 🎉


          ✅ **已完成**：{completed_count}/{total_count} 个关注点

          🎯 **当前重点**：{current_point}


          💡 **专业建议**：基于目前收集的信息，我发现您的项目有很好的可行性。继续保持这个节奏！

          '
        nearly_complete: '太好了！我们即将完成所有需求收集 🎯


          ✅ **已收集**：{completed_count} 个关注点

          🔚 **最后一步**：{current_point}


          💡 **即将完成**：回答完这个问题后，我就可以为您生成完整的需求文档了！

          '
        quarter_complete: '很好！我们已经完成了 25% 的需求收集 📊


          ✅ **已收集信息**：{completed_points}

          🔄 **进行中**：{current_point}

          ⏳ **待收集**：{remaining_points}


          💡 **小贴士**：您回答得很详细，这将帮助我生成更准确的需求文档。

          '
        three_quarters_complete: '非常棒！我们已经完成了 75% 的需求收集 🚀


          ✅ **几乎完成**：只剩下 {remaining_count} 个关键点需要确认

          📋 **即将生成**：专业的需求文档


          💡 **温馨提示**：最后几个问题通常是关键的实施细节，请仔细考虑。

          '
      searching_next: 寻找下一个待处理的关注点...
      skip_continue: 好的，我们继续下一个问题。
      skip_no_processing: 请求跳过问题，但没有找到正在处理的关注点。
      skip_processing: 正在处理跳过问题的请求...
    question:
      optimization_context: '优化问题生成使用的上下文: ''{user_context}'', 对话历史: ''{conversation_history}'''
      optimization_failed: '问题生成失败，使用默认问题: {error}'
      user_context: 用户希望了解相关信息
    suggestions:
      description_guidance: 关于「{point_name}」，您可以从以下方面思考：{description}。请告诉我您的具体想法或需求。
      general_guidance: 基于您目前提供的信息，我建议您可以从以下几个方面继续完善：项目的具体目标、预期的用户群体、技术实现方案等。
      general_suggestions: '基于您目前提供的全部信息，我有以下几点通用建议：


        {suggestions}


        您希望我详细解释哪个方面呢？'
      general_teaching_guidance: 我理解您需要指导。让我为您提供一些实用的方法和建议，帮助您更好地准备相关信息。
      multiple_points: 我注意到还有几个重要信息需要确认：{point_names}。让我们先从最重要的开始，您希望我针对哪个方面给您一些建议呢？
      no_pending: 没有待处理的关注点，提供与类别相关的通用建议。
      no_suggestions: 关注点 '{point_name}' 没有配置具体的建议，使用其描述信息作为引导。
      single_point: '关于「{point_name}」，我这里有一些建议供您参考：


        {formatted_suggestions}


        您觉得哪几点比较符合您的想法，或者您有其他补充吗？'
      single_point_simple: '关于「{point_name}」，我的一点建议是：


        {suggestions}


        您对此有什么看法？'
      smart_tips:
        budget_consideration: '💡 **预算规划小贴士**：

          • 建议预留 20% 的缓冲资金应对意外情况

          • 可以考虑分阶段实施，降低初期投入风险

          • 不同供应商的报价可能差异较大，建议多方比较

          '
        risk_management: '⚠️ **风险管控建议**：

          • 识别项目中的主要风险点

          • 准备备选方案和应急计划

          • 定期评估项目进展和风险状况

          '
        technical_choices: '🔧 **技术选择指导**：

          • 优先选择成熟稳定的技术方案

          • 考虑团队的技术能力和学习成本

          • 评估长期维护和扩展的便利性

          '
        timeline_planning: '⏰ **时间规划建议**：

          • 复杂项目通常需要比预期多 30% 的时间

          • 建议设置几个关键里程碑节点进行进度检查

          • 考虑节假日和团队成员的时间安排

          '
        user_experience: '👥 **用户体验要点**：

          • 简单易用比功能丰富更重要

          • 考虑不同用户群体的使用习惯

          • 提供清晰的操作指引和帮助信息

          '
  capabilities:
    explanation: '我的主要能力包括：


      1. 需求分析：帮助您梳理和明确项目需求

      2. 信息收集：通过有针对性的提问收集关键信息

      3. 文档生成：生成规范的需求文档

      4. 文档修改：根据您的反馈调整和完善文档


      我支持多个领域的需求分析，包括软件开发、UI/UX设计、市场营销等。请告诉我您的具体需求，我会为您提供专业的帮助。'
    full: '我的主要能力包括：


      1. 需求分析：帮助您梳理和明确项目需求

      2. 信息收集：通过有针对性的提问收集关键信息

      3. 文档生成：生成规范的需求文档

      4. 文档修改：根据您的反馈调整和完善文档


      我支持多个领域的需求分析，包括软件开发、UI/UX设计、市场营销等。请告诉我您的具体需求，我会为您提供专业的帮助。'
    main: '我可以帮助您：

      1. 📋 需求分析：深入了解您的项目需求

      2. 📝 需求整理：将零散的想法整理成清晰的需求文档

      3. 💡 建议提供：基于经验提供专业建议

      4. 📊 文档生成：自动生成专业的需求文档

      '
    simple: 我可以帮您分析需求、收集信息、生成文档。请告诉我您的具体需求。
  chat:
    friendly: 很高兴与您聊天！不过，我的专长是帮助分析需求和生成文档。您有什么项目需求需要我帮忙梳理吗？
    general: 很高兴与您聊天！不过，我的专长是帮助分析需求和生成文档。您有什么项目需求需要我帮忙梳理吗？
    simple: 很高兴与您交流！请告诉我您的项目需求，我来帮您分析。
  conversation:
    default:
      requirement_prompt: '请告诉我您的具体需求，我将为您提供专业的分析和建议。


        💡 **您可以从这些方面开始**：

        • 项目的基本想法和目标

        • 希望解决的问题或需求

        • 目标用户或使用场景

        • 预期的效果或成果


        💬 **温馨提示**：不用担心描述不够专业，我会通过提问帮您逐步完善所有细节。

        '
    modification:
      completed: 文档已根据您的要求进行修改。
      idle_state_prompt: 目前没有正在处理的文档。请先告诉我您的需求，我来帮您生成文档。
      need_more_info: 为了更好地修改文档，请您提供更具体的修改要求。
    restart:
      confirmation: 好的，我已经重置了会话状态。现在我们可以开始新的需求采集。请告诉我您的需求是什么？
  empathy:
    fallback: 我理解您的感受。请告诉我您的具体需求，我会尽力为您提供帮助。
    negative_general: 我理解您遇到的困难，这确实让人感到沮丧。请告诉我您希望如何解决这个问题，或者您需要什么样的帮助？
  exception:
    general_request:
      processing_error: 处理您的请求时遇到问题，请稍后再试或重新描述您的需求。
    rephrase:
      detailed: 抱歉我的问题可能不够清晰。让我换一种方式问：请您详细描述一下您的具体需求，这样我能更好地为您提供帮助。
    suggestions:
      fallback: 我建议您先明确项目的基本信息，比如项目类型、目标用户、主要功能等，这样我能为您提供更有针对性的建议。
  fallback:
    emergency: 抱歉，我遇到了一些问题。请告诉我您的具体需求，我会尽力帮助您。
    general: 我理解您的请求，请告诉我您的具体需求，我会尽力提供帮助。
    processing_failed: 请详细描述您的需求。
    requirement_prompt: 请描述您的详细需求。
    unknown_situation: 抱歉，我遇到了一些问题。请您重新描述一下需求，我会尽力帮助您。
  formatting:
    history:
      ai_prefix: 'AI助手: '
      empty: 暂无历史信息（这是新的对话）
      user_prefix: '用户: '
    status:
      unknown_focus_point: 未知关注点
  help:
    full: '我可以为您提供以下帮助：


      1. 需求梳理：帮您明确项目目标和具体需求

      2. 信息收集：通过专业问题收集关键信息

      3. 文档生成：自动生成规范的需求文档

      4. 文档优化：根据您的反馈完善文档内容


      请直接告诉我您的项目需求，我会引导您完成整个需求分析过程。'
    simple: 我可以帮您分析需求并生成文档。请告诉我您的项目需求。
  introduction:
    full: 您好！我是一个专注于需求分析和文档生成的AI助手。我可以帮助您梳理需求、收集关键信息，并生成规范的需求文档。无论是软件开发、UI设计还是其他项目需求，我都能提供专业的分析和整理服务。请告诉我您的需求，我们一起开始吧！
    simple: 您好！我是AI需求采集助手，可以帮您整理和分析业务需求。请问有什么需要帮助的？
    youji_platform: '您好！我是由己AI助手，专门为您提供业务领域的专业建议和指导。


      🏢 **由己平台核心业务**：

      • 智能需求采集系统 - 通过AI对话收集和分析项目需求

      • 在线用工平台 - 连接企业与全球优秀人才

      • 项目管理工具 - 提供完整的项目生命周期管理

      • 智能匹配服务 - 基于AI算法的精准人才匹配


      💡 **我可以帮您**：

      • 📋 需求分析和整理

      • 🎯 项目规划和建议

      • 📊 成本和时间评估

      • 🔍 风险识别和解决方案


      请告诉我您的具体需求，我会为您提供专业的分析和建议！

      '
  keyword_mapping:
    app:
      domain_id: LY_005
      domain_name: 软件开发
    logo:
      domain_id: LY_001
      domain_name: 平面设计
    ui:
      domain_id: LY_002
      domain_name: UI/UX设计
    ux:
      domain_id: LY_002
      domain_name: UI/UX设计
    交互:
      domain_id: LY_002
      domain_name: UI/UX设计
    合同:
      domain_id: LY_004
      domain_name: 法律咨询
    小程序:
      domain_id: LY_005
      domain_name: 软件开发
    平面设计:
      domain_id: LY_001
      domain_name: 平面设计
    广告:
      domain_id: LY_003
      domain_name: 营销推广
    开发:
      domain_id: LY_005
      domain_name: 软件开发
    推广:
      domain_id: LY_003
      domain_name: 营销推广
    法律:
      domain_id: LY_004
      domain_name: 法律咨询
    活动:
      domain_id: LY_003
      domain_name: 营销推广
    海报:
      domain_id: LY_001
      domain_name: 平面设计
    界面:
      domain_id: LY_002
      domain_name: UI/UX设计
    知识产权:
      domain_id: LY_004
      domain_name: 法律咨询
    网站:
      domain_id: LY_005
      domain_name: 软件开发
    营销:
      domain_id: LY_003
      domain_name: 营销推广
    设计:
      domain_id: LY_001
      domain_name: 平面设计
    软件:
      domain_id: LY_005
      domain_name: 软件开发
  logging:
    debug:
      composite_intent_detected: '检测到复合意图组合: {intent_combination}'
      domain_restore_attempt: 尝试从数据库恢复会话 {session_id} 的领域/类别信息。
      intent_keyword_match: '关键词匹配结果: {matched_keywords}'
      intent_llm_analysis: '启用LLM意图分析: {message_preview}'
      no_active_state: 数据库中没有会话 {session_id} 的活动状态，保持IDLE
      problem_statement_restored: '从历史消息恢复核心问题陈述: ''{problem_statement}'''
      session_init_complete: 会话 {session_id} 初始化完成
    error:
      determine_state: '确定状态失败: {error}'
      document_modification_failed: '文档修改失败: {error}'
      domain_guidance_generation_failed: '生成领域引导失败: {error}'
      focus_points_not_found: 未找到领域 {domain_id} 类别 {category_id} 的关注点
      format_focus_points_failed: '格式化关注点状态失败 - session_id: {session_id}, error: {error}'
      init_collecting_state: '初始化信息收集状态失败: {error}'
      initial_question_failed: '生成初始问题失败: {error}'
      intent_processing_failed: '意图处理失败: {error_msg}'
      knowledge_base_not_initialized: 知识库代理未初始化
      load_focus_points_failed: '加载关注点失败: {error}'
      no_category_id: 领域 {domain_id} 没有类别ID
      unknown_situation_generation_failed: '生成未知情况回复失败: {error}'
    info:
      composite_intent_resolution: '复合意图解析: {detected_intents} -> {selected_intent} (策略: {resolution_strategy})'
      domain_category_saved: '已保存领域和类别信息 - 领域: {domain}, 类别: {category}'
      domain_transition_collecting: 会话 {session_id} 转换到信息收集状态
      domain_transition_documenting: 会话 {session_id} 转换到文档编辑状态
      extraction_result: '信息提取结果: {count} 个关注点'
      focus_points_reset: 关注点状态已重置
      intent_recognition_result: '意图识别完成: {final_intent} (置信度: {confidence}, 情绪: {emotion})'
      problem_statement_recorded: '记录核心问题陈述: ''{problem_statement}'''
      reset_status: 重置会话状态完成
      state_aware_processing: '状态感知处理: {current_state} + {detected_intents} -> {final_action}'
      state_transition_collecting: 会话 {session_id} 已转换到COLLECTING_INFO状态
      state_transition_documenting: 会话 {session_id} 已转换到DOCUMENTING状态
      unknown_point: 未知关注点
    warning:
      conversation_history_failed: '获取对话历史失败: {error}'
      domain_restore_failed: '无法从数据库恢复领域/类别信息，可能是表结构问题: {error}'
      dynamic_reply_empty: DynamicReplyFactory生成的回复为空
      dynamic_reply_not_initialized: DynamicReplyFactory未初始化
      intent_config_not_found: 未找到意图 {intent} 的配置
      llm_no_valid_unknown: LLM未返回有效的未知情况回复
      unrecognized_subtype: '未识别的子类型: {sub_type}'
  number_mapping:
    '1':
      domain_id: LY_001
      domain_name: 平面设计
    '2':
      domain_id: LY_002
      domain_name: UI/UX设计
    '3':
      domain_id: LY_003
      domain_name: 营销推广
    '4':
      domain_id: LY_004
      domain_name: 法律咨询
    '5':
      domain_id: LY_005
      domain_name: 软件开发
    一:
      domain_id: LY_001
      domain_name: 平面设计
    三:
      domain_id: LY_003
      domain_name: 营销推广
    二:
      domain_id: LY_002
      domain_name: UI/UX设计
    五:
      domain_id: LY_005
      domain_name: 软件开发
    四:
      domain_id: LY_004
      domain_name: 法律咨询
  prompts:
    capabilities:
      instruction: 用户询问你的功能或能力，请详细介绍你的主要功能和能力，包括需求分析、信息收集、文档生成等，以及你支持的领域范围。语气要专业、友好。
    chat:
      instruction: 用户在进行闲聊，请友好回应，然后自然地引导用户回到系统的核心功能上，询问他们是否有需要帮助的项目需求。保持轻松友好的语气。
    domain_guidance:
      instruction: 这是一个全新的需求，用户的需求可能非常开放和模糊。核心目标不是猜测一个具体答案，而是通过一个高质量的引导性问题，帮助用户将想法聚焦到具体可执行的业务领域。
    empathy:
      default_instruction: 用户表达了负面情绪或困难，请先表示理解和共情，然后询问是否需要帮助，或者引导对话回到核心业务上。
    greeting:
      instruction: 用户向你问候，请友好回应并简要介绍你是一个AI需求采集助手，专门帮助用户整理和分析业务需求。然后询问用户有什么需求需要帮助整理。回复要简洁、专业、友好。
    introduction:
      instruction: 用户询问你的功能或身份，请详细介绍你是一个专业的AI需求采集助手，说明你的主要功能和能力，包括需求分析、文档生成等。语气要专业、自信、友好。
    restart:
      instruction: 用户请求重新开始对话。请确认会话已重置，并引导用户开始新的需求采集。回复要简洁、友好。
  requirement_collection:
    clarification: 为了更好地理解您的需求，请详细说明：
    contextual_suggestions:
      design_project: '设计项目需要特别注意视觉效果和用户体验！让我为您提供一些专业建议：


        🎨 **设计关键点**：

        • 品牌定位：想要传达什么样的品牌形象？

        • 目标受众：设计面向哪个年龄段和群体？

        • 应用场景：在哪些场合使用这个设计？

        • 风格偏好：现代简约、传统经典还是创意个性？


        💡 **专业建议**：好的设计需要平衡美观性和实用性。


        请先描述一下您希望通过这个设计传达什么样的感觉或印象？

        '
      marketing_project: '营销项目的成功关键在于精准定位和有效传播！我来为您分析：


        📈 **营销要素**：

        • 目标市场：主要面向哪个市场和人群？

        • 产品特色：有什么独特的卖点？

        • 竞争环境：主要竞争对手是谁？

        • 预算范围：营销投入的大概范围？


        💡 **专业建议**：建议先做市场调研，了解目标用户的真实需求。


        请先告诉我您的产品或服务有什么独特优势？

        '
      software_development: '我注意到您想开发软件项目，这很棒！基于我的经验，我建议我们重点关注：


        🎯 **核心要素**：

        • 目标用户：谁会使用这个软件？

        • 核心功能：最重要的3-5个功能是什么？

        • 平台选择：网页版、手机App还是桌面软件？

        • 数据处理：需要存储什么数据？


        💡 **专业建议**：建议先从核心功能开始，后续可以逐步扩展。


        请先告诉我您的目标用户群体是谁？

        '
    continue: 很好！请继续告诉我：
    default_prompt: 请告诉我您的具体需求，我将为您提供专业的分析和建议。
    start: 好的，让我来帮您分析这个项目。我需要了解一些关键信息：
  simplified_selection_template: '我没有完全理解您的选择"{user_input}"。


    让我们用最简单的方式：


    **请直接回复以下4个字中的任意一个：**


    • **设计** - 视觉设计相关

    • **开发** - 技术开发相关

    • **营销** - 推广营销相关

    • **法律** - 法律服务相关


    或者您可以说"我不确定"，我会为您提供通用的需求收集服务。

    '
  unknown_action: 抱歉，我好像遇到了一点内部问题，我们换个话题继续吧？
  user_interaction:
    defaults:
      detailed_requirement: '请描述您的具体需求，我将为您提供专业的帮助。


        💡 **温馨提示**：

        • 可以先说出核心想法，不必一次性说完所有细节

        • 我会根据您的描述提出针对性的问题

        • 整个过程大约需要5-10分钟，最终会生成专业的需求文档

        '
      no_history: 暂无历史信息（这是新的对话）
      requirement_prompt: '请描述您的详细需求。


        🎯 **建议从以下方面考虑**：

        • 项目目标：您希望解决什么问题？

        • 目标用户：主要面向哪些人群？

        • 核心功能：最重要的功能有哪些？

        • 预期效果：希望达到什么样的结果？


        💬 您可以先说出大概的想法，我会引导您逐步完善细节。

        '
      unknown_intent: 未知意图
      user_skip_choice: 用户选择跳过
    instructions:
      full_prompt_unknown: '{prompt_instruction}


        用户输入: {message}

        意图: {intent_string}'
    processing:
      idle_modify_intent: 在IDLE状态下检测到修改意图
      intent_string_not_json: '意图信息不是JSON格式: {intent_info}'
      llm_success_unknown: 'LLM成功生成未知情况回复: {response}...'
    redirect:
      business_needs: 我理解您的想法。让我们回到您的具体业务需求上，请告诉我您想要实现什么？
keyword_rules:
  ask_question:
  - 什么是
  - 如何
  - 怎么
  - 能否
  - 可以
  business_requirement:
  - 我想
  - 我需要
  - 要做
  - 想要
  - 希望
  - 打算
  confirm:
  - 确认
  - 好的
  - 是的
  - 对
  - 没错
  - 正确
  emotional_support:
  - 心情不好
  - 安慰我
  - 难过
  - 沮丧
  - 不开心
  - 郁闷
  - 情绪低落
  - 心情差
  general_chat:
  - 聊天
  - 闲聊
  - 随便聊聊
  - 聊一聊
  - 说说话
  greeting:
  - 你好
  - 您好
  - hello
  - hi
  - 嗨
  modify:
  - 修改
  - 改
  - 更改
  - 调整
  restart:
  - 重新开始
  - 重来
  - 全部重来
  - 重新
