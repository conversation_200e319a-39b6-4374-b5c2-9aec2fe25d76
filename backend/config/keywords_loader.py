#!/usr/bin/env python3
"""
统一关键词加载器

功能：
1. 从统一配置文件加载所有关键词定义
2. 提供向后兼容的API接口
3. 支持缓存和热重载
4. 提供关键词验证和统计功能

设计原则：
- 单一数据源：所有关键词从keywords_config.yaml加载
- 向后兼容：保持现有代码的API不变
- 性能优化：支持缓存和延迟加载
- 易于维护：集中管理，统一接口
"""

import logging
import time
from typing import Dict, List, Optional, Any
import yaml
from pathlib import Path


class KeywordsLoader:
    """统一关键词加载器"""

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化关键词加载器

        Args:
            config_path: 配置文件路径，默认使用项目标准路径
        """
        self.logger = logging.getLogger(__name__)

        # 配置文件路径
        if config_path:
            self.config_path = Path(config_path)
        else:
            # 默认路径：backend/config/keywords_config.yaml
            current_dir = Path(__file__).parent
            self.config_path = current_dir / "keywords_config.yaml"

        # 缓存相关
        self._cache: Optional[Dict[str, Any]] = None
        self._cache_timestamp: Optional[float] = None
        self._cache_ttl = 300  # 缓存5分钟

        # 统计信息
        self.stats = {
            "load_count": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "last_load_time": None
        }

        self.logger.info(f"关键词加载器初始化完成，配置文件: {self.config_path}")

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if not self.config_path.exists():
                raise FileNotFoundError(f"关键词配置文件不存在: {self.config_path}")

            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            self.stats["load_count"] += 1
            self.stats["last_load_time"] = time.time()

            self.logger.debug(f"成功加载关键词配置，版本: {config.get('metadata', {}).get('version', 'unknown')}")
            return config

        except Exception as e:
            self.logger.error(f"加载关键词配置失败: {e}", exc_info=True)
            raise

    def _get_cached_config(self) -> Dict[str, Any]:
        """获取缓存的配置，如果过期则重新加载"""
        current_time = time.time()

        # 检查缓存是否有效
        if (self._cache is not None and
            self._cache_timestamp is not None and
            current_time - self._cache_timestamp < self._cache_ttl):
            self.stats["cache_hits"] += 1
            return self._cache

        # 缓存无效，重新加载
        self.stats["cache_misses"] += 1
        self._cache = self._load_config()
        self._cache_timestamp = current_time

        return self._cache

    def get_knowledge_base_keywords(self) -> Dict[str, List[str]]:
        """
        获取知识库查询关键词

        Returns:
            Dict[str, List[str]]: 分类的关键词字典
        """
        config = self._get_cached_config()
        return config.get("knowledge_base_keywords", {})

    def get_system_capability_keywords(self) -> Dict[str, List[str]]:
        """
        获取系统能力查询关键词

        Returns:
            Dict[str, List[str]]: 系统能力关键词字典
        """
        config = self._get_cached_config()
        return config.get("system_capability_keywords", {})

    def get_intent_keywords(self) -> Dict[str, List[str]]:
        """
        获取意图识别关键词

        Returns:
            Dict[str, List[str]]: 意图关键词字典
        """
        config = self._get_cached_config()
        return config.get("intent_keywords", {})

    def get_emotion_keywords(self) -> Dict[str, List[str]]:
        """
        获取情感分析关键词

        Returns:
            Dict[str, List[str]]: 情感关键词字典
        """
        config = self._get_cached_config()
        return config.get("emotion_keywords", {})

    def get_safety_keywords(self) -> Dict[str, List[str]]:
        """
        获取安全审查关键词

        Returns:
            Dict[str, List[str]]: 安全关键词字典，按违规类型分类
        """
        config = self._get_cached_config()
        return config.get("safety_keywords", {})

    def get_all_knowledge_base_keywords_flat(self) -> List[str]:
        """
        获取所有知识库查询关键词的扁平列表

        Returns:
            List[str]: 所有关键词的扁平列表
        """
        keywords = self.get_knowledge_base_keywords()
        flat_list = []
        for category_keywords in keywords.values():
            flat_list.extend(category_keywords)
        return list(set(flat_list))  # 去重

    def get_keywords_by_category(self, category: str) -> List[str]:
        """
        根据分类获取关键词

        Args:
            category: 关键词分类

        Returns:
            List[str]: 该分类的关键词列表
        """
        keywords = self.get_knowledge_base_keywords()
        return keywords.get(category, [])

    def get_keywords(self, path: str) -> Optional[List[str]]:
        """
        根据路径获取关键词列表

        Args:
            path: 关键词路径，如 "strategy_keywords.capabilities_strategy.capability_inquiry"

        Returns:
            Optional[List[str]]: 关键词列表，未找到返回None
        """
        config = self._get_cached_config()

        # 按点分割路径
        keys = path.split('.')
        current = config

        try:
            for key in keys:
                current = current[key]

            # 确保返回的是列表
            if isinstance(current, list):
                return current
            else:
                self.logger.warning(f"路径 {path} 对应的值不是列表类型: {type(current)}")
                return None

        except (KeyError, TypeError) as e:
            self.logger.warning(f"无法找到关键词路径: {path}, 错误: {e}")
            return None

    def search_keyword_category(self, keyword: str) -> Optional[str]:
        """
        搜索关键词属于哪个分类

        Args:
            keyword: 要搜索的关键词

        Returns:
            Optional[str]: 关键词所属分类，未找到返回None
        """
        keywords = self.get_knowledge_base_keywords()
        keyword_lower = keyword.lower()

        for category, category_keywords in keywords.items():
            if keyword_lower in [kw.lower() for kw in category_keywords]:
                return category

        return None

    def validate_keywords(self) -> Dict[str, Any]:
        """
        验证关键词配置的完整性

        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            config = self._get_cached_config()

            # 基础结构检查
            required_sections = ["knowledge_base_keywords", "system_capability_keywords", "intent_keywords", "safety_keywords"]
            missing_sections = [section for section in required_sections if section not in config]

            # 关键词统计
            kb_keywords = self.get_knowledge_base_keywords()
            total_categories = len(kb_keywords)
            total_keywords = sum(len(keywords) for keywords in kb_keywords.values())

            # 重复关键词检查
            all_keywords = []
            for category_keywords in kb_keywords.values():
                all_keywords.extend([kw.lower() for kw in category_keywords])

            duplicates = []
            seen = set()
            for keyword in all_keywords:
                if keyword in seen:
                    duplicates.append(keyword)
                seen.add(keyword)

            return {
                "valid": len(missing_sections) == 0,
                "missing_sections": missing_sections,
                "total_categories": total_categories,
                "total_keywords": total_keywords,
                "duplicate_keywords": list(set(duplicates)),
                "config_version": config.get("metadata", {}).get("version", "unknown")
            }

        except Exception as e:
            return {
                "valid": False,
                "error": str(e)
            }

    def get_stats(self) -> Dict[str, Any]:
        """获取加载器统计信息"""
        return self.stats.copy()

    def reload(self):
        """强制重新加载配置"""
        self._cache = None
        self._cache_timestamp = None
        self.logger.info("强制重新加载关键词配置")


# 全局单例实例
_keywords_loader_instance: Optional[KeywordsLoader] = None


def get_keywords_loader() -> KeywordsLoader:
    """
    获取全局关键词加载器实例

    Returns:
        KeywordsLoader: 关键词加载器实例
    """
    global _keywords_loader_instance

    if _keywords_loader_instance is None:
        _keywords_loader_instance = KeywordsLoader()

    return _keywords_loader_instance


# 便捷函数，保持向后兼容性
def get_knowledge_base_keywords() -> Dict[str, List[str]]:
    """获取知识库查询关键词（便捷函数）"""
    return get_keywords_loader().get_knowledge_base_keywords()


def get_all_knowledge_base_keywords_flat() -> List[str]:
    """获取所有知识库查询关键词的扁平列表（便捷函数）"""
    return get_keywords_loader().get_all_knowledge_base_keywords_flat()
