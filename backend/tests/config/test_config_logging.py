#!/usr/bin/env python3
"""
配置日志记录功能测试

测试配置降级、覆盖、来源追踪等日志记录功能
"""

import unittest
import logging
from unittest.mock import patch, MagicMock
from datetime import datetime

from backend.config.config_logging import (
    ConfigLogger,
    ConfigEvent,
    ConfigEventType,
    ConfigSource,
    get_config_logger,
    log_config_load,
    log_config_override,
    log_config_fallback
)


class TestConfigEvent(unittest.TestCase):
    """配置事件数据结构测试"""

    def test_config_event_creation(self):
        """测试配置事件创建"""
        event = ConfigEvent(
            timestamp=datetime.now().isoformat(),
            event_type=ConfigEventType.LOAD,
            config_key="test.key",
            source=ConfigSource.CONFIG_FILE,
            new_value="test_value"
        )
        
        self.assertEqual(event.event_type, ConfigEventType.LOAD)
        self.assertEqual(event.config_key, "test.key")
        self.assertEqual(event.source, ConfigSource.CONFIG_FILE)
        self.assertEqual(event.new_value, "test_value")

    def test_config_event_with_sensitive_data(self):
        """测试敏感数据配置事件"""
        event = ConfigEvent(
            timestamp=datetime.now().isoformat(),
            event_type=ConfigEventType.OVERRIDE,
            config_key="api.secret_key",
            source=ConfigSource.ENV_VAR,
            old_value="old_secret",
            new_value="new_secret",
            is_sensitive=True
        )
        
        self.assertTrue(event.is_sensitive)
        self.assertEqual(event.old_value, "old_secret")
        self.assertEqual(event.new_value, "new_secret")


class TestConfigLogger(unittest.TestCase):
    """配置日志记录器测试"""

    def setUp(self):
        """测试前准备"""
        self.config_logger = ConfigLogger("test_config_logger")

    def test_sensitive_key_detection(self):
        """测试敏感键检测"""
        # 敏感键
        sensitive_keys = [
            "api_key",
            "secret_token",
            "database_password",
            "auth_credential",
            "private_key"
        ]
        
        for key in sensitive_keys:
            self.assertTrue(self.config_logger._is_sensitive_key(key))

        # 非敏感键
        non_sensitive_keys = [
            "app_name",
            "timeout",
            "max_retries",
            "log_level"
        ]
        
        for key in non_sensitive_keys:
            self.assertFalse(self.config_logger._is_sensitive_key(key))

    def test_sensitive_value_masking(self):
        """测试敏感值脱敏"""
        # 字符串脱敏
        masked = self.config_logger._mask_sensitive_value("secret123456", True)
        self.assertEqual(masked, "se********56")
        
        # 短字符串脱敏
        masked = self.config_logger._mask_sensitive_value("abc", True)
        self.assertEqual(masked, "***")
        
        # 数字脱敏
        masked = self.config_logger._mask_sensitive_value(12345, True)
        self.assertEqual(masked, "***")
        
        # 非敏感值不脱敏
        masked = self.config_logger._mask_sensitive_value("normal_value", False)
        self.assertEqual(masked, "normal_value")

    def test_log_message_formatting(self):
        """测试日志消息格式化"""
        event = ConfigEvent(
            timestamp=datetime.now().isoformat(),
            event_type=ConfigEventType.OVERRIDE,
            config_key="test.config",
            source=ConfigSource.ENV_VAR,
            old_value="old_value",
            new_value="new_value",
            source_detail="/path/to/config",
            reason="environment override"
        )
        
        message = self.config_logger._format_log_message(event)
        
        self.assertIn("[OVERRIDE]", message)
        self.assertIn("key=test.config", message)
        self.assertIn("source=env_var", message)
        self.assertIn("source_detail=/path/to/config", message)
        self.assertIn("change: old_value -> new_value", message)
        self.assertIn("reason=environment override", message)

    @patch('backend.config.config_logging.logger')
    def test_log_config_load(self, mock_logger):
        """测试配置加载日志"""
        self.config_logger.log_config_load(
            "app.name", "test_app", ConfigSource.CONFIG_FILE, "/path/to/config.yaml"
        )
        
        # 验证日志被调用
        mock_logger.log.assert_called_once()
        call_args = mock_logger.log.call_args
        
        # 验证日志级别
        self.assertEqual(call_args[0][0], logging.INFO)
        
        # 验证日志消息包含关键信息
        message = call_args[0][1]
        self.assertIn("[LOAD]", message)
        self.assertIn("key=app.name", message)

    @patch('backend.config.config_logging.logger')
    def test_log_config_override(self, mock_logger):
        """测试配置覆盖日志"""
        self.config_logger.log_config_override(
            "timeout", 30, 60, ConfigSource.ENV_VAR, reason="performance tuning"
        )
        
        # 验证日志被调用
        mock_logger.log.assert_called_once()
        call_args = mock_logger.log.call_args
        
        # 验证日志级别为WARNING
        self.assertEqual(call_args[0][0], logging.WARNING)
        
        # 验证日志消息
        message = call_args[0][1]
        self.assertIn("[OVERRIDE]", message)
        self.assertIn("change: 30 -> 60", message)

    @patch('backend.config.config_logging.logger')
    def test_log_config_fallback(self, mock_logger):
        """测试配置降级日志"""
        self.config_logger.log_config_fallback(
            "database.host", "localhost", ConfigSource.HARDCODED, 
            "config file not found"
        )
        
        # 验证日志被调用
        mock_logger.log.assert_called_once()
        call_args = mock_logger.log.call_args
        
        # 验证日志级别为WARNING
        self.assertEqual(call_args[0][0], logging.WARNING)
        
        # 验证日志消息
        message = call_args[0][1]
        self.assertIn("[FALLBACK]", message)
        self.assertIn("reason=config file not found", message)

    @patch('backend.config.config_logging.logger')
    def test_log_sensitive_config(self, mock_logger):
        """测试敏感配置日志"""
        self.config_logger.log_config_override(
            "api_key", "old_secret_key_123456", "new_secret_key_789012",
            ConfigSource.ENV_VAR
        )
        
        # 验证日志被调用
        mock_logger.log.assert_called_once()
        call_args = mock_logger.log.call_args
        
        # 验证敏感值被脱敏
        message = call_args[0][1]
        self.assertNotIn("old_secret_key_123456", message)
        self.assertNotIn("new_secret_key_789012", message)
        self.assertIn("ol**********56", message)  # 脱敏后的值

    @patch('backend.config.config_logging.logger')
    def test_log_config_validation(self, mock_logger):
        """测试配置验证日志"""
        # 验证成功
        self.config_logger.log_config_validation(
            "port", 8080, True
        )
        
        call_args = mock_logger.log.call_args
        self.assertEqual(call_args[0][0], logging.INFO)
        
        # 验证失败
        mock_logger.reset_mock()
        self.config_logger.log_config_validation(
            "port", "invalid_port", False, "Port must be integer"
        )
        
        call_args = mock_logger.log.call_args
        self.assertEqual(call_args[0][0], logging.ERROR)
        
        message = call_args[0][1]
        self.assertIn("error=Port must be integer", message)

    def test_structured_log_data(self):
        """测试结构化日志数据"""
        event = ConfigEvent(
            timestamp=datetime.now().isoformat(),
            event_type=ConfigEventType.MERGE,
            config_key="merged.config",
            source=ConfigSource.UNKNOWN,
            new_value="merged_value",
            is_sensitive=False
        )
        
        structured_data = self.config_logger._get_structured_log_data(event)
        
        self.assertIsInstance(structured_data, dict)
        self.assertEqual(structured_data['event_type'], 'merge')
        self.assertEqual(structured_data['source'], 'unknown')
        self.assertEqual(structured_data['config_key'], 'merged.config')
        self.assertFalse(structured_data['is_sensitive'])


class TestGlobalFunctions(unittest.TestCase):
    """全局便捷函数测试"""

    def test_get_config_logger(self):
        """测试获取配置日志记录器"""
        logger = get_config_logger()
        self.assertIsInstance(logger, ConfigLogger)
        
        # 测试单例模式
        logger2 = get_config_logger()
        self.assertIs(logger, logger2)

    @patch('backend.config.config_logging.config_logger')
    def test_convenience_functions(self, mock_config_logger):
        """测试便捷函数"""
        # 测试配置加载便捷函数
        log_config_load("test.key", "test_value", "config_file", "/path/to/config")
        mock_config_logger.log_config_load.assert_called_once()

        # 测试配置覆盖便捷函数
        mock_config_logger.reset_mock()
        log_config_override("test.key", "old", "new", "env_var", "test reason")
        mock_config_logger.log_config_override.assert_called_once()

        # 测试配置降级便捷函数
        mock_config_logger.reset_mock()
        log_config_fallback("test.key", "fallback_value", "test reason")
        mock_config_logger.log_config_fallback.assert_called_once()


if __name__ == '__main__':
    unittest.main()
