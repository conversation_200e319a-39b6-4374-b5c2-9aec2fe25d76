#!/usr/bin/env python3
"""
硬编码兜底配置测试

测试硬编码兜底配置在各种异常情况下的表现，确保系统能够在
配置文件完全无法加载时仍能启动。
"""

import unittest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, MagicMock

from backend.config.hardcoded_fallbacks import (
    HardcodedFallbackManager,
    hardcoded_fallback_manager,
    get_hardcoded_fallback_manager,
    get_critical_database_path,
    get_critical_llm_model,
    get_critical_app_name,
    get_critical_llm_timeout,
    HARDCODED_APP_CONFIG,
    HARDCODED_DATABASE_CONFIG,
    HARDCODED_LLM_CONFIG
)


class TestHardcodedFallbackManager(unittest.TestCase):
    """硬编码兜底配置管理器测试"""

    def setUp(self):
        """测试前准备"""
        self.manager = HardcodedFallbackManager()

    def test_initialization(self):
        """测试初始化"""
        self.assertIsNotNone(self.manager._config)
        self.assertIn("app", self.manager._config)
        self.assertIn("data", self.manager._config)
        self.assertIn("llm", self.manager._config)

    def test_get_config_valid_path(self):
        """测试获取有效配置路径"""
        # 测试应用名称
        app_name = self.manager.get_config("app.name")
        self.assertEqual(app_name, "需求采集系统")

        # 测试数据库路径
        db_path = self.manager.get_config("data.database.path")
        self.assertEqual(db_path, "backend/data/aidatabase.db")

        # 测试LLM默认模型
        llm_model = self.manager.get_config("llm.default_model")
        self.assertEqual(llm_model, "deepseek-chat")

    def test_get_config_invalid_path(self):
        """测试获取无效配置路径"""
        # 测试不存在的路径
        result = self.manager.get_config("nonexistent.path", "default_value")
        self.assertEqual(result, "default_value")

        # 测试部分存在的路径
        result = self.manager.get_config("app.nonexistent", "default_value")
        self.assertEqual(result, "default_value")

    def test_get_config_nested_path(self):
        """测试获取嵌套配置路径"""
        # 测试深层嵌套路径
        timeout = self.manager.get_config("system.performance.llm_timeout")
        self.assertEqual(timeout, 30)

        # 测试数据库重试次数
        retries = self.manager.get_config("data.database.max_retries")
        self.assertEqual(retries, 3)

    def test_get_specific_configs(self):
        """测试获取特定配置方法"""
        # 测试数据库配置
        db_config = self.manager.get_database_config()
        self.assertIsInstance(db_config, dict)
        self.assertIn("path", db_config)
        self.assertIn("timeout", db_config)

        # 测试LLM配置
        llm_config = self.manager.get_llm_config()
        self.assertIsInstance(llm_config, dict)
        self.assertIn("default_model", llm_config)
        self.assertIn("default_params", llm_config)

        # 测试应用配置
        app_config = self.manager.get_app_config()
        self.assertIsInstance(app_config, dict)
        self.assertIn("name", app_config)
        self.assertIn("version", app_config)

        # 测试性能配置
        perf_config = self.manager.get_performance_config()
        self.assertIsInstance(perf_config, dict)
        self.assertIn("llm_timeout", perf_config)

    def test_is_critical_config_available(self):
        """测试关键配置可用性检查"""
        # 正常情况下应该返回True
        self.assertTrue(self.manager.is_critical_config_available())

    def test_log_fallback_usage(self):
        """测试兜底配置使用日志"""
        with patch.object(self.manager.logger, 'warning') as mock_warning:
            self.manager.log_fallback_usage("test.config", "测试原因")
            mock_warning.assert_called_once()
            
            # 检查日志参数
            call_args = mock_warning.call_args
            self.assertIn("test.config", call_args[0][0])
            self.assertIn("测试原因", call_args[0][0])

    def test_config_immutability(self):
        """测试配置不可变性"""
        # 获取配置副本
        db_config1 = self.manager.get_database_config()
        db_config2 = self.manager.get_database_config()

        # 修改一个副本不应影响另一个
        db_config1["path"] = "modified_path"
        self.assertNotEqual(db_config1["path"], db_config2["path"])

    def test_error_handling(self):
        """测试错误处理"""
        # 模拟配置获取异常
        with patch.object(self.manager, '_config', None):
            result = self.manager.get_config("any.path", "default")
            self.assertEqual(result, "default")


class TestGlobalFunctions(unittest.TestCase):
    """全局便捷函数测试"""

    def test_get_hardcoded_fallback_manager(self):
        """测试获取全局管理器实例"""
        manager = get_hardcoded_fallback_manager()
        self.assertIsInstance(manager, HardcodedFallbackManager)
        
        # 测试单例模式
        manager2 = get_hardcoded_fallback_manager()
        self.assertIs(manager, manager2)

    def test_convenience_functions(self):
        """测试便捷函数"""
        # 测试数据库路径
        db_path = get_critical_database_path()
        self.assertEqual(db_path, "backend/data/aidatabase.db")

        # 测试LLM模型
        llm_model = get_critical_llm_model()
        self.assertEqual(llm_model, "deepseek-chat")

        # 测试应用名称
        app_name = get_critical_app_name()
        self.assertEqual(app_name, "需求采集系统")

        # 测试LLM超时
        llm_timeout = get_critical_llm_timeout()
        self.assertEqual(llm_timeout, 30)


class TestHardcodedConstants(unittest.TestCase):
    """硬编码常量测试"""

    def test_app_config_constants(self):
        """测试应用配置常量"""
        self.assertIn("name", HARDCODED_APP_CONFIG)
        self.assertIn("version", HARDCODED_APP_CONFIG)
        self.assertIn("environment", HARDCODED_APP_CONFIG)

    def test_database_config_constants(self):
        """测试数据库配置常量"""
        self.assertIn("path", HARDCODED_DATABASE_CONFIG)
        self.assertIn("timeout", HARDCODED_DATABASE_CONFIG)
        self.assertIn("max_retries", HARDCODED_DATABASE_CONFIG)

    def test_llm_config_constants(self):
        """测试LLM配置常量"""
        self.assertIn("default_model", HARDCODED_LLM_CONFIG)
        self.assertIn("default_params", HARDCODED_LLM_CONFIG)
        self.assertIn("scenario_mapping", HARDCODED_LLM_CONFIG)

        # 测试默认参数
        default_params = HARDCODED_LLM_CONFIG["default_params"]
        self.assertIn("temperature", default_params)
        self.assertIn("max_tokens", default_params)
        self.assertIn("timeout", default_params)

        # 测试场景映射
        scenario_mapping = HARDCODED_LLM_CONFIG["scenario_mapping"]
        self.assertIn("intent_recognition", scenario_mapping)
        self.assertIn("domain_classifier", scenario_mapping)


class TestIntegrationWithConfigLoaders(unittest.TestCase):
    """与配置加载器集成测试"""

    def test_modular_loader_integration(self):
        """测试与模块化配置加载器的集成"""
        try:
            from backend.config.modular_loader import ModularConfigLoader
            
            # 创建配置加载器实例
            loader = ModularConfigLoader(enabled=True)
            
            # 测试能够获取硬编码兜底配置
            app_name = loader.get_config("app.name", None)
            self.assertIsNotNone(app_name)
            
        except ImportError:
            self.skipTest("ModularConfigLoader not available")

    def test_unified_config_loader_fallback(self):
        """测试统一配置加载器的兜底机制"""
        # 这个测试需要模拟配置文件加载失败的情况
        # 由于涉及文件系统操作，这里只做基本的导入测试
        try:
            from backend.config.unified_config_loader import UnifiedConfigLoader
            # 如果能成功导入，说明硬编码兜底集成正常
            self.assertTrue(True)
        except ImportError:
            self.skipTest("UnifiedConfigLoader not available")


if __name__ == '__main__':
    unittest.main()
