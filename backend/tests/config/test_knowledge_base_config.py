"""
知识库配置管理器单元测试
"""

import yaml
import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch, MagicMock

from backend.config.knowledge_base_config import (
    KnowledgeBaseConfig,
    KnowledgeBaseConfigManager,
    get_knowledge_base_config_manager,
    is_knowledge_base_enabled,
    is_feature_enabled,
    get_knowledge_base_config
)


class TestKnowledgeBaseConfig(unittest.TestCase):
    """测试KnowledgeBaseConfig数据类"""

    def test_default_initialization(self):
        """测试默认初始化"""
        config = KnowledgeBaseConfig()

        self.assertFalse(config.enabled)
        self.assertIsInstance(config.features, dict)
        self.assertIsInstance(config.chroma_db, dict)
        self.assertIsInstance(config.document_processing, dict)
        self.assertIsInstance(config.retrieval, dict)
        self.assertIsInstance(config.safety, dict)
        self.assertIsInstance(config.performance, dict)
        self.assertIsInstance(config.role_filters, dict)
        self.assertIsInstance(config.logging, dict)

    def test_custom_initialization(self):
        """测试自定义初始化"""
        config = KnowledgeBaseConfig(
            enabled=True,
            features={"rag_query": True}
        )

        self.assertTrue(config.enabled)
        self.assertTrue(config.features["rag_query"])


class TestKnowledgeBaseConfigManager(unittest.TestCase):
    """测试KnowledgeBaseConfigManager类"""

    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "test_knowledge_base.yaml"

        # 创建测试配置文件
        self.test_config = {
            "knowledge_base": {
                "enabled": True,
                "features": {
                    "rag_query": True,
                    "intent_enhancement": False,
                    "mode_switching": True,
                    "document_ingestion": False
                },
                "chroma_db": {
                    "path": "test/data/chroma_db",
                    "collection_name": "test_collection",
                    "embedding_model": "test-model"
                },
                "document_processing": {
                    "chunk_size": 800,
                    "chunk_overlap": 100,
                    "max_chunks_per_doc": 50,
                    "supported_formats": ["md", "txt"]
                },
                "retrieval": {
                    "top_k": 3,
                    "similarity_threshold": 0.8
                },
                "safety": {
                    "fallback_to_requirement": True,
                    "max_retry_attempts": 2
                },
                "role_filters": {
                    "enabled": True,
                    "available_roles": ["company", "developer"],
                    "default_role": None
                }
            }
        }

        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(self.test_config, f)

    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_load_config_success(self):
        """测试成功加载配置"""
        manager = KnowledgeBaseConfigManager(str(self.config_file))

        self.assertTrue(manager.is_knowledge_base_enabled())
        self.assertTrue(manager.is_feature_enabled("rag_query"))
        self.assertFalse(manager.is_feature_enabled("intent_enhancement"))
        self.assertTrue(manager.is_feature_enabled("mode_switching"))

    def test_load_config_file_not_exists(self):
        """测试配置文件不存在"""
        non_existent_file = Path(self.temp_dir) / "non_existent.yaml"
        manager = KnowledgeBaseConfigManager(str(non_existent_file))

        # 应该使用默认配置
        self.assertFalse(manager.is_knowledge_base_enabled())
        config = manager.get_config()
        self.assertIsInstance(config, KnowledgeBaseConfig)

    def test_load_config_invalid_format(self):
        """测试无效配置格式"""
        invalid_config_file = Path(self.temp_dir) / "invalid.yaml"
        with open(invalid_config_file, 'w') as f:
            f.write("invalid: yaml: content:")

        manager = KnowledgeBaseConfigManager(str(invalid_config_file))

        # 应该使用默认配置
        self.assertFalse(manager.is_knowledge_base_enabled())

    def test_reload_config(self):
        """测试配置热更新"""
        manager = KnowledgeBaseConfigManager(str(self.config_file))

        # 初始状态
        self.assertTrue(manager.is_knowledge_base_enabled())

        # 修改配置文件
        modified_config = self.test_config.copy()
        modified_config["knowledge_base"]["enabled"] = False

        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(modified_config, f)

        # 重新加载
        success = manager.reload_config()
        self.assertTrue(success)
        self.assertFalse(manager.is_knowledge_base_enabled())

    def test_enable_disable_knowledge_base(self):
        """测试启用/禁用知识库功能"""
        manager = KnowledgeBaseConfigManager(str(self.config_file))

        # 禁用
        success = manager.disable_knowledge_base()
        self.assertTrue(success)
        self.assertFalse(manager.is_knowledge_base_enabled())

        # 启用
        success = manager.enable_knowledge_base()
        self.assertTrue(success)
        self.assertTrue(manager.is_knowledge_base_enabled())

    def test_enable_disable_feature(self):
        """测试启用/禁用特定功能"""
        manager = KnowledgeBaseConfigManager(str(self.config_file))

        # 禁用功能
        success = manager.disable_feature("rag_query")
        self.assertTrue(success)
        self.assertFalse(manager.is_feature_enabled("rag_query"))

        # 启用功能
        success = manager.enable_feature("rag_query")
        self.assertTrue(success)
        self.assertTrue(manager.is_feature_enabled("rag_query"))

    def test_enable_feature_when_kb_disabled(self):
        """测试在知识库禁用时启用功能"""
        manager = KnowledgeBaseConfigManager(str(self.config_file))

        # 先禁用知识库
        manager.disable_knowledge_base()

        # 尝试启用功能应该失败
        success = manager.enable_feature("rag_query")
        self.assertFalse(success)

    def test_get_config_methods(self):
        """测试获取配置的各种方法"""
        manager = KnowledgeBaseConfigManager(str(self.config_file))

        # 获取完整配置
        config = manager.get_config()
        self.assertIsInstance(config, KnowledgeBaseConfig)

        # 获取特定配置
        chroma_config = manager.get_chroma_db_config()
        self.assertEqual(chroma_config["collection_name"], "test_collection")

        retrieval_config = manager.get_retrieval_config()
        self.assertEqual(retrieval_config["top_k"], 3)

        safety_config = manager.get_safety_config()
        self.assertEqual(safety_config["max_retry_attempts"], 2)

        performance_config = manager.get_performance_config()
        self.assertIsInstance(performance_config, dict)

    def test_get_available_roles(self):
        """测试获取可用角色"""
        manager = KnowledgeBaseConfigManager(str(self.config_file))

        roles = manager.get_available_roles()
        # 默认配置应该包含company和developer
        self.assertIn("company", roles)
        self.assertIn("developer", roles)

    def test_get_status(self):
        """测试获取状态信息"""
        manager = KnowledgeBaseConfigManager(str(self.config_file))

        status = manager.get_status()
        self.assertIn("config_path", status)
        self.assertIn("config_exists", status)
        self.assertIn("knowledge_base_enabled", status)
        self.assertIn("enabled_features", status)

        self.assertTrue(status["config_exists"])
        self.assertTrue(status["knowledge_base_enabled"])
        self.assertIn("rag_query", status["enabled_features"])

    def test_validate_config(self):
        """测试配置验证"""
        manager = KnowledgeBaseConfigManager(str(self.config_file))

        validation_result = manager.validate_config()
        self.assertIn("valid", validation_result)
        self.assertIn("errors", validation_result)
        self.assertIn("warnings", validation_result)

        # 测试配置应该是有效的
        self.assertTrue(validation_result["valid"])

    def test_validate_config_invalid_params(self):
        """测试无效参数的配置验证"""
        # 创建无效配置
        invalid_config = {
            "knowledge_base": {
                "enabled": True,
                "document_processing": {
                    "chunk_size": 0,  # 无效值
                    "chunk_overlap": 100
                },
                "retrieval": {
                    "top_k": -1,  # 无效值
                    "similarity_threshold": 1.5  # 无效值
                }
            }
        }

        invalid_config_file = Path(self.temp_dir) / "invalid_params.yaml"
        with open(invalid_config_file, 'w', encoding='utf-8') as f:
            yaml.dump(invalid_config, f)

        manager = KnowledgeBaseConfigManager(str(invalid_config_file))
        validation_result = manager.validate_config()

        self.assertFalse(validation_result["valid"])
        self.assertGreater(len(validation_result["errors"]), 0)


class TestGlobalFunctions(unittest.TestCase):
    """测试全局便捷函数"""

    def setUp(self):
        """测试前准备"""
        # 重置全局配置管理器
        import backend.config.knowledge_base_config as kb_config
        kb_config._config_manager = None

    @patch('backend.config.knowledge_base_config.KnowledgeBaseConfigManager')
    def test_get_knowledge_base_config_manager_singleton(self, mock_manager_class):
        """测试单例模式"""
        mock_instance = MagicMock()
        mock_manager_class.return_value = mock_instance

        # 第一次调用
        manager1 = get_knowledge_base_config_manager()

        # 第二次调用
        manager2 = get_knowledge_base_config_manager()

        # 应该是同一个实例
        self.assertIs(manager1, manager2)

        # 构造函数只应该被调用一次
        mock_manager_class.assert_called_once()

    @patch('backend.config.knowledge_base_config.get_knowledge_base_config_manager')
    def test_is_knowledge_base_enabled(self, mock_get_manager):
        """测试is_knowledge_base_enabled函数"""
        mock_manager = MagicMock()
        mock_manager.is_knowledge_base_enabled.return_value = True
        mock_get_manager.return_value = mock_manager

        result = is_knowledge_base_enabled()

        self.assertTrue(result)
        mock_manager.is_knowledge_base_enabled.assert_called_once()

    @patch('backend.config.knowledge_base_config.get_knowledge_base_config_manager')
    def test_is_feature_enabled(self, mock_get_manager):
        """测试is_feature_enabled函数"""
        mock_manager = MagicMock()
        mock_manager.is_feature_enabled.return_value = True
        mock_get_manager.return_value = mock_manager

        result = is_feature_enabled("rag_query")

        self.assertTrue(result)
        mock_manager.is_feature_enabled.assert_called_once_with("rag_query")

    @patch('backend.config.knowledge_base_config.get_knowledge_base_config_manager')
    def test_get_knowledge_base_config(self, mock_get_manager):
        """测试get_knowledge_base_config函数"""
        mock_manager = MagicMock()
        mock_config = KnowledgeBaseConfig()
        mock_manager.get_config.return_value = mock_config
        mock_get_manager.return_value = mock_manager

        result = get_knowledge_base_config()

        self.assertIs(result, mock_config)
        mock_manager.get_config.assert_called_once()


if __name__ == '__main__':
    unittest.main()
