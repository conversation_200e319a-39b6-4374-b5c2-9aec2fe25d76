#!/usr/bin/env python3
"""
配置验证框架测试

测试基于Pydantic的配置验证功能
"""

import unittest
import tempfile
import yaml
from pathlib import Path

from backend.config.validation import (
    ConfigValidator,
    ValidationResult,
    get_config_validator,
    AppConfigSchema,
    DatabaseConfigSchema,
    LLMConfigSchema,
    ConfigValidationError
)


class TestConfigValidationFramework(unittest.TestCase):
    """配置验证框架测试"""

    def setUp(self):
        """测试前准备"""
        self.validator = ConfigValidator()

    def test_valid_config_validation(self):
        """测试有效配置验证"""
        valid_config = {
            "app": {
                "name": "测试应用",
                "version": "1.0.0",
                "environment": "development"
            },
            "data": {
                "database": {
                    "path": "test.db",
                    "timeout": 30
                }
            },
            "llm": {
                "default_model": "test-model",
                "models": {
                    "test-model": {
                        "provider": "test",
                        "model_name": "test-model"
                    }
                }
            }
        }
        
        result = self.validator.validate_config(valid_config)

        if not result.is_valid:
            print("Validation errors:")
            print(result.get_error_report())

        self.assertTrue(result.is_valid)
        self.assertEqual(len(result.errors), 0)
        self.assertIsNotNone(result.validated_config)

    def test_invalid_config_validation(self):
        """测试无效配置验证"""
        invalid_config = {
            "app": {
                "name": "",  # 空名称，应该失败
                "version": "invalid-version"  # 无效版本格式
            },
            "data": {
                "database": {
                    "path": "test.txt",  # 不是.db文件
                    "timeout": -1  # 负数超时
                }
            }
        }
        
        result = self.validator.validate_config(invalid_config)
        
        self.assertFalse(result.is_valid)
        self.assertGreater(len(result.errors), 0)
        self.assertIsNotNone(result.schema_errors)

    def test_missing_required_fields(self):
        """测试缺失必需字段"""
        incomplete_config = {
            "app": {
                # 缺少name字段
                "version": "1.0.0"
            }
        }
        
        result = self.validator.validate_config(incomplete_config)
        
        self.assertFalse(result.is_valid)
        self.assertTrue(any("name" in error for error in result.errors))

    def test_type_validation(self):
        """测试类型验证"""
        wrong_type_config = {
            "app": {
                "name": "测试应用",
                "version": "1.0.0",
                "debug": "not_a_boolean"  # 应该是布尔值
            },
            "data": {
                "database": {
                    "path": "test.db",
                    "timeout": "not_a_number"  # 应该是数字
                }
            },
            "llm": {
                "default_model": "test-model"
            }
        }
        
        result = self.validator.validate_config(wrong_type_config)
        
        self.assertFalse(result.is_valid)
        self.assertGreater(len(result.errors), 0)

    def test_value_constraints(self):
        """测试值约束验证"""
        constraint_violation_config = {
            "app": {
                "name": "测试应用",
                "version": "1.0.0"
            },
            "data": {
                "database": {
                    "path": "test.db",
                    "timeout": 1000,  # 超过最大值300
                    "max_retries": 20  # 超过最大值10
                }
            },
            "llm": {
                "default_model": "test-model",
                "models": {
                    "test-model": {
                        "provider": "test",
                        "model_name": "test",
                        "temperature": 3.0  # 超过最大值2.0
                    }
                }
            }
        }
        
        result = self.validator.validate_config(constraint_violation_config)
        
        self.assertFalse(result.is_valid)
        self.assertTrue(any("timeout" in error for error in result.errors))
        self.assertTrue(any("temperature" in error for error in result.errors))

    def test_config_file_validation(self):
        """测试配置文件验证"""
        # 创建临时配置文件
        valid_config = {
            "app": {
                "name": "文件测试应用",
                "version": "1.0.0"
            },
            "data": {
                "database": {
                    "path": "file_test.db"
                }
            },
            "llm": {
                "default_model": "file-test-model"
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(valid_config, f)
            temp_file = f.name
        
        try:
            result = self.validator.validate_config_file(temp_file)
            self.assertTrue(result.is_valid)
        finally:
            Path(temp_file).unlink()

    def test_invalid_yaml_file(self):
        """测试无效YAML文件"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write("invalid: yaml: content: [unclosed")
            temp_file = f.name
        
        try:
            result = self.validator.validate_config_file(temp_file)
            self.assertFalse(result.is_valid)
            self.assertTrue(any("YAML格式错误" in error for error in result.errors))
        finally:
            Path(temp_file).unlink()

    def test_nonexistent_file(self):
        """测试不存在的文件"""
        result = self.validator.validate_config_file("nonexistent_file.yaml")
        
        self.assertFalse(result.is_valid)
        self.assertTrue(any("不存在" in error for error in result.errors))

    def test_partial_config_validation(self):
        """测试部分配置验证"""
        # 测试应用配置
        app_config = {
            "name": "部分测试应用",
            "version": "2.0.0",
            "environment": "production"
        }
        
        result = self.validator.validate_partial_config("app", app_config)
        self.assertTrue(result.is_valid)
        
        # 测试无效的部分配置
        invalid_app_config = {
            "name": "",  # 空名称
            "version": "invalid"  # 无效版本
        }
        
        result = self.validator.validate_partial_config("app", invalid_app_config)
        self.assertFalse(result.is_valid)

    def test_llm_config_integrity(self):
        """测试LLM配置完整性"""
        # 测试场景映射引用不存在的模型
        llm_config = {
            "default_model": "existing-model",
            "models": {
                "existing-model": {
                    "provider": "test",
                    "model_name": "test"
                }
            },
            "scenario_mapping": {
                "test_scenario": "nonexistent-model"  # 引用不存在的模型
            }
        }
        
        result = self.validator.validate_partial_config("llm", llm_config)
        self.assertFalse(result.is_valid)
        self.assertTrue(any("不存在" in error for error in result.errors))

    def test_required_fields_check(self):
        """测试必需字段检查"""
        incomplete_config = {
            "app": {
                "version": "1.0.0"
                # 缺少name字段
            },
            "data": {
                "database": {
                    # 缺少path字段
                    "timeout": 30
                }
            }
            # 缺少llm配置
        }
        
        missing_fields = self.validator.check_required_fields(incomplete_config)
        
        self.assertGreater(len(missing_fields), 0)
        self.assertIn("app.name", missing_fields)
        self.assertIn("data.database.path", missing_fields)
        self.assertIn("llm.default_model", missing_fields)

    def test_validation_result_error_report(self):
        """测试验证结果错误报告"""
        invalid_config = {
            "app": {
                "name": "",
                "version": "invalid"
            }
        }
        
        result = self.validator.validate_config(invalid_config)
        
        self.assertFalse(result.is_valid)
        
        # 测试错误报告格式
        error_report = result.get_error_report()
        self.assertIn("❌ 配置验证失败", error_report)
        self.assertIn("🔴 错误:", error_report)
        
        # 测试摘要
        summary = result.get_summary()
        self.assertIn("配置验证失败", summary)
        self.assertIn("错误", summary)

    def test_get_config_validator_singleton(self):
        """测试配置验证器单例模式"""
        validator1 = get_config_validator()
        validator2 = get_config_validator()
        
        self.assertIs(validator1, validator2)
        self.assertIsInstance(validator1, ConfigValidator)


class TestConfigSchemas(unittest.TestCase):
    """配置模式测试"""

    def test_app_config_schema(self):
        """测试应用配置模式"""
        # 有效配置
        valid_app_config = {
            "name": "测试应用",
            "version": "1.2.3",
            "environment": "development",
            "debug": True
        }
        
        app_config = AppConfigSchema(**valid_app_config)
        self.assertEqual(app_config.name, "测试应用")
        self.assertEqual(app_config.version, "1.2.3")

    def test_database_config_schema(self):
        """测试数据库配置模式"""
        # 有效配置
        valid_db_config = {
            "path": "test.db",
            "timeout": 60,
            "check_same_thread": False,
            "max_retries": 5
        }
        
        db_config = DatabaseConfigSchema(**valid_db_config)
        self.assertEqual(db_config.path, "test.db")
        self.assertEqual(db_config.timeout, 60)

    def test_llm_config_schema(self):
        """测试LLM配置模式"""
        # 有效配置
        valid_llm_config = {
            "default_model": "gpt-4",
            "models": {
                "gpt-4": {
                    "provider": "openai",
                    "model_name": "gpt-4",
                    "temperature": 0.7,
                    "max_tokens": 2000
                }
            },
            "scenario_mapping": {
                "chat": "gpt-4"
            }
        }
        
        llm_config = LLMConfigSchema(**valid_llm_config)
        self.assertEqual(llm_config.default_model, "gpt-4")
        self.assertIn("gpt-4", llm_config.models)


if __name__ == '__main__':
    unittest.main()
