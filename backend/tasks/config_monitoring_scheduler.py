"""
配置监控定时任务调度器
定期执行配置完整性检查和硬编码扫描
"""

import asyncio
import schedule
import time
import threading
from datetime import datetime, timedelta
from typing import Optional
import logging

from backend.services.config_monitoring_service import ConfigMonitoringService
from backend.config.logging_config import get_logger

class ConfigMonitoringScheduler:
    """配置监控定时任务调度器"""

    def __init__(self):
        self.logger = get_logger(__name__)
        self.monitoring_service = ConfigMonitoringService()
        self.is_running = False
        self.scheduler_thread: Optional[threading.Thread] = None

    def start_scheduler(self):
        """启动定时任务调度器"""
        if self.is_running:
            self.logger.warning("定时任务调度器已在运行")
            return

        self.logger.info("启动配置监控定时任务调度器")

        # 配置定时任务
        self._setup_scheduled_tasks()

        # 在单独线程中运行调度器
        self.is_running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()

        self.logger.info("配置监控定时任务调度器已启动")

    def stop_scheduler(self):
        """停止定时任务调度器"""
        if not self.is_running:
            self.logger.warning("定时任务调度器未在运行")
            return

        self.logger.info("停止配置监控定时任务调度器")
        self.is_running = False

        if self.scheduler_thread and self.scheduler_thread.is_alive():
            # 从配置获取线程等待超时时间
            try:
                from backend.config import config_service
                unified_config = config_service.get_unified_config()
                thread_timeout = unified_config.get_threshold("performance.timeout.short_operation", 5)
            except Exception:
                thread_timeout = 5
            self.scheduler_thread.join(timeout=thread_timeout)

        schedule.clear()
        self.logger.info("配置监控定时任务调度器已停止")

    def _setup_scheduled_tasks(self):
        """设置定时任务"""

        # 每日配置完整性检查 (每天上午9点)
        schedule.every().day.at("09:00").do(self._daily_config_check)

        # 每周硬编码扫描 (每周一上午10点)
        schedule.every().monday.at("10:00").do(self._weekly_hardcode_scan)

        # 每小时健康度检查 (用于监控)
        schedule.every().hour.do(self._hourly_health_check)

        self.logger.info("定时任务已配置:")
        self.logger.info("  - 每日配置完整性检查: 09:00")
        self.logger.info("  - 每周硬编码扫描: 周一 10:00")
        self.logger.info("  - 每小时健康度检查")

    def _run_scheduler(self):
        """运行调度器主循环"""
        self.logger.info("定时任务调度器主循环已启动")

        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                self.logger.error(f"定时任务调度器运行错误: {e}")
                time.sleep(60)

        self.logger.info("定时任务调度器主循环已退出")

    def _daily_config_check(self):
        """每日配置完整性检查"""
        try:
            self.logger.info("开始执行每日配置完整性检查")

            result = self.monitoring_service.check_config_integrity()

            # 记录检查结果
            self.logger.info(f"每日配置检查完成 - 健康度: {result['health_score']:.1f}%, 状态: {result['status']}")

            # 如果发现问题，记录详细信息
            if result['missing_configs']:
                self.logger.warning(f"发现 {len(result['missing_configs'])} 个缺失配置项")
                for config in result['missing_configs']:
                    self.logger.warning(f"  缺失配置: {config}")

            if result['invalid_configs']:
                self.logger.error(f"发现 {len(result['invalid_configs'])} 个无效配置项")
                for config_error in result['invalid_configs']:
                    self.logger.error(f"  无效配置: {config_error['config']} - {config_error['error']}")

            # 保存检查报告
            report_data = {
                "type": "daily_config_check",
                "timestamp": datetime.now().isoformat(),
                "result": result
            }

            self.monitoring_service.save_monitoring_report(report_data)

            # 如果健康度低于阈值，发送告警
            if result['health_score'] < 90:
                self._send_alert("配置健康度告警", f"配置健康度降至 {result['health_score']:.1f}%，请及时检查")

        except Exception as e:
            self.logger.error(f"每日配置完整性检查失败: {e}")

    def _weekly_hardcode_scan(self):
        """每周硬编码扫描"""
        try:
            self.logger.info("开始执行每周硬编码扫描")

            result = self.monitoring_service.scan_hardcode_regression()

            # 记录扫描结果
            self.logger.info(f"每周硬编码扫描完成 - 扫描文件: {result['scanned_files']}, 风险等级: {result['risk_level']}")

            # 如果发现潜在硬编码，记录详细信息
            if result['potential_hardcodes']:
                self.logger.warning(f"发现 {len(result['potential_hardcodes'])} 个潜在硬编码")
                for hardcode in result['potential_hardcodes'][:5]:  # 只记录前5个
                    self.logger.warning(f"  潜在硬编码: {hardcode['file']}:{hardcode['line']} - {hardcode['content']}")

            # 保存扫描报告
            report_data = {
                "type": "weekly_hardcode_scan",
                "timestamp": datetime.now().isoformat(),
                "result": result
            }

            self.monitoring_service.save_monitoring_report(report_data)

            # 如果风险等级较高，发送告警
            if result['risk_level'] in ['MEDIUM', 'HIGH']:
                self._send_alert("硬编码回归告警", f"检测到 {result['risk_level']} 风险等级的硬编码回归")

        except Exception as e:
            self.logger.error(f"每周硬编码扫描失败: {e}")

    def _hourly_health_check(self):
        """每小时健康度检查"""
        try:
            # 轻量级健康检查，只检查关键配置
            critical_configs = [
                "message_templates.error.technical_issue",
                "message_templates.greeting.requirement_assistant",
                "thresholds.keyword_match_threshold"
            ]

            missing_count = 0
            for config_path in critical_configs:
                try:
                    value = self.monitoring_service.config.get_config_value(config_path)
                    if value is None or value == "":
                        missing_count += 1
                except:
                    missing_count += 1

            health_score = ((len(critical_configs) - missing_count) / len(critical_configs)) * 100

            if health_score < 100:
                self.logger.warning(f"关键配置健康度检查: {health_score:.1f}% ({missing_count} 个关键配置缺失)")
            else:
                self.logger.debug(f"关键配置健康度检查: {health_score:.1f}% (正常)")

        except Exception as e:
            self.logger.error(f"每小时健康度检查失败: {e}")

    def _send_alert(self, title: str, message: str):
        """发送告警通知"""
        try:
            # 这里可以集成邮件、短信、钉钉等通知方式
            # 目前只记录到日志
            self.logger.warning(f"📢 告警通知 - {title}: {message}")

            # 可以扩展为发送邮件
            # self._send_email_alert(title, message)

            # 可以扩展为发送钉钉消息
            # self._send_dingtalk_alert(title, message)

        except Exception as e:
            self.logger.error(f"发送告警通知失败: {e}")

    def run_immediate_check(self):
        """立即执行一次完整检查"""
        try:
            self.logger.info("执行立即完整检查")

            # 执行配置检查
            config_result = self.monitoring_service.check_config_integrity()

            # 执行硬编码扫描
            hardcode_result = self.monitoring_service.scan_hardcode_regression()

            # 组合结果
            report_data = {
                "type": "immediate_full_check",
                "timestamp": datetime.now().isoformat(),
                "config_check": config_result,
                "hardcode_scan": hardcode_result
            }

            # 保存报告
            report_file = self.monitoring_service.save_monitoring_report(report_data)

            self.logger.info(f"立即完整检查完成，报告已保存: {report_file}")

            return report_data

        except Exception as e:
            self.logger.error(f"立即完整检查失败: {e}")
            raise

    def get_scheduler_status(self) -> dict:
        """获取调度器状态"""
        return {
            "is_running": self.is_running,
            "next_runs": {
                "daily_config_check": self._get_next_run_time("09:00"),
                "weekly_hardcode_scan": self._get_next_weekly_run_time(),
                "hourly_health_check": self._get_next_hourly_run_time()
            },
            "thread_alive": self.scheduler_thread.is_alive() if self.scheduler_thread else False
        }

    def _get_next_run_time(self, time_str: str) -> str:
        """获取下次运行时间"""
        try:
            now = datetime.now()
            today = now.date()
            run_time = datetime.strptime(f"{today} {time_str}", "%Y-%m-%d %H:%M")

            if run_time <= now:
                run_time += timedelta(days=1)

            return run_time.isoformat()
        except:
            return "未知"

    def _get_next_weekly_run_time(self) -> str:
        """获取下次周运行时间"""
        try:
            now = datetime.now()
            days_until_monday = (7 - now.weekday()) % 7
            if days_until_monday == 0 and now.hour >= 10:
                days_until_monday = 7

            next_monday = now + timedelta(days=days_until_monday)
            next_run = next_monday.replace(hour=10, minute=0, second=0, microsecond=0)

            return next_run.isoformat()
        except:
            return "未知"

    def _get_next_hourly_run_time(self) -> str:
        """获取下次小时运行时间"""
        try:
            now = datetime.now()
            next_hour = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
            return next_hour.isoformat()
        except:
            return "未知"

# 全局调度器实例
_scheduler_instance: Optional[ConfigMonitoringScheduler] = None

def get_monitoring_scheduler() -> ConfigMonitoringScheduler:
    """获取监控调度器实例"""
    global _scheduler_instance
    if _scheduler_instance is None:
        _scheduler_instance = ConfigMonitoringScheduler()
    return _scheduler_instance

def start_monitoring_scheduler():
    """启动监控调度器"""
    scheduler = get_monitoring_scheduler()
    scheduler.start_scheduler()

def stop_monitoring_scheduler():
    """停止监控调度器"""
    scheduler = get_monitoring_scheduler()
    scheduler.stop_scheduler()
