"""
JWT认证工具
"""
import jwt
import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from fastapi import HTTPException, status
from backend.models.admin import AdminRole
import logging

logger = logging.getLogger(__name__)


class JWTManager:
    """JWT管理器"""

    def __init__(self, secret_key: Optional[str] = None, algorithm: str = "HS256"):
        self.secret_key = secret_key or secrets.token_urlsafe(32)
        self.algorithm = algorithm
        self.access_token_expire_minutes = 60 * 24  # 24小时
        self.refresh_token_expire_days = 7  # 7天

    def create_access_token(self, admin_id: int, username: str, role: AdminRole) -> str:
        """创建访问令牌"""
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        payload = {
            "sub": str(admin_id),
            "username": username,
            "role": role.value,
            "type": "access",
            "exp": expire,
            "iat": datetime.utcnow()
        }
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)

    def create_refresh_token(self, admin_id: int) -> str:
        """创建刷新令牌"""
        expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        payload = {
            "sub": str(admin_id),
            "type": "refresh",
            "exp": expire,
            "iat": datetime.utcnow()
        }
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)

    def verify_token(self, token: str) -> Dict[str, Any]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token已过期",
                headers={"WWW-Authenticate": "Bearer"}
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token无效",
                headers={"WWW-Authenticate": "Bearer"}
            )

    def get_admin_id_from_token(self, token: str) -> int:
        """从令牌中获取管理员ID"""
        payload = self.verify_token(token)
        return int(payload["sub"])

    def get_admin_role_from_token(self, token: str) -> AdminRole:
        """从令牌中获取管理员角色"""
        payload = self.verify_token(token)
        return AdminRole(payload["role"])

    def is_token_expired(self, token: str) -> bool:
        """检查令牌是否过期"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            exp = payload.get("exp")
            if exp:
                return datetime.utcnow() > datetime.fromtimestamp(exp)
            return True
        except jwt.JWTError:
            return True


# 全局JWT管理器实例
jwt_manager = JWTManager()


def require_role(required_roles: list[AdminRole]):
    """角色权限装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 这里需要从请求中获取当前用户角色
            # 具体实现在FastAPI依赖注入中完成
            return func(*args, **kwargs)
        return wrapper
    return decorator


class PermissionChecker:
    """权限检查器"""

    # 权限矩阵定义
    PERMISSIONS = {
        # 用户管理权限
        "user_management": [AdminRole.SUPER_ADMIN, AdminRole.CUSTOMER_SERVICE],
        "user_view": [AdminRole.SUPER_ADMIN, AdminRole.CUSTOMER_SERVICE, AdminRole.DATA_ANALYST],

        # 系统配置权限
        "system_config": [AdminRole.SUPER_ADMIN, AdminRole.SYSTEM_ADMIN],

        # 数据统计权限
        "data_stats": [AdminRole.SUPER_ADMIN, AdminRole.SYSTEM_ADMIN, AdminRole.DATA_ANALYST, AdminRole.CUSTOMER_SERVICE],

        # 系统监控权限
        "system_monitor": [AdminRole.SUPER_ADMIN, AdminRole.SYSTEM_ADMIN],

        # 对话管理权限
        "conversation_management": [AdminRole.SUPER_ADMIN, AdminRole.CUSTOMER_SERVICE],

        # 操作日志权限
        "operation_logs": [AdminRole.SUPER_ADMIN, AdminRole.SYSTEM_ADMIN],
    }

    @classmethod
    def check_permission(cls, user_role: AdminRole, permission: str) -> bool:
        """检查用户是否有指定权限"""
        allowed_roles = cls.PERMISSIONS.get(permission, [])
        return user_role in allowed_roles

    @classmethod
    def require_permission(cls, permission: str):
        """权限检查装饰器"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                # 这里需要从请求中获取当前用户角色
                # 具体实现在FastAPI依赖注入中完成
                return func(*args, **kwargs)
            return wrapper
        return decorator


def create_permission_error(permission: str) -> HTTPException:
    """创建权限错误响应"""
    return HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail=f"权限不足，需要 {permission} 权限"
    )


def create_auth_error(message: str = "认证失败") -> HTTPException:
    """创建认证错误响应"""
    return HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail=message,
        headers={"WWW-Authenticate": "Bearer"}
    )
