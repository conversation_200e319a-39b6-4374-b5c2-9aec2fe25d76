"""
进度提示管理器
用于在长时间处理过程中提供用户友好的进度提示
"""

import time
import threading
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ProgressStage(Enum):
    """进度阶段枚举"""
    STARTING = "starting"
    ANALYZING = "analyzing"
    PROCESSING = "processing"
    EXTRACTING = "extracting"
    GENERATING = "generating"
    FINALIZING = "finalizing"
    COMPLETED = "completed"


@dataclass
class ProgressUpdate:
    """进度更新数据结构"""
    stage: ProgressStage
    percentage: float
    message: str
    details: Optional[str] = None
    estimated_remaining: Optional[int] = None  # 预估剩余秒数


class ProgressIndicator:
    """进度提示管理器 - 多用户安全版本"""

    def __init__(self, session_id: str, user_id: str):
        self.session_id = session_id
        self.user_id = user_id
        self.start_time = time.time()
        self.current_stage = ProgressStage.STARTING
        self.progress_percentage = 0.0
        self.stage_messages = self._init_stage_messages()
        self.callbacks: List[Callable[[ProgressUpdate], None]] = []
        self._lock = threading.Lock()  # 实例级别的锁，保护实例状态

        # 添加用户标识到日志中
        logger.debug(f"创建进度指示器: session={session_id}, user={user_id}")

    def _init_stage_messages(self) -> Dict[ProgressStage, List[str]]:
        """初始化各阶段的提示消息"""
        return {
            ProgressStage.STARTING: [
                "🚀 正在启动需求分析...",
                "📋 准备收集您的需求信息...",
                "🎯 开始理解您的项目需求..."
            ],
            ProgressStage.ANALYZING: [
                "🔍 正在分析您的需求...",
                "🧠 深入理解项目背景...",
                "📊 评估需求复杂度...",
                "🎨 识别关键需求点..."
            ],
            ProgressStage.PROCESSING: [
                "⚙️ 正在处理需求信息...",
                "🔄 整理和归类需求...",
                "📝 提取关键信息点...",
                "🎯 确定重点关注领域..."
            ],
            ProgressStage.EXTRACTING: [
                "📤 正在提取核心需求...",
                "🎪 整合多维度信息...",
                "🔗 建立需求关联性...",
                "📋 构建需求框架..."
            ],
            ProgressStage.GENERATING: [
                "✨ 正在生成专业问题...",
                "🎨 优化问题表达方式...",
                "🎯 定制化问题内容...",
                "📝 准备详细询问..."
            ],
            ProgressStage.FINALIZING: [
                "🏁 正在完成最后处理...",
                "✅ 验证信息完整性...",
                "🎉 准备呈现结果...",
                "📋 整理最终输出..."
            ]
        }

    def add_callback(self, callback: Callable[[ProgressUpdate], None]):
        """添加进度更新回调"""
        self.callbacks.append(callback)

    def update_progress(self, stage: ProgressStage, percentage: float,
                       custom_message: Optional[str] = None,
                       details: Optional[str] = None):
        """更新进度 - 线程安全"""
        with self._lock:
            self.current_stage = stage
            self.progress_percentage = min(100.0, max(0.0, percentage))

            # 选择消息
            if custom_message:
                message = custom_message
            else:
                messages = self.stage_messages.get(stage, ["正在处理..."])
                # 根据进度选择不同的消息
                msg_index = min(len(messages) - 1, int(percentage / 25))
                message = messages[msg_index]

            # 计算预估剩余时间
            elapsed_time = time.time() - self.start_time
            if percentage > 0:
                estimated_total = elapsed_time * (100 / percentage)
                estimated_remaining = max(0, int(estimated_total - elapsed_time))
            else:
                estimated_remaining = None

            # 创建进度更新
            update = ProgressUpdate(
                stage=stage,
                percentage=percentage,
                message=message,
                details=details,
                estimated_remaining=estimated_remaining
            )

            # 记录进度更新（用于调试多用户情况）
            logger.debug(f"进度更新 [{self.session_id}_{self.user_id}]: {stage.value} {percentage}% - {message}")

        # 在锁外调用回调函数，避免死锁
        for callback in self.callbacks:
            try:
                callback(update)
            except Exception as e:
                logger.error(f"进度回调执行失败 [{self.session_id}_{self.user_id}]: {e}")

    def get_progress_message(self) -> str:
        """获取当前进度消息 - 线程安全"""
        with self._lock:
            elapsed = int(time.time() - self.start_time)

            if self.progress_percentage >= 100:
                return f"✅ 处理完成 (耗时 {elapsed}秒)"

            message_parts = []

            # 基础进度信息
            stage_messages = self.stage_messages.get(self.current_stage, ["正在处理..."])
            current_message = stage_messages[0]
            message_parts.append(current_message)

            # 进度百分比
            if self.progress_percentage > 0:
                message_parts.append(f"({self.progress_percentage:.0f}%)")

            # 时间信息
            if elapsed > 10:  # 超过10秒才显示时间信息
                message_parts.append(f"已用时 {elapsed}秒")

            return " ".join(message_parts)


class RequirementCollectionProgressTracker:
    """需求采集进度跟踪器 - 多用户安全版本"""

    def __init__(self, session_id: str, user_id: str):
        self.session_id = session_id
        self.user_id = user_id
        self.total_focus_points = 0
        self.completed_points = 0
        self.processing_points = 0
        self.current_point_name = ""
        self._lock = threading.Lock()  # 实例级别的锁

        logger.debug(f"创建需求采集进度跟踪器: session={session_id}, user={user_id}")

    def initialize(self, focus_points: List[Dict[str, Any]]):
        """初始化关注点信息 - 线程安全"""
        with self._lock:
            self.total_focus_points = len(focus_points)
            self.completed_points = 0
            self.processing_points = 0
            logger.debug(f"初始化进度跟踪器 [{self.session_id}_{self.user_id}]: {len(focus_points)} 个关注点")

    def update_point_status(self, point_id: str, point_name: str, status: str):
        """更新关注点状态 - 线程安全"""
        with self._lock:
            if status == "processing":
                self.current_point_name = point_name
                self.processing_points = 1
            elif status == "completed":
                self.completed_points += 1
                self.processing_points = 0
                self.current_point_name = ""

            logger.debug(f"更新关注点状态 [{self.session_id}_{self.user_id}]: {point_name} -> {status}")

    def get_collection_progress_message(self) -> str:
        """获取采集进度消息 - 线程安全"""
        with self._lock:
            if self.total_focus_points == 0:
                return "🚀 准备开始需求采集..."

            progress_percentage = (self.completed_points / self.total_focus_points) * 100

            message_parts = []

            # 当前处理状态
            if self.current_point_name:
                message_parts.append(f"🎯 正在了解：{self.current_point_name}")

            # 进度信息
            if progress_percentage == 0:
                message_parts.append("📋 开始收集需求信息")
            elif progress_percentage < 25:
                message_parts.append(f"📝 已收集 {self.completed_points}/{self.total_focus_points} 个方面")
            elif progress_percentage < 50:
                message_parts.append(f"✨ 很好！已完成 {progress_percentage:.0f}%")
            elif progress_percentage < 75:
                message_parts.append(f"🎯 太棒了！已完成过半 ({progress_percentage:.0f}%)")
            elif progress_percentage < 100:
                message_parts.append(f"🏆 即将完成！({progress_percentage:.0f}%)")
            else:
                message_parts.append("🎉 需求收集完成！")

            return " | ".join(message_parts)

    def get_detailed_status(self) -> Dict[str, Any]:
        """获取详细状态信息 - 线程安全"""
        with self._lock:
            return {
                "session_id": self.session_id,
                "user_id": self.user_id,
                "total_points": self.total_focus_points,
                "completed_points": self.completed_points,
                "processing_points": self.processing_points,
                "pending_points": self.total_focus_points - self.completed_points - self.processing_points,
                "progress_percentage": (self.completed_points / self.total_focus_points * 100) if self.total_focus_points > 0 else 0,
                "current_point": self.current_point_name
            }


# 多用户安全的进度跟踪器管理

class ProgressTrackerManager:
    """线程安全的进度跟踪器管理器"""

    def __init__(self):
        self._trackers: Dict[str, RequirementCollectionProgressTracker] = {}
        self._lock = threading.RLock()  # 可重入锁，支持同一线程多次获取

    def get_tracker(self, session_id: str, user_id: str) -> RequirementCollectionProgressTracker:
        """获取或创建进度跟踪器 - 线程安全"""
        key = f"{session_id}_{user_id}"

        with self._lock:
            if key not in self._trackers:
                self._trackers[key] = RequirementCollectionProgressTracker(session_id, user_id)
                logger.debug(f"创建新的进度跟踪器: {key}")
            return self._trackers[key]

    def cleanup_tracker(self, session_id: str, user_id: str):
        """清理进度跟踪器 - 线程安全"""
        key = f"{session_id}_{user_id}"

        with self._lock:
            if key in self._trackers:
                del self._trackers[key]
                logger.debug(f"清理进度跟踪器: {key}")

    def get_active_trackers_count(self) -> int:
        """获取活跃跟踪器数量"""
        with self._lock:
            return len(self._trackers)

    def cleanup_all(self):
        """清理所有跟踪器（用于系统关闭）"""
        with self._lock:
            count = len(self._trackers)
            self._trackers.clear()
            logger.info(f"清理了 {count} 个进度跟踪器")


# 全局管理器实例
_progress_manager = ProgressTrackerManager()


def get_progress_tracker(session_id: str, user_id: str) -> RequirementCollectionProgressTracker:
    """获取或创建进度跟踪器 - 多用户安全"""
    return _progress_manager.get_tracker(session_id, user_id)


def cleanup_progress_tracker(session_id: str, user_id: str):
    """清理进度跟踪器 - 多用户安全"""
    _progress_manager.cleanup_tracker(session_id, user_id)


def get_progress_manager_stats() -> Dict[str, int]:
    """获取进度管理器统计信息"""
    return {
        "active_trackers": _progress_manager.get_active_trackers_count()
    }
