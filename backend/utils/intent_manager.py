"""
意图管理器 - 统一意图配置的核心工具类

目标：
- 提供统一的意图配置管理
- 解决模板与代码不同步问题
- 建立单一数据源（Single Source of Truth）

作者：AI Assistant
版本：1.0
"""

import yaml
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging
from backend.config import config_service

class IntentManager:
    """
    意图管理器 - 统一意图配置管理

    功能：
    1. 加载和解析意图配置文件
    2. 提供意图查询和验证API
    3. 生成决策规则和状态转换规则
    4. 支持配置热重载（可选）
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化意图管理器

        Args:
            config_path: 配置文件路径，默认为 backend/config/intent_definitions.yaml
        """
        self.logger = logging.getLogger(__name__)

        # 设置默认配置文件路径
        if config_path is None:
            # 获取项目根目录
            current_dir = Path(__file__).parent
            project_root = current_dir.parent
            config_path = project_root / "config" / "intent_definitions.yaml"

        self.config_path = Path(config_path)
        self.config = None

        # 加载配置
        self._load_config()

    def _load_config(self) -> None:
        """
        加载意图配置文件

        Raises:
            FileNotFoundError: 配置文件不存在
            yaml.YAMLError: YAML格式错误
            ValueError: 配置内容无效
        """
        try:
            if not self.config_path.exists():
                raise FileNotFoundError(f"意图配置文件不存在: {self.config_path}")

            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)

            # 验证配置格式
            self._validate_config()

            # 检查是否为重复加载
            load_repeat_count = getattr(self, '_load_count', 0) + 1
            setattr(self, '_load_count', load_repeat_count)

            if load_repeat_count > 1:
                self.logger.info(f"成功加载意图配置文件: {self.config_path} [重复 {load_repeat_count - 1} 次]")
                self.logger.info(f"配置版本: {self.config.get('intent_system', {}).get('version', 'unknown')} [重复 {load_repeat_count - 1} 次]")
            else:
                self.logger.info(f"成功加载意图配置文件: {self.config_path}")
                self.logger.info(f"配置版本: {self.config.get('intent_system', {}).get('version', 'unknown')}")

        except Exception as e:
            self.logger.error(f"加载意图配置文件失败: {e}")
            raise

    def _validate_config(self) -> None:
        """
        验证配置文件格式和内容

        Raises:
            ValueError: 配置格式无效
        """
        if not self.config:
            raise ValueError("配置文件为空")

        if 'intent_system' not in self.config:
            raise ValueError("配置文件缺少 'intent_system' 根节点")

        intent_system = self.config['intent_system']

        if 'intents' not in intent_system:
            raise ValueError("配置文件缺少 'intents' 节点")

        # 验证每个意图的必需字段
        required_fields = ['description', 'action', 'priority', 'supported_states']
        for intent_name, intent_config in intent_system['intents'].items():
            for field in required_fields:
                if field not in intent_config:
                    raise ValueError(f"意图 '{intent_name}' 缺少必需字段: {field}")

        self.logger.debug("配置文件格式验证通过")

    def get_valid_intents(self) -> List[str]:
        """
        获取所有有效意图列表

        Returns:
            List[str]: 有效意图名称列表
        """
        if not self.config:
            return []

        return list(self.config['intent_system']['intents'].keys())

    def get_intent_config(self, intent: str) -> Optional[Dict[str, Any]]:
        """
        获取特定意图的配置信息

        Args:
            intent: 意图名称

        Returns:
            Dict[str, Any]: 意图配置，如果不存在返回None
        """
        if not self.config:
            return None

        return self.config['intent_system']['intents'].get(intent)

    def is_valid_intent(self, intent: str) -> bool:
        """
        验证意图是否有效

        Args:
            intent: 意图名称

        Returns:
            bool: 是否为有效意图
        """
        return intent in self.get_valid_intents()

    def get_intent_action(self, intent: str) -> Optional[str]:
        """
        获取意图对应的动作

        Args:
            intent: 意图名称

        Returns:
            str: 动作名称，如果意图不存在返回None
        """
        intent_config = self.get_intent_config(intent)
        return intent_config.get('action') if intent_config else None

    def get_intent_priority(self, intent: str) -> int:
        """
        获取意图的优先级

        Args:
            intent: 意图名称

        Returns:
            int: 优先级数值，数值越大优先级越高，不存在返回0
        """
        intent_config = self.get_intent_config(intent)
        return intent_config.get('priority', 0) if intent_config else 0

    def get_supported_states(self, intent: str) -> List[str]:
        """
        获取意图支持的状态列表

        Args:
            intent: 意图名称

        Returns:
            List[str]: 支持的状态列表
        """
        intent_config = self.get_intent_config(intent)
        return intent_config.get('supported_states', []) if intent_config else []

    def get_state_transitions(self, state: str) -> Dict[str, str]:
        """
        获取特定状态的转换规则

        Args:
            state: 当前状态

        Returns:
            Dict[str, str]: 状态转换规则 {意图: 目标状态}
        """
        if not self.config:
            return {}

        transitions = self.config['intent_system'].get('state_transitions', {})
        return transitions.get(state, {})

    def get_decision_rules(self) -> Dict[str, Any]:
        """
        获取决策规则配置

        Returns:
            Dict[str, Any]: 决策规则配置
        """
        if not self.config:
            return {}

        return self.config['intent_system'].get('decision_rules', {})

    def get_priority_order(self) -> List[str]:
        """
        获取意图优先级顺序

        Returns:
            List[str]: 按优先级排序的意图列表
        """
        decision_rules = self.get_decision_rules()
        return decision_rules.get('priority_order', [])

    def get_default_action(self) -> str:
        """
        获取默认动作

        Returns:
            str: 默认动作名称
        """
        decision_rules = self.get_decision_rules()
        return decision_rules.get('default_action', 'clarify_intent')

    def get_confidence_thresholds(self) -> Dict[str, float]:
        """
        获取置信度阈值配置

        Returns:
            Dict[str, float]: 置信度阈值 {级别: 阈值}
        """
        decision_rules = self.get_decision_rules()
        
        return decision_rules.get('confidence_thresholds', {
            'high': config.get_threshold("confidence.high", 0.8),
            'medium': config.get_threshold("confidence.low", 0.6),
            'low': config.get_threshold("confidence.minimum", 0.4)
        })

    def reload_config(self) -> bool:
        """
        重新加载配置文件

        Returns:
            bool: 是否重载成功
        """
        try:
            self._load_config()
            self.logger.info("配置文件重载成功")
            return True
        except Exception as e:
            self.logger.error(f"配置文件重载失败: {e}")
            return False

    def get_config_info(self) -> Dict[str, Any]:
        """
        获取配置文件基本信息

        Returns:
            Dict[str, Any]: 配置信息
        """
        if not self.config:
            return {}

        intent_system = self.config.get('intent_system', {})
        intents = intent_system.get('intents', {})

        return {
            'version': intent_system.get('version', 'unknown'),
            'description': intent_system.get('description', ''),
            'config_path': str(self.config_path),
            'intent_count': len(intents),
            'intent_names': list(intents.keys()),
            'states': list(intent_system.get('state_transitions', {}).keys())
        }


# 全局单例实例（可选）
_intent_manager_instance = None

def get_intent_manager() -> IntentManager:
    """
    获取全局意图管理器实例（单例模式）

    Returns:
        IntentManager: 意图管理器实例
    """
    global _intent_manager_instance
    if _intent_manager_instance is None:
        _intent_manager_instance = IntentManager()
    return _intent_manager_instance
