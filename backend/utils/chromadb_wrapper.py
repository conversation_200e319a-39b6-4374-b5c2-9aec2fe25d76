"""
ChromaDB包装器 - 禁用遥测
"""

import os
import chromadb
from chromadb import Settings

# 强制禁用遥测
os.environ['ANONYMIZED_TELEMETRY'] = 'False'
os.environ['CHROMA_TELEMETRY_DISABLED'] = 'True'

class SafeSettings(Settings):
    """安全的ChromaDB设置类"""
    
    def __init__(self, *args, **kwargs):
        # 强制禁用遥测
        kwargs['anonymized_telemetry'] = False
        super().__init__(*args, **kwargs)


def create_safe_client(path, **kwargs):
    """创建安全的ChromaDB客户端"""
    settings = SafeSettings(
        anonymized_telemetry=False,
        allow_reset=True,
        is_persistent=True,
        **kwargs
    )
    
    return chromadb.PersistentClient(
        path=path,
        settings=settings
    )


# 替换默认的Settings
chromadb.Settings = SafeSettings
