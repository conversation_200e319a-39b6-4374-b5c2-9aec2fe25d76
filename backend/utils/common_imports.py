#!/usr/bin/env python3
"""
通用导入模块

集中管理常用的标准库和第三方库导入，减少重复导入和提高导入一致性。

使用方式:
```python
# 代替多个单独的 import 语句
from backend.utils.common_imports import *

# 或者选择性导入
```

注意：
- 此模块只包含标准库和稳定的第三方库
- 项目特定的模块仍应单独导入
- 避免导入过于专用的模块
"""

# ============================================================================
# Python 标准库
# ============================================================================

# 数据类型和类型注解
from typing import (
    Dict, List, Optional, Union, Any, Callable, Iterator, Tuple,
    Set, FrozenSet, Mapping, Sequence, TypeVar, Generic, Type
)
from dataclasses import dataclass, field, asdict
from enum import Enum, IntEnum, auto
from collections import defaultdict, deque, Counter, OrderedDict, namedtuple

# 文件和路径操作
from pathlib import Path
import os

# 字符串和正则表达式

# 日期时间
import time as time_module

# 数据序列化
import json

# 系统和进程
import sys

# 数学和随机

# 网络和URL

# 加密和哈希
import hashlib
import uuid

# 错误处理和调试

# 日志
import logging

# 异步编程

# ============================================================================
# 第三方库 (常用且稳定的)
# ============================================================================

# 数据处理
try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    pd = None
    PANDAS_AVAILABLE = False

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    np = None
    NUMPY_AVAILABLE = False

# 网络请求
try:
    REQUESTS_AVAILABLE = True
except ImportError:
    requests = None
    REQUESTS_AVAILABLE = False

try:
    import aiohttp
    AIOHTTP_AVAILABLE = True
except ImportError:
    aiohttp = None
    AIOHTTP_AVAILABLE = False

# 数据库
try:
    import sqlite3
    SQLITE_AVAILABLE = True
except ImportError:
    sqlite3 = None
    SQLITE_AVAILABLE = False

# Web框架
try:
    from fastapi import FastAPI, HTTPException, Depends, status
    from fastapi.responses import JSONResponse
    from fastapi.middleware.cors import CORSMiddleware
    FASTAPI_AVAILABLE = True
except ImportError:
    FastAPI = HTTPException = Depends = status = JSONResponse = CORSMiddleware = None
    FASTAPI_AVAILABLE = False

try:
    from pydantic import BaseModel, Field, validator
    PYDANTIC_AVAILABLE = True
except ImportError:
    BaseModel = Field = validator = None
    PYDANTIC_AVAILABLE = False

# ============================================================================
# 常用工具函数
# ============================================================================

def get_logger(name: str = __name__, level: int = logging.INFO) -> logging.Logger:
    """获取配置好的日志记录器"""
    logger = logging.getLogger(name)
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(level)
    return logger

def safe_json_loads(data: str, default: Any = None) -> Any:
    """安全地解析JSON，失败时返回默认值"""
    try:
        return json.loads(data)
    except (json.JSONDecodeError, TypeError, ValueError):
        return default

def safe_yaml_loads(data: str, default: Any = None) -> Any:
    """安全地解析YAML，失败时返回默认值"""
    if not YAML_AVAILABLE:
        return default
    try:
        return yaml.safe_load(data)
    except (yaml.YAMLError, TypeError, ValueError):
        return default

def ensure_directory(path: Union[str, Path]) -> Path:
    """确保目录存在，如果不存在则创建"""
    path_obj = Path(path)
    path_obj.mkdir(parents=True, exist_ok=True)
    return path_obj

def read_text_file(file_path: Union[str, Path], encoding: str = 'utf-8', default: str = '') -> str:
    """安全地读取文本文件"""
    try:
        return Path(file_path).read_text(encoding=encoding)
    except (IOError, OSError, UnicodeError):
        return default

def write_text_file(file_path: Union[str, Path], content: str, encoding: str = 'utf-8') -> bool:
    """安全地写入文本文件"""
    try:
        Path(file_path).write_text(content, encoding=encoding)
        return True
    except (IOError, OSError, UnicodeError):
        return False

def get_file_hash(file_path: Union[str, Path], algorithm: str = 'md5') -> Optional[str]:
    """计算文件哈希值"""
    try:
        hash_func = hashlib.new(algorithm)
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_func.update(chunk)
        return hash_func.hexdigest()
    except (IOError, OSError, ValueError):
        return None

def timing_decorator(func: Callable) -> Callable:
    """计时装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time_module.perf_counter()
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            end_time = time_module.perf_counter()
            duration = end_time - start_time
            logger = get_logger(func.__module__)
            logger.debug(f"{func.__name__} 执行时间: {duration:.4f}秒")
    return wrapper

def retry_decorator(max_attempts: int = 3, delay: float = 1.0,
                   exceptions: Tuple[Exception, ...] = (Exception,)) -> Callable:
    """重试装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    if attempt == max_attempts - 1:
                        raise
                    logger = get_logger(func.__module__)
                    logger.warning(f"{func.__name__} 第{attempt + 1}次尝试失败: {e}，{delay}秒后重试")
                    time_module.sleep(delay)
            return None
        return wrapper
    return decorator

# ============================================================================
# 模块可用性检查
# ============================================================================

def check_module_availability() -> Dict[str, bool]:
    """检查可选模块的可用性"""
    return {
        'yaml': YAML_AVAILABLE,
        'pandas': PANDAS_AVAILABLE,
        'numpy': NUMPY_AVAILABLE,
        'requests': REQUESTS_AVAILABLE,
        'aiohttp': AIOHTTP_AVAILABLE,
        'sqlite3': SQLITE_AVAILABLE,
        'fastapi': FASTAPI_AVAILABLE,
        'pydantic': PYDANTIC_AVAILABLE,
    }

# ============================================================================
# 导出列表
# ============================================================================

__all__ = [
    # 类型注解
    'Dict', 'List', 'Optional', 'Union', 'Any', 'Callable', 'Iterator', 'Tuple',
    'Set', 'FrozenSet', 'Mapping', 'Sequence', 'TypeVar', 'Generic', 'Type',
    'dataclass', 'field', 'asdict', 'Enum', 'IntEnum', 'auto',
    'defaultdict', 'deque', 'Counter', 'OrderedDict', 'namedtuple',

    # 文件和路径
    'Path', 'os', 'shutil', 'tempfile', 'glob',

    # 字符串和正则
    're', 'string', 'unicodedata',

    # 日期时间
    'datetime', 'date', 'time', 'timedelta', 'timezone', 'time_module', 'calendar',

    # 数据序列化
    'json', 'pickle', 'csv', 'configparser',

    # 系统
    'sys', 'subprocess', 'platform', 'signal', 'threading', 'multiprocessing',
    'concurrent', 'contextmanager', 'suppress', 'closing',

    # 数学
    'math', 'random', 'statistics', 'decimal', 'fractions',

    # 网络
    'urllib', 'http', 'socket',

    # 加密
    'hashlib', 'hmac', 'secrets', 'uuid',

    # 调试
    'traceback', 'warnings', 'inspect', 'pdb',

    # 日志
    'logging', 'RotatingFileHandler', 'TimedRotatingFileHandler',

    # 异步和函数式
    'asyncio', 'wraps', 'partial', 'lru_cache', 'reduce',
    'chain', 'combinations', 'permutations', 'product', 'cycle',

    # 工具函数
    'get_logger', 'safe_json_loads', 'safe_yaml_loads', 'ensure_directory',
    'read_text_file', 'write_text_file', 'get_file_hash',
    'timing_decorator', 'retry_decorator', 'check_module_availability',
]

# 添加条件导出
if YAML_AVAILABLE:
    __all__.append('yaml')
if PANDAS_AVAILABLE:
    __all__.append('pd')
if NUMPY_AVAILABLE:
    __all__.append('np')
if REQUESTS_AVAILABLE:
    __all__.append('requests')
if AIOHTTP_AVAILABLE:
    __all__.append('aiohttp')
if SQLITE_AVAILABLE:
    __all__.append('sqlite3')
if FASTAPI_AVAILABLE:
    __all__.extend(['FastAPI', 'HTTPException', 'Depends', 'status', 'JSONResponse', 'CORSMiddleware'])
if PYDANTIC_AVAILABLE:
    __all__.extend(['BaseModel', 'Field', 'validator'])
