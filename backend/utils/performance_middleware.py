"""
性能监控中间件

此模块提供FastAPI性能监控中间件，用于自动跟踪API请求的性能指标。
主要功能包括：
1. 自动记录API响应时间
2. 跟踪请求成功率和错误率
3. 监控并发请求数量
4. 记录请求详细信息
"""

import time
import logging
from typing import Callable, Dict, Any
from fastapi import FastAPI, Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from .performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


class PerformanceMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""

    def __init__(self, app: ASGIApp, enabled: bool = True):
        """
        初始化性能监控中间件

        Args:
            app: ASGI应用
            enabled: 是否启用性能监控
        """
        super().__init__(app)
        self.enabled = enabled
        self.active_requests = 0

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并记录性能指标"""
        if not self.enabled:
            return await call_next(request)

        # 记录请求开始时间
        start_time = time.time()

        # 增加活跃请求计数
        self.active_requests += 1

        # 获取请求路径（用作端点标识）
        endpoint = request.url.path
        method = request.method
        endpoint_key = f"{method}_{endpoint.replace('/', '_').strip('_')}"

        try:
            # 处理请求
            response = await call_next(request)

            # 记录成功请求
            elapsed_time = time.time() - start_time

            # 使用性能监控器记录API调用
            if endpoint_key not in performance_monitor.api_metrics:
                from .performance_monitor import TimingMetric
                performance_monitor.api_metrics[endpoint_key] = TimingMetric(
                    f"api_{endpoint_key}", f"API端点 {method} {endpoint} 的响应时间"
                )

            performance_monitor.api_metrics[endpoint_key].record(elapsed_time)

            # 记录详细日志
            logger.info(
                f"API请求完成: {method} {endpoint} - "
                f"状态码: {response.status_code} - "
                f"响应时间: {elapsed_time:.3f}s - "
                f"活跃请求: {self.active_requests}"
            )

            return response

        except Exception as e:
            # 记录错误请求
            elapsed_time = time.time() - start_time

            logger.error(
                f"API请求异常: {method} {endpoint} - "
                f"错误: {str(e)} - "
                f"响应时间: {elapsed_time:.3f}s - "
                f"活跃请求: {self.active_requests}"
            )

            # 仍然记录响应时间（即使是错误请求）
            if endpoint_key not in performance_monitor.api_metrics:
                from .performance_monitor import TimingMetric
                performance_monitor.api_metrics[endpoint_key] = TimingMetric(
                    f"api_{endpoint_key}", f"API端点 {method} {endpoint} 的响应时间"
                )

            performance_monitor.api_metrics[endpoint_key].record(elapsed_time)

            # 返回错误响应
            return JSONResponse(
                status_code=500,
                content={"detail": "Internal server error"}
            )

        finally:
            # 减少活跃请求计数
            self.active_requests -= 1


def add_performance_middleware(app: FastAPI, enabled: bool = True):
    """
    为FastAPI应用添加性能监控中间件

    Args:
        app: FastAPI应用实例
        enabled: 是否启用性能监控
    """
    if enabled:
        app.add_middleware(PerformanceMiddleware, enabled=enabled)
        logger.info("性能监控中间件已添加")
    else:
        logger.info("性能监控中间件已禁用")


def get_performance_stats() -> Dict[str, Any]:
    """
    获取当前性能统计信息

    Returns:
        Dict[str, Any]: 性能统计数据
    """
    return {
        "api_metrics": performance_monitor.get_api_metrics(),
        "llm_metrics": performance_monitor.get_llm_metrics(),
        "db_metrics": performance_monitor.get_db_metrics(),
        "resource_metrics": performance_monitor.get_resource_metrics(),
        "summary": {
            "total_api_calls": sum(
                metric.get("count", 0)
                for metric in performance_monitor.get_api_metrics().values()
            ),
            "avg_response_time": _calculate_avg_response_time(),
            "system_cpu_avg": performance_monitor.resource_metric.get_avg_cpu(),
            "system_memory_avg": performance_monitor.resource_metric.get_avg_memory()
        }
    }


def _calculate_avg_response_time() -> float:
    """计算平均响应时间"""
    api_metrics = performance_monitor.get_api_metrics()
    if not api_metrics:
        return 0.0

    total_time = 0.0
    total_count = 0

    for metric in api_metrics.values():
        count = metric.get("count", 0)
        avg_time = metric.get("avg_time", 0.0)
        total_time += avg_time * count
        total_count += count

    return total_time / total_count if total_count > 0 else 0.0


def reset_performance_stats():
    """重置性能统计数据"""
    performance_monitor.reset_metrics()
    logger.info("性能统计数据已重置")


def save_performance_report(filename: str = None) -> str:
    """
    保存性能报告

    Args:
        filename: 文件名，如果为None则使用当前时间戳

    Returns:
        str: 保存的文件路径
    """
    return performance_monitor.save_metrics(filename)
