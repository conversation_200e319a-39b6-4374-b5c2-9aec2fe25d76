# 后台配置 API OpenAPI 草案（仅文档）

openapi: 3.0.3
info:
  title: Admin Config Backend API
  version: 0.1.0
  description: 后台管理用于配置观测与受控操作的只读优先 API 草案
servers:
  - url: https://admin.example.com/api
    description: 生产环境（示例）
  - url: https://staging.admin.example.com/api
    description: Staging 环境（示例）

paths:
  /config/status:
    get:
      summary: 获取配置状态
      operationId: getConfigStatus
      tags: [Config]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigStatus'
  /config/snapshot/meta:
    get:
      summary: 获取当前快照元信息
      operationId: getSnapshotMeta
      tags: [Config]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SnapshotMeta'
  /config/files/list:
    get:
      summary: 列出配置文件
      operationId: listConfigFiles
      tags: [Files]
      parameters:
        - in: query
          name: prefix
          schema:
            type: string
          description: 路径前缀过滤（如 files/）
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/ConfigFileItem'
  /config/files/content:
    get:
      summary: 获取配置文件内容（只读，敏感脱敏）
      operationId: getFileContent
      tags: [Files]
      parameters:
        - in: query
          name: path
          required: true
          schema:
            type: string
          description: 配置文件相对路径（如 files/strategies.yaml）
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  path:
                    type: string
                  content:
                    type: string
  /config/diff/report:
    get:
      summary: 获取单文件视图 vs 分文件视图对齐报告
      operationId: getDiffReport
      tags: [Alignment]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DiffReport'
  /config/metrics:
    get:
      summary: 获取配置相关指标
      operationId: getConfigMetrics
      tags: [Metrics]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MetricsPayload'
  /config/reload:
    post:
      summary: 受控触发配置重载（Dev/Staging）
      operationId: postConfigReload
      tags: [Ops]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReloadRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReloadResult'
      security:
        - bearerAuth: []

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ConfigStatus:
      type: object
      properties:
        sections:
          type: array
          items:
            type: string
        loading_strategy:
          type: string
          example: layered_config
        architecture:
          type: object
          properties:
            name:
              type: string
              example: unified_single_file
            version:
              type: integer
              example: 1
            support:
              type: object
            description:
              type: string
        sources:
          type: object
          properties:
            defaults:
              type: string
            files:
              type: array
              items:
                type: string
            env_variables:
              type: string
        last_updated:
          type: string
        system_version:
          type: string
    SnapshotMeta:
      type: object
      properties:
        version:
          type: string
        built_at:
          type: string
          format: date-time
        size:
          type: integer
          description: bytes
        source_summary:
          type: object
          properties:
            defaults:
              type: string
            files:
              type: array
              items:
                type: string
            env:
              type: array
              items:
                type: string
    ConfigFileItem:
      type: object
      properties:
        path:
          type: string
        size:
          type: integer
        mtime:
          type: string
        owner:
          type: string
        last_commit_author:
          type: string
        changes_count:
          type: integer
    DiffReport:
      type: object
      properties:
        summary:
          type: object
          properties:
            added:
              type: integer
            removed:
              type: integer
            changed:
              type: integer
        samples:
          type: array
          items:
            type: object
            properties:
              path:
                type: string
              before:
                type: string
              after:
                type: string
    MetricsPayload:
      type: object
      properties:
        load_time_ms:
          type: object
          properties:
            p50:
              type: number
            p95:
              type: number
            p99:
              type: number
        error_rate:
          type: number
        degrade_rate:
          type: number
        cache_hit_ratio:
          type: number
        callback_latency_ms:
          type: object
          properties:
            p50:
              type: number
            p95:
              type: number
            p99:
              type: number
        rollback_events:
          type: integer
    ReloadRequest:
      type: object
      properties:
        namespaces:
          type: array
          items:
            type: string
        reason:
          type: string
        env:
          type: string
          enum: [dev, staging, prod]
      required: [reason, env]
    ReloadResult:
      type: object
      properties:
        ok:
          type: boolean
        snapshot_version:
          type: string
        message:
          type: string

