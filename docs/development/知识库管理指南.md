# 知识库管理指南

## 概述

本文档介绍如何管理由己平台的RAG知识库，包括数据导入、更新、验证和维护等操作。

## 知识库架构

### 技术栈
- **向量数据库**: ChromaDB
- **嵌入模型**: moka-ai/m3e-base
- **检索方式**: 语义相似度搜索 + 关键词加速
- **相似度阈值**: 0.7
- **返回文档数**: top_k=5

### 数据来源
- **帮助文档**: `docs/由己帮助/` 目录下的所有 .md 文件
- **平台技术文档**: 系统架构、功能介绍等基础信息

### 关键词配置
- **配置文件**: `backend/config/keywords_config.yaml`
- **加载器**: `backend/config/keywords_loader.py`
- **用途**: 关键词加速识别，提高查询响应速度
- **分类**: 注册、功能、使用方法、技术支持、产品信息等

## 操作指南

### 1. 知识库重建（推荐方式）

使用专业的重建脚本进行完整重建：

```bash
cd /path/to/project
python scripts/reset_knowledge_base.py --seed
```

**功能**：
- 自动备份现有数据到 `backend/data/chroma_db_backup_YYYYMMDD_HHMMSS`
- 删除旧的知识库数据
- 重建集合 `hybrid_knowledge_base`（cosine距离，禁用遥测）
- 导入基础种子数据

**回滚方式**：
```bash
# 如果需要回滚，将备份目录恢复
mv backend/data/chroma_db_backup_YYYYMMDD_HHMMSS backend/data/chroma_db
```

### 2. 批量导入帮助文档

导入 `docs/由己帮助/` 目录下的所有文档：

```bash
python scripts/import_kb_from_dir.py \
  --dir "docs/由己帮助" --pattern ".md,.txt" \
  --chunk-size 800 --overlap 100 --min-chunk-length 50
```

**特性**：
- 递归扫描指定目录
- 基于段落聚合分块
- 自动角色识别（company/developer路径自动设置role）
- 元数据包含：`source_file`, `chunk_index`, `total_chunks`, `role`

### 3. 单文件导入

导入单个帮助文档：

```bash
python scripts/import_single_file.py --file "docs/由己帮助/general.md"
```

### 4. 快速种子数据导入（开发用）

#### 完整导入（包含帮助文档）
```bash
python scripts/seed_knowledge_base_full.py
```

#### 基础信息导入（仅平台技术信息）
```bash
python scripts/seed_knowledge_base.py
```

**适用场景**：
- 开发测试环境快速验证
- 仅更新平台技术架构描述

### 5. 验证知识库状态

#### 专业验证脚本（推荐）
```bash
python scripts/verify_kb_queries.py --top-k 5
```

**输出**：
- 控制台显示命中数统计
- 生成报告文件：`docs/kb_verification_report.json` 和 `.md`

**预置测试查询**：
- 你好，请介绍一下由己平台
- 由己平台支持哪些功能
- 如何使用由己平台进行需求采集
- 平台是否支持RAG知识库与语义检索
- 联系方式与客服渠道

#### 直接查询验证
```python
from backend.agents.rag_knowledge_base_agent import RAGKnowledgeBaseAgent
from backend.config.knowledge_base_config import KnowledgeBaseConfigManager

# 创建RAG代理
config_manager = KnowledgeBaseConfigManager()
rag_agent = RAGKnowledgeBaseAgent(config_manager)

# 查询测试
result = await rag_agent.query('如何注册雇主账号')
print(f"查询成功: {result.success}")
print(f"答案: {result.answer}")
```

#### API接口验证
```bash
curl -X POST "http://localhost:8000/chat" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: test_user" \
  -d '{"message": "如何注册雇主账号", "session_id": "test_session"}'
```

### 6. 角色元数据补写

为历史数据补写角色信息：

```bash
python scripts/backfill_role_metadata.py
```

**功能**：根据 `source_file` 路径推断 company/developer 角色，补写 `role` 元数据

### 7. 关键词配置维护

#### 查看当前关键词配置
```python
from backend.config.keywords_loader import get_keywords_loader

loader = get_keywords_loader()

# 查看注册相关关键词
registration_keywords = loader.get_keywords_by_category('registration')
print(f"注册关键词: {registration_keywords}")

# 查看所有知识库关键词数量
all_keywords = loader.get_all_knowledge_base_keywords_flat()
print(f"总关键词数: {len(all_keywords)}")
```

#### 添加新关键词
编辑 `backend/config/keywords_config.yaml` 文件：

```yaml
knowledge_base_keywords:
  registration:
    - "注册"
    - "账号"
    - "雇主"           # 新增
    - "工作者"         # 新增
    - "雇主账号"       # 新增
    - "如何注册雇主"   # 新增
```

#### 验证关键词更新
```bash
# 测试关键词加载
python -c "from backend.config.keywords_loader import get_keywords_loader; print(len(get_keywords_loader().get_all_knowledge_base_keywords_flat()))"

# 测试API识别
curl -X POST "http://localhost:8000/chat" -H "Content-Type: application/json" -H "X-Session-ID: test" -d '{"message": "雇主账号怎么注册", "session_id": "test"}'
```

### 4. 常见维护操作

#### 检查知识库文档数量
```python
# 获取所有文档
all_docs = rag_agent._collection.get()
print(f'知识库中共有 {len(all_docs["documents"])} 个文档')
```

#### 搜索特定内容
```python
# 搜索特定关键词
results = rag_agent._collection.query(
    query_texts=['雇主账号注册'],
    n_results=3
)
```

#### 清空知识库
```python
# 获取所有文档ID并删除
existing = rag_agent._collection.get()
if existing['ids']:
    rag_agent._collection.delete(ids=existing['ids'])
```

## 配置管理

### 知识库配置文件
- **主配置**: `backend/config/data/knowledge_base.yaml`
- **启用状态**: `enabled: true`
- **检索参数**: `similarity_threshold: 0.7`, `top_k: 5`

### 配置问题修复记录（2025-08-21）

#### 问题1: 知识库功能被禁用
**现象**:
- 用户查询"如何注册雇主账号"返回"文档中没有相关信息"
- 知识库配置显示 `enabled=False`

**根因**:
知识库配置管理器在加载配置时，没有正确提取 `data.knowledge_base` 配置中的 `knowledge_base` 子配置

**修复方案**:
修改 `backend/config/knowledge_base_config.py` 中的 `_load_config` 方法：
```python
# 修复前
kb_config = self.modular_config_loader.get_config('data.knowledge_base', None)

# 修复后
data_config = self.modular_config_loader.get_config('data.knowledge_base', None)
if data_config and isinstance(data_config, dict):
    kb_config = data_config.get('knowledge_base')
```

#### 问题2: LLM默认参数配置警告
**现象**:
- 日志出现 `使用硬编码兜底配置: llm.default_params` 警告
- 配置事件显示 `[FALLBACK]`

**根因**:
`backend/config/service.py` 中尝试获取 `llm.default_params`，但实际配置路径在 `llm.models.default_params`

**修复方案**:
修改 `backend/config/service.py` 中的 `get_scenario_params` 方法：
```python
# 先尝试从 llm.models 获取 default_params
llm_models_config = self._modular_config.get_config('llm.models', {})
default_params = llm_models_config.get('default_params', {})

# 如果还没有，尝试直接获取 llm.default_params
if not default_params:
    default_params = self._modular_config.get_config('llm.default_params', {})
```

#### 问题3: 架构描述不一致
**现象**:
- 知识库返回"三层识别架构：关键词加速、语义匹配、意图识别"
- 实际系统使用"两层识别架构：关键词加速、LLM意图识别"

**根因**:
文档和配置中存在过时的架构描述

**修复位置**:
1. `scripts/seed_knowledge_base.py` - 更新种子数据描述
2. `backend/config/business/rules.yaml` - 设置 `use_semantic_matching: false`
3. `backend/config/dynamic/versions.yaml` - 设置 `semantic_matching.enabled: false`

#### 问题4: 知识库数据丢失
**现象**:
- 知识库只包含5个基础平台文档
- 缺少 `docs/由己帮助/` 目录下的用户帮助文档

**根因**:
配置修复过程中使用了简化的种子数据脚本，覆盖了完整的帮助文档

**修复方案**:
1. 创建 `scripts/seed_knowledge_base_full.py` 完整导入脚本
2. 重新导入所有帮助文档（22个文档 + 5个平台文档 = 27个文档）
3. 验证关键查询如"如何注册雇主账号"能正确返回结果

#### 问题5: 关键词配置不完整
**现象**:
- 用户查询"雇主账号怎么注册"可能无法被关键词加速识别
- 缺少角色相关关键词（雇主、工作者、开发者等）

**根因**:
`backend/config/keywords_config.yaml` 中缺少角色相关的关键词

**修复方案**:
在关键词配置的多个位置添加角色相关关键词：
```yaml
# knowledge_base_keywords.registration 部分
- "雇主"
- "工作者"
- "开发者"
- "企业"
- "公司"
- "雇主账号"
- "工作者账号"
- "企业账号"
- "雇主注册"
- "工作者注册"
- "如何注册雇主"
- "如何注册工作者"
```

**验证结果**:
- 关键词总数从50个增加到70个
- "雇主账号怎么注册"查询能正确返回详细注册指南

## 故障排除

### 常见问题

#### 1. 知识库检索无结果
**可能原因**:
- 相似度阈值过高
- 文档未正确导入
- 查询词与文档内容差异较大

**解决方案**:
- 检查相似度阈值设置
- 重新导入知识库数据
- 使用更精确的查询词

#### 2. 配置加载失败
**可能原因**:
- 配置文件路径错误
- 环境变量未设置
- 配置格式错误

**解决方案**:
- 检查配置文件路径和格式
- 验证环境变量设置
- 查看配置加载日志

#### 3. ChromaDB连接失败
**可能原因**:
- 数据库文件权限问题
- 存储路径不存在
- 依赖包版本冲突

**解决方案**:
- 检查数据库目录权限
- 确保存储路径存在
- 更新相关依赖包

### 日志查看
- **应用日志**: `logs/app.log`
- **错误日志**: `logs/error.log`
- **会话日志**: `logs/session.log`

## 最佳实践

1. **定期备份**: 定期备份 ChromaDB 数据目录
2. **版本控制**: 帮助文档更新后及时重新导入知识库
3. **性能监控**: 关注查询响应时间和相似度分布
4. **内容质量**: 保持帮助文档的准确性和完整性
5. **测试验证**: 每次更新后进行功能验证

## 快速参考

### 常用命令
```bash
# 完整重建知识库（推荐）
python scripts/reset_knowledge_base.py --seed

# 导入帮助文档
python scripts/import_kb_from_dir.py --dir "docs/由己帮助" --pattern ".md,.txt"

# 验证知识库
python scripts/verify_kb_queries.py --top-k 5

# 快速测试（开发用）
python scripts/seed_knowledge_base_full.py

# API测试
curl -X POST "http://localhost:8000/chat" -H "Content-Type: application/json" -H "X-Session-ID: test" -d '{"message": "如何注册雇主账号", "session_id": "test"}'
```

### 检查清单
- [ ] 知识库配置 `enabled: true`
- [ ] ChromaDB连接正常
- [ ] 文档数量正确（预期27个文档）
- [ ] 关键查询测试通过
- [ ] 相似度阈值合理（0.7）
- [ ] 无配置警告或错误

## 相关文件

### 专业脚本（推荐使用）
- `scripts/reset_knowledge_base.py` - 知识库重建
- `scripts/import_kb_from_dir.py` - 批量导入
- `scripts/import_single_file.py` - 单文件导入
- `scripts/verify_kb_queries.py` - 验证查询
- `scripts/backfill_role_metadata.py` - 角色补写

### 开发脚本（快速测试）
- `scripts/seed_knowledge_base_full.py` - 完整知识库导入
- `scripts/seed_knowledge_base.py` - 基础信息导入

### 配置文件
- `backend/config/data/knowledge_base.yaml` - 知识库配置
- `backend/config/knowledge_base_config.py` - 配置管理器
- `backend/config/business/rules.yaml` - 业务规则
- `backend/config/dynamic/versions.yaml` - 版本控制

### 核心代码
- `backend/agents/rag_knowledge_base_agent.py` - RAG代理
- `backend/handlers/knowledge_base_handler.py` - 知识库处理器

### 相关文档
- `docs/development/知识库SOP.md` - 标准操作程序
- `docs/development/配置管理指南.md` - 配置管理
- `docs/由己帮助/` - 用户帮助文档源

---

*最后更新: 2025-08-21*
*维护者: 开发团队*
*相关问题修复: 知识库配置路径、LLM参数配置、架构描述一致性、数据完整性*
