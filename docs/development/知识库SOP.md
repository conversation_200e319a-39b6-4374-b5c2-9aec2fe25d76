# 知识库SOP（Standard Operating Procedure）

适用范围：RAG知识库的重建、导入、验证、维护与回滚

## 一、重建（备份→删除→重建→种子）
1) 备份与重建
- 执行：`python scripts/reset_knowledge_base.py --seed`
- 行为：备份 backend/data/chroma_db → 删除 → 重建集合 hybrid_knowledge_base（cosine，禁用遥测）→ 导入5条种子
- 回滚：将备份目录恢复为 backend/data/chroma_db

2) 绝对路径确认
- 代理会将 chroma_db.path 解析为“项目根”绝对路径
- 建议在 data/knowledge_base.yaml 中按环境配置不同路径

## 二、导入（批量/单文件/补写）
1) 批量导入
- 执行：
```
python scripts/import_kb_from_dir.py \
  --dir "docs/由己帮助" --pattern ".md,.txt" \
  --chunk-size 800 --overlap 100 --min-chunk-length 50
```
- 特性：递归扫描；基于段落聚合分块；自动角色（包含 company/developer 的路径自动写入 role）
- 元数据：`source_file` `chunk_index` `total_chunks` `role`

2) 单文件导入
- 执行：
```
python scripts/import_single_file.py --file "docs/由己帮助/general.md"
```
- 元数据同上

3) 角色补写（历史数据）
- 执行：`python scripts/backfill_role_metadata.py`
- 行为：根据 `source_file` 推断 company/developer，补写 `role`

## 三、验证（命中率/相似度）
- 执行：`python scripts/verify_kb_queries.py --top-k 5`
- 输出：
  - 控制台命中数
  - 报告文件：`docs/kb_verification_report.json` / `.md`
- 预置问句：
  - 你好，请介绍一下由己平台
  - 由己平台支持哪些功能
  - 如何使用由己平台进行需求采集
  - 平台是否支持RAG知识库与语义检索
  - 联系方式与客服渠道

## 四、运行时行为与注意事项
- 运行时仅连接集合，不进行创建/删除/清理（避免误删与 schema 风险）
- 遥测日志已静音，不影响功能
- 角色语境：可在查询 context 传入 `role_filter`（company/developer），代理会在提示词中进行语境提示

## 五、维护与巡检
- 周期性执行验证脚本，关注命中率与相似度趋势
- 监控集合大小、角色分布（代理的 get_collection_stats 提供基础统计）
- 依赖版本固化并记录兼容矩阵（chromadb、sentence-transformers、posthog）

## 六、常见问题与处理
1) “集合不存在”
- 原因：路径不一致或未初始化
- 处理：执行 `reset_knowledge_base.py --seed`；确认 data/knowledge_base.yaml 路径；重启服务

2) “no such column: collections.topic”
- 原因：历史库与当前版本不兼容
- 处理：用重建脚本清库；运行期不再修库

3) 降级答案生效但无LLM输出
- 原因：LLM调用失败或上下文过短
- 处理：检查模型配置与日志；查看 fallback 输出是否包含目标信息

## 七、发布前Checklist
- 验证脚本通过（命中率、相似度达标）
- 产品侧实测问答可用
- 日志无异常（schema/遥测噪音已消除）
- 有有效备份与回滚路径

## 八、变更历史
- 2025-08：
  - 融入模块化配置；绝对路径解析；运行时仅连接集合
  - 导入脚本标准化元数据；自动角色与补写工具；标准化验证报告

