# CI门禁使用指南

## 概述

本项目实施了完整的CI门禁系统，确保配置管理架构升级的质量和一致性。系统包括pre-commit钩子和GitHub Actions工作流。

## Pre-commit钩子

### 安装和设置

```bash
# 自动安装和配置
python scripts/setup_pre_commit.py

# 手动安装
pip install pre-commit
pre-commit install
```

### 钩子列表

#### 基础检查
- **trailing-whitespace**: 移除行尾空白
- **end-of-file-fixer**: 确保文件以换行符结尾
- **check-yaml**: YAML语法检查
- **check-json**: JSON语法检查
- **check-merge-conflict**: 检查合并冲突标记
- **check-added-large-files**: 检查大文件（>1MB）

#### Python代码质量
- **black**: 代码格式化（行长度100）
- **isort**: 导入排序
- **flake8**: 代码风格检查
- **bandit**: 安全检查

#### 配置管理专用钩子
- **config-schema-validation**: 配置Schema验证
- **hardcode-detection**: 硬编码检测
- **config-integrity-check**: 配置完整性检查
- **legacy-config-check**: 禁止新增旧配置依赖
- **critical-config-keys-check**: 关键配置键检查

### 使用方法

```bash
# 运行所有钩子
pre-commit run --all-files

# 运行特定钩子
pre-commit run config-schema-validation
pre-commit run legacy-config-check

# 跳过钩子提交（不推荐）
git commit --no-verify

# 更新钩子版本
pre-commit autoupdate
```

## GitHub Actions工作流

### 配置管理验证工作流

文件：`.github/workflows/config-management-validation.yml`

#### 触发条件
- Push到main/develop分支
- Pull Request到main/develop分支
- 涉及配置文件变更时

#### 验证步骤

1. **YAML语法验证**
   - 检查所有YAML文件语法
   - 确保文件可以正确解析

2. **Schema验证**
   - 使用Pydantic Schema验证配置结构
   - 确保配置符合定义的模式

3. **未知键检查**
   - 检查配置文件中的未知键
   - 防止配置错误

4. **关键键验证**
   - 验证关键配置键存在
   - 确保系统启动必需的配置完整

5. **旧依赖检查**
   - 检查是否有新增的旧配置依赖
   - 防止回退到已弃用的配置系统

6. **集成测试**
   - 测试配置系统集成
   - 验证配置加载和验证功能

### 硬编码检查工作流

文件：`.github/workflows/hardcode-check.yml`

包含硬编码检测、配置验证、代码质量检查和集成测试。

## 配置验证CLI工具

### 基本用法

```bash
# 运行所有验证
python tools/config_validation_cli.py

# 指定配置目录
python tools/config_validation_cli.py --config-dir backend/config

# 严格模式（任何失败都返回错误码）
python tools/config_validation_cli.py --strict

# JSON格式输出
python tools/config_validation_cli.py --format json

# 输出到文件
python tools/config_validation_cli.py --output validation-report.txt
```

### 验证项目

1. **YAML语法验证**: 检查所有YAML文件语法正确性
2. **Schema验证**: 使用Pydantic验证配置结构
3. **关键键验证**: 确保关键配置键存在
4. **旧依赖检查**: 检查旧配置系统依赖
5. **默认配置检查**: 验证默认配置目录结构

## PR模板更新

PR模板已更新，包含配置管理相关检查项：

### 配置管理检查清单
- [ ] 配置文件遵循定义的Schema
- [ ] 未新增对旧配置系统的依赖
- [ ] 关键配置键有默认值
- [ ] 配置变更已通过验证测试
- [ ] 已更新配置文档

## 常见问题和解决方案

### 1. Pre-commit钩子失败

**问题**: 提交时钩子检查失败

**解决方案**:
```bash
# 查看具体错误
pre-commit run --all-files

# 修复代码格式问题
black backend/ tools/
isort backend/ tools/

# 修复配置问题后重新提交
git add .
git commit -m "修复配置问题"
```

### 2. Schema验证失败

**问题**: 配置文件不符合Schema定义

**解决方案**:
1. 检查配置文件语法
2. 对照Schema定义修复结构
3. 运行验证工具确认修复

### 3. 旧依赖检查失败

**问题**: 代码中包含旧配置依赖

**解决方案**:
1. 使用新的配置接口替换旧接口
2. 参考迁移指南进行代码更新
3. 如需临时豁免，在代码中添加注释说明

### 4. 关键配置键缺失

**问题**: 缺少系统启动必需的配置键

**解决方案**:
1. 检查配置文件完整性
2. 添加缺失的配置键
3. 确保默认值合理

## 最佳实践

### 开发流程

1. **提交前检查**
   ```bash
   # 运行本地验证
   python tools/config_validation_cli.py
   pre-commit run --all-files
   ```

2. **配置变更**
   - 先更新Schema定义
   - 再修改配置文件
   - 运行验证确保一致性

3. **代码变更**
   - 避免新增旧配置依赖
   - 使用新的配置接口
   - 添加适当的测试

### 配置管理

1. **Schema优先**: 先定义Schema，再编写配置
2. **默认值**: 关键配置必须有合理默认值
3. **文档同步**: 配置变更时同步更新文档
4. **向后兼容**: 保持配置变更的向后兼容性

### 故障排除

1. **查看CI日志**: GitHub Actions提供详细的错误信息
2. **本地复现**: 使用相同的工具在本地复现问题
3. **逐步修复**: 一次修复一个问题，避免批量修改
4. **测试验证**: 修复后运行完整的验证流程

## 工具参考

### 配置验证工具
- `tools/config_validation_cli.py`: 主要验证工具
- `backend/config/validation/test_schemas.py`: Schema测试
- `tools/hardcode_detector.py`: 硬编码检测

### 配置文件
- `.pre-commit-config.yaml`: Pre-commit配置
- `.github/workflows/`: GitHub Actions工作流
- `backend/config/validation/schemas.py`: Schema定义

### 文档
- `docs/development/配置管理指南.md`: 配置管理总体指南
- `docs/development/配置验证框架设计文档.md`: 验证框架设计
- `.githooks/README.md`: Git钩子详细说明

## 联系和支持

如果遇到问题或需要帮助：
1. 查看相关文档和错误信息
2. 运行本地验证工具诊断问题
3. 参考最佳实践和常见问题解决方案
4. 必要时寻求团队支持
