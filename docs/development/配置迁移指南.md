# 配置迁移指南

## 概述

本指南帮助开发者从旧的配置系统迁移到新的配置管理架构。新系统提供了更好的类型安全、验证机制和可维护性。

## 迁移对照表

### 1. 配置文件导入

**❌ 旧的方式:**
```python
from backend.config.unified_config_loader import get_config_value, UnifiedConfigLoader
from backend.config.unified_dynamic_config import UnifiedDynamicConfig
```

**✅ 新的方式:**
```python
from backend.config.manager import ConfigManager
from backend.config.loader import ConfigLoader
```

### 2. 配置值获取

**❌ 旧的方式:**
```python
# 获取单个配置值
value = get_config_value("llm.default_model")
value = get_config_value("database.connection.timeout", default=30)

# 获取嵌套配置
db_config = get_config_value("database")
```

**✅ 新的方式:**
```python
# 获取单个配置值
config = ConfigManager()
value = config.get("llm.default_model")
value = config.get("database.connection.timeout", default=30)

# 获取嵌套配置
db_config = config.get_section("database")
```

### 3. 配置加载器使用

**❌ 旧的方式:**
```python
loader = UnifiedConfigLoader()
config_data = loader.load_config()
```

**✅ 新的方式:**
```python
loader = ConfigLoader()
config_data = loader.load_all_configs()
```

### 4. 动态配置

**❌ 旧的方式:**
```python
dynamic_config = UnifiedDynamicConfig()
dynamic_config.update_config("some.key", "new_value")
```

**✅ 新的方式:**
```python
config = ConfigManager()
config.set_runtime("some.key", "new_value")
```

### 5. 配置文件路径

**❌ 旧的方式:**
```python
# 引用旧的默认配置文件
config_file = "unified_config.defaults.yaml"
```

**✅ 新的方式:**
```python
# 使用新的模块化配置目录
config_dir = "backend/config/defaults/"
business_config = "backend/config/business/rules.yaml"
```

## 常见迁移场景

### 场景1: Agent配置获取

**❌ 旧代码:**
```python
class SomeAgent:
    def __init__(self):
        self.llm_model = get_config_value("llm.default_model", "gpt-3.5-turbo")
        self.timeout = get_config_value("llm.timeout", 30)
        self.temperature = get_config_value("llm.temperature", 0.7)
```

**✅ 新代码:**
```python
class SomeAgent:
    def __init__(self):
        config = ConfigManager()
        self.llm_model = config.get("llm.default_model", "gpt-3.5-turbo")
        self.timeout = config.get("llm.timeout", 30)
        self.temperature = config.get("llm.temperature", 0.7)
```

### 场景2: 数据库配置

**❌ 旧代码:**
```python
def get_database_config():
    return {
        "path": get_config_value("database.path", "data/database.db"),
        "timeout": get_config_value("database.timeout", 30),
        "check_same_thread": get_config_value("database.check_same_thread", False)
    }
```

**✅ 新代码:**
```python
def get_database_config():
    config = ConfigManager()
    return config.get_section("database", {
        "path": "data/database.db",
        "timeout": 30,
        "check_same_thread": False
    })
```

### 场景3: 条件配置加载

**❌ 旧代码:**
```python
def load_environment_config():
    env = get_config_value("app.environment", "development")
    if env == "production":
        return get_config_value("production")
    else:
        return get_config_value("development")
```

**✅ 新代码:**
```python
def load_environment_config():
    config = ConfigManager()
    env = config.get("app.environment", "development")
    return config.get_section(env, {})
```

### 场景4: 配置验证

**❌ 旧代码:**
```python
def validate_config():
    model = get_config_value("llm.default_model")
    if not model:
        raise ValueError("LLM模型未配置")
    
    timeout = get_config_value("llm.timeout", 30)
    if timeout <= 0:
        raise ValueError("超时时间必须大于0")
```

**✅ 新代码:**
```python
def validate_config():
    config = ConfigManager()
    # 新系统自动进行Schema验证
    # 如果配置不符合要求会自动抛出详细的验证错误
    llm_config = config.get_section("llm")
    return llm_config  # 已经过验证的配置
```

## 迁移步骤

### 步骤1: 识别旧配置依赖

使用扫描工具识别需要迁移的文件：

```bash
# 扫描整个项目
python tools/legacy_dependency_scanner.py

# 只查看优先级列表
python tools/legacy_dependency_scanner.py --priority-only

# 检查Git变更
python tools/legacy_dependency_scanner.py --git-check
```

### 步骤2: 按优先级迁移

1. **P0级别**: 高频使用、高危违规的文件
2. **P1级别**: 中等频率使用的文件
3. **P2级别**: 低频使用的文件
4. **P3级别**: 很少使用的文件

### 步骤3: 逐文件迁移

对于每个文件：

1. **备份原文件**
2. **替换导入语句**
3. **更新配置获取方式**
4. **运行测试验证**
5. **提交变更**

### 步骤4: 验证迁移结果

```bash
# 运行配置验证
python tools/config_validation_cli.py

# 运行单元测试
python -m pytest tests/config/

# 检查是否还有旧依赖
python tools/legacy_dependency_scanner.py --directory backend
```

## 豁免机制

### 何时使用豁免

在以下情况下可以临时使用豁免：

1. **遗留系统集成**: 与第三方系统的集成代码
2. **复杂重构**: 需要大量时间的复杂重构
3. **测试代码**: 专门测试旧系统的测试代码
4. **临时修复**: 紧急修复中的临时代码

### 如何申请豁免

#### 1. 在PR中标记

在PR标题或描述中添加：
```
[LEGACY-EXEMPT] 修复紧急问题，临时使用旧配置接口
```

#### 2. 在代码中注释

```python
# LEGACY-EXEMPT: 与第三方系统集成，计划在v2.0中重构
from backend.config.unified_config_loader import get_config_value

def legacy_integration():
    # LEGACY-EXEMPT: 临时修复，已创建issue #123跟踪重构
    api_key = get_config_value("legacy.api_key")
    return api_key
```

#### 3. 提供迁移计划

豁免申请应包含：
- **豁免原因**: 为什么必须使用旧接口
- **影响评估**: 对系统的影响程度
- **迁移计划**: 何时迁移到新接口
- **跟踪方式**: 如何跟踪迁移进度

### 豁免审核

带有豁免标记的PR需要：
1. **配置管理团队评审**
2. **技术负责人批准**
3. **创建跟踪Issue**
4. **定期回顾豁免状态**

## 常见问题

### Q1: 新配置系统的性能如何？

A: 新系统通过以下方式优化性能：
- 配置缓存机制
- 延迟加载
- Schema预编译
- 来源追踪优化

### Q2: 如何处理配置热更新？

A: 新系统支持运行时配置更新：
```python
config = ConfigManager()
config.set_runtime("some.key", "new_value")
config.reload()  # 重新加载配置
```

### Q3: 如何调试配置问题？

A: 使用配置来源追踪：
```python
config = ConfigManager()
value, source = config.get_with_source("some.key")
print(f"配置值: {value}, 来源: {source}")
```

### Q4: 迁移过程中出现错误怎么办？

A: 按以下步骤排查：
1. 检查配置文件语法
2. 运行配置验证工具
3. 查看详细错误信息
4. 参考错误解决方案文档

## 工具和资源

### 迁移工具
- `tools/legacy_dependency_scanner.py`: 旧依赖扫描
- `tools/config_validation_cli.py`: 配置验证
- `backend/config/validation/test_schemas.py`: Schema测试

### 文档资源
- [配置管理指南](./配置管理指南.md)
- [配置错误示例与解决方案](./配置错误示例与解决方案.md)
- [CI门禁使用指南](./CI门禁使用指南.md)

### 示例代码
- `backend/config/defaults/`: 默认配置示例
- `tests/config/`: 配置测试示例
- `docs/examples/`: 迁移示例代码

## 联系支持

如果在迁移过程中遇到问题：
1. 查看相关文档和FAQ
2. 运行诊断工具
3. 创建Issue描述问题
4. 联系配置管理团队

---

**重要提醒**: 迁移是一个渐进的过程，不要急于一次性迁移所有代码。按优先级逐步迁移，确保每个步骤都经过充分测试。
