# 🚨 配置系统迁移通知 - 旧系统已废弃

> **重要通知**: 旧配置系统已正式标记为 **DEPRECATED**，请立即停止使用并迁移到新系统。

## 📅 重要时间节点

| 时间 | 状态 | 说明 |
|------|------|------|
| 2024年1月 | ✅ 新系统上线 | 新配置管理系统正式发布 |
| 2024年3月 | ✅ 兼容层部署 | 旧系统兼容层上线，开始迁移 |
| 2024年6月 | ✅ 大规模迁移 | 核心组件完成迁移 |
| 2024年9月 | ✅ 废弃标记 | 旧系统正式标记为DEPRECATED |
| **2024年12月** | ⚠️ **日落执行** | **旧系统将被完全移除** |
| 2025年1月 | 🗑️ 完全移除 | 旧系统代码和文件被删除 |

## ❌ 已废弃的组件

### 配置文件
- `unified_config.defaults.yaml` - **已废弃**
- `backend/config/old_config.yaml` - **已废弃**
- `config/legacy_defaults.yaml` - **已废弃**

### 代码组件
- `unified_config_loader` - **已废弃**
- `get_config_value()` - **已废弃**
- `load_config_from_yaml()` - **已废弃**
- `unified_dynamic_config` - **已废弃**
- `backend.utils.legacy_config` - **已废弃**

### 环境变量
- `UNIFIED_CONFIG_*` 前缀 - **已废弃**
- `OLD_CONFIG_*` 前缀 - **已废弃**

## ✅ 新系统API

### 基本用法
```python
from backend.config.manager import get_config_manager

# 获取配置管理器
config_manager = get_config_manager()

# 读取配置
debug_mode = config_manager.get("app.debug", False)
db_host = config_manager.get("database.host", "localhost")

# 获取配置来源
value, source = config_manager.get_with_source("app.debug")

# 设置运行时配置
config_manager.set_runtime("app.temp_setting", "temp_value")

# 获取配置段
db_config = config_manager.get_section("database")
```

### 环境变量覆盖
```bash
# 新的环境变量格式
export AID_CONF__APP__DEBUG=true
export AID_CONF__DATABASE__HOST=localhost
export AID_CONF__LLM__API_KEY=your_api_key
```

## 🔄 迁移指南

### 1. 代码迁移

#### 旧代码 ❌
```python
# 废弃的用法
from unified_config import get_config_value
from backend.utils.legacy_config import load_config

# 不要再使用这些
config_value = get_config_value("app.debug")
config_dict = load_config("config.yaml")
```

#### 新代码 ✅
```python
# 推荐的用法
from backend.config.manager import get_config_manager

config_manager = get_config_manager()
config_value = config_manager.get("app.debug")
config_dict = config_manager.get_section("app")
```

### 2. 配置文件迁移

#### 旧配置文件 ❌
```yaml
# unified_config.defaults.yaml (已废弃)
app:
  debug: false
  name: "Old App"
```

#### 新配置文件 ✅
```yaml
# backend/config/defaults/app.yaml
app:
  debug: false
  name: "New App"
  version: "1.0.0"
```

### 3. 环境变量迁移

#### 旧环境变量 ❌
```bash
# 废弃的格式
export UNIFIED_CONFIG_APP_DEBUG=true
export OLD_CONFIG_DB_HOST=localhost
```

#### 新环境变量 ✅
```bash
# 新的格式
export AID_CONF__APP__DEBUG=true
export AID_CONF__DATABASE__HOST=localhost
```

## 🛠️ 迁移工具

### 自动化迁移脚本
```bash
# 扫描旧配置引用
python tools/legacy_dependency_scanner.py scan

# 生成迁移报告
python tools/legacy_migration_helper.py analyze

# 执行自动迁移（部分场景）
python tools/legacy_migration_helper.py migrate --dry-run
```

### 影响分析工具
```bash
# 分析配置变更影响
python tools/config_impact_analyzer.py analyze app.debug

# 生成测试清单
python tools/config_impact_analyzer.py test-list app.debug
```

## 🚨 紧急迁移指南

如果您的代码仍在使用旧系统，请立即执行以下步骤：

### 第1步：评估影响
```bash
# 扫描您的代码中的旧配置引用
grep -r "unified_config" your_code_directory/
grep -r "get_config_value" your_code_directory/
grep -r "load_config_from_yaml" your_code_directory/
```

### 第2步：制定迁移计划
1. 列出所有需要迁移的文件
2. 识别使用的配置键
3. 确认新系统中对应的API
4. 制定测试计划

### 第3步：执行迁移
1. 更新导入语句
2. 替换API调用
3. 更新配置键格式
4. 运行测试验证

### 第4步：验证迁移
```bash
# 运行测试
python -m pytest tests/ -v

# 检查配置加载
python tools/config_validator.py check

# 验证功能正常
python tools/system_health_checker.py
```

## 📞 获取帮助

### 技术支持
- **配置管理团队**: <EMAIL>
- **架构团队**: <EMAIL>
- **技术支持**: <EMAIL>

### 文档资源
- [配置管理指南](配置管理指南.md)
- [配置管理最佳实践](配置管理最佳实践.md)
- [API文档](../api/配置管理API文档.md)
- [故障排除指南](配置管理故障排除指南.md)

### 在线支持
- **Slack频道**: #config-migration-help
- **技术论坛**: 内部技术论坛配置管理版块
- **工单系统**: 提交技术支持工单

## ⚠️ 风险提醒

### 高风险操作
- **继续使用旧API**: 可能在日落后导致系统故障
- **混合使用新旧系统**: 可能导致配置不一致
- **忽略迁移警告**: 可能错过迁移窗口期

### 缓解措施
- **立即开始迁移**: 不要等到最后期限
- **充分测试**: 确保迁移后功能正常
- **监控告警**: 关注系统健康状态
- **准备回滚**: 制定应急预案

## 📊 迁移进度跟踪

### 当前状态
- ✅ 新系统稳定运行
- ✅ 兼容层正常工作
- ✅ 监控告警完善
- 🔄 旧系统使用度下降中
- ⏳ 计划12月完成日落

### 迁移统计
- **已迁移组件**: 85%
- **剩余旧引用**: 15%
- **日均旧系统访问**: < 50次
- **迁移完成度**: 预计11月底达到95%

## 🎯 行动计划

### 立即行动（本周内）
1. **扫描代码**: 识别所有旧配置引用
2. **评估工作量**: 估算迁移所需时间
3. **制定计划**: 安排迁移时间表
4. **开始迁移**: 从低风险组件开始

### 短期目标（本月内）
1. **完成核心迁移**: 迁移关键业务组件
2. **测试验证**: 确保功能正常
3. **文档更新**: 更新相关文档
4. **团队培训**: 确保团队熟悉新系统

### 中期目标（下月内）
1. **完成全部迁移**: 100%迁移完成
2. **清理旧代码**: 移除废弃引用
3. **优化配置**: 利用新系统特性
4. **监控调优**: 完善监控配置

---

## 📝 总结

旧配置系统的废弃是技术架构升级的重要一步。新系统提供了更强大的功能、更好的安全性和更完善的监控能力。

**请立即开始迁移工作，确保在日落期限前完成所有迁移任务。**

如有任何问题，请及时联系配置管理团队获取支持。

---

**文档版本**: v2.0  
**最后更新**: 2024-12-19  
**负责人**: 配置管理团队  
**紧急联系**: <EMAIL>
