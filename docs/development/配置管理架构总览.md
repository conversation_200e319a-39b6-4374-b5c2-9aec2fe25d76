# 配置管理架构总览（2025-08）

## 目标与原则
- 统一访问、分模块治理：以统一入口提供稳定API，以模块化配置提升可维护性
- 兼容优先：模块化为主、旧路径为备，平滑迁移
- 小步快跑、可回滚：每一步操作具备验证与回滚路径

## 架构总览
```mermaid
flowchart TD
  U[应用代码] -->|统一入口API| L(UnifiedConfigLoader)
  L -->|模块化优先| M(ModularLoader)
  L -->|旧路径回退| O(旧配置路径)
  M -->|映射| F1[system/*]
  M --> F2[business/*]
  M --> F3[llm/*]
  M --> F4[data/*]
  F4 --> KB[data/knowledge_base.yaml]
  U -->|RAG配置读取| KB
```

## 统一入口与读取优先级
- 统一入口：backend/config/unified_config_loader.py
- 优先级：
  1) 模块化路径（如 data.knowledge_base.knowledge_base）
  2) 旧路径回退（如 knowledge_base）
- 示例（知识库）：
  - 首选 data.knowledge_base.knowledge_base
  - 回退 knowledge_base

## 模块清单（主要）
- system/
  - base.yaml / performance.yaml / security.yaml
- business/
  - rules.yaml / templates.yaml / thresholds.yaml
- llm/
  - models.yaml / scenarios.yaml / prompts.yaml
- data/
  - database.yaml / storage.yaml / knowledge_base.yaml
- dynamic/
  - keywords.yaml / versions.yaml

## 知识库配置集成
- 文件：backend/config/data/knowledge_base.yaml
- 关键项：
  - knowledge_base.enabled / features.*
  - chroma_db.path / collection_name / embedding_model
  - document_processing.chunk_size / chunk_overlap / supported_formats
  - retrieval.top_k / similarity_threshold / max_context_length
  - performance.cache_enabled / cache_ttl / max_concurrent_queries
  - safety.timeout_seconds / max_retry_attempts
- 运行时读取：backend/config/knowledge_base_config.py（模块化优先、旧路径回退）

## 运行时行为与安全约束
- RAG代理运行期仅“连接”集合，不再创建/删除/清理（避免误删与 schema 风险）
- ChromaDB 路径解析为“项目根”绝对路径，规避 cwd 漂移
- 遥测日志在日志配置中静音（chromadb.telemetry.product.posthog -> CRITICAL）

## 环境与路径
- 建议按环境分离路径（dev/test/prod）：
  - data/knowledge_base.yaml 中 chroma_db.path 可设为 backend/data/chroma_db_{env}
- 依赖版本建议固化：chromadb / sentence-transformers / posthog

## 工具脚本矩阵
- 重建：scripts/reset_knowledge_base.py（备份→删除→重建→可选种子）
- 批量导入：scripts/import_kb_from_dir.py（自动分块、自动角色）
- 单文件导入：scripts/import_single_file.py（标准化元数据）
- 角色补写：scripts/backfill_role_metadata.py（按 source_file 推断）
- 验证：scripts/verify_kb_queries.py（命中数、相似度、报告）

## 验证与发布 Checklist
1) verify_kb_queries.py 通过（命中率/相似度达标）
2) 应用端功能自测通过（RAG查询有结果，降级路径可用）
3) 日志无异常（无 schema 报错、遥测噪音被静音）
4) 备份与回滚路径确认可用

## 风险与回滚
- 风险：历史数据库 schema/版本不一致、路径漂移、导入元数据缺失
- 回滚：
  - 重建前备份 backend/data/chroma_db 到带时间戳目录
  - 若失败，用备份替换现有目录即可

## 变更记录（摘要）
- 引入模块化配置并纳入知识库配置
- 统一入口优先模块化、回退旧路径
- RAG运行时去除修库/清理逻辑
- 绝对路径解析、遥测静音
- 导入元数据标准化、角色自动标注与补写、标准化验证

