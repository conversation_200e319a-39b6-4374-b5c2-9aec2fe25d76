# 环境变量配置规范

## 概述

本文档定义了项目中环境变量配置的命名规范、类型解析、安全管控和最佳实践。环境变量配置是配置优先级链中的重要一环，用于在不同环境中覆盖默认配置。

## 命名规范

### 基本格式

所有配置相关的环境变量必须使用 `AID_CONF__` 前缀：

```bash
AID_CONF__<CONFIG_PATH>
```

### 层级映射规则

配置键的层级结构通过双下划线 `__` 分隔：

| 配置键路径 | 环境变量名 | 示例值 |
|------------|------------|--------|
| `llm.default_model` | `AID_CONF__LLM__DEFAULT_MODEL` | `gpt-4` |
| `database.connection.timeout` | `AID_CONF__DATABASE__CONNECTION__TIMEOUT` | `30` |
| `system.security.enabled` | `AID_CONF__SYSTEM__SECURITY__ENABLED` | `true` |
| `app.debug` | `AID_CONF__APP__DEBUG` | `false` |

### 命名约定

1. **全大写**：环境变量名使用全大写字母
2. **双下划线分隔**：配置层级使用 `__` 分隔
3. **无特殊字符**：只使用字母、数字和下划线
4. **语义清晰**：变量名应该清晰表达配置含义

## 类型解析

### 支持的数据类型

环境变量解析器支持以下数据类型的自动转换：

#### 1. 字符串 (String)
```bash
# 默认类型，无需特殊处理
AID_CONF__APP__NAME="AI Assistant"
AID_CONF__LLM__DEFAULT_MODEL="gpt-3.5-turbo"
```

#### 2. 布尔值 (Boolean)
```bash
# 支持的真值: true, True, TRUE, yes, Yes, YES, 1, on, On, ON
AID_CONF__APP__DEBUG=true
AID_CONF__SYSTEM__SECURITY__ENABLED=yes
AID_CONF__CACHE__ENABLED=1

# 支持的假值: false, False, FALSE, no, No, NO, 0, off, Off, OFF
AID_CONF__APP__PRODUCTION=false
AID_CONF__LOGGING__VERBOSE=no
AID_CONF__FEATURES__EXPERIMENTAL=0
```

#### 3. 整数 (Integer)
```bash
AID_CONF__DATABASE__CONNECTION__TIMEOUT=30
AID_CONF__LLM__MAX_TOKENS=2000
AID_CONF__SYSTEM__MAX_WORKERS=4
```

#### 4. 浮点数 (Float)
```bash
AID_CONF__LLM__TEMPERATURE=0.7
AID_CONF__SYSTEM__CPU_THRESHOLD=85.5
AID_CONF__CACHE__HIT_RATIO=0.95
```

#### 5. JSON对象 (JSON)
```bash
# 复杂对象使用JSON格式
AID_CONF__LLM__MODELS='{"gpt-4": {"provider": "openai", "max_tokens": 4000}}'
AID_CONF__DATABASE__POOLS='{"read": 5, "write": 2}'
AID_CONF__FEATURES__FLAGS='["feature_a", "feature_b"]'
```

### 类型推断规则

解析器按以下顺序尝试类型转换：

1. **JSON解析**：如果值以 `{` 或 `[` 开头，尝试JSON解析
2. **布尔值**：匹配布尔值关键词
3. **数字**：尝试整数，然后浮点数
4. **字符串**：默认类型

## 安全白名单机制

### 白名单原则

只有在配置Schema中明确声明的键才允许通过环境变量覆盖：

```python
# ✅ 允许：Schema中已声明的键
AID_CONF__LLM__DEFAULT_MODEL=gpt-4

# ❌ 拒绝：Schema中未声明的键
AID_CONF__UNKNOWN__KEY=value  # 将被拒绝并记录警告
```

### 敏感配置管控

敏感配置项必须通过环境变量或密钥管理器设置，不允许在配置文件中明文存储：

#### 敏感配置清单
- **API密钥**: `*.api_key`, `*.secret_key`
- **数据库密码**: `*.password`, `*.passwd`
- **加密密钥**: `*.encryption_key`, `*.private_key`
- **认证令牌**: `*.token`, `*.jwt_secret`
- **第三方凭证**: `*.credentials`, `*.auth_*`

#### 敏感配置示例
```bash
# ✅ 正确：通过环境变量设置敏感信息
AID_CONF__LLM__OPENAI__API_KEY=sk-xxx...
AID_CONF__DATABASE__PASSWORD=secure_password
AID_CONF__SECURITY__JWT_SECRET=jwt_secret_key

# ❌ 错误：敏感信息不应出现在配置文件中
# llm:
#   openai:
#     api_key: sk-xxx...  # 这将被拒绝
```

## 配置示例

### 开发环境配置
```bash
# 应用配置
AID_CONF__APP__DEBUG=true
AID_CONF__APP__LOG_LEVEL=DEBUG
AID_CONF__APP__ENVIRONMENT=development

# LLM配置
AID_CONF__LLM__DEFAULT_MODEL=gpt-3.5-turbo
AID_CONF__LLM__TEMPERATURE=0.7
AID_CONF__LLM__MAX_TOKENS=2000
AID_CONF__LLM__OPENAI__API_KEY=sk-development-key

# 数据库配置
AID_CONF__DATABASE__CONNECTION__PATH=data/dev_database.db
AID_CONF__DATABASE__CONNECTION__TIMEOUT=30

# 性能配置
AID_CONF__PERFORMANCE__CACHE__ENABLED=true
AID_CONF__PERFORMANCE__MAX_WORKERS=2
```

### 生产环境配置
```bash
# 应用配置
AID_CONF__APP__DEBUG=false
AID_CONF__APP__LOG_LEVEL=INFO
AID_CONF__APP__ENVIRONMENT=production

# LLM配置
AID_CONF__LLM__DEFAULT_MODEL=gpt-4
AID_CONF__LLM__TEMPERATURE=0.3
AID_CONF__LLM__MAX_TOKENS=4000
AID_CONF__LLM__OPENAI__API_KEY=sk-production-key

# 数据库配置
AID_CONF__DATABASE__CONNECTION__PATH=/var/lib/aid/database.db
AID_CONF__DATABASE__CONNECTION__TIMEOUT=60

# 性能配置
AID_CONF__PERFORMANCE__CACHE__ENABLED=true
AID_CONF__PERFORMANCE__MAX_WORKERS=8
```

### Docker环境配置
```dockerfile
# Dockerfile
ENV AID_CONF__APP__ENVIRONMENT=production
ENV AID_CONF__APP__DEBUG=false
ENV AID_CONF__DATABASE__CONNECTION__PATH=/app/data/database.db
ENV AID_CONF__PERFORMANCE__MAX_WORKERS=4

# docker-compose.yml
version: '3.8'
services:
  aid-app:
    environment:
      - AID_CONF__APP__DEBUG=false
      - AID_CONF__LLM__DEFAULT_MODEL=gpt-4
      - AID_CONF__DATABASE__CONNECTION__TIMEOUT=60
      - AID_CONF__LLM__OPENAI__API_KEY=${OPENAI_API_KEY}
```

## 最佳实践

### 1. 环境分离
```bash
# 使用不同的环境变量文件
.env.development
.env.staging  
.env.production
```

### 2. 敏感信息管理
```bash
# ✅ 使用外部密钥管理
AID_CONF__LLM__OPENAI__API_KEY=$(vault kv get -field=api_key secret/openai)

# ✅ 使用环境变量文件（不提交到版本控制）
echo "AID_CONF__DATABASE__PASSWORD=secret" >> .env.local

# ❌ 避免在脚本中硬编码
export AID_CONF__API_KEY=hardcoded_key  # 不推荐
```

### 3. 配置验证
```bash
# 启动前验证必需的环境变量
required_vars=(
    "AID_CONF__LLM__OPENAI__API_KEY"
    "AID_CONF__DATABASE__CONNECTION__PATH"
)

for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        echo "错误: 缺少必需的环境变量 $var"
        exit 1
    fi
done
```

### 4. 配置文档化
```bash
# 在README或部署文档中说明必需的环境变量
# 必需的环境变量:
# - AID_CONF__LLM__OPENAI__API_KEY: OpenAI API密钥
# - AID_CONF__DATABASE__CONNECTION__PATH: 数据库文件路径
# - AID_CONF__APP__ENVIRONMENT: 运行环境 (development/staging/production)
```

## 错误处理

### 类型转换错误
```bash
# 错误的布尔值
AID_CONF__APP__DEBUG=maybe  # 将记录警告并使用默认值

# 错误的数字格式
AID_CONF__DATABASE__TIMEOUT=thirty  # 将记录警告并使用默认值

# 错误的JSON格式
AID_CONF__LLM__MODELS='{"invalid": json}'  # 将记录警告并使用默认值
```

### 未知键处理
```bash
# 未在Schema中声明的键
AID_CONF__UNKNOWN__SETTING=value
# 日志: WARN - 环境变量 'AID_CONF__UNKNOWN__SETTING' 未在配置Schema中声明，已忽略
```

### 敏感信息泄露防护
```bash
# 敏感信息不会出现在日志中
AID_CONF__LLM__API_KEY=secret_key
# 日志: INFO - 环境变量覆盖: llm.api_key = [REDACTED] (来源: environment)
```

## 调试和故障排除

### 查看环境变量覆盖
```bash
# 查看所有AID配置环境变量
env | grep AID_CONF__

# 查看特定配置的来源
python -c "
from backend.config.manager import ConfigManager
config = ConfigManager()
value, source = config.get_with_source('llm.default_model')
print(f'值: {value}, 来源: {source}')
"
```

### 验证配置解析
```bash
# 使用配置验证工具
python tools/config_validation_cli.py --check-env-vars

# 查看配置合并结果
python -c "
from backend.config.manager import ConfigManager
config = ConfigManager()
print(config.dump_config())
"
```

## 工具支持

### 环境变量生成器
```bash
# 从配置文件生成环境变量模板
python tools/generate_env_template.py --config backend/config/llm/models.yaml
```

### 配置差异对比
```bash
# 对比不同环境的配置差异
python tools/config_diff.py --env1 development --env2 production
```

### 敏感信息扫描
```bash
# 扫描配置文件中的敏感信息
python tools/sensitive_config_scanner.py --directory backend/config/
```

---

**注意事项**:
1. 环境变量名区分大小写
2. 敏感信息必须通过环境变量设置
3. 未知键会被拒绝并记录警告
4. 类型转换失败会使用默认值并记录警告
5. 所有环境变量覆盖都会记录来源信息
