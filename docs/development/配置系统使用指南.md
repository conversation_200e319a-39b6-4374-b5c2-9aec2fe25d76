# 配置系统使用指南

## 📋 概述

本指南介绍优化后的配置系统架构和使用方法。配置系统采用模块化设计，支持多层配置源和兜底配置机制。

## 🏗️ 系统架构

### 配置层次结构
```
config_service (统一访问接口)
    ↓
modular_loader (模块化配置加载器)
    ↓
┌─────────────────┬─────────────────┐
│   专用配置源     │    兜底配置      │
│  (优先级1-14)   │  (优先级-20)    │
├─────────────────┼─────────────────┤
│ business.rules  │ unified_config. │
│ llm.models      │ defaults.yaml   │
│ data.knowledge  │ (17KB精简版)    │
│ system.core     │                 │
│ ... (16个源)    │                 │
└─────────────────┴─────────────────┘
```

### 核心组件
- **config_service**: 统一配置访问接口
- **modular_loader**: 模块化配置加载器
- **专用配置源**: 16个功能专用的配置文件
- **兜底配置**: 精简的默认配置文件

## 🚀 快速开始

### 基本使用
```python
# 推荐方式：使用 config_service
from backend.config import config_service

# 获取LLM配置
llm_config = config_service.get_llm_config('default')

# 获取业务规则
max_retries = config_service.get_business_rule('retry.max_attempts', 3)

# 获取数据库查询
query = config_service.get_database_query('users.find_by_id')

# 获取消息模板
template = config_service.get_message_template('greeting.basic', '你好')
```

### 高级使用
```python
# 直接使用模块化加载器
from backend.config.modular_loader import get_modular_config_loader

loader = get_modular_config_loader()

# 获取任意配置
value = loader.get_config('system.debug_mode', False)

# 创建配置快照
snapshot = loader.create_snapshot()
print(f"配置源数量: {len(snapshot.source_info)}")
```

## 📚 API 参考

### config_service 主要方法

#### get_llm_config(scenario: str) -> Dict
获取LLM配置
```python
# 获取默认LLM配置
config = config_service.get_llm_config('default')
# 返回: {'model_name': 'deepseek-chat', 'temperature': 0.7, ...}

# 获取特定场景配置
config = config_service.get_llm_config('greeting_generator')
```

#### get_business_rule(key: str, default=None) -> Any
获取业务规则
```python
# 获取重试次数
retries = config_service.get_business_rule('retry.max_attempts', 3)

# 获取优先级设置
priority = config_service.get_business_rule('focus_point_priority.p0', True)
```

#### get_database_query(name: str) -> str
获取数据库查询语句
```python
# 获取用户查询
query = config_service.get_database_query('users.find_by_id')
# 返回: "SELECT * FROM users WHERE id = ? AND user_id = ?"
```

#### get_message_template(key: str, default: str) -> str
获取消息模板
```python
# 获取问候模板
greeting = config_service.get_message_template('greeting.basic', '你好')
```

#### get_threshold(key: str, default=None) -> Any
获取阈值配置
```python
# 获取置信度阈值
confidence = config_service.get_threshold('confidence.default', 0.7)
```

### modular_loader 主要方法

#### get_config(key: str, default=None) -> Any
获取任意配置项
```python
loader = get_modular_config_loader()

# 使用点号分隔的键名
value = loader.get_config('system.version', '1.0')
value = loader.get_config('llm.default_model', 'gpt-3.5-turbo')
```

#### create_snapshot() -> ConfigSnapshot
创建配置快照，用于调试和监控
```python
snapshot = loader.create_snapshot()

# 查看配置源信息
for name, info in snapshot.source_info.items():
    print(f"{name}: 优先级 {info['priority']}, 加载状态 {info['loaded']}")
```

## 🔧 配置文件结构

### 专用配置文件
- `business/rules.yaml` - 业务规则配置
- `llm/models.yaml` - LLM模型配置
- `data/knowledge.yaml` - 知识库配置
- `system/core.yaml` - 系统核心配置
- ... (共16个配置文件)

### 兜底配置文件
- `unified_config.defaults.yaml` - 精简的默认配置（17KB）

## ⚠️ 注意事项

### 废弃的用法
```python
# ❌ 不推荐：已废弃的接口
from backend.config.unified_config_loader import get_unified_config
config = get_unified_config()  # 可能存在兼容性问题
```

### 推荐的用法
```python
# ✅ 推荐：使用统一配置服务
from backend.config import config_service
value = config_service.get_business_rule('key', default)
```

### 配置优先级
1. **专用配置源** (优先级1-14) - 最高优先级
2. **兜底配置** (优先级-20) - 最低优先级
3. **硬编码默认值** - 最后防线

### 性能建议
- 优先使用 `config_service` 的专用方法
- 避免频繁调用 `create_snapshot()`
- 配置值建议在初始化时缓存

## 🐛 故障排除

### 常见问题

#### 配置获取返回 None
```python
# 检查配置键名是否正确
value = config_service.get_business_rule('correct.key.name', 'default')

# 检查配置文件是否存在
loader = get_modular_config_loader()
snapshot = loader.create_snapshot()
print(snapshot.source_info)
```

#### 循环导入错误
```python
# 避免在模块级别导入，使用函数内导入
def get_config_value():
    from backend.config import config_service
    return config_service.get_business_rule('key', default)
```

### 调试工具
```python
# 查看所有配置源状态
from backend.config.modular_loader import get_modular_config_loader

loader = get_modular_config_loader()
snapshot = loader.create_snapshot()

for name, info in snapshot.source_info.items():
    status = "✅" if info.get('loaded') else "❌"
    print(f"{status} {name}: 优先级 {info.get('priority')}")
```

## 📈 最佳实践

1. **统一使用 config_service**: 避免直接使用底层加载器
2. **合理设置默认值**: 所有配置获取都应提供合理默认值
3. **配置键名规范**: 使用点号分隔的层次化键名
4. **避免硬编码**: 将可变参数放入配置文件
5. **文档同步更新**: 配置变更时及时更新文档

---

**文档版本**: v2.0  
**更新时间**: 2025-08-22  
**适用版本**: 配置系统 v3.0+
