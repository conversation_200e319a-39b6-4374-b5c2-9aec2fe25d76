# 配置索引（已废弃）

> 🚨 **重要通知**: 本文档已废弃，请使用新的配置管理系统索引。

## ⚠️ 配置系统已升级

本项目已完成配置管理架构的全面升级，旧的模块化配置系统已被新的配置管理架构替代。

### 📋 新文档位置
请查阅新的配置管理系统文档：
- **[配置管理系统索引](配置管理系统索引.md)** ← 新的主索引文档
- [配置管理指南](../配置管理指南.md)
- [配置管理最佳实践](../配置管理最佳实践.md)
- [DEPRECATED配置系统迁移通知](../DEPRECATED_配置系统迁移通知.md)

### 🔄 快速迁移指南

#### 旧API ❌
```python
# 不要再使用这些
from unified_config import get_config_value
from backend.utils.legacy_config import load_config

config_value = get_config_value("app.debug")
```

#### 新API ✅
```python
# 使用新的配置管理器
from backend.config.manager import get_config_manager

config_manager = get_config_manager()
config_value = config_manager.get("app.debug")
```

---

## 📜 历史内容（仅供参考）

> ⚠️ 以下内容已过时，仅作为历史参考保留。请使用新的配置管理系统。

---

## data/knowledge_base.yaml（知识库配置）
- 关键路径：data.knowledge_base.knowledge_base
- 常用键位：
  - chroma_db.path: ChromaDB 持久化目录（建议按环境分离：backend/data/chroma_db_{env}）
  - chroma_db.collection_name: 集合名称（默认 hybrid_knowledge_base）
  - chroma_db.embedding_model: 嵌入模型（默认 moka-ai/m3e-base）
  - document_processing.chunk_size / chunk_overlap / min_chunk_length
  - retrieval.top_k / similarity_threshold / max_context_length
  - performance.cache_enabled / cache_ttl / max_concurrent_queries
- 运行时行为：
  - 代理仅连接集合，不负责创建/删除/修复；这些由脚本完成
  - ChromaDB 路径解析为“项目根”绝对路径，避免 cwd 漂移
- 维护建议：
  - 固化依赖版本（chromadb/sentence-transformers/posthog）
  - 按环境分离路径，避免数据相互污染
  - 发生 schema 报错时不要在运行期修库，使用脚本重建

## data/database.yaml（应用数据库配置）
- 关键路径：data.database
- 常用键位：
  - database.connection.path: backend/data/aidatabase.db（统一TEXT类型的conversation_id）
  - queries.*: 各业务查询语句（务必包含 user_id 过滤，防止数据泄漏）
  - migrations.*: 迁移配置（建议手工执行敏感迁移）
- 维护建议：
  - 避免在生产自动迁移；使用备份 + 离线迁移
  - 大量查询更新前，先在测试环境验证

## data/storage.yaml（存储配置）
- 用途：对象存储、本地文件路径、备份与保留策略
- 建议：按环境分别配置备份路径与保留周期

## llm/*（模型与场景）
- models.yaml：模型供应商、API密钥、超时、重试、费用标签
- scenarios.yaml：不同业务场景的模型参数（温度、max_tokens、top_p等）
- prompts.yaml：模板组织与管理（建议按领域/场景分组）
- 维护建议：
  - 验证日志需要同时记录“场景名”和“实际模型名”（已实现）
  - 在场景层固化关键参数，减少临时覆盖

## business/*（业务规则与模板）
- rules.yaml：业务规则集（建议保留注释、示例与修改人）
- templates.yaml：消息/文档模板（支持版本号与默认模板）
- thresholds.yaml：各类阈值（如重试次数、超时）

## system/*（系统基础配置）
- base.yaml：基础开关、端口、路径
- performance.yaml：缓存、并发、限流
- security.yaml：日志脱敏、审计、IP白名单

---

## 运维SOP与脚本
- 重建：scripts/reset_knowledge_base.py（备份→删除→重建→种子）
- 导入：scripts/import_kb_from_dir.py（自动分块+自动角色）/ scripts/import_single_file.py
- 补写：scripts/backfill_role_metadata.py（按 source_file 推断 role）
- 验证：scripts/verify_kb_queries.py（命中数、相似度、报告）
- 建议：将上述脚本纳入日常巡检（如每周/每次部署后）

## 常见坑位与注释
- ChromaDB“no such column: collections.topic”：历史库与版本不兼容，严格用重建脚本解决
- 运行期不要“自动修库/清理”，避免误删；代理仅连接集合
- 日志遥测噪音：保持 anonymized_telemetry=false + 日志层静音
- 数据库查询：务必包含 user_id 过滤，避免多用户数据串扰

## 变更建议流程
1) 在测试环境验证配置改动（附验证脚本/结果）
2) 提交PR并更新本索引或相关配置注释
3) 生产变更前完成备份与回滚方案确认
4) 变更后执行 verify 脚本并归档报告

