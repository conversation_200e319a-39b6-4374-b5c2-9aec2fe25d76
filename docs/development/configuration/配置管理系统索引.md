# 配置管理系统索引

> ⚠️ **重要**: 本项目已完成配置管理架构升级，旧配置系统已废弃。本索引基于新配置管理架构。

本索引汇总项目的配置管理体系、核心组件、维护指南与最佳实践，便于快速定位与日常维护。

## 🏗️ 新配置管理架构

### 核心入口
- **主入口**: `backend.config.manager.ConfigManager`
- **便捷函数**: `backend.config.manager.get_config_manager()`
- **优先级链**: `defaults → files → env → runtime`

### 目录结构
```
backend/config/
├── manager.py                 # 配置管理器主入口
├── modular_loader.py         # 模块化配置加载器
├── source_tracker.py         # 配置来源追踪
├── merger.py                 # 配置合并器
├── defaults/                 # 默认配置目录 ✨
│   ├── app.yaml
│   ├── database.yaml
│   ├── llm.yaml
│   ├── logging.yaml
│   ├── business.yaml
│   └── system.yaml
├── validation/               # 配置验证 ✨
│   ├── validator.py
│   ├── schemas/
│   └── error_formatter.py
├── security/                 # 安全管理 ✨
│   ├── sensitive_key_manager.py
│   ├── access_auditor.py
│   └── key_manager.py
├── monitoring/               # 监控告警 ✨
│   ├── metrics_collector.py
│   ├── health_checker.py
│   └── dashboard_data.py
└── legacy/                   # 兼容层（已废弃）
    └── wrapper.py
```

## 📋 核心配置模块

### 🔧 应用配置 (app.*)
**默认文件**: `backend/config/defaults/app.yaml`
**关键配置键**:
- `app.debug`: 调试模式开关
- `app.name`: 应用名称
- `app.version`: 应用版本
- `app.environment`: 运行环境 (dev/staging/prod)

**环境变量覆盖**:
```bash
export AID_CONF__APP__DEBUG=true
export AID_CONF__APP__ENVIRONMENT=production
```

### 🗄️ 数据库配置 (database.*)
**默认文件**: `backend/config/defaults/database.yaml`
**关键配置键**:
- `database.connection.path`: 数据库文件路径
- `database.connection.timeout`: 连接超时时间
- `database.pool.max_connections`: 最大连接数
- `database.migrations.auto_migrate`: 自动迁移开关

**安全注意事项**:
- 所有查询必须包含 `user_id` 过滤
- 生产环境禁用自动迁移
- 敏感操作需要手动备份

### 🤖 LLM配置 (llm.*)
**默认文件**: `backend/config/defaults/llm.yaml`
**关键配置键**:
- `llm.default_model`: 默认模型
- `llm.api_keys.*`: API密钥配置
- `llm.models.*`: 模型参数配置
- `llm.scenarios.*`: 场景配置

**敏感信息管理**:
```bash
# API密钥通过环境变量设置
export AID_CONF__LLM__API_KEYS__OPENAI=your_openai_key
export AID_CONF__LLM__API_KEYS__ANTHROPIC=your_anthropic_key
```

### 📊 业务配置 (business.*)
**默认文件**: `backend/config/defaults/business.yaml`
**关键配置键**:
- `business.rules.*`: 业务规则配置
- `business.templates.*`: 消息模板配置
- `business.thresholds.*`: 业务阈值配置

### 🔍 知识库配置 (knowledge_base.*)
**关键配置键**:
- `knowledge_base.chroma_db.path`: ChromaDB存储路径
- `knowledge_base.chroma_db.collection_name`: 集合名称
- `knowledge_base.embedding_model`: 嵌入模型
- `knowledge_base.retrieval.top_k`: 检索数量
- `knowledge_base.performance.cache_enabled`: 缓存开关

**维护建议**:
- 按环境分离数据路径
- 使用脚本管理集合创建/删除
- 定期验证检索质量

### 📝 日志配置 (logging.*)
**默认文件**: `backend/config/defaults/logging.yaml`
**关键配置键**:
- `logging.level`: 日志级别
- `logging.handlers.*`: 日志处理器配置
- `logging.formatters.*`: 日志格式配置
- `logging.loggers.*`: 特定日志器配置

### ⚙️ 系统配置 (system.*)
**默认文件**: `backend/config/defaults/system.yaml`
**关键配置键**:
- `system.performance.*`: 性能相关配置
- `system.security.*`: 安全相关配置
- `system.monitoring.*`: 监控相关配置

## 🔧 基本使用方法

### 获取配置
```python
from backend.config.manager import get_config_manager

# 获取配置管理器
config_manager = get_config_manager()

# 读取配置
debug_mode = config_manager.get("app.debug", False)
db_host = config_manager.get("database.host", "localhost")

# 获取配置段
db_config = config_manager.get_section("database")

# 获取配置来源
value, source = config_manager.get_with_source("app.debug")
print(f"app.debug = {value} (来源: {source})")
```

### 运行时配置
```python
# 设置运行时配置
config_manager.set_runtime("app.temp_setting", "temp_value")

# 清除运行时配置
config_manager.clear_runtime("app.temp_setting")
```

### 环境变量覆盖
```bash
# 基本格式: AID_CONF__<SECTION>__<KEY>=<VALUE>
export AID_CONF__APP__DEBUG=true
export AID_CONF__DATABASE__HOST=localhost
export AID_CONF__LLM__API_KEY=your_secret_key

# 复杂值（JSON格式）
export AID_CONF__LLM__MODELS='["gpt-4", "claude-3"]'
export AID_CONF__DATABASE__CONFIG='{"timeout": 30, "pool_size": 10}'
```

## 🛠️ 维护工具

### 配置验证
```bash
# 验证配置文件
python tools/config_static_checker.py

# 验证特定配置键
python tools/config_validator.py check app.debug
```

### 影响分析
```bash
# 分析配置变更影响
python tools/config_impact_analyzer.py analyze app.debug

# 生成测试清单
python tools/config_impact_analyzer.py test-list app.debug
```

### 监控工具
```bash
# 查看配置健康状态
python tools/config_monitor.py health

# 查看监控指标
python tools/config_monitor.py metrics

# 实时监控
python tools/config_monitor.py watch
```

### 安全工具
```bash
# 扫描敏感信息
python tools/config_security_scanner.py scan

# 生成访问审计报告
python tools/config_access_auditor.py report
```

## 📊 监控与告警

### Web监控面板
访问 `http://localhost:8000/static/monitoring_dashboard.html` 查看：
- 配置系统健康状态
- 配置加载性能指标
- 配置访问统计
- 告警历史记录

### 关键监控指标
- **配置加载时间**: < 2秒
- **配置验证错误率**: < 0.1%
- **敏感配置访问频率**: 监控异常访问
- **环境变量覆盖频率**: 跟踪配置变更

### 告警规则
- 配置系统不可用
- 配置加载时间过长
- 配置验证错误激增
- 敏感配置异常访问

## 🔒 安全最佳实践

### 敏感信息管理
1. **永远不要硬编码敏感信息**
2. **使用环境变量或密钥管理器**
3. **启用自动脱敏功能**
4. **定期轮换敏感配置**

### 访问控制
1. **使用配置白名单**
2. **记录配置访问日志**
3. **定期审计配置安全**
4. **限制配置文件访问权限**

## 🚨 故障排除

### 常见问题
1. **配置未生效**: 检查配置优先级和来源
2. **环境变量格式错误**: 确保使用正确的 `AID_CONF__` 前缀
3. **验证失败**: 检查配置值是否符合 Schema 定义
4. **权限错误**: 检查配置文件访问权限

### 调试工具
```bash
# 查看配置来源
python tools/config_monitor.py sources

# 检查配置文件
python tools/config_static_checker.py

# 分析配置影响
python tools/config_impact_analyzer.py analyze <config_key>
```

## 📚 相关文档

### 核心文档
- [配置管理指南](../配置管理指南.md)
- [配置管理最佳实践](../配置管理最佳实践.md)
- [配置管理API文档](../../api/配置管理API文档.md)

### 迁移文档
- [DEPRECATED配置系统迁移通知](../DEPRECATED_配置系统迁移通知.md)
- [旧托底清理与日落计划](../旧托底清理与日落计划.md)

### 技术文档
- [配置管理架构升级实施跟踪文档](../配置管理架构升级实施跟踪文档.md)
- [环境变量配置规范](../环境变量配置规范.md)

## ⚠️ 重要提醒

### 已废弃的组件
- ❌ `unified_config.defaults.yaml`
- ❌ `unified_config_loader`
- ❌ `get_config_value()`
- ❌ `load_config_from_yaml()`

### 新系统优势
- ✅ 强类型配置验证
- ✅ 完整的配置来源追踪
- ✅ 敏感信息自动保护
- ✅ 实时监控和告警
- ✅ 环境变量灵活覆盖

---

**文档版本**: v2.0  
**最后更新**: 2024-12-19  
**负责人**: 配置管理团队  
**技术支持**: <EMAIL>
