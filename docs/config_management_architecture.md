# 可扩展配置管理架构实施文档

## 1. 架构升级目标

基于现有配置系统痛点（单文件2700+行、职责混杂、热更新粒度粗），构建模块化、可扩展的配置管理架构：

```mermaid
graph TD
    A[统一配置门面] --> B[模块化配置源]
    A --> C[多级缓存架构]
    A --> D[配置源聚合]
    A --> E[类型安全验证]
    A --> F[热更新机制]
    A --> G[性能监控]
    B --> B1[system/]
    B --> B2[business/]
    B --> B3[llm/]
    B --> B4[data/]
    B --> B5[dynamic/]
```

## 2. 配置文件架构设计

### 2.1 多文件配置结构

```
backend/config/
├── unified_config.yaml              # 主配置文件（引用其他配置）
├── system/                          # 系统级配置
│   ├── base.yaml                    # 基础系统配置
│   ├── performance.yaml             # 性能配置
│   └── security.yaml                # 安全配置
├── business/                        # 业务配置
│   ├── rules.yaml                   # 业务规则
│   ├── templates.yaml               # 消息模板
│   └── thresholds.yaml              # 阈值配置
├── llm/                             # LLM相关配置
│   ├── models.yaml                  # 模型配置
│   ├── scenarios.yaml               # 场景配置
│   └── prompts.yaml                 # 提示词配置
├── data/                            # 数据配置
│   ├── database.yaml                # 数据库配置
│   └── storage.yaml                 # 存储配置
└── dynamic/                         # 动态配置
    ├── keywords.yaml                # 关键词配置
    └── versions.yaml                # 版本配置
```

### 2.2 主配置文件设计

```yaml
# backend/config/unified_config.yaml
# ============================================================================
# 统一配置入口 - 职责：配置引用和加载策略
# ============================================================================

# 配置加载策略
loading:
  strategy: "hierarchical"  # hierarchical, parallel, lazy
  cache_enabled: true
  auto_reload: true
  reload_interval: 60

# 配置文件映射
config_sources:
  # 系统配置
  system:
    base: "system/base.yaml"
    performance: "system/performance.yaml"
    security: "system/security.yaml"

  # 业务配置
  business:
    rules: "business/rules.yaml"
    templates: "business/templates.yaml"
    thresholds: "business/thresholds.yaml"

  # LLM配置
  llm:
    models: "llm/models.yaml"
    scenarios: "llm/scenarios.yaml"
    prompts: "llm/prompts.yaml"

  # 数据配置
  data:
    database: "data/database.yaml"
    storage: "data/storage.yaml"

  # 动态配置
  dynamic:
    keywords: "dynamic/keywords.yaml"
    versions: "dynamic/versions.yaml"

# 配置优先级
priority:
  1: "dynamic/versions.yaml"    # 最高优先级：运行时动态配置
  2: "system/base.yaml"         # 系统基础配置
  3: "business/rules.yaml"      # 业务规则配置
  4: "llm/models.yaml"          # LLM模型配置
  5: "data/database.yaml"       # 数据库配置
  6: "business/templates.yaml"  # 消息模板配置
  7: "business/thresholds.yaml" # 阈值配置

# 兼容性配置
compatibility:
  preserve_legacy_structure: true
  migration_mode: "gradual"
  backward_compatibility: true
```

### 2.3 模块化配置文件示例

**system/base.yaml**
```yaml
# 系统基础配置
system:
  version: "3.0"
  description: "需求采集系统配置"
  language: "zh-CN"
  supported_languages: ["zh-CN", "en-US"]
  fallback_enabled: true
  debug_mode: false

logging:
  level: "INFO"
  format: "json"
  max_file_size: "10MB"
  backup_count: 5
```

**business/rules.yaml**
```yaml
# 业务规则配置
business_rules:
  document_confirmation:
    confirmation_keywords:
    - 确认
    - 没问题
    - 正确
    - 同意

  focus_point_priority:
    p0: true
    p1: true
    p2: true

  quality_control:
    max_input_length: 1000
    min_input_length: 2
    spam_detection_enabled: true

  requirement_collection:
    completion_threshold: 0.8
    max_focus_points: 10
    min_focus_points: 3

  retry:
    backoff_factor: 1.5
    max_pending_attempts: 3
    max_total_attempts: 5
```

**llm/models.yaml**
```yaml
# LLM模型配置
llm:
  default_model: "deepseek-chat"

  # 默认LLM参数
  default_params:
    temperature: 0.7
    max_tokens: 2000
    top_p: 0.9
    frequency_penalty: 0.0
    presence_penalty: 0.0

  # 场景特定参数
  scenario_params:
    intent_recognition:
      temperature: 0.3
      max_tokens: 500
    document_generation:
      temperature: 0.5
      max_tokens: 4000
    domain_classification:
      temperature: 0.2
      max_tokens: 200
```

## 3. 实施阶段规划

### 阶段一：配置文件结构搭建
#### 具体任务
1. **创建配置文件目录结构**
   ```bash
   mkdir -p backend/config/{system,business,llm,data,dynamic}
   ```

2. **拆分现有配置文件**
   - 文件：`backend/config/unified_config.yaml`（原2700+行）
   - 拆分为：
     - `backend/config/system/base.yaml`
     - `backend/config/business/rules.yaml`
     - `backend/config/business/templates.yaml`
     - `backend/config/business/thresholds.yaml`
     - `backend/config/llm/models.yaml`
     - `backend/config/llm/scenarios.yaml`
     - `backend/config/data/database.yaml`
     - `backend/config/dynamic/keywords.yaml`

3. **创建主配置文件**
   - 文件：`backend/config/unified_config.yaml`
   - 内容：如上所示的配置引用结构

#### 验证清单
| 检查项 | 验证方法 | 工具 |
|--------|----------|------|
| 配置文件完整性 | 检查所有配置文件是否存在 | `ls -la backend/config/*/` |
| 配置引用正确性 | 验证主配置文件引用 | `python -c "import yaml; print(yaml.safe_load(open('backend/config/unified_config.yaml')))"` |
| 配置合并一致性 | 验证拆分后配置与原配置一致 | `python scripts/check_config_consistency.py --compare` |

#### 风险控制
- **风险点**：配置拆分过程中丢失配置项
- **缓解策略**：
  ```bash
  # 备份原配置
  cp backend/config/unified_config.yaml backend/config/unified_config.yaml.backup

  # 验证配置完整性
  python tools/config_validator.py --check-integrity --original backend/config/unified_config.yaml.backup

  # 逐步迁移，先备份后拆分
  ```

---

### 阶段二：模块化配置加载器实现
#### 具体任务
1. **创建模块化配置加载器**
   - 新增文件：`backend/config/modular_loader.py`
   - 实现代码：
     ```python
from typing import Any, Dict, Optional, Callable
from pathlib import Path
import yaml
import logging
from dataclasses import dataclass
from threading import RLock

@dataclass
class ConfigSource:
    """配置源类"""
    name: str
    file_path: Path
    priority: int
    config_data: Dict[str, Any] = None
    last_modified: float = 0
    change_listeners: list = None

    def __post_init__(self):
        if self.change_listeners is None:
            self.change_listeners = []
        self._load_initial_config()

    def _load_initial_config(self):
        """加载初始配置"""
        if self.file_path.exists():
            self.last_modified = self.file_path.stat().st_mtime
            with open(self.file_path, 'r', encoding='utf-8') as f:
                self.config_data = yaml.safe_load(f) or {}
        else:
            self.config_data = {}

    def reload(self):
        """重新加载配置"""
        if self.file_path.exists():
            new_modified = self.file_path.stat().st_mtime
            if new_modified > self.last_modified:
                old_config = self.config_data.copy()
                self._load_initial_config()

                # 通知变更监听器
                for listener in self.change_listeners:
                    try:
                        listener(old_config, self.config_data)
                    except Exception as e:
                        logging.error(f"配置变更通知失败 {self.name}: {e}")

    def add_change_listener(self, listener: Callable):
        """添加变更监听器"""
        self.change_listeners.append(listener)

class ModularConfigLoader:
    """模块化配置加载器"""

    def __init__(self, config_dir: str = "backend/config"):
        self.config_dir = Path(config_dir)
        self.config_sources = {}
        self.config_cache = {}
        self.cache_lock = RLock()
        self.monitoring = ConfigMonitoring()

        # 初始化配置源
        self._initialize_sources()

    def _initialize_sources(self):
        """初始化配置源映射"""
        # 从主配置文件加载配置源映射
        main_config_path = self.config_dir / "unified_config.yaml"
        if not main_config_path.exists():
            raise FileNotFoundError(f"主配置文件不存在: {main_config_path}")

        with open(main_config_path, 'r', encoding='utf-8') as f:
            main_config = yaml.safe_load(f)

        # 加载配置源
        config_sources = main_config.get('config_sources', {})
        priority_map = main_config.get('priority', {})

        for category, sources in config_sources.items():
            for source_name, file_path in sources.items():
                config_key = f"{category}.{source_name}"
                full_path = self.config_dir / file_path

                priority = priority_map.get(config_key, 999)
                source = ConfigSource(
                    name=config_key,
                    file_path=full_path,
                    priority=priority
                )
                self.config_sources[config_key] = source

    def get_config(self, config_key: str, default: Any = None) -> Any:
        """获取配置，支持模块化配置"""
        # 1. 检查缓存
        with self.cache_lock:
            if config_key in self.config_cache:
                return self.config_cache[config_key]

        # 2. 获取配置源
        source = self.config_sources.get(config_key)
        if not source:
            return default

        # 3. 加载配置
        try:
            config_data = source.config_data
            with self.cache_lock:
                self.config_cache[config_key] = config_data

            # 记录访问
            self.monitoring.record_access(config_key, len(config_data))

            return config_data
        except Exception as e:
            self.monitoring.record_error(config_key, str(e))
            return default

    def get_business_rules(self) -> Dict[str, Any]:
        """获取业务规则配置"""
        return self.get_config('business.rules', {})

    def get_llm_config(self, scenario: str = 'default') -> Dict[str, Any]:
        """获取LLM配置"""
        models_config = self.get_config('llm.models', {})
        scenarios_config = self.get_config('llm.scenarios', {})

        # 合并配置逻辑
        return self._merge_llm_configs(models_config, scenarios_config, scenario)

    def watch_config_changes(self, config_key: str, callback: Callable):
        """监听特定配置变更"""
        if config_key in self.config_sources:
            self.config_sources[config_key].add_change_listener(callback)

    def reload_config(self, config_key: str = None):
        """重新加载配置"""
        if config_key:
            if config_key in self.config_sources:
                self.config_sources[config_key].reload()
                with self.cache_lock:
                    if config_key in self.config_cache:
                        del self.config_cache[config_key]
        else:
            # 重新加载所有配置
            for source in self.config_sources.values():
                source.reload()
            with self.cache_lock:
                self.config_cache.clear()

    def start_auto_reload(self):
        """启动自动重载"""
        import threading
        import time

        def auto_reload_worker():
            while True:
                time.sleep(60)  # 每分钟检查一次
                self.reload_config()

        thread = threading.Thread(target=auto_reload_worker, daemon=True)
        thread.start()
     ```

2. **创建配置监控组件**
   - 新增文件：`backend/config/monitoring.py`
   - 实现代码：
     ```python
import time
from collections import defaultdict
from dataclasses import dataclass
from typing import Dict, List

@dataclass
class ConfigAccessRecord:
    """配置访问记录"""
    config_key: str
    access_time: float
    data_size: int
    success: bool

class ConfigMonitoring:
    """配置监控"""

    def __init__(self):
        self.access_records = []
        self.error_records = []
        self.access_stats = defaultdict(int)
        self.start_time = time.time()

    def record_access(self, config_key: str, data_size: int = 0):
        """记录配置访问"""
        record = ConfigAccessRecord(
            config_key=config_key,
            access_time=time.time(),
            data_size=data_size,
            success=True
        )
        self.access_records.append(record)
        self.access_stats[config_key] += 1

        # 保持记录数量在合理范围内
        if len(self.access_records) > 10000:
            self.access_records = self.access_records[-5000:]

    def record_error(self, config_key: str, error_msg: str):
        """记录配置访问错误"""
        self.error_records.append({
            'config_key': config_key,
            'error_msg': error_msg,
            'timestamp': time.time()
        })

    def get_performance_report(self) -> Dict[str, any]:
        """获取性能报告"""
        total_access = len(self.access_records)
        total_errors = len(self.error_records)

        if total_access == 0:
            return {
                'total_access': 0,
                'error_rate': 0,
                'top_accessed': [],
                'uptime': time.time() - self.start_time
            }

        # 计算错误率
        error_rate = total_errors / max(total_access, 1) * 100

        # 统计最常访问的配置
        access_count = defaultdict(int)
        for record in self.access_records:
            access_count[record.config_key] += 1

        top_accessed = sorted(
            access_count.items(),
            key=lambda x: x[1],
            reverse=True
        )[:10]

        return {
            'total_access': total_access,
            'total_errors': total_errors,
            'error_rate': error_rate,
            'top_accessed': top_accessed,
            'uptime': time.time() - self.start_time
        }
     ```

#### 验证清单
| 检查项 | 验证方法 | 工具 |
|--------|----------|------|
| 模块化加载器功能 | 测试配置获取和重载 | `python -m pytest tests/test_modular_loader.py` |
| 配置监控功能 | 验证访问统计和错误记录 | `python -c "from backend.config.monitoring import ConfigMonitoring; m = ConfigMonitoring(); m.record_access('test'); print(m.get_performance_report())"` |
| 配置热更新 | 测试配置文件变更检测 | `touch backend/config/business/rules.yaml && python -c "from backend.config.modular_loader import ModularConfigLoader; loader = ModularConfigLoader(); print(loader.get_config('business.rules'))"` |

#### 风险控制
- **风险点**：模块化加载器与现有代码不兼容
- **缓解策略**：
  ```bash
  # 创建兼容性包装器
  python scripts/create_compatibility_wrapper.py

  # 渐进式迁移测试
  python scripts/test_gradual_migration.py
  ```

---

### 阶段三：兼容性保持和API迁移
#### 具体任务
1. **创建兼容性包装器**
   - 修改文件：`backend/config/unified_config_loader.py`
   - 重构为：
     ```python
from .modular_loader import ModularConfigLoader
import logging

class LegacyConfigWrapper:
    """遗留配置API包装器"""

    def __init__(self):
        self.modular_loader = ModularConfigLoader()
        self._cache = {}
        self.logger = logging.getLogger(__name__)

    def get_unified_config(self):
        """保持原有API兼容性"""
        return self

    def get_llm_config(self, config_type: str = 'default'):
        """兼容原有LLM配置获取"""
        return self.modular_loader.get_llm_config(config_type)

    def get_business_rule(self, key: str, default=None):
        """兼容原有业务规则获取"""
        business_rules = self.modular_loader.get_business_rules()
        # 支持点分割路径
        if '.' in key:
            keys = key.split('.')
            value = business_rules
            try:
                for k in keys:
                    value = value[k]
                return value
            except (KeyError, TypeError):
                return default
        else:
            return business_rules.get(key, default)

    def get_message_template(self, template_path: str, default=None, **kwargs):
        """兼容原有消息模板获取"""
        templates = self.modular_loader.get_config('business.templates', {})
        # 解析模板路径
        path_parts = template_path.split('.')
        current = templates

        for part in path_parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return default or ""

        template = current if isinstance(current, str) else (default or "")

        # 格式化模板
        if kwargs and template:
            try:
                return template.format(**kwargs)
            except (KeyError, ValueError):
                return template

        return template

# 保持全局实例
_unified_config = LegacyConfigWrapper()

def get_unified_config():
    """保持原有全局函数接口"""
    return _unified_config
     ```

2. **逐步迁移现有代码**
   - 创建迁移脚本：`scripts/migrate_config_usage.py`
   - 实现代码：
     ```python
import re
import os
from pathlib import Path

def migrate_config_imports():
    """迁移配置导入语句"""
    backend_dir = Path("backend")

    # 查找所有使用配置的Python文件
    python_files = list(backend_dir.rglob("*.py"))

    for file_path in python_files:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 替换导入语句
        old_imports = [
            r'from backend\.config\.unified_config_loader import get_unified_config',
            r'from backend\.config\.service import config_service',
            r'from backend\.config\.unified_dynamic_config import dynamic_keyword_config'
        ]

        new_imports = [
            'from backend.config.unified_config_loader import get_unified_config',
            'from backend.config.legacy_service import config_service',
            'from backend.config.legacy_service import dynamic_keyword_config'
        ]

        for old_pattern, new_import in zip(old_imports, new_imports):
            content = re.sub(old_pattern, new_import, content)

        # 如果文件有变化，写回
        if content != f.read():
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Migrated: {file_path}")

def migrate_config_calls():
    """迁移配置调用"""
    # 这里可以添加更复杂的迁移逻辑
    pass
     ```

#### 验证清单
| 检查项 | 验证方法 | 工具 |
|--------|----------|------|
| 向后兼容性 | 验证现有代码无需修改即可运行 | `python scripts/test_compatibility.py` |
| API一致性 | 验证所有原有API仍然可用 | `python -c "from backend.config.unified_config_loader import get_unified_config; config = get_unified_config(); print(config.get_llm_config())"` |
| 性能影响 | 对比新旧架构性能差异 | `python scripts/performance_test.py --compare` |

#### 风险控制
- **风险点**：迁移过程中破坏现有功能
- **缓解策略**：
  ```bash
  # 创建功能测试套件
  python scripts/create_compatibility_tests.py

  # 分批迁移，每批验证
  python scripts/migrate_config_usage.py --batch-size 10 --verify
  ```

---

### 阶段四：性能优化和清理
#### 具体任务
1. **实现智能预加载**
   - 修改文件：`backend/config/modular_loader.py`
   - 添加预加载逻辑：
     ```python
class ModularConfigLoader:
    def __init__(self, config_dir: str = "backend/config"):
        # ... 现有初始化代码 ...

        # 预加载配置
        self.preload_patterns = {
            "frequent": ["business.rules", "system.base", "llm.models"],
            "startup": ["system.security", "data.database"],
            "on_demand": ["business.templates", "llm.prompts"]
        }

        # 启动时预加载高频配置
        self._preload_frequent_configs()

    def _preload_frequent_configs(self):
        """预加载高频配置"""
        for config_key in self.preload_patterns["frequent"]:
            self.get_config(config_key)

    def get_config_with_intelligence(self, config_key: str, default: Any = None) -> Any:
        """智能配置获取"""
        # 如果是预加载模式，直接返回缓存
        if config_key in self.preload_patterns["frequent"]:
            cached = self.config_cache.get(config_key)
            if cached is not None:
                return cached

        # 否则正常获取
        return self.get_config(config_key, default)
     ```

2. **实现配置版本控制**
   - 新增文件：`backend/config/version_control.py`
   - 实现代码：
     ```python
import json
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

class ConfigVersionControl:
    """配置版本控制"""

    def __init__(self, storage_dir: str = "backend/data/config_versions"):
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)

    def save_version(self, config_key: str, config_data: Dict[str, Any],
                    description: str = "自动保存") -> str:
        """保存配置版本"""
        version_id = self._generate_version_id()
        version_data = {
            "version_id": version_id,
            "config_key": config_key,
            "config_data": config_data,
            "description": description,
            "timestamp": datetime.now().isoformat(),
            "checksum": self._calculate_checksum(config_data)
        }

        version_file = self.storage_dir / f"{config_key}_{version_id}.json"
        with open(version_file, 'w', encoding='utf-8') as f:
            json.dump(version_data, f, ensure_ascii=False, indent=2)

        # 更新最新版本链接
        latest_file = self.storage_dir / f"{config_key}_latest.json"
        with open(latest_file, 'w', encoding='utf-8') as f:
            json.dump(version_data, f, ensure_ascii=False, indent=2)

        return version_id

    def rollback_version(self, config_key: str, version_id: str) -> bool:
        """回滚到指定版本"""
        version_file = self.storage_dir / f"{config_key}_{version_id}.json"
        if not version_file.exists():
            return False

        with open(version_file, 'r', encoding='utf-8') as f:
            version_data = json.load(f)

        # 这里可以触发配置重载
        return True

    def _generate_version_id(self) -> str:
        """生成版本ID"""
        import uuid
        return str(uuid.uuid4())[:8]

    def _calculate_checksum(self, data: Dict[str, Any]) -> str:
        """计算配置校验和"""
        serialized = json.dumps(data, sort_keys=True)
        return hashlib.md5(serialized.encode()).hexdigest()
     ```

3. **清理遗留代码**
   - 逐步移除不再使用的代码
   - 优化配置加载性能

#### 验证清单
| 优化项 | 验证方法 | 工具 |
|--------|----------|------|
| 智能预加载 | 测试预加载效果 | `python scripts/performance_test.py --preload-test` |
| 版本控制 | 测试版本保存和回滚 | `python -c "from backend.config.version_control import ConfigVersionControl; vc = ConfigVersionControl(); print(vc.save_version('test', {'key': 'value'}))"` |
| 性能优化 | 对比优化前后性能 | `python scripts/performance_test.py --before-after` |

#### 风险控制
- **风险点**：优化过程中引入性能问题
- **缓解策略**：
  ```bash
  # 性能基准测试
  python scripts/performance_test.py --baseline

  # 逐步优化，每次只优化一个方面
  python scripts/performance_test.py --test-preload

  # 持续监控
  python tools/config_monitor.py --continuous
  ```

---

## 4. A/B测试方案

### 4.1 流量路由策略

```mermaid
graph LR
    A[生产流量] --> B{CONFIG_ROUTING_STRATEGY}
    B -->|new| C[新模块化配置架构]
    B -->|old| D[旧单文件配置架构]
    C --> E[数据采集]
    D --> E
    E --> F[差异分析]
    F --> G{差异阈值}
    G -->|<5%| H[自动切换]
    G -->|>=5%| I[人工干预]
```

### 4.2 实施步骤

```bash
# 配置路由设置
export CONFIG_ROUTING_STRATEGY=weighted
export CONFIG_ROUTING_WEIGHT=10  # 起始流量比例
export ENABLE_NEW_CONFIG_ARCH=true

# 启动监控
python tools/config_monitor.py --start

# 逐步增加流量
for weight in 10 25 50 100; do
    export CONFIG_ROUTING_WEIGHT=$weight
    echo "当前流量权重: ${weight}%"

    # 等待48小时观察
    sleep 172800

    # 运行性能测试
    python scripts/performance_test.py --monitor

    # 生成报告
    python scripts/generate_ab_test_report.py --weight $weight
done

# 最终切换
export CONFIG_ROUTING_WEIGHT=100
export CONFIG_ROUTING_STRATEGY=new_only
```

### 4.3 监控指标

| 指标 | 新架构目标 | 旧架构基准 | 监控工具 |
|------|------------|------------|----------|
| 配置加载时间 | ≤200ms | 500ms | `scripts/performance_test.py --config-load` |
| 内存使用量 | 减少20% | 基准值 | `tools/memory_monitor.py` |
| 配置文件冲突 | 0次 | 当前值 | `tools/git_conflict_monitor.py` |
| 热更新响应时间 | ≤1s | 5s | `scripts/test_hot_reload.py` |

## 5. 迁移工具和脚本

### 5.1 配置迁移工具

```python
# scripts/config_migration_tool.py
import yaml
import shutil
from pathlib import Path
from typing import Dict, Any

class ConfigMigrationTool:
    """配置迁移工具"""

    def __init__(self, source_dir: str, target_dir: str):
        self.source_dir = Path(source_dir)
        self.target_dir = Path(target_dir)
        self.backup_dir = self.target_dir / "backup"

    def migrate(self):
        """执行迁移"""
        print("开始配置迁移...")

        # 创建备份
        self._create_backup()

        # 创建目标目录结构
        self._create_target_structure()

        # 拆分配置文件
        self._split_config_files()

        # 验证迁移结果
        self._validate_migration()

        print("配置迁移完成！")

    def _create_backup(self):
        """创建备份"""
        if self.target_dir.exists():
            shutil.copytree(self.target_dir, self.backup_dir)
            print(f"备份已创建: {self.backup_dir}")

    def _create_target_structure(self):
        """创建目标目录结构"""
        subdirs = ['system', 'business', 'llm', 'data', 'dynamic']
        for subdir in subdirs:
            (self.target_dir / subdir).mkdir(parents=True, exist_ok=True)

    def _split_config_files(self):
        """拆分配置文件"""
        # 读取原配置文件
        source_file = self.source_dir / "unified_config.yaml"
        if not source_file.exists():
            raise FileNotFoundError(f"源配置文件不存在: {source_file}")

        with open(source_file, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)

        # 拆分配置到不同文件
        split_rules = {
            'system': ['system'],
            'business': ['business_rules', 'business_templates', 'business_thresholds'],
            'llm': ['llm'],
            'data': ['database'],
            'dynamic': ['dynamic_keywords']
        }

        for category, keys in split_rules.items():
            category_config = {}
            for key in keys:
                if key in config_data:
                    category_config[key] = config_data[key]
                    del config_data[key]

            # 保存到对应文件
            target_file = self.target_dir / category / f"{category}.yaml"
            with open(target_file, 'w', encoding='utf-8') as f:
                yaml.dump(category_config, f, default_flow_style=False, allow_unicode=True)

        # 创建主配置文件
        main_config = {
            'loading': {
                'strategy': 'hierarchical',
                'cache_enabled': True,
                'auto_reload': True
            },
            'config_sources': {
                'system': {'base': 'system/base.yaml'},
                'business': {'rules': 'business/rules.yaml'},
                'llm': {'models': 'llm/models.yaml'},
                'data': {'database': 'data/database.yaml'},
                'dynamic': {'keywords': 'dynamic/keywords.yaml'}
            },
            'priority': {
                '1': 'dynamic/keywords.yaml',
                '2': 'system/base.yaml',
                '3': 'business/rules.yaml',
                '4': 'llm/models.yaml',
                '5': 'data/database.yaml'
            }
        }

        with open(self.target_dir / "unified_config.yaml", 'w', encoding='utf-8') as f:
            yaml.dump(main_config, f, default_flow_style=False, allow_unicode=True)

    def _validate_migration(self):
        """验证迁移结果"""
        print("验证迁移结果...")

        # 检查文件完整性
        required_files = [
            'system/base.yaml',
            'business/rules.yaml',
            'llm/models.yaml',
            'data/database.yaml',
            'dynamic/keywords.yaml',
            'unified_config.yaml'
        ]

        for file_path in required_files:
            full_path = self.target_dir / file_path
            if not full_path.exists():
                raise FileNotFoundError(f"迁移后文件缺失: {file_path}")

            # 验证YAML格式
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    yaml.safe_load(f)
            except yaml.YAMLError as e:
                raise ValueError(f"YAML格式错误 {file_path}: {e}")

        print("迁移验证通过！")

if __name__ == "__main__":
    migrator = ConfigMigrationTool(
        source_dir="backend/config",
        target_dir="backend/config"
    )
    migrator.migrate()
```

### 5.2 性能测试脚本

```python
# scripts/config_performance_test.py
import time
import statistics
from pathlib import Path
from typing import List, Dict, Any
import sys

class ConfigPerformanceTest:
    """配置性能测试"""

    def __init__(self):
        self.results = {}

    def test_config_loading(self, iterations: int = 100) -> Dict[str, Any]:
        """测试配置加载性能"""
        print(f"开始配置加载性能测试（{iterations}次迭代）...")

        times = []

        for i in range(iterations):
            start_time = time.time()

            # 测试配置加载
            try:
                from backend.config.unified_config_loader import get_unified_config
                config = get_unified_config()
                llm_config = config.get_llm_config()
                business_rules = config.get_business_rule("retry.max_attempts")
            except Exception as e:
                print(f"配置加载失败: {e}")
                continue

            end_time = time.time()
            times.append((end_time - start_time) * 1000)  # 转换为毫秒

        if not times:
            return {"error": "所有测试都失败了"}

        result = {
            "avg_time": statistics.mean(times),
            "median_time": statistics.median(times),
            "min_time": min(times),
            "max_time": max(times),
            "p95_time": statistics.quantiles(times, n=20)[18],  # 95th percentile
            "total_tests": len(times),
            "success_rate": len(times) / iterations * 100
        }

        self.results["config_loading"] = result
        return result

    def test_memory_usage(self) -> Dict[str, Any]:
        """测试内存使用"""
        print("开始内存使用测试...")

        try:
            import psutil
            import os

            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB

            # 加载配置
            from backend.config.unified_config_loader import get_unified_config
            config = get_unified_config()

            # 访问各种配置
            for _ in range(100):
                config.get_llm_config()
                config.get_business_rule("retry.max_attempts")
                config.get_message_template("error.general")

            final_memory = process.memory_info().rss / 1024 / 1024  # MB

            result = {
                "initial_memory": initial_memory,
                "final_memory": final_memory,
                "memory_increase": final_memory - initial_memory,
                "memory_increase_percent": (final_memory - initial_memory) / initial_memory * 100
            }

            self.results["memory_usage"] = result
            return result

        except ImportError:
            return {"error": "psutil not available"}

    def test_concurrent_access(self, threads: int = 10, iterations: int = 100) -> Dict[str, Any]:
        """测试并发访问"""
        print(f"开始并发访问测试（{threads}线程，{iterations}次迭代）...")

        import threading

        results = []
        lock = threading.Lock()

        def worker(thread_id):
            thread_times = []

            for i in range(iterations):
                start_time = time.time()

                try:
                    from backend.config.unified_config_loader import get_unified_config
                    config = get_unified_config()
                    config.get_llm_config()
                    config.get_business_rule("retry.max_attempts")
                except Exception as e:
                    print(f"线程 {thread_id} 配置访问失败: {e}")
                    continue

                end_time = time.time()
                thread_times.append((end_time - start_time) * 1000)

            with lock:
                results.extend(thread_times)

        # 启动线程
        thread_list = []
        for i in range(threads):
            thread = threading.Thread(target=worker, args=(i,))
            thread_list.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in thread_list:
            thread.join()

        if not results:
            return {"error": "所有测试都失败了"}

        result = {
            "avg_time": statistics.mean(results),
            "median_time": statistics.median(results),
            "min_time": min(results),
            "max_time": max(results),
            "total_tests": len(results),
            "success_rate": len(results) / (threads * iterations) * 100
        }

        self.results["concurrent_access"] = result
        return result

    def generate_report(self) -> Dict[str, Any]:
        """生成性能测试报告"""
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "results": self.results,
            "summary": self._generate_summary()
        }

        # 保存报告
        report_file = Path("reports/config_performance_test_report.json")
        report_file.parent.mkdir(parents=True, exist_ok=True)

        import json
        with open(report_file


---

## 6. 实施计划（汇总版）

注：后台对齐检查项见第9节《后台对齐检查项》。
- 阶段0 盘点与基线（1-2天）
  - 目标：建立现状基线与风险清单
  - 关键任务：执行“三步检查法”；盘点 unified_config.yaml 命名空间/调用点/敏感项；输出 files/* 拆分草案
  - 交付物：配置盘点报告、拆分与命名规范草案
  - 验收：报告完整，无冲突项遗漏

- 阶段1 目录与文件骨架（0.5天）
  - 目标：创建目标目录与空白骨架，不改变现有加载
  - 关键任务：建立 backend/config/{system,business,llm,data,dynamic} 及基础 YAML 文件
  - 交付物：PR-1（仅新增文件与文档）
  - 验收：编译/测试通过；业务无影响

- 阶段2 聚合器 PoC 与门面适配（1-2天）
  - 目标：在不改外部 API 前提下，使聚合器能从 defaults+unified_config+ENV 构建统一视图
  - 关键任务：实现聚合器；Facade 支持聚合视图（特性开关 config.aggregation.enabled=false）；get_config_status 仅“透出”架构信息
  - 交付物：PR-2（聚合器/适配层与测试）
  - 验收：单测通过；关闭开关时与现有行为等效

- 阶段3 内容对齐与双写验证（2-3天）
  - 目标：files/* 与 unified_config.yaml 语义一致
  - 关键任务：编写对齐脚本；Dev 开启 compare 模式，双视图深度 diff；修正直至 diff=0
  - 交付物：PR-3（对齐脚本、files/* 首版、对齐报告）
  - 验收：对齐报告 diff=0；关键路径回归一致

- 阶段4 灰度切换与强校验（2天）
  - 目标：Staging 切源至 files/*；开启关键域强校验
  - 关键任务：打开 config.aggregation.enabled（Staging）；失败自动回退健康快照
  - 交付物：PR-4（开关切换/校验策略）
  - 验收：Staging 无回滚事件；指标稳定；业务验收通过

- 阶段5 生产灰度与观测（2-3天）
  - 目标：生产灰度启用 files/* 源，手动 reload，观察指标≥7天
  - 关键任务：10-20% 流量灰度；搭建监控看板（耗时/失败与降级/命中率/回调时延）
  - 交付物：PR-5（生产开关与仪表）
  - 验收：指标达标，SLA 不下降，无安全事件

- 阶段6 清理与沉淀（1天）
  - 目标：清理冗余路径、完善文档与培训
  - 关键任务：将 unified_config.yaml 移出主加载路径但保留回滚入口；沉淀命名空间所有权/流程/回滚指南
  - 交付物：PR-6（清理与文档）
  - 验收：回滚演练通过，文档齐备

## 7. 验收清单（总表）

- 行为一致性
  - [ ] 旧 API 全保留（get_unified_config/get_config_value/get_message_templates/get_business_rules 等）
  - [ ] 决策策略/消息模板/阈值等关键输出与现状一致

- 聚合正确性
  - [ ] 优先级与覆盖规则符合“ENV>files>defaults（可选：Remote>ENV>files>defaults）”
  - [ ] 跨域引用（策略→规则、模板变量）校验通过

- 性能与稳定
  - [ ] 加载耗时 p95 ≤ 300ms，p99 ≤ 800ms（示例阈值）
  - [ ] 缓存命中率 ≥ 85%
  - [ ] 灰度/稳定期无频繁回滚

- 可观测性
  - [ ] 日志包含来源链与快照版本、变更摘要、回滚原因
  - [ ] 指标写入 logs/performance 并可视化（耗时/失败与降级/命中率/回调时延）
  - [ ] LLM 日志打印“场景名 + 实际模型名”

- 安全合规
  - [ ] 敏感信息仅来自 ENV/密管；不硬编码在 files/*
  - [ ] 多用户/多租户数据隔离不被破坏；访问带审计

- 回滚能力
  - [ ] 一键切回“单文件 + 分层合并”路径
  - [ ] 健康快照可用且可回退

- 文档与测试
  - [ ] 完整迁移/回滚/操作手册；命名空间所有权与准入清单
  - [ ] 单元、集成与回归测试全部通过；关键用例齐全

## 9. 后台对齐检查项（锚点）
- 只读可观测
  - [ ] 接入 /api/config/status、/snapshot/meta、/files/list、/diff/report、/metrics
  - [ ] 仪表板显示快照版本、来源链、关键指标
- 安全与权限
  - [ ] 敏感字段脱敏展示；RBAC 生效；下载受审计
- 运维操作
  - [ ] Dev/Staging 支持受控 reload（Prod 默认关闭）
  - [ ] 快照查看/下载与健康快照标记
- 对齐与回归
  - [ ] 对齐报告可视化；关键场景数据一致
- 回滚能力
  - [ ] 回滚演练记录（Staging）已归档
- 链接：详见《docs/admin_config_backend_design.md》



---

## 8. 执行跟踪板块（常驻）

### 8.1 执行总览
- 文档版本：v0.1（草案）
- 最近更新：待填
- 负责人（RACI）：
  - A（最终负责）：待填
  - R（执行）：后端：待填；QA：待填；DevOps：待填
  - C（协作）：架构：待填；安全：待填
  - I（知会）：产品/运营：待填
- 当前阶段：待填（阶段0/1/2/3/4/5/6）
- 里程碑：
  - M1：PoC 合并（阶段2结束）
  - M2：对齐报告为零差（阶段3结束）
  - M3：Staging 强校验稳定（阶段4结束）
  - M4：生产灰度通过（阶段5结束）
  - M5：清理与沉淀完成（阶段6结束）

### 8.2 状态快照（阶段结项模板）
- 阶段编号：阶段X（示例）
- 完成项：
  - [ ] 事项A
  - [ ] 事项B
- 遗留项：
  - [ ] 风险/债务A（计划在阶段Y处理）
- 已知风险与缓解：
  - 风险：描述……；缓解：描述……
- 回滚点与验证：
  - 回滚点：描述……
  - 验证结果：描述……

### 8.3 阶段任务清单（Checklist 模板）
- 阶段0：
  - [ ] 三步检查法执行完成
  - [ ] 盘点报告归档
  - [ ] 拆分与命名规范确认
- 阶段1：
  - [ ] 目录创建与空白文件提交
  - [ ] 基础模板与注释齐备
- 阶段2：
  - [ ] 聚合器 PoC 合并
  - [ ] Facade 适配完成（开关默认关闭）
  - [ ] 单测覆盖聚合优先级
- 阶段3：
  - [ ] 对齐脚本完成
  - [ ] 双视图 diff=0
  - [ ] 对齐报告归档
- 阶段4：
  - [ ] Staging 开启强校验
  - [ ] 回滚机制验证通过
- 阶段5：
  - [ ] 生产灰度 10-20% 开启
  - [ ] 监控看板上线并稳定运行
- 阶段6：
  - [ ] 关闭旧路径（保留回滚入口）
  - [ ] 操作手册/培训完成

### 8.4 变更日志（模板）
- 日期：YYYY-MM-DD
- 变更摘要：
- 影响范围：
- 风险评估：
- 回滚预案：
- 验证结果：
- 责任人：

### 8.5 验收记录（逐项打钩 + 证据）
- 行为一致性：
  - [ ] 旧 API 全保留（证据链接：）
  - [ ] 关键输出一致（证据链接：）
- 聚合正确性：
  - [ ] 优先级与覆盖规则符合（证据链接：）
  - [ ] 引用校验通过（证据链接：）
- 性能与稳定：
  - [ ] 耗时与命中率达标（证据链接：）
  - [ ] 无回滚事件（证据链接：）
- 可观测性：
  - [ ] 日志/指标接入（证据链接：）
- 安全合规：
  - [ ] ENV/密管替代敏感配置（证据链接：）
- 回滚能力：
  - [ ] 单文件路径可回切（证据链接：）
- 文档与测试：
  - [ ] 文档齐备（证据链接：）
  - [ ] 测试通过（证据链接：）
