# 知识库配置管理

## 概述

知识库配置管理功能允许管理员通过可视化界面实时调整知识库的各项参数，包括检索配置、性能设置、安全参数等，无需手动编辑配置文件。

## 功能特性

### 🔍 检索配置管理
- **检索文档数量 (top_k)**: 控制每次查询返回的文档数量 (1-20)
- **相似度阈值**: 控制文档匹配的精确度 (0.0-1.0)
- **最大上下文长度**: 传递给LLM的最大字符数 (1000-10000)

### ⚡ 性能配置管理
- **缓存启用**: 开启/关闭查询结果缓存
- **缓存TTL**: 缓存有效期设置 (300-86400秒)
- **最大并发查询数**: 同时处理的查询数量限制 (1-20)

### 🎛️ 配置预设
系统提供4种预设配置方案：

1. **快速查询**: 适用于快速响应场景
   - top_k: 3, threshold: 0.8, context: 2000
   
2. **详细咨询**: 适用于需要详细回答的场景
   - top_k: 7, threshold: 0.6, context: 5000
   
3. **专业问答**: 推荐配置，平衡质量和速度
   - top_k: 5, threshold: 0.7, context: 4000
   
4. **高精度模式**: 适用于对准确性要求极高的场景
   - top_k: 3, threshold: 0.9, context: 3000

## 使用指南

### 访问配置管理
1. 登录后台管理系统
2. 进入"知识库管理"页面
3. 点击"配置管理"标签页

### 调整配置参数
1. 在配置表单中修改相应参数
2. 点击"验证配置"检查参数有效性
3. 点击"保存配置"应用更改

### 使用配置预设
1. 在预设下拉菜单中选择合适的预设
2. 系统会弹出确认对话框
3. 确认后预设配置将自动应用

### 配置验证
- 系统会自动验证参数范围和有效性
- 显示错误和警告信息
- 检查ChromaDB连接状态

## API接口

### 获取配置
```http
GET /api/admin/knowledge-base/config
```

### 更新配置
```http
PUT /api/admin/knowledge-base/config
Content-Type: application/json

{
  "retrieval": {
    "top_k": 5,
    "similarity_threshold": 0.7,
    "max_context_length": 4000
  },
  "performance": {
    "cache_enabled": true,
    "cache_ttl": 3600,
    "max_concurrent_queries": 5
  }
}
```

### 应用预设
```http
POST /api/admin/knowledge-base/config/apply-preset/{preset_name}
```

### 验证配置
```http
POST /api/admin/knowledge-base/config/validate
```

## 配置参数说明

### 检索参数优化建议

| 场景 | top_k | similarity_threshold | max_context_length |
|------|-------|---------------------|-------------------|
| 快速问答 | 3-5 | 0.7-0.8 | 2000-3000 |
| 详细咨询 | 5-7 | 0.6-0.7 | 4000-5000 |
| 专业分析 | 7-10 | 0.5-0.6 | 5000-8000 |

### 性能参数优化建议

| 用户规模 | cache_ttl | max_concurrent_queries |
|----------|-----------|----------------------|
| 小型 (< 100) | 1800-3600 | 3-5 |
| 中型 (100-500) | 3600-7200 | 5-10 |
| 大型 (> 500) | 7200-14400 | 10-20 |

## 注意事项

1. **配置更改影响**: 配置更改会立即生效，影响所有新的查询请求
2. **性能影响**: 增加top_k和context_length会提高查询质量但降低响应速度
3. **内存使用**: 高并发设置需要确保服务器有足够内存
4. **缓存策略**: 频繁更改配置会导致缓存失效，影响性能

## 故障排除

### 常见问题

1. **配置验证失败**
   - 检查参数是否在有效范围内
   - 确认ChromaDB服务正常运行

2. **配置更新失败**
   - 检查配置文件写入权限
   - 确认统一配置服务正常

3. **预设应用失败**
   - 检查预设名称是否正确
   - 确认网络连接正常

### 日志查看
配置管理相关日志位于：
- 应用日志: `logs/app.log`
- 错误日志: `logs/error.log`
- 性能日志: `logs/performance/`

## 最佳实践

1. **渐进式调整**: 建议小幅度调整参数，观察效果后再进一步优化
2. **定期验证**: 定期运行配置验证，确保系统健康
3. **备份配置**: 重大更改前备份当前配置
4. **监控性能**: 关注配置更改对系统性能的影响
5. **用户反馈**: 收集用户对查询质量的反馈，指导配置优化

## 更新历史

- **v1.0**: 初始版本，支持基础配置管理
- **v1.1**: 添加配置预设功能
- **v1.2**: 增强配置验证和错误处理
- **v1.3**: 优化用户界面和操作体验
