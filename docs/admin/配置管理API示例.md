# 知识库配置管理API使用示例

## 概述

本文档提供知识库配置管理API的详细使用示例，包括获取配置、更新配置、应用预设等操作。

## API基础信息

- **基础URL**: `/api/admin/knowledge-base`
- **认证**: 需要管理员权限
- **内容类型**: `application/json`

## 1. 获取当前配置

### 请求
```http
GET /api/admin/knowledge-base/config
Authorization: Bearer <admin_token>
```

### 响应示例
```json
{
  "enabled": true,
  "retrieval": {
    "top_k": 5,
    "similarity_threshold": 0.7,
    "max_context_length": 4000
  },
  "document_processing": {
    "chunk_size": 800,
    "chunk_overlap": 100,
    "max_chunks_per_doc": 50,
    "supported_formats": ["md", "txt"]
  },
  "performance": {
    "cache_enabled": true,
    "cache_ttl": 3600,
    "max_concurrent_queries": 5
  },
  "safety": {
    "fallback_to_requirement": true,
    "max_retry_attempts": 3,
    "timeout_seconds": 10,
    "error_threshold": 5
  },
  "role_filters": {
    "enabled": true,
    "available_roles": ["company", "developer"],
    "default_role": null
  },
  "logging": {
    "enabled": true,
    "log_level": "INFO",
    "log_queries": true,
    "log_responses": false
  }
}
```

## 2. 更新完整配置

### 请求
```http
PUT /api/admin/knowledge-base/config
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "retrieval": {
    "top_k": 7,
    "similarity_threshold": 0.6,
    "max_context_length": 5000
  },
  "performance": {
    "cache_enabled": true,
    "cache_ttl": 7200,
    "max_concurrent_queries": 8
  }
}
```

### 响应示例
```json
{
  "message": "知识库配置更新成功",
  "success": true
}
```

## 3. 更新检索配置

### 请求
```http
PUT /api/admin/knowledge-base/config/retrieval
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "top_k": 3,
  "similarity_threshold": 0.8,
  "max_context_length": 2000
}
```

### 响应示例
```json
{
  "message": "检索配置更新成功",
  "success": true
}
```

## 4. 更新性能配置

### 请求
```http
PUT /api/admin/knowledge-base/config/performance
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "cache_enabled": false,
  "cache_ttl": 1800,
  "max_concurrent_queries": 3
}
```

### 响应示例
```json
{
  "message": "性能配置更新成功",
  "success": true
}
```

## 5. 获取配置预设

### 请求
```http
GET /api/admin/knowledge-base/config/presets
Authorization: Bearer <admin_token>
```

### 响应示例
```json
{
  "presets": {
    "fast_query": {
      "name": "快速查询",
      "description": "适用于快速响应场景，检索精度较高",
      "config": {
        "retrieval": {
          "top_k": 3,
          "similarity_threshold": 0.8,
          "max_context_length": 2000
        },
        "performance": {
          "cache_enabled": true,
          "cache_ttl": 7200,
          "max_concurrent_queries": 10
        }
      }
    },
    "detailed_consultation": {
      "name": "详细咨询",
      "description": "适用于需要详细回答的咨询场景",
      "config": {
        "retrieval": {
          "top_k": 7,
          "similarity_threshold": 0.6,
          "max_context_length": 5000
        },
        "performance": {
          "cache_enabled": true,
          "cache_ttl": 3600,
          "max_concurrent_queries": 5
        }
      }
    },
    "professional_qa": {
      "name": "专业问答",
      "description": "当前推荐配置，平衡查询质量和响应速度",
      "config": {
        "retrieval": {
          "top_k": 5,
          "similarity_threshold": 0.7,
          "max_context_length": 4000
        },
        "performance": {
          "cache_enabled": true,
          "cache_ttl": 3600,
          "max_concurrent_queries": 5
        }
      }
    },
    "high_precision": {
      "name": "高精度模式",
      "description": "适用于对准确性要求极高的场景",
      "config": {
        "retrieval": {
          "top_k": 3,
          "similarity_threshold": 0.9,
          "max_context_length": 3000
        },
        "performance": {
          "cache_enabled": true,
          "cache_ttl": 1800,
          "max_concurrent_queries": 3
        }
      }
    }
  }
}
```

## 6. 应用配置预设

### 请求
```http
POST /api/admin/knowledge-base/config/apply-preset/fast_query
Authorization: Bearer <admin_token>
```

### 响应示例
```json
{
  "message": "配置预设 'fast_query' 应用成功",
  "success": true
}
```

## 7. 验证配置

### 请求
```http
POST /api/admin/knowledge-base/config/validate
Authorization: Bearer <admin_token>
```

### 响应示例
```json
{
  "valid": true,
  "errors": [],
  "warnings": [],
  "chromadb_connection": "正常"
}
```

### 验证失败示例
```json
{
  "valid": false,
  "errors": [
    "top_k 必须在 1-20 之间",
    "similarity_threshold 必须在 0-1 之间"
  ],
  "warnings": [
    "cache_ttl 建议不少于 300 秒"
  ]
}
```

## 8. 重新加载配置

### 请求
```http
POST /api/admin/knowledge-base/config/reload
Authorization: Bearer <admin_token>
```

### 响应示例
```json
{
  "message": "知识库配置重新加载成功",
  "success": true
}
```

## JavaScript/TypeScript 客户端示例

```typescript
class KnowledgeBaseConfigClient {
  private baseURL = '/api/admin/knowledge-base';
  
  // 获取配置
  async getConfig() {
    const response = await fetch(`${this.baseURL}/config`, {
      headers: {
        'Authorization': `Bearer ${this.getToken()}`,
      }
    });
    return response.json();
  }
  
  // 更新配置
  async updateConfig(config: any) {
    const response = await fetch(`${this.baseURL}/config`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.getToken()}`,
      },
      body: JSON.stringify(config)
    });
    return response.json();
  }
  
  // 应用预设
  async applyPreset(presetName: string) {
    const response = await fetch(`${this.baseURL}/config/apply-preset/${presetName}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.getToken()}`,
      }
    });
    return response.json();
  }
  
  // 验证配置
  async validateConfig() {
    const response = await fetch(`${this.baseURL}/config/validate`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.getToken()}`,
      }
    });
    return response.json();
  }
  
  private getToken(): string {
    // 实现获取认证token的逻辑
    return localStorage.getItem('admin_token') || '';
  }
}

// 使用示例
const configClient = new KnowledgeBaseConfigClient();

// 获取当前配置
const currentConfig = await configClient.getConfig();
console.log('当前配置:', currentConfig);

// 应用快速查询预设
const result = await configClient.applyPreset('fast_query');
console.log('预设应用结果:', result);

// 验证配置
const validation = await configClient.validateConfig();
console.log('配置验证结果:', validation);
```

## 错误处理

### 常见错误码

- **400**: 请求参数错误或配置验证失败
- **401**: 未授权访问
- **404**: 配置预设不存在
- **500**: 服务器内部错误

### 错误响应示例
```json
{
  "detail": "配置验证失败: top_k 必须在 1-20 之间"
}
```

## 最佳实践

1. **配置更新前先验证**: 使用验证API确保配置有效
2. **渐进式调整**: 小幅度调整参数，观察效果
3. **备份重要配置**: 重大更改前记录当前配置
4. **监控性能影响**: 关注配置更改对系统性能的影响
5. **使用预设**: 优先使用经过验证的配置预设
