# 配置系统迁移指南

本文档介绍了如何从旧配置系统迁移到新的模块化配置管理系统。

## 更新时间

2025-08-21

## 1. 新配置系统优势

1. **模块化设计** - 配置按功能模块分离，便于维护和扩展
2. **多层配置** - 支持默认配置、文件配置、环境变量和运行时配置的多层覆盖
3. **Schema验证** - 基于Pydantic的强类型配置验证
4. **配置监控** - 内置指标收集和告警机制
5. **安全保障** - 敏感键检测和密钥管理
6. **来源追踪** - 追踪每个配置项的来源

## 2. 迁移步骤

### 2.1 更新导入语句

旧代码:
```python
from backend.config.unified_config_loader import get_config
```

新代码:
```python
from backend.config.manager import get_config
# 或者使用便捷函数
from backend.config import get_config
```

### 2.2 更新配置键引用

旧配置键:
```yaml
unified_config:
  app:
    name: MyApp
```

新配置键:
```yaml
app:
  name: MyApp
```

### 2.3 使用新的配置管理器

旧方式:
```python
config = get_config()
app_name = config.get('app.name')
```

新方式:
```python
from backend.config import get_config

app_name = get_config('app.name', 'default_name')
```

## 3. 监控和安全功能

新配置系统包含以下新增功能：

### 3.1 配置监控
- 指标API端点: `/config-monitoring/*`
- Prometheus导出支持
- 告警规则配置

### 3.2 安全功能
- 敏感键自动检测
- 环境变量白名单控制
- 密钥管理器集成

## 4. 弃用说明

以下组件和功能已被弃用：

1. `unified_config.defaults.yaml` - 请使用模块化默认配置文件
2. `unified_config_loader` - 请使用新的 `config.manager`
3. `unified_dynamic_config` - 请使用运行时配置功能

## 5. 常见问题

### 5.1 配置键找不到
确保使用正确的配置键路径，并检查配置文件结构。

### 5.2 环境变量不生效
检查配置键是否在白名单中，环境变量格式应为 `AID_CONF__SECTION__KEY`。

### 5.3 Schema验证失败
检查配置值类型是否与Schema定义一致。

## 6. 最佳实践

1. 使用环境变量覆盖敏感或环境相关的配置
2. 为不同环境维护不同的配置文件
3. 定期检查配置监控指标
4. 使用白名单控制可覆盖的配置项
5. 将敏感信息存储在密钥管理器中
