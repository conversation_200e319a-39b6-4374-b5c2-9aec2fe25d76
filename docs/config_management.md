# 配置管理（分文件维护 + 聚合统一视图）目标形态初稿

## 文档目的与读者
- 目的：定义“分文件维护 + 聚合统一视图”的目标配置管理架构，确保扩展性、可靠性、可观测性与可回滚性
- 读者：后端开发、架构师、DevOps、QA
- 范围：配置源组织、聚合优先级、缓存策略、热更新、验证与监控、迁移与回滚、测试与验收

## 核心原则
- 单一门面：所有业务读取统一经 Config Facade，对外呈现单一内存视图
- 分而治之：按领域分文件维护（策略/规则/模板/阈值/LLM 等），职责清晰、评审友好
- 聚合统一：运行时聚合为一份只读快照，原子切换、可回滚
- 观测先行：全链路日志与指标，问题可追踪、行为可解释
- 兼容不破坏：逐步演进，不改变既有 API 与业务语义

## 架构总览
- 分层：访问门面层 → 多级缓存与只读快照 → 聚合器 → 多配置源 → 校验 → 原子切换 → 订阅通知 → 日志/指标
- 对外始终是统一视图；对内支持多源扩展（分文件、环境变量、远程源、默认值）

```mermaid
flowchart LR
  A[业务代码] --> B[Config Facade\nget/section/context/subscribe]
  B --> C[多级缓存\n只读快照]
  C -- miss/失效 --> D[Aggregator 聚合器]
  D --> S1[FileSources\nbusiness_rules.yaml\nstrategies.yaml\nmessage_templates.yaml\nllm.yaml\nthresholds.yaml]
  D --> S2[EnvVars]
  D --> S3[RemoteSource\nHTTP/DB/MQ]
  D --> S4[Defaults]
  D --> V[Schema 校验/类型约束]
  V -->|通过| G[生成新快照(原子切换)]
  V -.失败/降级.-> C
  G --> C
  G --> E[订阅通知]
  B --> M[监控/日志\n命中率/耗时/降级/来源链]
  D --> M
  V --> M
```

## 配置源与目录组织（目标形态）
- 目录结构（建议）：
  - backend/config/
    - defaults/
      - defaults.yaml
    - files/
      - business_rules.yaml   （规则书）
      - strategies.yaml       （大脑/策略）
      - message_templates.yaml（嘴/模板）
      - llm.yaml              （LLM 模型与场景参数）
      - thresholds.yaml       （阈值/限额/性能参数）
    - override/
      - env.example           （环境变量示例）
- 说明：
  - defaults 为最低优先级托底值，纳入版本控制
  - files 为分文件维护主战场，按领域归档
  - 环境变量用于敏感或环境相关参数覆盖（API Key、DB 连接等）
  - 远程源（可选）后期接入，用于在线策略下发/开关

## 聚合与优先级策略
- 优先级（高 → 低）：
  1) Context overrides（请求/会话级覆盖，可选）
  2) 环境变量（ENV）
  3) 远程源（HTTP/DB/MQ）
  4) 分文件（files）
  5) 默认值（defaults）
- 命名空间示例：
  - business_rules.*、strategies.*、message_templates.*、llm.*、thresholds.*、system.*、performance.*、security.*、logging.*
- 冲突与引用：
  - 严格 key 优先级覆盖；跨命名空间引用需通过校验（例如策略引用的规则必须存在）
  - 字段新增默认为向后兼容（warning）；关键字段缺失或类型错误为 hard fail + 回滚

## 访问门面（Config Facade）设计
- 统一接口：
  - get(key, default=None, cast=Type)
  - get_many(keys: list[str]) -> dict
  - section(path) -> 强类型对象（如 llm.section("intent_recognition"））
  - subscribe(pattern|keys, callback) 订阅变更
  - reload(namespaces=? , reason=?) 受控重载（灰度/窗口）
  - dump_effective_config(scope=…) 导出有效配置（调试与追责）
- 作用域覆盖（可选）：
  - with config_context(env/tenant/session, overrides={…})
- 返回值约定：只读数据结构（dict-like / dataclass），避免外部突变
- 兼容性：保持现有 API 不变（get_unified_config / get_config_value / get_message_templates / get_business_rules 等）

## 缓存与快照
- 多级缓存：进程内只读快照（immutable），线程安全；Key 级与命名空间级失效
- 原子切换：新快照构建与校验完成后一次性替换；失败或校验未过则保留“上次健康快照”
- 指标：hit ratio、加载耗时、快照大小、重建次数

## 验证与类型安全
- Schema 驱动（从轻量内置开始，可演进为 Pydantic）
- 关键域强校验：llm、strategies、business_rules、message_templates、thresholds、security
- 引用完整性：策略→规则存在；模板变量可解析；阈值范围合理
- 模式切换：兼容模式（warning）→ 强制模式（error + 回滚）
- 示例校验项：
  - llm.models.* 必含 provider、model_name、timeout、max_tokens、temperature 范围
  - strategies.* 引用的 business_rules.* 必须存在
  - message_templates.* 变量须在上下文能解析

## 热更新与监听
- 推荐分阶段：
  - 开发/测试：文件监听 + 防抖（批量合并）；订阅回调异步
  - 生产：先手动 reload（灰度），稳定后再考虑自动监听
- 订阅粒度：命名空间或 key 前缀（如 strategies.*、llm.default_model）
- 事务性：批次变更聚合为一次快照更新；回调传递版本号

## 日志与可观测性
- 日志：
  - 配置加载与聚合：耗时、来源链路、变更摘要、目标快照版本
  - LLM 调参日志：场景名 + 实际模型名；token 与 provider 信息
  - 错误与回滚：校验失败原因、降级行为
- 指标（写入 logs/performance）：
  - 配置加载耗时、失败/降级次数、缓存命中率、订阅回调时延分位
- 追踪：
  - debug 模式下 get() 可输出“命中缓存/命中源 + 覆盖链路”

## 安全与权限
- 秘钥与敏感信息：禁止硬编码在分文件；仅允许来自环境变量或密钥管理服务
- 访问隔离（可选）：多租户/会话级覆盖需记录审计轨迹
- 远程源：读超时、重试与降级策略；异常不影响主路径

## 迁移路线图（小步快跑，可回滚）
- 阶段0 盘点：
  - 列出现有 unified_config.yaml 的命名空间，映射到分文件结构；标注风险与重复项
- 阶段1 引入聚合器（不动调用）：
  - 聚合器从“单文件 + defaults + env”构建统一视图；门面仅包装
- 阶段2 落地分文件（只读对齐）：
  - 生成 files/*.yaml，与 unified_config.yaml 内容保持同步（对齐脚本）；仍以 unified_config 为源
- 阶段3 切换源（灰度）：
  - 聚合器读 files + defaults + env（unified_config 作为冗余回退）；开启校验兼容模式
- 阶段4 强校验与增量热更：
  - 关键域开启强校验；生产先手动 reload；订阅机制上线
- 阶段5 清理与文档：
  - 关闭统一大文件路径（仅保留回滚开关）；完善文档与培训

## 回滚计划
- 快照回滚：保留前一版本可用快照，一键切回
- 源回滚：切回“单文件 + defaults + env”路径
- 功能回滚：关闭自动监听、关闭远程源，仅保留分文件静态加载

## 测试与验收标准
- 单元测试：
  - 聚合优先级与冲突覆盖；校验错误触发回滚；订阅通知与回调时序；只读快照不可突变
- 集成测试：
  - 分文件修改 → reload → 新快照生效；缓存命中率与加载耗时可观测
- 回归测试：
  - 旧 API 行为不变；关键场景（DecisionEngine 策略、业务规则引用、模板变量）一致
- 验收门槛：
  - 生产先手动 reload，连续 7 天无回滚事件方可考虑开启自动监听

## 风险清单与缓解
- 分文件与统一视图不同步：对齐脚本 + 校验报告 + 构建阻断
- 引用不一致（策略→规则、模板变量）：Schema 引用校验 + 构建期检测
- 热更导致不一致：防抖合并 + 原子切换 + 健康快照回退
- 多团队修改冲突：命名空间所有权与评审责任制；准入清单

## 命名规范与示例
- 键命名：模块.子域.字段（如 llm.default.temperature；decision_engine.strategies.rank_top_k）
- 示例（简化）：
  - files/strategies.yaml：strategies.intent_recognition.matchers/weights/thresholds
  - files/business_rules.yaml：business_rules.action_handlers.handler_classes
  - files/message_templates.yaml：message_templates.greeting.basic / error.technical_issue
  - files/llm.yaml：llm.default_model / llm.models.* / llm.scenarios.*
  - files/thresholds.yaml：thresholds.business.* / thresholds.confidence.* / thresholds.performance.*

## 与既有系统的契合点
- DecisionEngine 单一决策源：以 strategies.yaml 为“脑”，business_rules.yaml 为“规则书”，message_templates.yaml 为“嘴”
- 三层识别机制：权重/阈值放入 thresholds.yaml 或 strategies.yaml 子域，聚合后统一可读
- 日志偏好：在 llm.scenarios 校验和 get() 输出中打印“场景名 + 实际模型名”；性能指标写入 logs/performance

---

附注：本初稿仅为目标形态设计说明，不包含代码改动与行为切换。任何实施改造将遵循“小步快跑、可观测、可回滚”的原则，并在发布前进行严格的测试与灰度。
