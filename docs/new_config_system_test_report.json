{"test_time": "2025-08-18T19:32:03.798215", "config_system": "modular_enabled", "summary": {"basic_config_success_rate": 1.0, "component_success_rate": 0.25, "error_handling_rate": 1.0, "scenario_success_rate": 0.0, "overall_health": "healthy"}, "detailed_results": {"basic_config_access": {"业务规则": {"success": true, "result_type": "dict", "result_size": 14804, "response_time": 3.814697265625e-06}, "LLM配置": {"success": true, "result_type": "dict", "result_size": 138, "response_time": 5.7220458984375e-06}, "消息模板": {"success": true, "result_type": "dict", "result_size": 19511, "response_time": 2.1457672119140625e-06}, "配置值访问": {"success": true, "result_type": "float", "result_size": 3, "response_time": 2.86102294921875e-06}, "消息模板访问": {"success": true, "result_type": "str", "result_size": 52, "response_time": 3.0994415283203125e-06}}, "component_tests": [{"name": "决策引擎", "success": false, "details": "'UnifiedDecisionEngine' object has no attribute 'process_user_input'"}, {"name": "配置服务", "success": true, "details": "LLM配置: 7 项"}, {"name": "LLM客户端工厂", "success": false, "details": "The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable"}, {"name": "关键词加速器", "success": false, "details": "KeywordAccelerator.match() missing 1 required positional argument: 'context'"}], "performance_metrics": {"获取业务规则": {"avg_time": 4.291534423828125e-07, "ops_per_second": 2330168.888888889}, "获取LLM配置": {"avg_time": 9.918212890625e-07, "ops_per_second": 1008246.1538461538}, "获取消息模板": {"avg_time": 4.1961669921875e-07, "ops_per_second": 2383127.272727273}, "获取配置值": {"avg_time": 7.796287536621094e-07, "ops_per_second": 1282661.7737003057}}, "performance_stats": {"uptime": 0.8013348579406738, "access_count": 974, "cache_hit_rate": 0.4353182751540041, "reload_count": 0, "error_count": 0, "error_rate": 0.0, "enabled": true, "sources_count": 13, "cache_size": 4}, "error_handling": {"不存在的配置": {"success": true, "result": "default", "handled_gracefully": true}, "空配置键": {"success": true, "result": "default", "handled_gracefully": true}, "None默认值": {"success": true, "result": null, "handled_gracefully": true}, "错误的模板路径": {"success": true, "result": "default", "handled_gracefully": true}}, "real_world_scenarios": {"用户问候": {"success": false, "error": "'UnifiedDecisionEngine' object has no attribute 'process_user_input'"}, "系统能力查询": {"success": false, "error": "'UnifiedDecisionEngine' object has no attribute 'process_user_input'"}, "需求收集": {"success": false, "error": "'UnifiedDecisionEngine' object has no attribute 'process_user_input'"}, "确认信息": {"success": false, "error": "'UnifiedDecisionEngine' object has no attribute 'process_user_input'"}}}, "recommendations": ["部分系统组件存在问题，需要检查"]}