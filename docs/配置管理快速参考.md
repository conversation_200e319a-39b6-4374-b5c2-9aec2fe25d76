# 配置管理快速参考卡片

**用途**: 配置管理架构升级的快速查询和操作指南  
**更新时间**: 2025-08-20  

---

## 🚀 快速状态检查

### 检查当前配置系统状态
```bash
# 验证模块化配置是否正常
python scripts/verify_modular_config_migration.py

# 检查后端服务状态
curl http://localhost:8000/health

# 查看配置预加载日志
tail -f logs/app.log | grep "配置预加载"
```

### 关键指标
- ✅ **配置预加载成功率**: 应为 100% (7/7)
- ✅ **核心服务迁移**: 应为 100% (5/5)  
- ⚠️ **全量文件迁移**: 当前 7.6% (5/66)

---

## 🔧 常用操作

### 获取配置值
```python
# 新方式 (推荐)
from backend.config.service import config_service

# 获取业务规则
max_attempts = config_service.get_business_rule("retry.max_attempts", 3)

# 获取LLM配置  
llm_config = config_service.get_llm_config("default")

# 获取消息模板
template = config_service.get_message_template("greeting.basic")
```

### 配置文件位置
```
backend/config/
├── business/rules.yaml      # 业务规则
├── business/templates.yaml  # 消息模板
├── business/thresholds.yaml # 阈值配置
├── llm/models.yaml         # LLM模型配置
├── data/knowledge_base.yaml # 知识库配置
└── system/base.yaml        # 系统基础配置
```

---

## ⚠️ 已知问题和解决方案

### 问题1: 托底配置缺失
**现象**: 配置文件损坏时系统异常  
**临时解决**: 恢复备份文件
```bash
cp backend/config/unified_config.defaults.yaml.backup backend/config/unified_config.defaults.yaml
```

### 问题2: 环境变量不生效
**现象**: 无法通过环境变量覆盖配置  
**临时解决**: 直接修改配置文件
```bash
# 修改对应的 .yaml 文件
vim backend/config/business/rules.yaml
```

### 问题3: 旧代码报错
**现象**: `get_unified_config` 未定义  
**解决**: 更新导入语句
```python
# 旧代码
from backend.config.unified_config_loader import get_unified_config

# 新代码  
from backend.config.modular_loader import get_modular_config_loader
```

---

## 🆘 紧急处理

### 系统无法启动
1. **检查配置文件**:
   ```bash
   ls -la backend/config/business/
   ls -la backend/config/llm/
   ```

2. **恢复备份**:
   ```bash
   cp backend/config/unified_config.yaml.backup backend/config/unified_config.yaml
   cp backend/config/unified_config.defaults.yaml.backup backend/config/unified_config.defaults.yaml
   ```

3. **重启服务**:
   ```bash
   pkill -f "python run_api.py"
   python run_api.py
   ```

### 配置获取失败
1. **检查配置键名**:
   ```python
   # 确保使用正确的键名格式
   config_service.get_config("business.rules.retry.max_attempts", 3)
   ```

2. **检查文件权限**:
   ```bash
   chmod 644 backend/config/business/*.yaml
   ```

3. **清除缓存**:
   ```python
   from backend.config.service import config_service
   config_service.reload_all()
   ```

---

## 📞 联系信息

### 紧急联系
- **系统管理员**: [待填写]
- **配置负责人**: [待填写]
- **值班电话**: [待填写]

### 相关文档
- [配置管理架构升级报告](./配置管理架构升级报告.md)
- [配置管理技术细节](./配置管理技术细节.md)
- [配置管理最佳实践](../.augment/rules/配置管理最佳实践.md)

---

## 🔍 故障排查清单

### 启动问题
- [ ] 检查Python环境和依赖
- [ ] 检查配置文件是否存在
- [ ] 检查配置文件语法是否正确
- [ ] 检查文件权限
- [ ] 查看启动日志

### 配置问题
- [ ] 验证配置键名格式
- [ ] 检查配置文件内容
- [ ] 验证配置数据类型
- [ ] 检查配置缓存状态
- [ ] 测试配置获取接口

### 性能问题
- [ ] 检查配置缓存命中率
- [ ] 监控配置加载时间
- [ ] 检查内存使用情况
- [ ] 分析配置访问频率
- [ ] 优化配置预加载

---

## 🎯 系统工程要点提醒

### 变更前必问的问题
1. **全局影响**: 这个变更会影响哪些其他组件？
2. **依赖关系**: 有哪些组件依赖于要修改的部分？
3. **容错机制**: 如果这个组件失败，系统如何降级？
4. **回滚方案**: 如何快速安全地回滚这个变更？
5. **监控覆盖**: 如何知道变更是否成功？

### 系统思维检查点
```
变更评估框架:
├── 直接影响: 立即受影响的组件
├── 间接影响: 依赖链上的组件
├── 系统影响: 整体稳定性和性能
└── 业务影响: 用户体验和业务流程
```

### 渐进式变更原则
1. **并行部署**: 新旧系统并存，不影响现有功能
2. **灰度验证**: 小范围验证，确认无问题后扩大范围
3. **实时监控**: 关键指标实时监控，异常立即回滚
4. **快速回滚**: 任何阶段都能快速安全回滚

### 完整性验证要求
- **功能完整**: 所有原有功能正常 + 新功能按预期工作
- **性能完整**: 响应时间、吞吐量、资源使用在合理范围
- **稳定性完整**: 启动稳定、长期运行稳定、异常恢复正常
- **可观测性完整**: 日志、监控、告警、故障排查完备

---

**快速参考版本**: v1.1
**适用系统版本**: 配置管理架构升级后
**更新频率**: 随系统变更更新
**系统工程要点**: 基于配置管理升级经验教训总结
