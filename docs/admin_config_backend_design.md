# 后台管理（配置管理）调整设计（初稿）

## 1. 背景与目标
- 背景：配置体系将演进为“分文件维护 + 聚合统一视图”，需要管理后台承担分文件治理、聚合状态可观测、变更闭环与回滚控制。
- 目标：
  - 可观测：直观看到“统一内存快照”的版本、来源、健康与指标
  - 治理：按命名空间/文件管理，确保变更有轨迹、可审计、可回滚
  - 安全：敏感信息不泄露，权限精确控制
  - 稳定：优先只读与受控操作，编辑发布走审批闭环
- 非目标（本阶段）：不引入远程配置源写入；生产默认不开放自动监听与直接编辑

## 2. 范围
- 页面与交互：配置总览、命名空间浏览、文件治理、对齐报告、健康与性能、运维操作、审批中心、审计日志、设置（RBAC）
- 后台 API（只读+受控）：/config/status、/config/snapshot/meta、/config/files/*、/config/diff/report、/config/metrics、/config/reload（受控）
- 工作流：草稿→预校验→审批→合并→对齐→灰度→上线→回滚

## 3. 信息架构（IA）
- 配置总览（Dashboard）
  - 当前快照：版本号、构建时间、大小、来源摘要（defaults/files/env）
  - 架构信息：architecture.name/version/support、loading_strategy
- 命名空间浏览（Explorer）
  - 树状导航 + 路径搜索（如 llm.scenarios.*）
  - 右侧键值预览（敏感字段脱敏）、来源链/覆盖链（defaults→files→env）
- 文件治理（Files）
  - files/* 列表：大小、mtime、Owner、最近提交人、提交次数
  - 查看文件内容（只读，长文件分页）
- 对齐报告（Alignment）
  - 单文件视图 vs 分文件视图的 Diff 摘要；可下载详细报告
- 健康与性能（Health & Performance）
  - 加载耗时分位、失败/降级率、缓存命中率、订阅回调时延、回滚事件时间线
- 运维操作（Ops）
  - 手动 reload（Dev/Staging；Prod 默认关闭，需二次确认+审计）
  - 快照查看/下载（只读 JSON）、标记“健康快照”
- 审批中心（Approvals）
  - 草稿列表、预校验结果、审批流、合并状态
- 审计日志（Audit）
  - 谁/何时/改了什么/为何改；外加对运维操作（reload/回滚）的记录
- 设置（Settings）
  - 角色/权限（RBAC）、命名空间所有权、敏感字段策略

## 4. 权限与安全（RBAC）
- 角色：
  - Viewer（只读）：查看所有页面与指标
  - Operator（运维）：可在 Dev/Staging 执行 reload/下载快照
  - Maintainer（维护者）：可发起配置草稿、查看校验结果、参与审批
  - Admin（管理员）：审批、RBAC 配置、紧急回滚批准
- 安全：
  - 敏感字段（如 api_key）脱敏展示（******），ENV 映射元信息仅 Admin 可见
  - 所有操作写入审计日志；下载动作需理由与票据号

## 5. 后台 API（MVP）
- GET /api/config/status
  - 返回：sections、loading_strategy、architecture、sources 状态
- GET /api/config/snapshot/meta
  - 返回：version、built_at、size、source_summary
- GET /api/config/files/list
  - 返回：path、size、mtime、owner、last_commit_author、changes_count
- GET /api/config/files/content?path=files/strategies.yaml
  - 返回：文件内容（只读，敏感字段已脱敏/剔除）
- GET /api/config/diff/report
  - 返回：单文件视图 vs 分文件视图 diff 摘要（keys_added/removed/changed）
- GET /api/config/metrics
  - 返回：load_time_ms{p50,p95,p99}、error_rate、degrade_rate、cache_hit_ratio、callback_latency_ms、rollback_events
- POST /api/config/reload
  - 入参：namespaces[]=…，reason，env
  - 说明：受 RBAC 控制、Prod 默认禁用；审计必填

## 6. 工作流（变更闭环）
- 草稿（Draft）
  - 允许在 Dev/独立分支进行编辑；后台仅展示草稿与差异摘要
- 预校验（Pre-Validate）
  - Schema + 引用完整性（策略→规则、模板变量、LLM 参数范围）
  - 不通过不给进入审批阶段
- 审批（Approve）
  - CODEOWNERS 对应命名空间所有者审批；至少 2 人制
- 合并（Merge）
  - 合入主分支；触发对齐脚本与报告
- 灰度（Gray）
  - Staging 开启 config.aggregation.enabled，观察指标
- 上线（Release）
  - 生产按窗口发布；默认手动 reload；观测 7 天
- 回滚（Rollback）
  - 基于“上次健康快照”与“单文件路径”一键回切

## 7. 观测与指标
- 指标：加载耗时、失败/降级率、缓存命中率、订阅回调时延、回滚事件
- 日志：加载/聚合/校验/回滚的关键日志；LLM 场景名+实际模型名
- 看板：按环境（Dev/Staging/Prod）维度展示

## 8. 数据模型（后台域内）
- SnapshotMeta：{version, built_at, size, sources:{defaults,files,env}, health}
- DiffReport：{summary:{added,removed,changed}, samples:[{path,before,after}]}
- ApprovalRecord：{id, ns, author, approvers, status, created_at, decided_at, notes}
- AuditLog：{id, actor, action, resource, env, timestamp, details}

## 9. 错误处理与降级
- 配置读取失败：保留“上次健康快照”；后台提示降级状态
- 指标异常：告警阈值触发，自动降级到只读模式（禁止 reload）
- API 限流：防止频繁 reload 或大文件拉取导致拥塞

## 10. 性能与容量
- 列表分页、内容惰性加载；大 YAML 分段渲染
- 服务端内容截断上限（例如 1MB/请求），支持下载打包（仅 Operator/Admin）

## 11. 风险与缓解
- 人为误操作 → RBAC + 二次确认 + 审计 + 回滚演练
- 敏感信息泄露 → 脱敏、最小可见原则、下载审计
- 多团队冲突 → 命名空间所有权与审批制；提交模板

## 12. 验收清单（后台侧）
- 只读可观测：
  - [ ] 能查看快照与架构信息、命名空间与文件、对齐报告
  - [ ] 指标看板可用，数据与日志一致
- 安全：
  - [ ] 敏感脱敏与 RBAC 生效；所有操作有审计
- 运维：
  - [ ] Dev/Staging 可受控 reload；Prod 默认关闭
- 对齐与回归：
  - [ ] 对齐报告正确；关键业务数据一致
- 性能：
  - [ ] 页面 p95 < 500ms；搜索 p95 < 800ms（示例）
- 回滚：
  - [ ] 快照查看/下载；回滚演练（Staging）通过

## 13. 与执行文档（docs/config_management_architecture.md）的对齐
- 在“第6节 实施计划”与“第7节 验收清单”中增加后台对应检查项：
  - 阶段2：只读 API 就绪
  - 阶段3：对齐报告接入后台
  - 阶段4：Staging 指标看板上线
  - 阶段5：生产灰度期间后台只读观测、手动 reload 受控
  - 阶段6：回滚演练记录归档

## 14. 实施计划（后台侧）
- 阶段A（只读可观测，1-2 周）
  - API：status/snapshot/files/diff/metrics
  - 页面：总览/命名空间/文件/对齐/指标
- 阶段B（受控操作，1 周）
  - API：reload（Dev/Staging）+ 快照下载
  - 权限：RBAC 与审计日志
- 阶段C（编辑闭环，2-3 周，灰度）
  - 草稿/预校验/审批/合并视图；与 CI 的对齐脚本与校验集成

## 15. 附录
- 遗漏排查清单（Omissions Checklist）
  - [ ] 命名空间所有权是否明确？
  - [ ] 敏感字段策略是否到位？
  - [ ] 指标阈值是否固化并告警？
  - [ ] 回滚演练是否计划与记录？
  - [ ] 审批人与值班人是否指定？
  - [ ] Dev/Staging/Prod 的权限边界是否明确？
- Go/No-Go 清单（后台）
  - [ ] 只读能力完整、RBAC 生效、审计无漏项
  - [ ] 指标与看板上线，告警已联调
  - [ ] 受控 reload 在 Staging 演练通过
  - [ ] 回滚预案可行，演练记录归档

---

## 16. 重要备忘：避免多ChromaDB数据库文件冲突

### 问题回顾（2025-08-19）
在RAG知识库排查中发现了多个ChromaDB数据库文件：
- `./admin-backend/backend/data/chroma_db/chroma.sqlite3`
- `./backend/data/chroma_db/chroma.sqlite3`
- 多个备份文件

这导致了：
1. 代码可能连接到错误的数据库
2. 数据不一致
3. 难以排查问题
4. "no such column: collections.topic" 错误（因连接到旧版数据库）

### 管理后台设计时的关键考虑

#### 1. 统一数据库路径策略
```yaml
# 推荐配置结构
knowledge_base:
  chroma_db:
    # 使用绝对路径或明确的相对路径基准
    path: "/var/lib/youji/chroma_db"  # 生产环境
    # 或者基于项目根的相对路径
    path: "data/shared/chroma_db"     # 开发环境
```

#### 2. 管理后台集成方案
**选项A：共享数据库（推荐）**
- 管理后台和主应用使用同一个ChromaDB实例
- 通过API接口访问，而不是直接数据库连接
- 避免并发写入冲突

**选项B：服务化架构**
- ChromaDB作为独立服务运行
- 主应用和管理后台都通过HTTP API访问
- 最佳的解耦方案

#### 3. 部署检查清单
- [ ] 确认只有一个ChromaDB数据库文件
- [ ] 环境变量YOUJI_CHROMA_DB_PATH已设置
- [ ] 启动时记录数据库路径到日志
- [ ] 监控数据库文件数量和大小

#### 4. 风险缓解
- **配置统一**：所有组件使用相同的配置源
- **环境隔离**：开发、测试、生产环境明确分离
- **启动检查**：检测并警告多数据库文件存在
- **监控告警**：数据库连接状态和文件完整性监控

### 实施优先级
1. **高优先级**：统一配置管理，确保所有组件使用同一路径
2. **中优先级**：添加启动检查，防止多实例创建
3. **低优先级**：完善监控和告警机制

---
注：本设计为管理后台的产品与接口层说明，不涉及代码改动。与“唯一执行文档”配套使用，确保阶段性推进与验收。
