# 代码清理工具使用指南

## 工具概述
本指南提供了使用AI助手进行代码清理分析的具体操作方法和最佳实践。

## 使用场景
- 项目重构后的代码清理
- 定期维护和优化
- 迁移过程中的冗余代码识别
- 新团队成员了解代码结构

## 标准使用流程

### 1. 准备阶段
```bash
# 确保项目处于干净状态
git status
git stash  # 如果有未提交的更改

# 创建分析分支
git checkout -b code-cleanup-analysis
```

### 2. 执行分析命令
向AI助手发送标准化请求：

```
请分析 {目录路径} 下的Python文件，检查是否存在可以清理的文件。

按照代码清理分析规范执行以下步骤：
1. 列出所有.py文件
2. 分析每个文件的类和函数定义
3. 在整个项目中搜索引用
4. 特别检查间接引用（配置文件、字符串引用、动态导入）
5. 按照标准格式输出结果

目标目录: {具体路径}
项目类型: {FastAPI/Django/Flask/其他}
特殊要求: {如有特殊框架或模式需要注意}
```

### 3. 结果验证流程

#### 3.1 自动验证
```bash
# 对于标记为"未使用"的文件，尝试删除并运行测试
mkdir -p backup/unused_files
mv {unused_file} backup/unused_files/

# 运行完整测试套件
python -m pytest tests/
python -m pytest backend/tests/

# 运行应用启动测试
python run_api.py --test-mode
```

#### 3.2 手动验证检查清单
- [ ] 检查配置文件中是否有字符串引用
- [ ] 检查文档和README中的示例代码
- [ ] 检查CI/CD脚本中的引用
- [ ] 检查Docker文件和部署脚本
- [ ] 检查数据库迁移脚本
- [ ] 检查定时任务和后台作业

## 常见模式识别

### 1. 工厂模式检查
```python
# 检查这类模式的使用
handlers = {
    'type1': 'module.handler1.Handler1',
    'type2': 'module.handler2.Handler2'
}

# 搜索关键词
grep -r "handler1\|Handler1" --include="*.py" .
grep -r "handler1\|Handler1" --include="*.yaml" .
grep -r "handler1\|Handler1" --include="*.json" .
```

### 2. 插件系统检查
```python
# 检查插件注册
@register_plugin('plugin_name')
class MyPlugin:
    pass

# 搜索插件引用
grep -r "plugin_name" --include="*.py" .
grep -r "MyPlugin" --include="*.py" .
```

### 3. API路由检查
```python
# FastAPI路由
@app.get("/api/endpoint")
def handler():
    pass

# 检查路由使用
grep -r "/api/endpoint" .
grep -r "handler" --include="*.py" .
```

## 特殊文件处理规则

### 1. 测试文件
```
规则: test_*.py 文件只有在对应的被测试文件也被删除时才能删除
检查: 
- test_user_service.py 对应 user_service.py
- 如果 user_service.py 被删除，test_user_service.py 也应删除
- 如果 user_service.py 保留，test_user_service.py 必须保留
```

### 2. 初始化文件
```
规则: __init__.py 文件的删除需要特别谨慎
检查:
- 是否有其他模块导入该包
- 是否在 setup.py 或 pyproject.toml 中被引用
- 是否是命名空间包的一部分
```

### 3. 配置和常量文件
```
规则: config.py, constants.py, settings.py 等文件通常不应删除
检查:
- 即使没有直接导入，也可能通过环境变量或配置系统使用
- 检查应用启动代码和配置加载逻辑
```

## 错误预防

### 1. 常见误判情况
- **动态导入**: `importlib.import_module(module_name)`
- **字符串引用**: 配置文件中的类名或函数名
- **反射调用**: `getattr(module, function_name)`
- **装饰器注册**: 通过装饰器自动注册的类或函数
- **框架约定**: 按命名约定自动发现的模块

### 2. 安全检查命令
```bash
# 检查字符串引用
grep -r "ClassName\|function_name" --include="*.yaml" .
grep -r "ClassName\|function_name" --include="*.json" .
grep -r "ClassName\|function_name" --include="*.toml" .

# 检查动态导入
grep -r "import_module\|__import__" --include="*.py" .
grep -r "getattr\|hasattr" --include="*.py" .

# 检查配置文件
find . -name "*.env*" -exec grep -l "module_name" {} \;
find . -name "config*" -exec grep -l "ClassName" {} \;
```

## 清理执行

### 1. 分批清理策略
```
第一批: 明确未使用的工具类和辅助函数
第二批: 废弃的业务逻辑模块
第三批: 旧版本的兼容性代码
第四批: 未完成的实验性功能
```

### 2. 清理后验证
```bash
# 运行完整测试
python -m pytest tests/ -v

# 检查导入错误
python -c "import backend; print('Import successful')"

# 运行应用
python run_api.py --check-config

# 检查静态分析
flake8 backend/
mypy backend/
```

## 回滚计划

### 1. 备份策略
```bash
# 创建清理前的完整备份
git tag cleanup-before-$(date +%Y%m%d-%H%M%S)
tar -czf backup/code-cleanup-$(date +%Y%m%d).tar.gz backend/

# 记录删除的文件
echo "$(date): Deleted files:" >> cleanup.log
ls -la backup/unused_files/ >> cleanup.log
```

### 2. 快速恢复
```bash
# 恢复单个文件
cp backup/unused_files/filename.py backend/original/path/

# 恢复所有文件
git checkout HEAD~1 -- backend/

# 完全回滚
git reset --hard cleanup-before-{timestamp}
```

## 最佳实践总结

1. **保守原则**: 宁可保留可疑文件，也不要误删重要代码
2. **渐进清理**: 分批次进行，每次清理后充分测试
3. **文档记录**: 详细记录清理过程和决策依据
4. **团队协作**: 清理前与团队成员确认，避免影响他人工作
5. **持续监控**: 清理后持续关注系统稳定性和功能完整性
