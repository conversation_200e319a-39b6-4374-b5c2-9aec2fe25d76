"""
启动FastAPI服务器的脚本(统一入口)

用于启动需求采集系统的API服务器，使用api/main.py作为统一入口。
提供与前端交互的完整接口，集成所有功能模块。
"""

import os
import sys

# 🔧 修复：在任何导入之前设置ChromaDB环境变量
os.environ['ANONYMIZED_TELEMETRY'] = 'False'
os.environ['CHROMA_TELEMETRY_DISABLED'] = 'True'

import uvicorn

def main():
    """主函数，启动FastAPI服务器"""
    host = "0.0.0.0"
    port = 8000
    reload = True
    
    print("========================================")
    print("🚀 启动需求采集系统API服务器 (统一入口)")
    print("========================================")
    print(f"✨ 服务器地址: http://{host}:{port}")
    print(f"🔄 代码重载: {'🟢 启用' if reload else '🔴 禁用'}")
    print(f"📊 ChromaDB遥测: 🚫 禁用")
    print("----------------------------------------")
    
    # 设置环境变量
    # os.environ["PYTHONPATH"] = os.path.dirname(os.path.abspath(__file__))
    
    # 启动FastAPI服务器
    uvicorn.run(
        "backend.api.main:app",
        host=host,
        port=port,
        reload=reload,
        # 可以在这里添加uvicorn的日志配置，如果需要的话
        # log_level="debug" # 示例: 设置uvicorn日志级别
    )
    
    print("----------------------------------------")
    print("🛑 服务器已停止")
    print("========================================")

if __name__ == "__main__":
    main()
