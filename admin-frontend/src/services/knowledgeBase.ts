/**
 * 知识库管理服务
 * 
 * 提供知识库管理相关的API调用功能
 */

import { request } from '../utils/request';

// 类型定义
export interface OverviewStats {
  total_documents: number;
  unique_documents: number;
  role_types: number;
  categories: number;
  role_distribution: Record<string, number>;
  category_distribution: Record<string, number>;
}

export interface Document {
  id: string;
  content: string;
  doc_id: string;
  title: string;
  role: string;
  category: string;
  source_path: string;
  chunk_index: number;
  content_preview: string;
}

export interface DocumentListResponse {
  documents: Document[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface QueryRequest {
  query_text: string;
  top_k?: number;
  similarity_threshold?: number;
  role_filter?: string;
}

export interface QueryResult {
  rank: number;
  content: string;
  content_preview: string;
  similarity_score: number;
  metadata: {
    title: string;
    role: string;
    category: string;
    source_path: string;
    doc_id: string;
  };
}

export interface QueryResponse {
  query: string;
  results: QueryResult[];
  total_results: number;
  parameters: {
    top_k: number;
    similarity_threshold: number;
    role_filter?: string;
  };
}

export interface StatisticsData {
  total_documents: number;
  content_length_stats: {
    mean: number;
    max: number;
    min: number;
    std: number;
  };
  content_length_distribution: Record<string, number>;
  role_statistics?: Record<string, number>;
  category_statistics?: Record<string, number>;
  role_category_matrix?: Record<string, Record<string, number>>;
}

export interface SystemStatus {
  chromadb_status: string;
  collection_info: Record<string, any>;
  config_status: Record<string, any>;
  document_count: number;
  components: Record<string, boolean>;
}

export interface AvailableFilters {
  roles: string[];
  categories: string[];
}

// API服务类
class KnowledgeBaseService {
  private baseURL = '/api/admin/knowledge-base';

  /**
   * 获取知识库概览统计
   */
  async getOverviewStats(): Promise<OverviewStats> {
    return request.get(`${this.baseURL}/overview`);
  }

  /**
   * 获取文档列表
   */
  async getDocuments(params: {
    role?: string;
    category?: string;
    search_term?: string;
    page?: number;
    limit?: number;
  } = {}): Promise<DocumentListResponse> {
    // 构建查询字符串
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, String(value));
      }
    });
    const queryString = queryParams.toString();
    const url = queryString ? `${this.baseURL}/documents?${queryString}` : `${this.baseURL}/documents`;

    return request.get(url);
  }

  /**
   * 查询知识库
   */
  async queryKnowledgeBase(data: QueryRequest): Promise<QueryResponse> {
    return request.post(`${this.baseURL}/query`, data);
  }

  /**
   * 获取统计分析数据
   */
  async getStatistics(): Promise<{ success: boolean; data: StatisticsData }> {
    return request.get(`${this.baseURL}/statistics`);
  }

  /**
   * 获取系统状态
   */
  async getSystemStatus(): Promise<SystemStatus> {
    return request.get(`${this.baseURL}/status`);
  }

  /**
   * 获取可用筛选选项
   */
  async getAvailableFilters(): Promise<{ success: boolean; data: AvailableFilters }> {
    return request.get(`${this.baseURL}/filters`);
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    status: string;
    service: string;
    details?: SystemStatus;
    error?: string;
  }> {
    return request.get(`${this.baseURL}/health`);
  }

  // ============================================================================
  // 知识库配置管理方法
  // ============================================================================

  /**
   * 获取知识库配置
   */
  async getConfig(): Promise<any> {
    return request.get(`${this.baseURL}/config`);
  }

  /**
   * 更新知识库配置
   */
  async updateConfig(config: any): Promise<{ message: string; success: boolean }> {
    return request.put(`${this.baseURL}/config`, config);
  }

  /**
   * 更新检索配置
   */
  async updateRetrievalConfig(config: {
    top_k?: number;
    similarity_threshold?: number;
    max_context_length?: number;
  }): Promise<{ message: string; success: boolean }> {
    return request.put(`${this.baseURL}/config/retrieval`, config);
  }

  /**
   * 更新性能配置
   */
  async updatePerformanceConfig(config: {
    cache_enabled?: boolean;
    cache_ttl?: number;
    max_concurrent_queries?: number;
  }): Promise<{ message: string; success: boolean }> {
    return request.put(`${this.baseURL}/config/performance`, config);
  }

  /**
   * 重新加载配置
   */
  async reloadConfig(): Promise<{ message: string; success: boolean }> {
    return request.post(`${this.baseURL}/config/reload`);
  }

  /**
   * 验证配置
   */
  async validateConfig(): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
    chromadb_connection?: string;
  }> {
    return request.post(`${this.baseURL}/config/validate`);
  }

  /**
   * 获取配置预设
   */
  async getConfigPresets(): Promise<{
    presets: Record<string, {
      name: string;
      description: string;
      config: any;
    }>;
  }> {
    return request.get(`${this.baseURL}/config/presets`);
  }

  /**
   * 应用配置预设
   */
  async applyConfigPreset(presetName: string): Promise<{ message: string; success: boolean }> {
    return request.post(`${this.baseURL}/config/apply-preset/${presetName}`);
  }
}

// 导出服务实例
export const knowledgeBaseService = new KnowledgeBaseService();

// 默认导出
export default knowledgeBaseService;
