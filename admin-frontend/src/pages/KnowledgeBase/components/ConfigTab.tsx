import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  InputNumber,
  Switch,
  Button,
  message,
  Spin,
  Row,
  Col,
  Select,
  Space,
  Alert,
  Tooltip,
  Modal
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { knowledgeBaseService } from '../../../services/knowledgeBase';

const { Option } = Select;
const { confirm } = Modal;

interface ConfigData {
  enabled: boolean;
  retrieval: {
    top_k: number;
    similarity_threshold: number;
    max_context_length: number;
  };
  document_processing: {
    chunk_size: number;
    chunk_overlap: number;
    max_chunks_per_doc: number;
  };
  performance: {
    cache_enabled: boolean;
    cache_ttl: number;
    max_concurrent_queries: number;
  };
  safety: {
    fallback_to_requirement: boolean;
    max_retry_attempts: number;
    timeout_seconds: number;
    error_threshold: number;
  };
  role_filters: {
    enabled: boolean;
    available_roles: string[];
    default_role: string | null;
  };
  logging: {
    enabled: boolean;
    log_level: string;
    log_queries: boolean;
    log_responses: boolean;
  };
}

interface ConfigPreset {
  name: string;
  description: string;
  config: any;
}

const ConfigTab: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [validating, setValidating] = useState(false);
  const [configData, setConfigData] = useState<ConfigData | null>(null);
  const [presets, setPresets] = useState<Record<string, ConfigPreset>>({});
  const [validationResult, setValidationResult] = useState<any>(null);

  // 加载配置数据
  const loadConfig = async () => {
    setLoading(true);
    try {
      const config = await knowledgeBaseService.getConfig();
      setConfigData(config);
      // 确保配置数据存在后再设置表单值
      if (config) {
        form.setFieldsValue(config);
      }
    } catch (error) {
      console.error('加载配置失败:', error);
      message.error('加载配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载配置预设
  const loadPresets = async () => {
    try {
      const response = await knowledgeBaseService.getConfigPresets();
      setPresets(response.presets || {});
    } catch (error) {
      console.error('加载配置预设失败:', error);
    }
  };

  // 保存配置
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setSaving(true);
      
      await knowledgeBaseService.updateConfig(values);
      message.success('配置保存成功');
      await loadConfig();
    } catch (error) {
      console.error('保存配置失败:', error);
      message.error('保存配置失败');
    } finally {
      setSaving(false);
    }
  };

  // 重新加载配置
  const handleReload = async () => {
    try {
      setLoading(true);
      await knowledgeBaseService.reloadConfig();
      message.success('配置重新加载成功');
      await loadConfig();
    } catch (error) {
      console.error('重新加载配置失败:', error);
      message.error('重新加载配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 验证配置
  const handleValidate = async () => {
    try {
      setValidating(true);
      const result = await knowledgeBaseService.validateConfig();
      setValidationResult(result);
      
      if (result.valid) {
        message.success('配置验证通过');
      } else {
        message.warning('配置验证发现问题');
      }
    } catch (error) {
      console.error('配置验证失败:', error);
      message.error('配置验证失败');
    } finally {
      setValidating(false);
    }
  };

  // 应用预设配置
  const handleApplyPreset = (presetName: string) => {
    const preset = presets[presetName];
    if (!preset) return;

    confirm({
      title: '应用配置预设',
      content: `确定要应用 "${preset.name}" 配置预设吗？这将覆盖当前配置。`,
      onOk: async () => {
        try {
          setSaving(true);
          await knowledgeBaseService.applyConfigPreset(presetName);
          message.success(`配置预设 "${preset.name}" 应用成功`);
          await loadConfig();
        } catch (error) {
          console.error('应用配置预设失败:', error);
          message.error('应用配置预设失败');
        } finally {
          setSaving(false);
        }
      }
    });
  };

  useEffect(() => {
    loadConfig();
    loadPresets();
  }, []);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载配置中...</div>
      </div>
    );
  }

  return (
    <div>
      {/* 操作栏 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <SettingOutlined />
              <span style={{ fontWeight: 'bold' }}>知识库配置管理</span>
            </Space>
          </Col>
          <Col>
            <Space>
              <Select
                placeholder="选择配置预设"
                style={{ width: 200 }}
                onChange={handleApplyPreset}
                value={undefined}
              >
                {Object.entries(presets).map(([key, preset]) => (
                  <Option key={key} value={key}>
                    <Space>
                      <ThunderboltOutlined />
                      {preset.name}
                    </Space>
                  </Option>
                ))}
              </Select>
              <Button
                icon={<CheckCircleOutlined />}
                onClick={handleValidate}
                loading={validating}
              >
                验证配置
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleReload}
                loading={loading}
              >
                重新加载
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSave}
                loading={saving}
              >
                保存配置
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 验证结果 */}
      {validationResult && (
        <Alert
          style={{ marginBottom: 16 }}
          type={validationResult.valid ? 'success' : 'error'}
          message={validationResult.valid ? '配置验证通过' : '配置验证失败'}
          description={
            <div>
              {validationResult.errors?.length > 0 && (
                <div>
                  <strong>错误:</strong>
                  <ul>
                    {validationResult.errors.map((error: string, index: number) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              )}
              {validationResult.warnings?.length > 0 && (
                <div>
                  <strong>警告:</strong>
                  <ul>
                    {validationResult.warnings.map((warning: string, index: number) => (
                      <li key={index}>{warning}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          }
          closable
          onClose={() => setValidationResult(null)}
        />
      )}

      {/* 配置表单 */}
      <Form
        form={form}
        layout="vertical"
        initialValues={configData || undefined}
      >
        <Row gutter={[16, 16]}>
          {/* 检索配置 */}
          <Col span={12}>
            <Card title="🔍 检索配置" size="small">
              <Form.Item
                label={
                  <Tooltip title="每次查询返回的文档数量，影响回答的全面性">
                    检索文档数量 (top_k)
                  </Tooltip>
                }
                name={['retrieval', 'top_k']}
                rules={[{ required: true, message: '请输入检索文档数量' }]}
              >
                <InputNumber
                  min={1}
                  max={20}
                  style={{ width: '100%' }}
                  placeholder="建议 3-7"
                />
              </Form.Item>

              <Form.Item
                label={
                  <Tooltip title="文档相似度阈值，越高越精确但可能遗漏相关内容">
                    相似度阈值
                  </Tooltip>
                }
                name={['retrieval', 'similarity_threshold']}
                rules={[{ required: true, message: '请输入相似度阈值' }]}
              >
                <InputNumber
                  min={0}
                  max={1}
                  step={0.1}
                  style={{ width: '100%' }}
                  placeholder="建议 0.6-0.8"
                />
              </Form.Item>

              <Form.Item
                label={
                  <Tooltip title="传递给LLM的最大字符数，影响回答详细程度">
                    最大上下文长度
                  </Tooltip>
                }
                name={['retrieval', 'max_context_length']}
                rules={[{ required: true, message: '请输入最大上下文长度' }]}
              >
                <InputNumber
                  min={1000}
                  max={10000}
                  style={{ width: '100%' }}
                  placeholder="建议 2000-5000"
                />
              </Form.Item>
            </Card>
          </Col>

          {/* 性能配置 */}
          <Col span={12}>
            <Card title="⚡ 性能配置" size="small">
              <Form.Item
                label="启用缓存"
                name={['performance', 'cache_enabled']}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label={
                  <Tooltip title="缓存有效期，单位秒">
                    缓存TTL (秒)
                  </Tooltip>
                }
                name={['performance', 'cache_ttl']}
                rules={[{ required: true, message: '请输入缓存TTL' }]}
              >
                <InputNumber
                  min={300}
                  max={86400}
                  style={{ width: '100%' }}
                  placeholder="建议 3600"
                />
              </Form.Item>

              <Form.Item
                label={
                  <Tooltip title="同时处理的最大查询数量">
                    最大并发查询数
                  </Tooltip>
                }
                name={['performance', 'max_concurrent_queries']}
                rules={[{ required: true, message: '请输入最大并发查询数' }]}
              >
                <InputNumber
                  min={1}
                  max={20}
                  style={{ width: '100%' }}
                  placeholder="建议 5-10"
                />
              </Form.Item>
            </Card>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default ConfigTab;
