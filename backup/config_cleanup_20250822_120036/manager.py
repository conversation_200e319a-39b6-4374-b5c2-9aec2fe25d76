#!/usr/bin/env python3
"""
配置管理器

统一的配置管理接口，支持多层级配置合并、环境变量覆盖和来源追踪
"""

import logging
from typing import Dict, Any, Optional, List, Tuple, Union, Set
from pathlib import Path
import copy

from .env_parser import EnvironmentConfigLoader
from .security.whitelist_manager import ConfigWhitelistManager
from .validation.schemas import RootConfigSchema
from .modular_loader import ModularConfigLoader


logger = logging.getLogger(__name__)


class ConfigSourceTracker:
    """配置来源追踪器"""
    
    def __init__(self):
        self.source_map: Dict[str, str] = {}
    
    def set_source(self, key: str, source: str):
        """设置配置键的来源"""
        self.source_map[key] = source
    
    def get_source(self, key: str) -> str:
        """获取配置键的来源"""
        return self.source_map.get(key, "unknown")
    
    def get_all_sources(self) -> Dict[str, str]:
        """获取所有配置键的来源"""
        return self.source_map.copy()


class ConfigMerger:
    """配置合并器"""
    
    def __init__(self, source_tracker: ConfigSourceTracker):
        self.source_tracker = source_tracker
    
    def merge_configs(self, base_config: Dict[str, Any], 
                     override_config: Dict[str, Any], 
                     override_source: str) -> Dict[str, Any]:
        """合并配置，支持深度合并"""
        result = copy.deepcopy(base_config)
        self._deep_merge(result, override_config, override_source, "")
        return result
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any], 
                   source: str, key_prefix: str):
        """深度合并配置"""
        for key, value in override.items():
            full_key = f"{key_prefix}.{key}" if key_prefix else key
            
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                # 递归合并嵌套字典
                self._deep_merge(base[key], value, source, full_key)
            else:
                # 直接覆盖
                base[key] = value
                self.source_tracker.set_source(full_key, source)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: Optional[str] = None, 
                 enable_env_override: bool = True,
                 whitelist_config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置目录路径
            enable_env_override: 是否启用环境变量覆盖
            whitelist_config_path: 白名单配置文件路径
        """
        self.config_dir = Path(config_dir) if config_dir else Path("backend/config")
        self.enable_env_override = enable_env_override
        
        # 初始化组件
        self.source_tracker = ConfigSourceTracker()
        self.merger = ConfigMerger(self.source_tracker)
        self.modular_loader = ModularConfigLoader(str(self.config_dir))
        
        # 初始化安全组件
        whitelist_path = whitelist_config_path or str(self.config_dir / "security" / "whitelist.yaml")
        self.whitelist_manager = ConfigWhitelistManager(whitelist_path)
        
        # 初始化环境变量加载器
        if self.enable_env_override:
            allowed_keys = self.whitelist_manager.get_allowed_keys()
            self.env_loader = EnvironmentConfigLoader(allowed_keys)
        else:
            self.env_loader = None
        
        # 配置缓存
        self._config_cache: Optional[Dict[str, Any]] = None
        self._schema_cache: Optional[RootConfigSchema] = None
        
        # 加载配置
        self._load_all_configs()
    
    def _load_all_configs(self):
        """加载所有配置"""
        try:
            # 1. 加载基础配置文件
            if self.modular_loader.enabled:
                snapshot = self.modular_loader.create_snapshot()
                base_config = snapshot.config_data
            else:
                # 如果模块化加载器禁用，使用默认配置
                base_config = self.modular_loader.default_config
            self._mark_sources(base_config, "config_files", "")
            
            # 2. 应用环境变量覆盖
            if self.enable_env_override and self.env_loader:
                env_config = self.env_loader.load_env_config()
                if env_config:
                    base_config = self.merger.merge_configs(
                        base_config, env_config, "environment"
                    )
                    logger.info(f"应用了 {len(env_config)} 个环境变量覆盖")
            
            # 3. 验证配置Schema
            try:
                self._schema_cache = RootConfigSchema(**base_config)
                logger.info("配置Schema验证通过")
            except Exception as e:
                logger.warning(f"配置Schema验证失败: {e}")
                # 继续使用原始配置，但记录警告
            
            # 4. 缓存最终配置
            self._config_cache = base_config
            
            logger.info("配置加载完成")
            
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            raise
    
    def _mark_sources(self, config: Dict[str, Any], source: str, key_prefix: str):
        """标记配置来源"""
        for key, value in config.items():
            full_key = f"{key_prefix}.{key}" if key_prefix else key
            
            if isinstance(value, dict):
                self._mark_sources(value, source, full_key)
            else:
                self.source_tracker.set_source(full_key, source)
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        if not self._config_cache:
            return default
        
        keys = key.split('.')
        current = self._config_cache
        
        try:
            for k in keys:
                current = current[k]
            return current
        except (KeyError, TypeError):
            return default
    
    def get_with_source(self, key: str, default: Any = None) -> Tuple[Any, str]:
        """获取配置值和来源"""
        value = self.get(key, default)
        source = self.source_tracker.get_source(key)
        return value, source
    
    def get_section(self, section: str, default: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """获取配置段"""
        value = self.get(section, default or {})
        if isinstance(value, dict):
            return value
        else:
            logger.warning(f"配置段 '{section}' 不是字典类型")
            return default or {}
    
    def set_runtime(self, key: str, value: Any):
        """设置运行时配置"""
        if not self._config_cache:
            self._config_cache = {}
        
        keys = key.split('.')
        current = self._config_cache
        
        # 遍历到倒数第二层
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            elif not isinstance(current[k], dict):
                current[k] = {}
            current = current[k]
        
        # 设置最终值
        final_key = keys[-1]
        current[final_key] = value
        
        # 记录来源
        self.source_tracker.set_source(key, "runtime")
        
        logger.debug(f"运行时配置更新: {key} = {value}")
    
    def reload(self):
        """重新加载配置"""
        logger.info("重新加载配置...")
        self._config_cache = None
        self._schema_cache = None
        self.source_tracker = ConfigSourceTracker()
        self.merger = ConfigMerger(self.source_tracker)
        self._load_all_configs()
    
    def validate_env_override(self, key: str, value: Any) -> Tuple[bool, str]:
        """验证环境变量覆盖"""
        if not self.enable_env_override:
            return False, "环境变量覆盖已禁用"
        
        is_valid, message, log_info = self.whitelist_manager.validate_env_override(key, value)
        
        if is_valid:
            logger.info(f"环境变量覆盖验证通过: {key} = {log_info['value']}")
        else:
            logger.warning(f"环境变量覆盖验证失败: {key} - {message}")
        
        return is_valid, message
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        summary = {
            'config_loaded': self._config_cache is not None,
            'schema_validated': self._schema_cache is not None,
            'env_override_enabled': self.enable_env_override,
            'total_config_keys': len(self.source_tracker.get_all_sources()),
        }
        
        # 按来源统计配置键
        sources = self.source_tracker.get_all_sources()
        source_counts = {}
        for source in sources.values():
            source_counts[source] = source_counts.get(source, 0) + 1
        summary['keys_by_source'] = source_counts
        
        # 环境变量覆盖信息
        if self.env_loader:
            env_info = self.env_loader.get_env_override_info()
            summary['env_override_info'] = env_info
        
        # 安全信息
        security_summary = self.whitelist_manager.get_security_summary()
        summary['security_info'] = security_summary
        
        return summary
    
    def dump_config(self, include_sources: bool = False) -> Dict[str, Any]:
        """导出配置"""
        if not self._config_cache:
            return {}
        
        if include_sources:
            return {
                'config': self._config_cache,
                'sources': self.source_tracker.get_all_sources()
            }
        else:
            return self._config_cache.copy()
    
    def get_schema(self) -> Optional[RootConfigSchema]:
        """获取配置Schema"""
        return self._schema_cache


# 全局配置管理器实例
_global_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _global_config_manager
    if _global_config_manager is None:
        _global_config_manager = ConfigManager()
    return _global_config_manager


def create_config_manager(config_dir: Optional[str] = None, 
                         enable_env_override: bool = True,
                         whitelist_config_path: Optional[str] = None) -> ConfigManager:
    """创建配置管理器实例"""
    return ConfigManager(
        config_dir=config_dir,
        enable_env_override=enable_env_override,
        whitelist_config_path=whitelist_config_path
    )


# 便捷函数
def get_config(key: str, default: Any = None) -> Any:
    """获取配置值的便捷函数"""
    return get_config_manager().get(key, default)


def get_config_with_source(key: str, default: Any = None) -> Tuple[Any, str]:
    """获取配置值和来源的便捷函数"""
    return get_config_manager().get_with_source(key, default)


def get_config_section(section: str, default: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """获取配置段的便捷函数"""
    return get_config_manager().get_section(section, default)


# 示例用法
if __name__ == "__main__":
    import os
    
    # 设置一些测试环境变量
    os.environ["AID_CONF__APP__DEBUG"] = "true"
    os.environ["AID_CONF__LLM__TEMPERATURE"] = "0.8"
    os.environ["AID_CONF__DATABASE__CONNECTION__TIMEOUT"] = "45"
    
    # 创建配置管理器
    config_manager = create_config_manager()
    
    # 测试配置获取
    print("配置测试:")
    print(f"app.debug = {config_manager.get('app.debug')}")
    print(f"llm.temperature = {config_manager.get('llm.temperature')}")
    print(f"database.connection.timeout = {config_manager.get('database.connection.timeout')}")
    
    # 测试来源追踪
    print("\n来源追踪:")
    value, source = config_manager.get_with_source('app.debug')
    print(f"app.debug = {value} (来源: {source})")
    
    # 配置摘要
    print("\n配置摘要:")
    summary = config_manager.get_config_summary()
    for key, value in summary.items():
        print(f"{key}: {value}")
