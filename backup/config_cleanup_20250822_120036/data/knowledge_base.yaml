# ============================================================================
# 知识库配置（从unified_config.yaml拆分）
# ============================================================================
# 文件说明：RAG知识库相关配置，包含ChromaDB、向量检索、文档处理等配置
# 拆分时间：2025-08-18 19:45:00
# 源配置块：knowledge_base
# 维护责任：AI算法团队 + 知识库管理团队
# 更新频率：中频（知识库功能调整时）
# ============================================================================

# 知识库主配置
knowledge_base:
  # 功能开关
  enabled: true
  
  # 功能特性开关
  features:
    rag_query: true                    # RAG查询功能
    intent_enhancement: false          # 意图增强功能
    mode_switching: true               # 模式切换功能
    document_ingestion: false          # 文档摄入功能
  
  # ChromaDB配置
  chroma_db:
    path: "backend/data/chroma_db"      # 将解析为“项目根”绝对路径；建议按环境分离（dev/test/prod）；变更需重启服务
    collection_name: "hybrid_knowledge_base"  # 变更需先离线迁移/重建集合（脚本化），避免运行期动库
    embedding_model: "moka-ai/m3e-base"       # 更换后需重建或重新嵌入；谨慎调整

    # ChromaDB客户端配置
    client_settings:
      anonymized_telemetry: false      # 请保持 false（已在日志层静音）
      allow_reset: true                # 仅供离线脚本使用；运行期代理不会执行 reset
      
    # 集合配置
    collection_metadata:
      hnsw_space: "cosine"            # 向量空间类型
      
  # 文档处理配置
  document_processing:
    chunk_size: 800                   # 文档分块大小（建议 400-1200）【热更新：是】
    chunk_overlap: 100                # 分块重叠大小（应小于 chunk_size，建议 50-200）【热更新：是】
    max_chunks_per_doc: 50            # 每个文档最大分块数（限制极端长文）【热更新：是】
    supported_formats: ["md", "txt"]  # 支持的文档格式（扩展需同步导入脚本）
    
    # 文本预处理
    preprocessing:
      remove_extra_whitespace: true
      normalize_unicode: true
      min_chunk_length: 50
      
  # 检索配置
  retrieval:
    top_k: 5                          # 检索返回的文档数量（建议1-20；过大增加延迟）【热更新：是】
    similarity_threshold: 0.7         # 相似度阈值（0-1；过高可能导致0命中）【热更新：是】
    max_context_length: 4000          # 最大上下文长度（上限受模型上下文限制）【热更新：是】
    
    # 检索策略
    strategy:
      use_mmr: false                  # 是否使用最大边际相关性
      mmr_diversity_bias: 0.5         # MMR多样性偏置
      
  # 安全配置
  safety:
    timeout_seconds: 10               # 查询超时时间
    max_retry_attempts: 3             # 最大重试次数
    
    # 内容过滤
    content_filter:
      enabled: true
      max_query_length: 1000
      blocked_patterns: []
      
  # 性能配置
  performance:
    cache_enabled: true               # 启用缓存
    cache_ttl: 3600                   # 缓存生存时间（秒）
    max_concurrent_queries: 5         # 最大并发查询数
    
    # 连接池配置
    connection_pool:
      max_connections: 10
      idle_timeout: 300
      
  # 角色过滤配置
  role_filters:
    enabled: false
    default_roles: ["user"]
    role_mapping: {}
    
  # 日志配置
  logging:
    level: "INFO"
    log_queries: true
    log_results: false
    performance_logging: true
    
    # 日志文件
    log_file: "logs/knowledge_base.log"
    max_file_size: "10MB"
    backup_count: 3

# 集成配置
integrations:
  # 外部API配置
  external_apis:
    openai:
      retry_attempts: 3
      timeout: 30
      
  # 知识库集成
  knowledge_base:
    enabled: true
    update_interval: 3600             # 更新间隔（秒）

# 默认配置（用于回退）
defaults:
  chroma_db:
    path: "backend/data/chroma_db"
    collection_name: "hybrid_knowledge_base"
    embedding_model: "moka-ai/m3e-base"
    
  retrieval:
    top_k: 3
    similarity_threshold: 0.3
    max_context_length: 2000
    
  performance:
    cache_enabled: true
    cache_ttl: 3600
    max_concurrent_queries: 5

# 环境特定配置
environments:
  development:
    chroma_db:
      path: "backend/data/chroma_db_dev"
    logging:
      level: "DEBUG"
      log_queries: true
      log_results: true
      
  testing:
    chroma_db:
      path: "backend/data/chroma_db_test"
      collection_name: "test_knowledge_base"
    performance:
      cache_enabled: false
      
  production:
    logging:
      level: "INFO"
      log_queries: false
      log_results: false
    performance:
      max_concurrent_queries: 20
      connection_pool:
        max_connections: 50

# 故障排除配置
troubleshooting:
  # 数据库修复
  database_repair:
    auto_repair: false
    backup_before_repair: true
    
  # 健康检查
  health_check:
    enabled: true
    check_interval: 300               # 5分钟
    
    # 检查项目
    checks:
      - "database_connection"
      - "collection_exists"
      - "embedding_model_available"

# ============================================================================
# 配置元数据
# ============================================================================
_metadata:
  file_version: "1.0"
  split_date: "2025-08-18T19:45:00"
  source_blocks: ["knowledge_base", "integrations"]
  config_type: "knowledge_base"
  total_items: 45

# ============================================================================
# 使用说明
# ============================================================================
# 1. ChromaDB配置：
#    - path: ChromaDB数据库文件路径
#    - collection_name: 集合名称
#    - embedding_model: 嵌入模型名称
#
# 2. 检索配置：
#    - top_k: 返回最相似的K个文档
#    - similarity_threshold: 相似度阈值，低于此值的结果将被过滤
#    - max_context_length: 最大上下文长度
#
# 3. 性能优化：
#    - 启用缓存可以显著提高重复查询的性能
#    - 调整max_concurrent_queries以平衡性能和资源使用
#
# 4. 故障排除：
#    - 如果遇到"no such column: collections.topic"错误，
#      请删除ChromaDB数据库目录并重新初始化
#    - 如果遇到遥测错误，请确保anonymized_telemetry设置为false
# ============================================================================
