# ============================================================================
# 数据库配置（从unified_config.yaml拆分）
# ============================================================================
# 文件说明：从unified_config.yaml拆分的数据库配置
# 拆分时间：2025-08-18 19:22:20
# 源配置块：conversation, database
# 维护责任：配置管理团队
# 更新频率：根据业务需求
# ============================================================================

database:
  connection:
    check_same_thread: false
    path: backend/data/aidatabase.db
    timeout: 30
  queries:
    backup:
      export_conversation: "SELECT c.conversation_id, c.user_id, c.domain_id, c.category_id, c.status, c.created_at, c.updated_at,\n\
        \       GROUP_CONCAT(m.content, '|||') as messages,\n       GROUP_CONCAT(d.content, '|||') as documents\nFROM conversations\
        \ c\nLEFT JOIN messages m ON c.conversation_id = m.conversation_id AND c.user_id = m.user_id\nLEFT JOIN documents\
        \ d ON c.conversation_id = d.conversation_id AND c.user_id = d.user_id\nWHERE c.conversation_id = ? AND c.user_id\
        \ = ?\nGROUP BY c.conversation_id, c.user_id\n"
    batch_size: 100
    concern_point_coverage:
      get_by_conversation: 'SELECT coverage_id, conversation_id, user_id, focus_id, status, attempts, is_covered, extracted_info,
        updated_at, additional_data

        FROM concern_point_coverage

        WHERE conversation_id = ? AND user_id = ?

        ORDER BY updated_at ASC

        '
      get_coverage_by_id: 'SELECT coverage_id, conversation_id, user_id, focus_id, status, attempts, is_covered, extracted_info,
        updated_at, additional_data

        FROM concern_point_coverage

        WHERE coverage_id = ? AND user_id = ?

        '
      get_processing_points: 'SELECT coverage_id, conversation_id, user_id, focus_id, status, attempts, is_covered, extracted_info,
        updated_at, additional_data

        FROM concern_point_coverage

        WHERE conversation_id = ? AND user_id = ? AND status = ''processing''

        ORDER BY updated_at ASC

        '
      insert_coverage: 'INSERT INTO concern_point_coverage (conversation_id, user_id, focus_id, status, attempts, is_covered,
        extracted_info, updated_at, additional_data)

        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)

        '
      update_status: 'UPDATE concern_point_coverage

        SET status = ?, updated_at = ?

        WHERE coverage_id = ? AND user_id = ?

        '
    conversations:
      check_exists: 'SELECT 1 FROM conversations

        WHERE conversation_id = ? AND user_id = ?

        LIMIT 1

        '
      create_new: 'INSERT OR IGNORE INTO conversations (conversation_id, user_id, status, created_at, updated_at, last_activity_at)

        VALUES (?, ?, ?, ?, ?, ?)

        '
      delete_expired: 'DELETE FROM conversations

        WHERE last_activity_at < ? AND user_id = ?

        '
      get_active: 'SELECT conversation_id, user_id, domain_id, category_id, status, created_at, updated_at, last_activity_at

        FROM conversations

        WHERE last_activity_at > ? AND user_id = ?

        ORDER BY last_activity_at DESC

        '
      get_domain_category: 'SELECT domain_id, category_id

        FROM conversations

        WHERE conversation_id = ? AND user_id = ?

        '
      get_expired: 'SELECT conversation_id, user_id, domain_id, category_id, status, created_at, updated_at, last_activity_at

        FROM conversations

        WHERE last_activity_at < ? AND user_id = ?

        ORDER BY last_activity_at ASC

        '
      get_info: 'SELECT conversation_id, user_id, domain_id, category_id, status, created_at, updated_at, last_activity_at

        FROM conversations

        WHERE conversation_id = ? AND user_id = ?

        '
      update_last_activity: 'UPDATE conversations

        SET updated_at = ?, last_activity_at = ?

        WHERE conversation_id = ? AND user_id = ?

        '
    documents:
      check_exists: 'SELECT 1 FROM documents

        WHERE document_id = ? AND user_id = ?

        LIMIT 1

        '
      delete_document: 'DELETE FROM documents

        WHERE document_id = ? AND user_id = ?

        '
      get_by_conversation: 'SELECT document_id, conversation_id, user_id, version, content, status, feedback, created_at,
        updated_at

        FROM documents

        WHERE conversation_id = ? AND user_id = ?

        ORDER BY version DESC

        '
      get_content: 'SELECT document_id, conversation_id, user_id, version, content, status, feedback, created_at, updated_at

        FROM documents

        WHERE document_id = ? AND user_id = ?

        '
      get_list: 'SELECT document_id, conversation_id, user_id, version, status, created_at, updated_at

        FROM documents

        WHERE user_id = ?

        ORDER BY updated_at DESC

        LIMIT ?

        '
      save_document: 'INSERT INTO documents (document_id, conversation_id, user_id, version, content, status, created_at,
        updated_at)

        VALUES (?, ?, ?, ?, ?, ?, ?, ?)

        '
      update_content: 'UPDATE documents

        SET content = ?, status = ?, updated_at = ?

        WHERE document_id = ? AND user_id = ?

        '
      update_status: 'UPDATE documents

        SET status = ?, updated_at = ?

        WHERE document_id = ? AND user_id = ?

        '
    focus_point_definitions:
      get_by_category: 'SELECT focus_id, category_id, name, description, priority, example, required

        FROM focus_point_definitions

        WHERE category_id = ?

        ORDER BY priority ASC

        '
      get_by_focus_id: 'SELECT focus_id, category_id, name, description, priority, example, required

        FROM focus_point_definitions

        WHERE focus_id = ?

        '
    focus_points:
      batch_insert: 'INSERT INTO concern_point_coverage (conversation_id, user_id, focus_id, status, attempts, is_covered,
        extracted_info, updated_at)

        VALUES (?, ?, ?, ?, ?, ?, ?, ?)

        '
      check_exists: 'SELECT 1 FROM concern_point_coverage

        WHERE conversation_id = ? AND user_id = ? AND focus_id = ?

        LIMIT 1

        '
      clear_processing: 'UPDATE concern_point_coverage

        SET status = ''pending'', updated_at = datetime(''now'')

        WHERE conversation_id = ? AND user_id = ? AND status = ''processing''

        '
      complex_update: 'UPDATE concern_point_coverage

        SET status = ?, is_covered = ?, extracted_info = ?, attempts = attempts + 1, updated_at = ?

        WHERE conversation_id = ? AND user_id = ? AND focus_id = ?

        '
      get_completed: 'SELECT focus_id, status, attempts, extracted_info, is_covered, updated_at

        FROM concern_point_coverage

        WHERE conversation_id = ? AND user_id = ? AND status = ''completed''

        '
      get_single_status: 'SELECT focus_id, status, attempts, extracted_info, is_covered, updated_at

        FROM concern_point_coverage

        WHERE conversation_id = ? AND user_id = ? AND focus_id = ?

        '
      get_status: 'SELECT focus_id, status, attempts, extracted_info, is_covered, updated_at

        FROM concern_point_coverage

        WHERE conversation_id = ? AND user_id = ?

        '
      insert_new: 'INSERT INTO concern_point_coverage (conversation_id, user_id, focus_id, status, attempts, is_covered, extracted_info,
        updated_at)

        VALUES (?, ?, ?, ?, ?, ?, ?, ?)

        '
      reset_all_status: 'UPDATE concern_point_coverage

        SET status = ''pending'', updated_at = ?

        WHERE conversation_id = ? AND user_id = ?

        '
      reset_status: 'DELETE FROM concern_point_coverage

        WHERE conversation_id = ? AND user_id = ?

        '
      update_status: 'UPDATE concern_point_coverage

        SET status = ?, updated_at = ?

        WHERE conversation_id = ? AND user_id = ? AND focus_id = ?

        '
    max_results: 1000
    messages:
      delete_conversation_messages: 'DELETE FROM messages

        WHERE conversation_id = ? AND user_id = ?

        '
      get_conversation_history_limited: 'SELECT conversation_id, user_id, sender_type, content, focus_id, message_type, created_at
        as timestamp

        FROM messages

        WHERE conversation_id = ? AND user_id = ? AND (message_type IS NULL OR message_type != ''summary'')

        ORDER BY created_at ASC

        LIMIT ?

        '
      get_first_user_message: 'SELECT content

        FROM messages

        WHERE conversation_id = ? AND user_id = ? AND sender_type = ''user''

        ORDER BY created_at ASC

        LIMIT 1

        '
      get_messages_by_focus: 'SELECT conversation_id, user_id, sender_type, content, focus_id, message_type, created_at

        FROM messages

        WHERE conversation_id = ? AND user_id = ? AND focus_id = ?

        ORDER BY created_at ASC

        '
      get_recent_messages: 'SELECT conversation_id, user_id, sender_type, content, focus_id, message_type, created_at

        FROM messages

        WHERE conversation_id = ? AND user_id = ?

        ORDER BY created_at DESC

        LIMIT ?

        '
      save_message: 'INSERT INTO messages (conversation_id, user_id, sender_type, content, focus_id, message_type, created_at)

        VALUES (?, ?, ?, ?, ?, ?, ?)

        '
    sessions:
      ensure_session_exists: 'INSERT OR IGNORE INTO sessions (session_id, user_id, created_at, updated_at, status)

        VALUES (?, ?, ?, ?, ''active'')

        '
      get_session: 'SELECT session_id, user_id, status, created_at, updated_at

        FROM sessions

        WHERE session_id = ? AND user_id = ?

        '
      update_session: 'UPDATE sessions

        SET updated_at = ?, status = ?

        WHERE session_id = ? AND user_id = ?

        '
    summaries:
      get_summary: 'SELECT summary_json

        FROM conversation_summaries

        WHERE conversation_id = ? AND user_id = ?

        '
      upsert_summary: 'INSERT OR REPLACE INTO conversation_summaries (conversation_id, user_id, summary_json, updated_at)

        VALUES (?, ?, ?, datetime(''now''))

        '
  tables:
    conversations:
      auto_cleanup: true
      cleanup_days: 30
    documents:
      auto_backup: true
      backup_interval: 24
    focus_points:
      max_per_conversation: 20
tables:
  users:
    primary_key: user_id
    indexes:
    - email
    - created_at
  sessions:
    primary_key: session_id
    indexes:
    - user_id
    - created_at
    - status
    foreign_keys:
      user_id: users.user_id
  conversation_history:
    primary_key: id
    indexes:
    - session_id
    - user_id
    - timestamp
    foreign_keys:
      session_id: sessions.session_id
      user_id: users.user_id
  requirements:
    primary_key: requirement_id
    indexes:
    - session_id
    - user_id
    - domain
    - status
    foreign_keys:
      session_id: sessions.session_id
      user_id: users.user_id
  focus_points:
    primary_key: focus_point_id
    indexes:
    - requirement_id
    - priority
    - status
    foreign_keys:
      requirement_id: requirements.requirement_id
queries:
  timeout: 30
  pagination:
    default_page_size: 20
    max_page_size: 100
  optimization:
    use_prepared_statements: true
    enable_query_cache: true
    cache_ttl: 300
  slow_query:
    enabled: true
    threshold: 1.0
    log_queries: true
migrations:
  migration_path: backend/database/migrations
  strategy: incremental
  auto_migrate: false
  backup_before_migrate: true
  version_table: schema_versions
  current_version: 1.0.0
backup:
  auto_backup: true
  backup_interval: 86400
  backup_path: backend/data/backups
  retention_days: 30
  max_backup_files: 10
  compression: true
  compression_level: 6
security:
  encryption:
    enabled: false
    algorithm: AES-256
  access_control:
    require_ssl: false
    allowed_hosts:
    - localhost
    - 127.0.0.1
  audit_log:
    enabled: true
    log_file: logs/database_audit.log
    log_operations:
    - INSERT
    - UPDATE
    - DELETE
monitoring:
  metrics:
    connection_count: true
    query_count: true
    response_time: true
    error_rate: true
  monitoring_interval: 60
  alerts:
    slow_query_threshold: 2.0
    error_rate_threshold: 0.05
    connection_pool_threshold: 0.8
development:
  debug: true
  log_queries: true
  seed_data: true
  reset_on_startup: false
  enable_sql_logging: true
  enable_query_profiling: true
production:
  debug: false
  log_queries: false
  connection_pool:
    max_connections: 50
    min_connections: 10
  security:
    require_ssl: true
    encryption:
      enabled: true
cleanup:
  auto_cleanup: true
  cleanup_interval: 86400
  rules:
    expired_sessions:
      enabled: true
      retention_days: 7
    old_logs:
      enabled: true
      retention_days: 30
    temp_data:
      enabled: true
      retention_hours: 24
_metadata:
  file_version: '1.0'
  split_date: '2025-08-18T19:22:20.061586'
  source_blocks:
  - conversation
  - database
  config_type: database
  total_items: 154
conversation:
  keyword_acceleration:
    enabled: false
    rules:
      ask_question:
        intent: ask_question
        keywords:
        - 你能做什么
        - 有什么功能
        - 能帮我什么
      business_requirement:
        intent: business_requirement
        keywords:
        - 我想做
        - 我需要
        - 帮我做
        - 制作
      confirm:
        intent: confirm
        keywords:
        - 确认
        - 没问题
        - 正确
        - 同意
        - 好的
        - ok
      emotional_support:
        intent: emotional_support
        keywords:
        - 心情不好
        - 安慰我
        - 难过
        - 沮丧
        - 不开心
        - 郁闷
      general_chat:
        intent: general_chat
        keywords:
        - 聊天
        - 闲聊
        - 随便聊聊
      greeting:
        intent: greeting
        keywords:
        - 你好
        - hello
        - hi
        - 您好
  states:
    available:
    - IDLE
    - PROCESSING_INTENT
    - COLLECTING_INFO
    - DOCUMENTING
    - COMPLETED
    - DOMAIN_CLARIFICATION
    - CATEGORY_CLARIFICATION
    - DIRECT_SELECTION
    default: IDLE
  transitions:
    CATEGORY_CLARIFICATION:
      clarification_failed: DIRECT_SELECTION
      clarification_success: COLLECTING_INFO
      restart: IDLE
    COLLECTING_INFO:
      business_requirement: COLLECTING_INFO
      confirm: DOCUMENTING
      provide_information: COLLECTING_INFO
    DIRECT_SELECTION:
      restart: IDLE
      selection_made: COLLECTING_INFO
    DOCUMENTING:
      confirm: IDLE
      modify: DOCUMENTING
      restart: IDLE
    DOMAIN_CLARIFICATION:
      clarification_failed: DIRECT_SELECTION
      clarification_success: COLLECTING_INFO
      restart: IDLE
    IDLE:
      ask_question: IDLE
      business_requirement: COLLECTING_INFO
      domain_classification_failed: DOMAIN_CLARIFICATION
      greeting: IDLE
