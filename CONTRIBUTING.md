# 🤝 贡献指南

感谢您对智能需求采集系统的关注！我们欢迎所有形式的贡献，包括但不限于：

- 🐛 Bug报告
- 💡 功能建议
- 📝 文档改进
- 🔧 代码贡献
- 🧪 测试用例

## 📋 贡献流程

### 1. 准备工作

1. **Fork 项目**
   ```bash
   # 在GitHub上Fork项目到您的账户
   # 然后克隆到本地
   git clone https://github.com/your-username/Data-Collection.git
   cd Data-Collection
   ```

2. **设置开发环境**
   ```bash
   # 创建虚拟环境
   python -m venv venv
   source venv/bin/activate  # Windows: venv\Scripts\activate
   
   # 安装依赖
   pip install -r requirements.txt
   
   # 前端环境
   cd frontend && npm install
   cd ../admin-frontend && npm install
   ```

3. **创建分支**
   ```bash
   git checkout -b feature/your-feature-name
   # 或
   git checkout -b fix/your-bug-fix
   ```

### 2. 开发规范

#### 代码风格
- **Python**: 遵循PEP 8规范
- **JavaScript/TypeScript**: 使用ESLint和Prettier
- **提交信息**: 使用约定式提交格式

#### 提交信息格式
```
type(scope): description

[optional body]

[optional footer]
```

类型说明：
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(handlers): 添加新的文档处理器

- 支持PDF文档解析
- 增加文档格式验证
- 优化处理性能

Closes #123
```

### 3. 测试要求

在提交代码前，请确保：

1. **运行测试套件**
   ```bash
   # 后端测试
   cd backend && python -m pytest
   
   # 前端测试
   cd frontend && npm test
   ```

2. **代码覆盖率**
   - 新功能需要有相应的测试用例
   - 保持测试覆盖率在80%以上

3. **功能测试**
   - 确保新功能正常工作
   - 验证不会破坏现有功能

### 4. 文档要求

- 新功能需要更新相关文档
- API变更需要更新API文档
- 重要变更需要更新README.md

### 5. 提交Pull Request

1. **推送分支**
   ```bash
   git push origin feature/your-feature-name
   ```

2. **创建PR**
   - 在GitHub上创建Pull Request
   - 填写详细的PR描述
   - 关联相关的Issue

3. **PR模板**
   ```markdown
   ## 变更描述
   简要描述此次变更的内容

   ## 变更类型
   - [ ] Bug修复
   - [ ] 新功能
   - [ ] 文档更新
   - [ ] 性能优化
   - [ ] 代码重构

   ## 测试
   - [ ] 已添加测试用例
   - [ ] 所有测试通过
   - [ ] 手动测试通过

   ## 检查清单
   - [ ] 代码遵循项目规范
   - [ ] 已更新相关文档
   - [ ] 提交信息符合规范
   ```

## 🐛 Bug报告

使用以下模板报告Bug：

```markdown
## Bug描述
清晰简洁地描述Bug

## 复现步骤
1. 进入 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 看到错误

## 期望行为
描述您期望发生的行为

## 实际行为
描述实际发生的行为

## 环境信息
- OS: [e.g. macOS 12.0]
- Python版本: [e.g. 3.11.0]
- Node.js版本: [e.g. 18.0.0]
- 浏览器: [e.g. Chrome 96.0]

## 附加信息
添加任何其他相关信息、截图或日志
```

## 💡 功能建议

使用以下模板提出功能建议：

```markdown
## 功能描述
清晰简洁地描述您希望的功能

## 问题背景
描述这个功能要解决的问题

## 解决方案
描述您希望的解决方案

## 替代方案
描述您考虑过的其他解决方案

## 附加信息
添加任何其他相关信息或截图
```

## 📞 联系方式

如果您有任何问题，可以通过以下方式联系我们：

- 📧 创建Issue讨论
- 💬 在Discussion中交流
- 📱 查看项目Wiki

## 🙏 致谢

感谢所有为项目做出贡献的开发者！您的贡献让这个项目变得更好。

---

**再次感谢您的贡献！** 🎉
