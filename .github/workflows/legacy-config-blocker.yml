# GitHub Actions 工作流：旧配置依赖阻断器
# 禁止在PR中新增对旧配置系统的依赖

name: 旧配置依赖阻断

on:
  pull_request:
    branches: [ main, develop ]
    types: [opened, synchronize, reopened]

env:
  PYTHON_VERSION: '3.11'

jobs:
  legacy-dependency-blocker:
    runs-on: ubuntu-latest
    name: 检查新增的旧配置依赖
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # 获取完整历史以便diff分析

    - name: 设置Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: 获取变更文件列表
      id: changed-files
      run: |
        # 获取PR中变更的Python文件
        git diff --name-only origin/${{ github.base_ref }}...HEAD | grep '\.py$' > changed_files.txt || true
        
        # 获取新增的Python文件
        git diff --name-status origin/${{ github.base_ref }}...HEAD | grep '^A.*\.py$' | cut -f2 > new_files.txt || true
        
        echo "变更的Python文件:"
        cat changed_files.txt
        echo "新增的Python文件:"
        cat new_files.txt

    - name: 检查新增的旧配置依赖
      id: check-legacy-deps
      run: |
        echo "开始检查新增的旧配置依赖..."
        
        # 定义旧配置模式
        LEGACY_PATTERNS=(
          "unified_config\.defaults\.yaml"
          "unified_config_loader"
          "get_config_value"
          "unified_dynamic_config"
          "from.*unified_config"
          "import.*unified_config"
          "UnifiedConfigLoader"
          "UnifiedDynamicConfig"
        )
        
        # 检查结果
        violations_found=false
        violation_details=""
        
        # 检查新增文件中的旧配置依赖
        if [ -s new_files.txt ]; then
          echo "检查新增文件中的旧配置依赖..."
          while IFS= read -r file; do
            if [ -f "$file" ]; then
              for pattern in "${LEGACY_PATTERNS[@]}"; do
                if grep -n "$pattern" "$file"; then
                  violations_found=true
                  violation_details="${violation_details}❌ 新增文件 $file 包含旧配置依赖: $pattern\n"
                  echo "❌ 新增文件 $file 包含旧配置依赖: $pattern"
                fi
              done
            fi
          done < new_files.txt
        fi
        
        # 检查现有文件中新增的旧配置依赖
        if [ -s changed_files.txt ]; then
          echo "检查现有文件中新增的旧配置依赖..."
          while IFS= read -r file; do
            if [ -f "$file" ]; then
              # 获取该文件在PR中新增的行
              git diff origin/${{ github.base_ref }}...HEAD "$file" | grep '^+' | grep -v '^+++' > added_lines.tmp || true
              
              if [ -s added_lines.tmp ]; then
                for pattern in "${LEGACY_PATTERNS[@]}"; do
                  if grep -q "$pattern" added_lines.tmp; then
                    violations_found=true
                    violation_details="${violation_details}❌ 文件 $file 新增了旧配置依赖: $pattern\n"
                    echo "❌ 文件 $file 新增了旧配置依赖: $pattern"
                    
                    # 显示具体的违规行
                    echo "   违规的新增行:"
                    grep "$pattern" added_lines.tmp | head -3 | sed 's/^/     /'
                  fi
                done
              fi
              rm -f added_lines.tmp
            fi
          done < changed_files.txt
        fi
        
        # 设置输出变量
        echo "violations_found=$violations_found" >> $GITHUB_OUTPUT
        echo -e "violation_details<<EOF" >> $GITHUB_OUTPUT
        echo -e "$violation_details" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT
        
        if [ "$violations_found" = true ]; then
          echo "发现旧配置依赖违规"
          exit 1
        else
          echo "✅ 未发现新增的旧配置依赖"
        fi

    - name: 检查豁免标记
      if: failure() && steps.check-legacy-deps.outputs.violations_found == 'true'
      id: check-exemption
      run: |
        echo "检查是否有豁免标记..."
        
        # 检查PR标题和描述中的豁免标记
        pr_title="${{ github.event.pull_request.title }}"
        pr_body="${{ github.event.pull_request.body }}"
        
        exemption_found=false
        exemption_reason=""
        
        # 检查豁免标记
        if echo "$pr_title $pr_body" | grep -i "\[LEGACY-EXEMPT\]"; then
          exemption_found=true
          exemption_reason="PR标题或描述中包含 [LEGACY-EXEMPT] 标记"
        fi
        
        # 检查代码中的豁免注释
        if [ -s changed_files.txt ]; then
          while IFS= read -r file; do
            if [ -f "$file" ]; then
              if git diff origin/${{ github.base_ref }}...HEAD "$file" | grep -i "# LEGACY-EXEMPT\|# legacy-exempt"; then
                exemption_found=true
                exemption_reason="${exemption_reason}\n代码中包含 # LEGACY-EXEMPT 注释"
              fi
            fi
          done < changed_files.txt
        fi
        
        echo "exemption_found=$exemption_found" >> $GITHUB_OUTPUT
        echo -e "exemption_reason<<EOF" >> $GITHUB_OUTPUT
        echo -e "$exemption_reason" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT
        
        if [ "$exemption_found" = true ]; then
          echo "⚠️  发现豁免标记: $exemption_reason"
        else
          echo "❌ 未发现豁免标记"
        fi

    - name: 生成违规报告
      if: failure() && steps.check-legacy-deps.outputs.violations_found == 'true'
      run: |
        echo "生成详细的违规报告..."
        
        cat > violation_report.md << 'EOF'
        # 🚨 旧配置依赖检查失败
        
        ## 检查结果
        
        在此PR中发现了新增的旧配置系统依赖，这违反了配置管理架构升级的迁移策略。
        
        ## 发现的违规项
        
        ${{ steps.check-legacy-deps.outputs.violation_details }}
        
        ## 为什么要阻止旧配置依赖？
        
        1. **架构升级**: 我们正在从旧的配置系统迁移到新的配置管理架构
        2. **技术债务**: 新增旧配置依赖会增加未来的迁移成本
        3. **一致性**: 确保所有新代码使用统一的配置接口
        4. **可维护性**: 避免混合使用新旧配置系统导致的复杂性
        
        ## 如何修复？
        
        ### 1. 使用新的配置接口
        
        **❌ 旧的方式:**
        ```python
        from backend.config.unified_config_loader import get_config_value
        value = get_config_value("some.key")
        ```
        
        **✅ 新的方式:**
        ```python
        from backend.config.manager import ConfigManager
        config = ConfigManager()
        value = config.get("some.key")
        ```
        
        ### 2. 参考迁移指南
        
        - 查看 [配置管理指南](docs/development/配置管理指南.md)
        - 参考 [配置迁移示例](docs/development/配置迁移示例.md)
        - 使用配置验证工具: `python tools/config_validation_cli.py`
        
        ### 3. 如果确实需要豁免
        
        如果确实需要临时使用旧配置接口，请：
        
        1. **在PR标题或描述中添加** `[LEGACY-EXEMPT]` 标记
        2. **在代码中添加注释** `# LEGACY-EXEMPT: 说明原因`
        3. **提供详细的豁免理由**，包括：
           - 为什么必须使用旧接口
           - 计划何时迁移到新接口
           - 迁移的具体时间表
        
        ## 需要评审
        
        如果使用了豁免标记，此PR需要配置管理团队的额外评审。
        
        ---
        
        💡 **提示**: 运行 `python tools/legacy_dependency_scanner.py` 可以扫描整个项目的旧配置依赖
        EOF
        
        echo "违规报告已生成"
        cat violation_report.md

    - name: 添加PR评论
      if: failure() && steps.check-legacy-deps.outputs.violations_found == 'true'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const report = fs.readFileSync('violation_report.md', 'utf8');
          
          // 检查是否有豁免
          const hasExemption = '${{ steps.check-exemption.outputs.exemption_found }}' === 'true';
          const exemptionReason = `${{ steps.check-exemption.outputs.exemption_reason }}`;
          
          let comment = report;
          
          if (hasExemption) {
            comment += `\n\n## ⚠️ 豁免状态\n\n发现豁免标记：\n${exemptionReason}\n\n**此PR需要配置管理团队的额外评审。**`;
          }
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

    - name: 设置PR标签
      if: failure() && steps.check-legacy-deps.outputs.violations_found == 'true'
      uses: actions/github-script@v6
      with:
        script: |
          const hasExemption = '${{ steps.check-exemption.outputs.exemption_found }}' === 'true';
          
          const labels = hasExemption 
            ? ['legacy-config-exempt', 'needs-config-team-review']
            : ['legacy-config-violation'];
          
          github.rest.issues.addLabels({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            labels: labels
          });

    - name: 最终检查结果
      if: always()
      run: |
        violations_found="${{ steps.check-legacy-deps.outputs.violations_found }}"
        exemption_found="${{ steps.check-exemption.outputs.exemption_found }}"
        
        if [ "$violations_found" = "true" ]; then
          if [ "$exemption_found" = "true" ]; then
            echo "⚠️  发现旧配置依赖，但有豁免标记。需要额外评审。"
            exit 0  # 允许通过，但需要人工评审
          else
            echo "❌ 发现旧配置依赖且无豁免标记。阻断PR。"
            exit 1  # 阻断PR
          fi
        else
          echo "✅ 未发现新增的旧配置依赖。检查通过。"
          exit 0
        fi
