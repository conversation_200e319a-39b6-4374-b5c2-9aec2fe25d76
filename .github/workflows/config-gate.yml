name: 配置门禁检查

on:
  pull_request:
    paths:
      - 'backend/config/**'
      - '*.yaml'
      - '*.yml'
      - '*.json'
      - 'tools/ci_config_gate.py'
      - 'tools/config_static_checker.py'
      - 'tests/config/**'
  push:
    branches:
      - main
      - develop
    paths:
      - 'backend/config/**'
      - '*.yaml'
      - '*.yml'
      - '*.json'

jobs:
  config-gate:
    name: 配置门禁检查
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 2  # 需要获取前一个提交用于diff
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pydantic pyyaml
    
    - name: 运行配置门禁检查
      id: gate-check
      run: |
        python tools/ci_config_gate.py \
          --output ci_gate_report.json \
          --fail-fast
      continue-on-error: true
    
    - name: 上传门禁报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: config-gate-report
        path: ci_gate_report.json
        retention-days: 30
    
    - name: 解析门禁结果
      id: parse-results
      if: always()
      run: |
        if [ -f ci_gate_report.json ]; then
          # 提取关键信息
          overall_status=$(python -c "import json; data=json.load(open('ci_gate_report.json')); print(data['overall_status'])")
          echo "overall_status=$overall_status" >> $GITHUB_OUTPUT
          
          # 提取错误和警告数
          errors=$(python -c "import json; data=json.load(open('ci_gate_report.json')); print(data['check_results']['static_check']['errors'] if data['check_results']['static_check'] else 0)")
          warnings=$(python -c "import json; data=json.load(open('ci_gate_report.json')); print(data['check_results']['static_check']['warnings'] if data['check_results']['static_check'] else 0)")
          echo "errors=$errors" >> $GITHUB_OUTPUT
          echo "warnings=$warnings" >> $GITHUB_OUTPUT
          
          # 提取影响分析结果
          critical_impact=$(python -c "import json; data=json.load(open('ci_gate_report.json')); print(data['check_results']['impact_analysis']['critical_impact_count'] if data['check_results']['impact_analysis'] else 0)")
          echo "critical_impact=$critical_impact" >> $GITHUB_OUTPUT
        else
          echo "overall_status=error" >> $GITHUB_OUTPUT
          echo "errors=999" >> $GITHUB_OUTPUT
          echo "warnings=0" >> $GITHUB_OUTPUT
          echo "critical_impact=0" >> $GITHUB_OUTPUT
        fi
    
    - name: 创建PR评论
      uses: actions/github-script@v6
      if: github.event_name == 'pull_request' && always()
      with:
        script: |
          const fs = require('fs');
          
          let reportContent = '## 🚀 配置门禁检查报告\n\n';
          
          const overallStatus = '${{ steps.parse-results.outputs.overall_status }}';
          const errors = parseInt('${{ steps.parse-results.outputs.errors }}');
          const warnings = parseInt('${{ steps.parse-results.outputs.warnings }}');
          const criticalImpact = parseInt('${{ steps.parse-results.outputs.critical_impact }}');
          
          // 状态图标
          const statusIcon = overallStatus === 'passed' ? '✅' : '❌';
          reportContent += `**总体状态**: ${statusIcon} ${overallStatus.toUpperCase()}\n\n`;
          
          // 检查结果摘要
          reportContent += '### 📊 检查结果摘要\n\n';
          reportContent += `- **错误数**: ${errors}\n`;
          reportContent += `- **警告数**: ${warnings}\n`;
          reportContent += `- **关键影响**: ${criticalImpact}\n\n`;
          
          // 详细报告
          if (fs.existsSync('ci_gate_report.json')) {
            try {
              const report = JSON.parse(fs.readFileSync('ci_gate_report.json', 'utf8'));
              
              // 静态检查详情
              if (report.check_results.static_check && report.check_results.static_check.details) {
                reportContent += '### 🔍 静态检查详情\n\n';
                const details = report.check_results.static_check.details.slice(0, 10); // 只显示前10个
                
                if (details.length > 0) {
                  reportContent += '| 类型 | 严重程度 | 文件 | 消息 |\n';
                  reportContent += '|------|----------|------|------|\n';
                  
                  details.forEach(detail => {
                    const severityIcon = detail.severity === 'error' ? '🔴' : detail.severity === 'warning' ? '🟡' : '🔵';
                    reportContent += `| ${detail.type} | ${severityIcon} ${detail.severity} | \`${detail.file}\` | ${detail.message} |\n`;
                  });
                  
                  if (report.check_results.static_check.details.length > 10) {
                    reportContent += `\n*还有 ${report.check_results.static_check.details.length - 10} 个问题，请查看完整报告*\n`;
                  }
                } else {
                  reportContent += '✅ 未发现静态检查问题\n';
                }
                reportContent += '\n';
              }
              
              // 影响分析
              if (report.check_results.impact_analysis && report.check_results.impact_analysis.changed_keys) {
                reportContent += '### 🎯 配置影响分析\n\n';
                const analysis = report.check_results.impact_analysis;
                
                if (analysis.changed_keys.length > 0) {
                  reportContent += `**变更的配置键**: ${analysis.changed_keys.join(', ')}\n`;
                  reportContent += `**最大影响级别**: ${analysis.max_impact_level}\n`;
                  reportContent += `**影响组件数**: ${analysis.affected_components ? analysis.affected_components.length : 0}\n\n`;
                } else {
                  reportContent += '✅ 未检测到配置变更\n\n';
                }
              }
              
              // 建议
              if (report.summary && report.summary.recommendations && report.summary.recommendations.length > 0) {
                reportContent += '### 💡 建议\n\n';
                report.summary.recommendations.forEach((rec, index) => {
                  reportContent += `${index + 1}. ${rec}\n`;
                });
                reportContent += '\n';
              }
              
            } catch (e) {
              reportContent += '⚠️ 无法解析详细报告\n\n';
            }
          }
          
          reportContent += '---\n';
          reportContent += '*此报告由配置门禁自动生成*';
          
          // 发布评论
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: reportContent
          });
    
    - name: 设置检查状态
      if: always()
      run: |
        if [ "${{ steps.parse-results.outputs.overall_status }}" = "passed" ]; then
          echo "✅ 配置门禁检查通过"
          exit 0
        else
          echo "❌ 配置门禁检查失败"
          exit 1
        fi

  # 配置文件语法检查（快速检查）
  syntax-check:
    name: 配置文件语法检查
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: 安装YAML检查工具
      run: |
        pip install pyyaml yamllint
    
    - name: 检查YAML语法
      run: |
        echo "检查YAML文件语法..."
        find . -name "*.yaml" -o -name "*.yml" | while read file; do
          echo "检查文件: $file"
          python -c "import yaml; yaml.safe_load(open('$file'))" || exit 1
        done
        echo "✅ YAML语法检查通过"
    
    - name: 运行yamllint
      run: |
        echo "运行yamllint检查..."
        yamllint -d relaxed backend/config/ || true  # 允许警告
        echo "✅ yamllint检查完成"

  # 配置Schema验证
  schema-validation:
    name: 配置Schema验证
    runs-on: ubuntu-latest
    needs: syntax-check
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install pydantic pyyaml
    
    - name: 验证配置Schema
      run: |
        echo "验证配置Schema..."
        python -c "
        import sys
        sys.path.append('.')
        from backend.config.validation.test_schemas import test_all_schemas
        try:
            test_all_schemas()
            print('✅ Schema验证通过')
        except Exception as e:
            print(f'❌ Schema验证失败: {e}')
            sys.exit(1)
        "

  # 安全检查
  security-check:
    name: 配置安全检查
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 检查敏感信息
      run: |
        echo "检查配置文件中的敏感信息..."
        
        # 检查常见的敏感信息模式
        sensitive_patterns=(
          "password.*=.*[^*]"
          "secret.*=.*[^*]"
          "key.*=.*[a-zA-Z0-9]{20,}"
          "token.*=.*[a-zA-Z0-9]{20,}"
          "sk-[a-zA-Z0-9]{32,}"
        )
        
        found_issues=false
        
        for pattern in "${sensitive_patterns[@]}"; do
          if grep -r -i -E "$pattern" backend/config/ --include="*.yaml" --include="*.yml" --include="*.json"; then
            echo "⚠️ 发现可能的敏感信息: $pattern"
            found_issues=true
          fi
        done
        
        if [ "$found_issues" = true ]; then
          echo "❌ 发现潜在的敏感信息，请检查配置文件"
          echo "💡 建议：敏感信息应通过环境变量设置"
          exit 1
        else
          echo "✅ 未发现明显的敏感信息"
        fi
