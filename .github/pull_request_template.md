# Pull Request

## 📋 变更描述

请简要描述此 PR 的变更内容：

<!-- 请在此处描述您的变更 -->

## 🎯 变更类型

请勾选适用的变更类型：

- [ ] 🐛 Bug 修复
- [ ] ✨ 新功能
- [ ] 📝 文档更新
- [ ] 🎨 代码格式调整
- [ ] ♻️ 代码重构
- [ ] ⚡ 性能优化
- [ ] 🧪 测试相关
- [ ] 🔧 构建/工具变更
- [ ] ⚙️ 配置变更

## 🔍 配置变更影响分析

<!-- ⚠️ 如果此PR涉及配置变更，请务必填写此部分 -->

### 变更的配置键
- [ ] 无配置变更
- [ ] 有配置变更，具体如下：
  - `配置键1`: 变更说明
  - `配置键2`: 变更说明

### 影响分析报告
<!-- 使用命令生成影响分析：python tools/config_impact_analyzer.py <config_keys> --format text -->
```
# 请在此处粘贴影响分析结果
# 如果有配置变更，请运行上述命令并粘贴结果
```

### 受影响组件
- [ ] 无组件受影响
- [ ] 受影响组件：
  - 组件名1: 影响说明
  - 组件名2: 影响说明

### 旧配置依赖检查
<!-- ⚠️ 重要：旧配置系统已标记为 DEPRECATED，严禁新增依赖 -->

#### 禁止引用的旧配置组件
- [ ] 未引用 `unified_config.defaults.yaml`
- [ ] 未引用 `unified_config_loader`
- [ ] 未引用 `get_config_value()` 函数
- [ ] 未引用 `load_config_from_yaml()` 函数
- [ ] 未引用 `unified_dynamic_config`
- [ ] 未引用其他废弃的配置API

#### 新配置API使用确认
如果涉及配置操作，请确认使用了新的API：
- [ ] 使用 `ConfigManager.get()` 获取配置
- [ ] 使用 `ConfigManager.get_with_source()` 获取配置来源
- [ ] 使用 `ConfigManager.set_runtime()` 设置运行时配置
- [ ] 使用 `ConfigManager.get_section()` 获取配置段

#### 豁免申请（仅在特殊情况下）
- [ ] 未新增对旧配置系统的依赖
- [ ] 新增了旧配置依赖，已申请豁免 (Issue: #xxx)
- [ ] 新增了旧配置依赖，原因说明：
  ```
  <!-- 请详细说明为什么必须使用旧配置接口，以及迁移计划 -->
  <!-- 注意：新增旧配置依赖需要架构委员会审批 -->
  ```

## 🔗 相关 Issue

<!-- 如果此 PR 解决了某个 Issue，请在此处链接 -->
Closes #(issue number)

## 🧪 测试

请描述您如何测试了这些变更：

- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试完成
- [ ] 性能测试通过（如适用）

### 测试步骤

1.
2.
3.

### 测试清单
<!-- 如果有配置变更，请运行测试清单生成命令 -->
```bash
# 生成测试清单命令（如适用）
python tools/config_impact_analyzer.py <config_keys> --test-only
```

## 🚀 部署计划

<!-- 如果涉及配置变更或高风险变更，请填写部署计划 -->

### 部署策略
- [ ] 直接部署（低风险变更）
- [ ] 分批部署（中等风险变更）
- [ ] 灰度发布（高风险变更）
- [ ] 蓝绿部署（关键变更）

### 部署前检查
- [ ] 备份当前配置
- [ ] 准备回滚脚本
- [ ] 设置监控告警
- [ ] 通知相关团队

### 部署后验证
- [ ] 验证配置生效
- [ ] 检查关键指标
- [ ] 运行冒烟测试
- [ ] 确认功能正常

## 🔄 回滚方案

<!-- 详细的回滚计划和触发条件 -->

### 回滚触发条件
- [ ] 系统无法启动
- [ ] 核心功能异常
- [ ] 错误率超过阈值: ____%
- [ ] 响应时间增加超过: ____%
- [ ] 其他条件: ____________

### 回滚步骤
1.
2.
3.

### 回滚时间要求
- [ ] 立即回滚（关键变更）
- [ ] 5分钟内回滚（高风险变更）
- [ ] 30分钟内回滚（中等风险变更）
- [ ] 下个发布窗口回滚（低风险变更）

### 回滚验证
- [ ] 配置回滚完成
- [ ] 系统功能恢复
- [ ] 监控指标正常
- [ ] 用户影响消除

## 📊 监控和告警

<!-- 部署后需要关注的监控指标 -->

### 关键指标
- [ ] 系统启动状态
- [ ] 配置加载时间
- [ ] 错误日志数量
- [ ] 业务功能可用性
- [ ] 其他指标: ____________

### 告警设置
- [ ] 已设置相关告警
- [ ] 告警接收人: ____________
- [ ] 告警升级策略: ____________

## 📸 截图（如适用）

<!-- 如果变更涉及 UI，请提供截图 -->

## ✅ 检查清单

请确认您已完成以下检查：

### 代码质量
- [ ] 代码遵循项目的编码规范
- [ ] 已运行代码格式化工具
- [ ] 已运行静态代码分析
- [ ] 代码已经过自我审查

### 测试
- [ ] 已添加必要的测试用例
- [ ] 所有测试都通过
- [ ] 测试覆盖率满足要求

### 文档
- [ ] 已更新相关文档
- [ ] 已更新 API 文档（如适用）
- [ ] 已更新 README（如适用）

### 兼容性
- [ ] 变更向后兼容
- [ ] 已考虑对现有功能的影响
- [ ] 已验证多用户场景（如适用）

### 配置管理（如涉及配置变更）
- [ ] 配置文件遵循定义的Schema
- [ ] 未新增对旧配置系统的依赖
- [ ] 关键配置键有默认值
- [ ] 配置变更已通过验证测试
- [ ] 已更新配置文档

## 📝 额外说明

<!-- 任何其他需要审查者知道的信息 -->

## 🔍 审查要点

请审查者特别关注：

- [ ] 业务逻辑正确性
- [ ] 性能影响
- [ ] 安全性考虑
- [ ] 错误处理
- [ ] 代码可维护性

---

## 🚨 旧配置系统废弃提醒

> ⚠️ **重要通知**: 旧配置系统（`unified_config.defaults.yaml` 等）已正式标记为 **DEPRECATED**，将在后续版本中移除。

### 如果您的PR涉及配置相关变更：

#### ❌ 禁止操作
- **禁止新增旧托底依赖**: 请勿在新代码中引用旧配置系统
- **禁止修改旧配置文件**: 不要修改 `unified_config.defaults.yaml`
- **禁止使用废弃API**: 避免使用已标记为废弃的配置函数

#### ✅ 推荐操作
- **使用新配置API**: 请使用 `ConfigManager` 相关API
- **遵循配置规范**: 新配置键需要Schema定义和默认值
- **安全考虑**: 敏感配置通过环境变量或密钥管理器设置
- **文档更新**: 及时更新相关配置文档

### 📚 参考资料

- 📖 [配置管理指南](docs/development/配置管理指南.md)
- 📋 [配置管理最佳实践](docs/development/配置管理最佳实践.md)
- 🔧 [配置管理API文档](docs/api/配置管理API文档.md)
- 🗓️ [旧托底清理与日落计划](docs/development/旧托底清理与日落计划.md)

### 🤝 获取帮助

如果您在配置迁移过程中遇到问题：
- 💬 联系配置管理团队获取技术支持
- 📝 查阅迁移指南和最佳实践文档
- 🔍 使用配置影响分析工具评估变更影响

### 🔍 自动检查

此PR将自动运行以下检查：
- ✅ **旧托底依赖扫描**: 检测新增的旧配置系统引用
- ✅ **配置Schema验证**: 验证配置键定义和格式
- ✅ **敏感信息检测**: 扫描硬编码的敏感信息
- ✅ **代码质量检查**: 静态代码分析和格式检查

如果自动检查失败，请根据错误信息进行修复，或联系相关团队获取帮助。

### 📊 配置系统迁移进度

当前迁移状态：
- ✅ 新配置管理系统已上线
- ✅ 兼容层正常运行
- ✅ 监控告警系统完善
- 🔄 旧系统使用度持续下降
- ⏳ 计划在下个季度完成日落

---

感谢您的贡献！ 🚀
