#!/usr/bin/env python3
"""
配置验证CLI工具

用于本地开发和CI中的配置验证
"""

import argparse
import json
import sys
import yaml
from pathlib import Path
from typing import Dict, List, Any, Tuple
import traceback

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from backend.config.validation.schemas import (
        BusinessConfigSchema,
        LLMConfigSchema,
        DatabaseConfigSchema,
        PerformanceConfigSchema,
        SecurityConfigSchema,
        RootConfigSchema
    )
except ImportError as e:
    print(f"❌ 无法导入配置Schema: {e}")
    print("请确保项目依赖已正确安装")
    sys.exit(1)


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.config_schemas = {
            'backend/config/business/rules.yaml': BusinessConfigSchema,
            'backend/config/llm/models.yaml': LLMConfigSchema,
            'backend/config/data/database.yaml': DatabaseConfigSchema,
            'backend/config/system/performance.yaml': PerformanceConfigSchema,
            'backend/config/system/security.yaml': SecurityConfigSchema,
        }
        
        self.critical_keys = {
            'backend/config/data/database.yaml': [
                'database.connection.path',
                'database.connection.timeout'
            ],
            'backend/config/llm/models.yaml': [
                'default_model',
                'models'
            ],
            'backend/config/system/security.yaml': [
                'authentication.enabled'
            ],
            'backend/config/system/performance.yaml': [
                'cache.enabled'
            ]
        }
        
        self.legacy_patterns = [
            'unified_config.defaults.yaml',
            'unified_config_loader',
            'get_config_value',
            'unified_dynamic_config'
        ]

    def validate_yaml_syntax(self, config_dir: Path) -> Tuple[bool, List[str]]:
        """验证YAML语法"""
        print("🔍 验证YAML语法...")
        errors = []
        
        yaml_files = list(config_dir.rglob("*.yaml"))
        if not yaml_files:
            errors.append(f"在 {config_dir} 中未找到YAML文件")
            return False, errors
        
        for yaml_file in yaml_files:
            try:
                with open(yaml_file, 'r', encoding='utf-8') as f:
                    yaml.safe_load(f)
                print(f"  ✅ {yaml_file.relative_to(config_dir)}")
            except yaml.YAMLError as e:
                error_msg = f"{yaml_file.relative_to(config_dir)}: YAML语法错误 - {e}"
                errors.append(error_msg)
                print(f"  ❌ {error_msg}")
            except Exception as e:
                error_msg = f"{yaml_file.relative_to(config_dir)}: 文件处理失败 - {e}"
                errors.append(error_msg)
                print(f"  ❌ {error_msg}")
        
        return len(errors) == 0, errors

    def validate_schemas(self) -> Tuple[bool, List[str]]:
        """验证配置Schema"""
        print("🔍 验证配置Schema...")
        errors = []
        
        for config_file, schema_class in self.config_schemas.items():
            config_path = Path(config_file)
            if not config_path.exists():
                error_msg = f"{config_file}: 配置文件不存在"
                errors.append(error_msg)
                print(f"  ❌ {error_msg}")
                continue
            
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                
                # 验证Schema
                schema_instance = schema_class(**config_data)
                print(f"  ✅ {config_path.name}")
                
            except Exception as e:
                error_msg = f"{config_file}: Schema验证失败 - {e}"
                errors.append(error_msg)
                print(f"  ❌ {error_msg}")
        
        return len(errors) == 0, errors

    def validate_critical_keys(self) -> Tuple[bool, List[str]]:
        """验证关键配置键"""
        print("🔍 验证关键配置键...")
        errors = []
        
        for config_file, keys in self.critical_keys.items():
            config_path = Path(config_file)
            if not config_path.exists():
                error_msg = f"{config_file}: 配置文件不存在"
                errors.append(error_msg)
                print(f"  ❌ {error_msg}")
                continue
            
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f) or {}
                
                for key_path in keys:
                    if self._check_key_exists(config_data, key_path):
                        print(f"  ✅ {config_path.name}: {key_path}")
                    else:
                        error_msg = f"{config_file}: 缺少关键键 {key_path}"
                        errors.append(error_msg)
                        print(f"  ❌ {error_msg}")
                        
            except Exception as e:
                error_msg = f"{config_file}: 文件处理失败 - {e}"
                errors.append(error_msg)
                print(f"  ❌ {error_msg}")
        
        return len(errors) == 0, errors

    def check_legacy_dependencies(self, source_dir: Path) -> Tuple[bool, List[str]]:
        """检查旧配置依赖"""
        print("🔍 检查旧配置依赖...")
        violations = []
        
        python_files = list(source_dir.rglob("*.py"))
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for pattern in self.legacy_patterns:
                    if pattern in content:
                        violation = f"{py_file.relative_to(source_dir)}: 发现旧配置依赖 '{pattern}'"
                        violations.append(violation)
                        print(f"  ❌ {violation}")
                        
            except Exception as e:
                print(f"  ⚠️  无法读取文件 {py_file}: {e}")
        
        if not violations:
            print("  ✅ 未发现旧配置依赖")
        
        return len(violations) == 0, violations

    def check_defaults_directory(self, config_dir: Path) -> Tuple[bool, List[str]]:
        """检查默认配置目录"""
        print("🔍 检查默认配置目录...")
        errors = []
        
        defaults_dir = config_dir / "defaults"
        if not defaults_dir.exists():
            error_msg = "默认配置目录不存在: backend/config/defaults"
            errors.append(error_msg)
            print(f"  ❌ {error_msg}")
            return False, errors
        
        # 检查必需的默认配置文件
        required_defaults = [
            "base.yaml",
            "business.yaml",
            "security.yaml"
        ]
        
        for default_file in required_defaults:
            default_path = defaults_dir / default_file
            if default_path.exists():
                print(f"  ✅ {default_file}")
            else:
                error_msg = f"缺少默认配置文件: {default_file}"
                errors.append(error_msg)
                print(f"  ❌ {error_msg}")
        
        return len(errors) == 0, errors

    def _check_key_exists(self, config_data: Dict[str, Any], key_path: str) -> bool:
        """检查配置键是否存在"""
        keys = key_path.split('.')
        current = config_data
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return False
        
        return True

    def generate_report(self, results: Dict[str, Tuple[bool, List[str]]], output_format: str = "text") -> str:
        """生成验证报告"""
        if output_format == "json":
            report = {
                "timestamp": "2025-08-20T23:45:00Z",
                "results": {},
                "summary": {
                    "total_checks": len(results),
                    "passed": sum(1 for success, _ in results.values() if success),
                    "failed": sum(1 for success, _ in results.values() if not success)
                }
            }
            
            for check_name, (success, errors) in results.items():
                report["results"][check_name] = {
                    "success": success,
                    "errors": errors
                }
            
            return json.dumps(report, indent=2, ensure_ascii=False)
        
        else:  # text format
            lines = ["配置验证报告", "=" * 50, ""]
            
            for check_name, (success, errors) in results.items():
                status = "✅ 通过" if success else "❌ 失败"
                lines.append(f"{check_name}: {status}")
                
                if errors:
                    for error in errors:
                        lines.append(f"  - {error}")
                lines.append("")
            
            # 汇总
            total = len(results)
            passed = sum(1 for success, _ in results.values() if success)
            failed = total - passed
            
            lines.extend([
                "汇总",
                "-" * 20,
                f"总检查项: {total}",
                f"通过: {passed}",
                f"失败: {failed}",
                f"成功率: {passed/total*100:.1f}%" if total > 0 else "成功率: 0%"
            ])
            
            return "\n".join(lines)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="配置验证CLI工具")
    parser.add_argument("--config-dir", default="backend/config", help="配置目录路径")
    parser.add_argument("--source-dir", default="backend", help="源代码目录路径")
    parser.add_argument("--format", choices=["text", "json"], default="text", help="输出格式")
    parser.add_argument("--strict", action="store_true", help="严格模式，任何失败都返回错误码")
    parser.add_argument("--output", help="输出文件路径")
    
    args = parser.parse_args()
    
    config_dir = Path(args.config_dir)
    source_dir = Path(args.source_dir)
    
    if not config_dir.exists():
        print(f"❌ 配置目录不存在: {config_dir}")
        sys.exit(1)
    
    if not source_dir.exists():
        print(f"❌ 源代码目录不存在: {source_dir}")
        sys.exit(1)
    
    validator = ConfigValidator()
    results = {}
    
    # 执行各项验证
    try:
        results["YAML语法验证"] = validator.validate_yaml_syntax(config_dir)
        results["Schema验证"] = validator.validate_schemas()
        results["关键键验证"] = validator.validate_critical_keys()
        results["旧依赖检查"] = validator.check_legacy_dependencies(source_dir)
        results["默认配置检查"] = validator.check_defaults_directory(config_dir)
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        if args.format == "text":
            traceback.print_exc()
        sys.exit(1)
    
    # 生成报告
    report = validator.generate_report(results, args.format)
    
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"报告已保存到: {args.output}")
    else:
        print("\n" + report)
    
    # 检查是否有失败的验证
    failed_checks = [name for name, (success, _) in results.items() if not success]
    
    if failed_checks:
        if args.strict:
            print(f"\n❌ 严格模式：发现 {len(failed_checks)} 个失败的检查项")
            sys.exit(1)
        else:
            print(f"\n⚠️  发现 {len(failed_checks)} 个失败的检查项，但非严格模式")
    else:
        print("\n✅ 所有验证检查通过！")


if __name__ == "__main__":
    main()
