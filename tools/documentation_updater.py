#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档更新工具

自动更新项目文档，标注旧托底为Deprecated，更新配置管理指南和最佳实践
"""

import os
import sys
import re
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))


class DocumentationUpdater:
    """文档更新器"""
    
    def __init__(self, dry_run: bool = True):
        self.dry_run = dry_run
        
        # 文档文件映射
        self.doc_files = {
            'main_readme': 'README.md',
            'config_guide': 'docs/development/配置管理指南.md',
            'implementation_guide': 'docs/development/配置管理架构升级实施跟踪文档.md',
            'project_summary': 'docs/配置管理架构升级项目执行阶段总结.md',
            'api_docs': 'docs/api/配置管理API文档.md',
            'best_practices': 'docs/development/配置管理最佳实践.md'
        }
        
        # 更新统计
        self.update_stats = {
            'files_updated': 0,
            'deprecated_sections_added': 0,
            'migration_guides_updated': 0,
            'api_docs_updated': 0
        }
    
    def update_all_documentation(self) -> Dict[str, Any]:
        """更新所有文档"""
        print("📚 开始更新项目文档...")
        
        results = {}
        
        # 更新主README
        results['readme'] = self._update_main_readme()
        
        # 更新配置管理指南
        results['config_guide'] = self._update_config_guide()
        
        # 更新实施跟踪文档
        results['implementation'] = self._update_implementation_guide()
        
        # 更新项目总结
        results['summary'] = self._update_project_summary()
        
        # 创建API文档
        results['api_docs'] = self._create_api_documentation()
        
        # 创建最佳实践文档
        results['best_practices'] = self._create_best_practices_doc()
        
        return {
            'success': True,
            'timestamp': datetime.now().isoformat(),
            'dry_run': self.dry_run,
            'results': results,
            'stats': self.update_stats
        }
    
    def _update_main_readme(self) -> Dict[str, Any]:
        """更新主README文件"""
        readme_path = Path(self.doc_files['main_readme'])
        
        if not readme_path.exists():
            return {'success': False, 'reason': 'file_not_found'}
        
        try:
            with open(readme_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 添加配置管理升级说明
            upgrade_section = """
## 🚀 配置管理系统升级

本项目已完成配置管理架构的全面升级，提供更强大、更安全的配置管理能力。

### ✨ 新特性

- **统一配置管理**: 支持 defaults → files → env → runtime 的完整覆盖链
- **环境变量覆盖**: 通过 `AID_CONF__` 前缀支持环境变量配置
- **配置验证**: 基于 Pydantic 的强类型配置验证
- **来源追踪**: 完整的配置来源追踪和审计
- **监控告警**: 实时的配置监控和告警系统
- **安全管控**: 敏感信息自动脱敏和白名单管理

### 📖 快速开始

```python
from backend.config.manager import get_config_manager

# 获取配置管理器
config_manager = get_config_manager()

# 读取配置
debug_mode = config_manager.get("app.debug", False)
db_host = config_manager.get("database.host", "localhost")

# 获取配置来源
value, source = config_manager.get_with_source("app.debug")
print(f"app.debug = {value} (来源: {source})")

# 设置运行时配置
config_manager.set_runtime("app.temp_setting", "temp_value")
```

### ⚠️ 旧配置系统已废弃

**重要提醒**: 旧的配置系统（unified_config.defaults.yaml 等）已标记为 **DEPRECATED**，将在后续版本中移除。

请使用新的配置管理API：
- ❌ ~~`unified_config.get()`~~ 
- ✅ `ConfigManager.get()`
- ❌ ~~`load_config_from_yaml()`~~
- ✅ `ConfigManager.get_section()`

### 📋 迁移指南

详细的迁移指南请参考：[配置管理指南](docs/development/配置管理指南.md)

"""
            
            # 在适当位置插入升级说明
            if "## 配置管理系统升级" not in content:
                # 在第一个二级标题前插入
                pattern = r'(# [^\n]+\n+[^\n]*\n+)(## )'
                replacement = r'\1' + upgrade_section + r'\2'
                
                if re.search(pattern, content):
                    content = re.sub(pattern, replacement, content, count=1)
                else:
                    # 如果没找到合适位置，在文件开头添加
                    content = upgrade_section + "\n" + content
                
                if not self.dry_run:
                    with open(readme_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.update_stats['files_updated'] += 1
                    self.update_stats['deprecated_sections_added'] += 1
                
                return {'success': True, 'action': 'added_upgrade_section'}
            else:
                return {'success': True, 'action': 'already_updated'}
                
        except Exception as e:
            return {'success': False, 'reason': str(e)}
    
    def _update_config_guide(self) -> Dict[str, Any]:
        """更新配置管理指南"""
        guide_path = Path(self.doc_files['config_guide'])
        
        if not guide_path.exists():
            return self._create_config_guide()
        
        try:
            with open(guide_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 添加废弃警告
            deprecation_warning = """
> ⚠️ **重要提醒**: 本文档中提到的旧配置系统（unified_config.defaults.yaml 等）已标记为 **DEPRECATED**。
> 
> 请使用新的配置管理API。旧系统将在未来版本中移除。
> 
> 最后更新时间: {timestamp}

""".format(timestamp=datetime.now().strftime('%Y-%m-%d'))
            
            # 在文档开头添加警告
            if "DEPRECATED" not in content:
                content = deprecation_warning + content
                
                if not self.dry_run:
                    with open(guide_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.update_stats['files_updated'] += 1
                    self.update_stats['deprecated_sections_added'] += 1
                
                return {'success': True, 'action': 'added_deprecation_warning'}
            else:
                return {'success': True, 'action': 'already_updated'}
                
        except Exception as e:
            return {'success': False, 'reason': str(e)}
    
    def _create_config_guide(self) -> Dict[str, Any]:
        """创建配置管理指南"""
        guide_path = Path(self.doc_files['config_guide'])
        guide_path.parent.mkdir(parents=True, exist_ok=True)
        
        guide_content = """# 配置管理指南

## 概述

本项目采用新一代配置管理系统，提供统一、安全、可追踪的配置管理能力。

## 配置优先级

配置系统采用四层优先级结构：

1. **Defaults** (最低优先级) - 代码中的默认值
2. **Files** - 配置文件中的值
3. **Environment** - 环境变量覆盖
4. **Runtime** (最高优先级) - 运行时动态设置

## 基本用法

### 获取配置

```python
from backend.config.manager import get_config_manager

config_manager = get_config_manager()

# 基本获取
debug_mode = config_manager.get("app.debug", False)

# 获取配置段
db_config = config_manager.get_section("database")

# 获取配置来源
value, source = config_manager.get_with_source("app.debug")
```

### 环境变量覆盖

使用 `AID_CONF__` 前缀设置环境变量：

```bash
# 设置 app.debug = true
export AID_CONF__APP__DEBUG=true

# 设置 database.host = "prod-db"
export AID_CONF__DATABASE__HOST=prod-db

# 设置复杂值（JSON格式）
export AID_CONF__LLM__MODELS='["gpt-4", "claude-3"]'
```

### 运行时配置

```python
# 设置运行时配置
config_manager.set_runtime("app.temp_setting", "temp_value")

# 清除运行时配置
config_manager.clear_runtime("app.temp_setting")
```

## 配置验证

系统使用 Pydantic 进行配置验证：

```python
from backend.config.validation.schemas import AppConfigSchema

# 配置会自动验证
app_config = config_manager.get_section("app")
# 如果配置不符合 Schema，会抛出验证错误
```

## 监控和告警

### 查看配置监控

```bash
# 查看监控指标
python tools/config_monitor.py metrics

# 查看告警
python tools/config_monitor.py alerts

# 实时监控
python tools/config_monitor.py watch
```

### Web监控面板

访问 `http://localhost:8000/static/monitoring_dashboard.html` 查看Web监控面板。

## 最佳实践

1. **使用环境变量**: 敏感配置通过环境变量设置
2. **配置验证**: 定义 Pydantic Schema 验证配置结构
3. **来源追踪**: 使用 `get_with_source()` 了解配置来源
4. **监控告警**: 关注配置系统的健康状态
5. **文档更新**: 及时更新配置文档

## 迁移指南

### 从旧系统迁移

旧配置系统已废弃，请按以下方式迁移：

| 旧API | 新API |
|-------|-------|
| `unified_config.get()` | `config_manager.get()` |
| `load_config_from_yaml()` | `config_manager.get_section()` |
| `get_config_value()` | `config_manager.get()` |

### 配置文件迁移

- ❌ `unified_config.defaults.yaml` (已废弃)
- ✅ `backend/config/defaults/` 目录下的模块化配置

## 故障排除

### 常见问题

1. **配置未生效**: 检查配置优先级和来源
2. **环境变量格式错误**: 确保使用正确的 `AID_CONF__` 前缀
3. **验证失败**: 检查配置值是否符合 Schema 定义

### 调试工具

```bash
# 查看配置来源
python tools/config_monitor.py sources

# 检查配置文件
python tools/config_static_checker.py

# 分析配置影响
python tools/config_impact_analyzer.py analyze app.debug
```

## 参考资料

- [配置管理架构升级实施跟踪文档](配置管理架构升级实施跟踪文档.md)
- [配置管理最佳实践](配置管理最佳实践.md)
- [API文档](../api/配置管理API文档.md)
"""
        
        if not self.dry_run:
            with open(guide_path, 'w', encoding='utf-8') as f:
                f.write(guide_content)
            
            self.update_stats['files_updated'] += 1
            self.update_stats['migration_guides_updated'] += 1
        
        return {'success': True, 'action': 'created_guide'}
    
    def _update_implementation_guide(self) -> Dict[str, Any]:
        """更新实施跟踪文档"""
        impl_path = Path(self.doc_files['implementation_guide'])
        
        if not impl_path.exists():
            return {'success': False, 'reason': 'file_not_found'}
        
        try:
            with open(impl_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 添加完成状态更新
            completion_update = f"""
## 项目完成状态更新

**更新时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

### ✅ 已完成任务

- [x] 配置优先级链与托底机制
- [x] 配置完整性校验与CI门禁  
- [x] 环境变量覆盖与安全白名单
- [x] 兼容层与键映射
- [x] 系统依赖图与影响分析
- [x] 分批灰度迁移规划
- [x] 监控指标与告警
- [x] 自动化验证与门禁完善
- [x] 旧托底使用度监控与清理

### 🎯 项目成果

1. **完整的配置管理体系**: 建立了从默认值到运行时的四层配置优先级
2. **强大的验证机制**: 基于Pydantic的配置验证和CI门禁
3. **全面的监控告警**: 实时监控配置系统健康状态
4. **安全的迁移方案**: 分批灰度迁移，确保业务连续性
5. **智能化清理工具**: 自动识别和清理废弃配置

### ⚠️ 旧系统废弃声明

旧配置系统（unified_config.defaults.yaml等）已正式标记为 **DEPRECATED**，建议尽快迁移到新系统。

"""
            
            # 在文档末尾添加完成状态
            if "项目完成状态更新" not in content:
                content += completion_update
                
                if not self.dry_run:
                    with open(impl_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.update_stats['files_updated'] += 1
                
                return {'success': True, 'action': 'added_completion_status'}
            else:
                return {'success': True, 'action': 'already_updated'}
                
        except Exception as e:
            return {'success': False, 'reason': str(e)}
    
    def _update_project_summary(self) -> Dict[str, Any]:
        """更新项目总结"""
        summary_path = Path(self.doc_files['project_summary'])
        
        if not summary_path.exists():
            return {'success': False, 'reason': 'file_not_found'}
        
        try:
            with open(summary_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 添加最终完成状态
            final_status = f"""
---

## 🎉 项目最终完成状态

**完成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**项目状态**: ✅ 已完成
**整体进度**: 100%

### 核心成就

1. **架构升级完成**: 新配置管理系统全面上线
2. **质量保障到位**: 完整的测试覆盖和CI门禁
3. **监控体系建立**: 实时监控和告警机制
4. **文档体系完善**: 全面的使用指南和最佳实践
5. **平滑迁移实现**: 零停机的系统升级

### 业务价值实现

- **系统稳定性**: 通过配置验证和托底机制大幅提升
- **运维效率**: 环境变量覆盖和监控工具显著提升
- **开发体验**: 完善的工具链和文档提升开发效率
- **安全合规**: 敏感信息保护和访问审计满足要求

### 后续维护

- 定期查看配置监控面板
- 关注旧系统使用度趋势
- 及时处理配置告警
- 持续优化配置结构

**项目圆满完成！** 🎊

"""
            
            # 在文档末尾添加最终状态
            if "项目最终完成状态" not in content:
                content += final_status
                
                if not self.dry_run:
                    with open(summary_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.update_stats['files_updated'] += 1
                
                return {'success': True, 'action': 'added_final_status'}
            else:
                return {'success': True, 'action': 'already_updated'}
                
        except Exception as e:
            return {'success': False, 'reason': str(e)}
    
    def _create_api_documentation(self) -> Dict[str, Any]:
        """创建API文档"""
        api_path = Path(self.doc_files['api_docs'])
        api_path.parent.mkdir(parents=True, exist_ok=True)
        
        api_content = """# 配置管理API文档

## ConfigManager 类

### 基本方法

#### get(key: str, default: Any = None) -> Any
获取配置值。

```python
debug_mode = config_manager.get("app.debug", False)
```

#### get_with_source(key: str) -> Tuple[Any, str]
获取配置值及其来源。

```python
value, source = config_manager.get_with_source("app.debug")
# source 可能的值: 'defaults', 'config_files', 'environment', 'runtime'
```

#### get_section(key: str) -> Dict[str, Any]
获取配置段。

```python
db_config = config_manager.get_section("database")
```

#### set_runtime(key: str, value: Any) -> None
设置运行时配置。

```python
config_manager.set_runtime("app.temp_setting", "temp_value")
```

#### clear_runtime(key: str) -> None
清除运行时配置。

```python
config_manager.clear_runtime("app.temp_setting")
```

### 高级方法

#### reload() -> None
重新加载配置。

```python
config_manager.reload()
```

#### dump_config(include_sources: bool = False) -> Dict[str, Any]
导出配置数据。

```python
config_data = config_manager.dump_config(include_sources=True)
```

#### get_config_summary() -> Dict[str, Any]
获取配置摘要。

```python
summary = config_manager.get_config_summary()
```

## 监控API

### 健康检查
```
GET /api/monitoring/health
```

### 获取指标
```
GET /api/monitoring/metrics
```

### 获取告警
```
GET /api/monitoring/alerts?limit=20
```

### 获取监控面板数据
```
GET /api/monitoring/dashboard
```

## 环境变量格式

### 基本格式
```
AID_CONF__<SECTION>__<KEY>=<VALUE>
```

### 示例
```bash
# 简单值
export AID_CONF__APP__DEBUG=true
export AID_CONF__DATABASE__HOST=localhost

# 复杂值（JSON）
export AID_CONF__LLM__MODELS='["gpt-4", "claude-3"]'
export AID_CONF__DATABASE__CONFIG='{"timeout": 30, "pool_size": 10}'
```

## 错误处理

### 配置验证错误
```python
from pydantic import ValidationError

try:
    config = config_manager.get_section("app")
except ValidationError as e:
    print(f"配置验证失败: {e}")
```

### 配置缺失处理
```python
# 使用默认值
value = config_manager.get("missing.key", "default_value")

# 检查配置是否存在
if config_manager.get("optional.key") is not None:
    # 配置存在
    pass
```
"""
        
        if not self.dry_run:
            with open(api_path, 'w', encoding='utf-8') as f:
                f.write(api_content)
            
            self.update_stats['files_updated'] += 1
            self.update_stats['api_docs_updated'] += 1
        
        return {'success': True, 'action': 'created_api_docs'}
    
    def _create_best_practices_doc(self) -> Dict[str, Any]:
        """创建最佳实践文档"""
        bp_path = Path(self.doc_files['best_practices'])
        bp_path.parent.mkdir(parents=True, exist_ok=True)
        
        bp_content = """# 配置管理最佳实践

## 配置设计原则

### 1. 分层设计
- **默认值**: 在代码中提供合理的默认值
- **配置文件**: 环境相关的配置
- **环境变量**: 敏感信息和部署特定配置
- **运行时**: 临时和动态配置

### 2. 安全优先
- 敏感信息必须通过环境变量设置
- 使用配置白名单限制可覆盖的键
- 启用敏感信息自动脱敏

### 3. 可观测性
- 启用配置监控和告警
- 记录配置变更历史
- 追踪配置来源

## 开发最佳实践

### 配置键命名规范
```
<模块>.<子模块>.<配置项>

示例:
- app.debug
- database.connection.host
- llm.models.default
- logging.level.root
```

### 环境变量命名
```bash
# 格式: AID_CONF__<模块>__<子模块>__<配置项>
export AID_CONF__APP__DEBUG=true
export AID_CONF__DATABASE__CONNECTION__HOST=localhost
```

### 配置验证
```python
from pydantic import BaseModel, Field

class DatabaseConfig(BaseModel):
    host: str = Field(default="localhost", description="数据库主机")
    port: int = Field(default=5432, ge=1, le=65535, description="数据库端口")
    timeout: int = Field(default=30, ge=1, description="连接超时时间")
```

## 部署最佳实践

### 环境配置管理
```bash
# 开发环境
export AID_CONF__APP__DEBUG=true
export AID_CONF__LOGGING__LEVEL=DEBUG

# 生产环境
export AID_CONF__APP__DEBUG=false
export AID_CONF__LOGGING__LEVEL=INFO
export AID_CONF__DATABASE__HOST=prod-db.example.com
```

### 配置文件组织
```
backend/config/
├── defaults/
│   ├── app.yaml
│   ├── database.yaml
│   └── llm.yaml
├── environments/
│   ├── development.yaml
│   ├── staging.yaml
│   └── production.yaml
└── schemas/
    ├── app_schema.py
    └── database_schema.py
```

## 监控最佳实践

### 关键指标监控
- 配置加载时间
- 配置验证错误率
- 环境变量覆盖频率
- 配置降级次数

### 告警设置
```yaml
# 关键告警
- name: config_load_failure
  threshold: 0
  severity: critical

# 性能告警  
- name: slow_config_load
  threshold: 5.0
  severity: high
```

### 监控面板
定期查看配置监控面板，关注：
- 系统健康状态
- 配置使用趋势
- 告警历史
- 性能指标

## 故障处理最佳实践

### 配置问题诊断
1. 检查配置来源: `config_manager.get_with_source(key)`
2. 验证配置格式: 运行静态检查工具
3. 查看监控指标: 检查是否有异常
4. 检查环境变量: 确认格式和值正确

### 应急处理
```python
# 临时修复配置问题
config_manager.set_runtime("problematic.key", "safe_value")

# 回退到默认值
config_manager.clear_runtime("problematic.key")
```

## 迁移最佳实践

### 渐进式迁移
1. 保持旧系统运行
2. 逐步迁移配置键
3. 验证新系统功能
4. 监控迁移效果
5. 清理旧系统

### 兼容性保持
- 使用兼容层包装旧API
- 提供迁移工具和脚本
- 详细的迁移文档
- 充分的测试覆盖

## 性能优化

### 配置缓存
- 启用配置缓存减少重复加载
- 合理设置缓存过期时间
- 监控缓存命中率

### 延迟加载
```python
# 延迟加载大型配置
@property
def large_config(self):
    if not hasattr(self, '_large_config'):
        self._large_config = config_manager.get_section("large_section")
    return self._large_config
```

## 安全最佳实践

### 敏感信息处理
- 永远不要在代码中硬编码敏感信息
- 使用环境变量或密钥管理系统
- 启用自动脱敏功能
- 定期轮换敏感配置

### 访问控制
- 限制配置文件访问权限
- 使用配置白名单
- 记录配置访问日志
- 定期审计配置安全

## 测试最佳实践

### 配置测试
```python
def test_config_loading():
    config_manager = ConfigManager()
    assert config_manager.get("app.debug") is not None
    
def test_env_override():
    os.environ["AID_CONF__APP__DEBUG"] = "true"
    config_manager = ConfigManager(enable_env_override=True)
    assert config_manager.get("app.debug") is True
```

### 集成测试
- 测试配置在不同环境下的行为
- 验证配置验证逻辑
- 测试配置热重载
- 验证监控告警功能
"""
        
        if not self.dry_run:
            with open(bp_path, 'w', encoding='utf-8') as f:
                f.write(bp_content)
            
            self.update_stats['files_updated'] += 1
        
        return {'success': True, 'action': 'created_best_practices'}


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="文档更新工具")
    parser.add_argument("--dry-run", action="store_true", default=True, help="试运行模式")
    parser.add_argument("--execute", action="store_true", help="执行实际更新")
    parser.add_argument("--output", help="输出报告文件路径")
    
    args = parser.parse_args()
    
    try:
        # 确定运行模式
        dry_run = not args.execute
        if args.execute:
            print("⚠️  执行模式：将进行实际文档更新")
            confirm = input("确认继续？(yes/no): ")
            if confirm.lower() != 'yes':
                print("❌ 操作已取消")
                return 1
        
        updater = DocumentationUpdater(dry_run=dry_run)
        results = updater.update_all_documentation()
        
        # 保存结果
        if args.output:
            import json
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"📄 更新报告已保存到: {args.output}")
        
        # 显示摘要
        print("\n📚 文档更新摘要:")
        stats = results['stats']
        print(f"  更新文件数: {stats['files_updated']}")
        print(f"  添加废弃标注: {stats['deprecated_sections_added']}")
        print(f"  更新迁移指南: {stats['migration_guides_updated']}")
        print(f"  更新API文档: {stats['api_docs_updated']}")
        
        if results['success']:
            print("\n✅ 文档更新完成")
        else:
            print("\n❌ 部分文档更新失败")
        
        return 0 if results['success'] else 1
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
