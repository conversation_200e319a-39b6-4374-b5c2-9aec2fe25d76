#!/usr/bin/env python3
"""
环境变量配置工具集

提供环境变量配置的生成、验证、对比等工具
"""

import argparse
import json
import os
import sys
import yaml
from pathlib import Path
from typing import Dict, List, Any, Set, Tuple

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from backend.config.manager import ConfigManager
from backend.config.env_parser import EnvironmentVariableParser
from backend.config.security.whitelist_manager import ConfigWhitelistManager


class EnvConfigTools:
    """环境变量配置工具"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.parser = EnvironmentVariableParser()
        self.whitelist_manager = ConfigWhitelistManager()
    
    def generate_env_template(self, output_file: str = None, 
                            include_examples: bool = True,
                            include_sensitive: bool = False) -> str:
        """生成环境变量模板"""
        template_lines = [
            "# AI Assistant 环境变量配置模板",
            "# 生成时间: " + str(Path(__file__).stat().st_mtime),
            "",
            "# 使用说明:",
            "# 1. 复制此文件为 .env",
            "# 2. 根据需要修改配置值",
            "# 3. 敏感信息请使用实际值替换",
            "",
        ]
        
        # 获取允许的配置键
        allowed_keys = self.whitelist_manager.get_allowed_keys()
        
        # 按类别组织配置键
        categories = self._categorize_keys(allowed_keys)
        
        for category, keys in categories.items():
            template_lines.append(f"# {category}")
            template_lines.append("# " + "=" * (len(category) + 2))
            
            for key in sorted(keys):
                env_name = f"AID_CONF__{key.upper().replace('.', '__')}"
                
                # 检查是否敏感
                is_sensitive, sensitivity_level, reason = self.whitelist_manager.detector.detect_sensitive_key(key)
                
                if is_sensitive and not include_sensitive:
                    template_lines.append(f"# {env_name}=<请设置{reason}>")
                else:
                    # 获取当前值作为示例
                    current_value = self.config_manager.get(key)
                    if current_value is not None and include_examples:
                        if isinstance(current_value, (dict, list)):
                            example_value = json.dumps(current_value)
                        else:
                            example_value = str(current_value)
                        
                        if is_sensitive:
                            example_value = f"<{reason}>"
                        
                        template_lines.append(f"{env_name}={example_value}")
                    else:
                        template_lines.append(f"# {env_name}=")
            
            template_lines.append("")
        
        template_content = "\n".join(template_lines)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(template_content)
            print(f"环境变量模板已生成: {output_file}")
        
        return template_content
    
    def _categorize_keys(self, keys: Set[str]) -> Dict[str, List[str]]:
        """将配置键按类别分组"""
        categories = {
            "应用配置": [],
            "LLM配置": [],
            "数据库配置": [],
            "系统配置": [],
            "性能配置": [],
            "安全配置": [],
            "业务配置": [],
            "其他配置": []
        }
        
        for key in keys:
            if key.startswith("app."):
                categories["应用配置"].append(key)
            elif key.startswith("llm."):
                categories["LLM配置"].append(key)
            elif key.startswith("database."):
                categories["数据库配置"].append(key)
            elif key.startswith("system."):
                categories["系统配置"].append(key)
            elif key.startswith("performance."):
                categories["性能配置"].append(key)
            elif key.startswith(("security.", "auth.")):
                categories["安全配置"].append(key)
            elif key.startswith("business."):
                categories["业务配置"].append(key)
            else:
                categories["其他配置"].append(key)
        
        # 移除空类别
        return {k: v for k, v in categories.items() if v}
    
    def validate_current_env(self) -> Dict[str, Any]:
        """验证当前环境变量"""
        results = {
            "total_env_vars": 0,
            "valid_overrides": 0,
            "invalid_overrides": 0,
            "ignored_vars": 0,
            "validation_results": []
        }
        
        # 获取所有AID_CONF__开头的环境变量
        aid_env_vars = {k: v for k, v in os.environ.items() 
                       if k.startswith(self.parser.ENV_PREFIX)}
        
        results["total_env_vars"] = len(aid_env_vars)
        
        for env_name, env_value in aid_env_vars.items():
            try:
                # 转换为配置键
                config_key = self.parser._env_name_to_config_key(env_name)
                
                # 验证是否允许覆盖
                is_valid, message = self.config_manager.validate_env_override(config_key, env_value)
                
                validation_result = {
                    "env_name": env_name,
                    "config_key": config_key,
                    "env_value": env_value[:50] + "..." if len(env_value) > 50 else env_value,
                    "is_valid": is_valid,
                    "message": message
                }
                
                results["validation_results"].append(validation_result)
                
                if is_valid:
                    results["valid_overrides"] += 1
                else:
                    results["invalid_overrides"] += 1
                    
            except Exception as e:
                results["ignored_vars"] += 1
                results["validation_results"].append({
                    "env_name": env_name,
                    "config_key": "解析失败",
                    "env_value": env_value[:50] + "..." if len(env_value) > 50 else env_value,
                    "is_valid": False,
                    "message": f"解析错误: {e}"
                })
        
        return results
    
    def compare_configs(self, env1: str, env2: str) -> Dict[str, Any]:
        """对比不同环境的配置"""
        # 这里简化实现，实际可以加载不同环境的配置文件
        print(f"对比环境 {env1} 和 {env2} 的配置差异...")
        
        # 获取当前配置
        current_config = self.config_manager.dump_config()
        
        # 模拟不同环境的配置差异
        differences = {
            "added_in_env2": [],
            "removed_in_env2": [],
            "changed_values": [],
            "same_values": []
        }
        
        return differences
    
    def scan_sensitive_info(self, directory: str = "backend/config") -> Dict[str, Any]:
        """扫描配置文件中的敏感信息"""
        results = {
            "scanned_files": 0,
            "sensitive_findings": [],
            "warnings": []
        }
        
        config_dir = Path(directory)
        
        for config_file in config_dir.rglob("*.yaml"):
            results["scanned_files"] += 1
            
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    config_data = yaml.safe_load(content)
                
                # 递归检查配置值
                self._scan_config_values(config_data, str(config_file), "", results)
                
            except Exception as e:
                results["warnings"].append(f"无法扫描文件 {config_file}: {e}")
        
        return results
    
    def _scan_config_values(self, config: Any, file_path: str, key_path: str, results: Dict[str, Any]):
        """递归扫描配置值中的敏感信息"""
        if isinstance(config, dict):
            for key, value in config.items():
                current_path = f"{key_path}.{key}" if key_path else key
                self._scan_config_values(value, file_path, current_path, results)
        elif isinstance(config, list):
            for i, item in enumerate(config):
                current_path = f"{key_path}[{i}]"
                self._scan_config_values(item, file_path, current_path, results)
        elif isinstance(config, str):
            # 检查字符串值是否敏感
            is_sensitive, sensitivity_level, reason = self.whitelist_manager.detector.detect_sensitive_value(
                config, key_path
            )
            
            if is_sensitive:
                results["sensitive_findings"].append({
                    "file": file_path,
                    "key": key_path,
                    "sensitivity_level": sensitivity_level.value,
                    "reason": reason,
                    "value_preview": config[:20] + "..." if len(config) > 20 else config
                })
    
    def generate_deployment_env(self, environment: str, output_file: str = None) -> str:
        """生成部署环境的环境变量文件"""
        env_configs = {
            "development": {
                "AID_CONF__APP__DEBUG": "true",
                "AID_CONF__APP__LOG_LEVEL": "DEBUG",
                "AID_CONF__APP__ENVIRONMENT": "development",
                "AID_CONF__LLM__TEMPERATURE": "0.7",
                "AID_CONF__DATABASE__CONNECTION__TIMEOUT": "30",
                "AID_CONF__PERFORMANCE__CACHE__ENABLED": "true"
            },
            "staging": {
                "AID_CONF__APP__DEBUG": "false",
                "AID_CONF__APP__LOG_LEVEL": "INFO",
                "AID_CONF__APP__ENVIRONMENT": "staging",
                "AID_CONF__LLM__TEMPERATURE": "0.5",
                "AID_CONF__DATABASE__CONNECTION__TIMEOUT": "45",
                "AID_CONF__PERFORMANCE__CACHE__ENABLED": "true"
            },
            "production": {
                "AID_CONF__APP__DEBUG": "false",
                "AID_CONF__APP__LOG_LEVEL": "WARNING",
                "AID_CONF__APP__ENVIRONMENT": "production",
                "AID_CONF__LLM__TEMPERATURE": "0.3",
                "AID_CONF__DATABASE__CONNECTION__TIMEOUT": "60",
                "AID_CONF__PERFORMANCE__CACHE__ENABLED": "true"
            }
        }
        
        if environment not in env_configs:
            raise ValueError(f"不支持的环境: {environment}")
        
        config = env_configs[environment]
        
        lines = [
            f"# {environment.upper()} 环境配置",
            f"# 生成时间: {Path(__file__).stat().st_mtime}",
            "",
        ]
        
        for key, value in config.items():
            lines.append(f"{key}={value}")
        
        # 添加敏感配置占位符
        lines.extend([
            "",
            "# 敏感配置 - 请手动设置",
            "# AID_CONF__LLM__OPENAI__API_KEY=<your-openai-api-key>",
            "# AID_CONF__DATABASE__PASSWORD=<your-database-password>",
        ])
        
        content = "\n".join(lines)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"{environment} 环境配置已生成: {output_file}")
        
        return content


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="环境变量配置工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 生成模板命令
    template_parser = subparsers.add_parser("template", help="生成环境变量模板")
    template_parser.add_argument("--output", "-o", help="输出文件路径")
    template_parser.add_argument("--no-examples", action="store_true", help="不包含示例值")
    template_parser.add_argument("--include-sensitive", action="store_true", help="包含敏感配置")
    
    # 验证命令
    validate_parser = subparsers.add_parser("validate", help="验证当前环境变量")
    validate_parser.add_argument("--format", choices=["text", "json"], default="text", help="输出格式")
    
    # 对比命令
    compare_parser = subparsers.add_parser("compare", help="对比不同环境配置")
    compare_parser.add_argument("env1", help="环境1")
    compare_parser.add_argument("env2", help="环境2")
    
    # 扫描命令
    scan_parser = subparsers.add_parser("scan", help="扫描敏感信息")
    scan_parser.add_argument("--directory", "-d", default="backend/config", help="扫描目录")
    
    # 部署命令
    deploy_parser = subparsers.add_parser("deploy", help="生成部署环境配置")
    deploy_parser.add_argument("environment", choices=["development", "staging", "production"], help="环境名称")
    deploy_parser.add_argument("--output", "-o", help="输出文件路径")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    tools = EnvConfigTools()
    
    try:
        if args.command == "template":
            tools.generate_env_template(
                output_file=args.output,
                include_examples=not args.no_examples,
                include_sensitive=args.include_sensitive
            )
        
        elif args.command == "validate":
            results = tools.validate_current_env()
            
            if args.format == "json":
                print(json.dumps(results, indent=2, ensure_ascii=False))
            else:
                print("环境变量验证结果:")
                print("=" * 50)
                print(f"总环境变量数: {results['total_env_vars']}")
                print(f"有效覆盖: {results['valid_overrides']}")
                print(f"无效覆盖: {results['invalid_overrides']}")
                print(f"忽略变量: {results['ignored_vars']}")
                
                if results['validation_results']:
                    print("\n详细结果:")
                    for result in results['validation_results']:
                        status = "✅" if result['is_valid'] else "❌"
                        print(f"{status} {result['env_name']} -> {result['config_key']}")
                        if not result['is_valid']:
                            print(f"   原因: {result['message']}")
        
        elif args.command == "compare":
            results = tools.compare_configs(args.env1, args.env2)
            print(f"配置对比结果: {args.env1} vs {args.env2}")
            print(json.dumps(results, indent=2, ensure_ascii=False))
        
        elif args.command == "scan":
            results = tools.scan_sensitive_info(args.directory)
            print("敏感信息扫描结果:")
            print("=" * 50)
            print(f"扫描文件数: {results['scanned_files']}")
            print(f"发现敏感信息: {len(results['sensitive_findings'])}")
            
            if results['sensitive_findings']:
                print("\n敏感信息详情:")
                for finding in results['sensitive_findings']:
                    print(f"❌ {finding['file']}:{finding['key']}")
                    print(f"   级别: {finding['sensitivity_level']}")
                    print(f"   原因: {finding['reason']}")
            
            if results['warnings']:
                print("\n警告:")
                for warning in results['warnings']:
                    print(f"⚠️  {warning}")
        
        elif args.command == "deploy":
            tools.generate_deployment_env(args.environment, args.output)
    
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
