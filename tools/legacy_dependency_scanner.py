#!/usr/bin/env python3
"""
旧配置依赖扫描工具

扫描项目中的旧配置系统依赖，生成迁移报告和热力图
"""

import argparse
import json
import re
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple, Set
from collections import defaultdict
import subprocess


class LegacyDependencyScanner:
    """旧配置依赖扫描器"""
    
    def __init__(self):
        self.legacy_patterns = {
            "unified_config_defaults_yaml": {
                "pattern": r"unified_config\.defaults\.yaml",
                "description": "旧的默认配置文件",
                "severity": "high",
                "replacement": "使用 backend/config/defaults/ 目录下的模块化配置"
            },
            "unified_config_loader": {
                "pattern": r"unified_config_loader",
                "description": "旧的配置加载器",
                "severity": "high",
                "replacement": "使用 ConfigManager 类"
            },
            "get_config_value": {
                "pattern": r"get_config_value",
                "description": "旧的配置获取函数",
                "severity": "high",
                "replacement": "使用 config.get() 方法"
            },
            "unified_dynamic_config": {
                "pattern": r"unified_dynamic_config",
                "description": "旧的动态配置",
                "severity": "medium",
                "replacement": "使用新的配置管理接口"
            },
            "from_unified_config": {
                "pattern": r"from.*unified_config",
                "description": "从旧配置模块导入",
                "severity": "high",
                "replacement": "从新的配置模块导入"
            },
            "import_unified_config": {
                "pattern": r"import.*unified_config",
                "description": "导入旧配置模块",
                "severity": "high",
                "replacement": "导入新的配置模块"
            },
            "UnifiedConfigLoader": {
                "pattern": r"UnifiedConfigLoader",
                "description": "旧的配置加载器类",
                "severity": "high",
                "replacement": "使用 ConfigManager 类"
            },
            "UnifiedDynamicConfig": {
                "pattern": r"UnifiedDynamicConfig",
                "description": "旧的动态配置类",
                "severity": "medium",
                "replacement": "使用新的配置管理类"
            }
        }
        
        self.exemption_patterns = [
            r"#\s*LEGACY-EXEMPT",
            r"#\s*legacy-exempt",
            r"#\s*TODO.*legacy",
            r"#\s*FIXME.*legacy"
        ]
    
    def scan_file(self, file_path: Path) -> Dict[str, Any]:
        """扫描单个文件"""
        result = {
            "file": str(file_path),
            "violations": [],
            "exemptions": [],
            "total_violations": 0,
            "severity_counts": {"high": 0, "medium": 0, "low": 0}
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                # 检查豁免标记
                exemption_found = False
                for exemption_pattern in self.exemption_patterns:
                    if re.search(exemption_pattern, line, re.IGNORECASE):
                        result["exemptions"].append({
                            "line": line_num,
                            "content": line.strip(),
                            "type": "exemption_comment"
                        })
                        exemption_found = True
                        break
                
                # 如果有豁免标记，跳过该行的违规检查
                if exemption_found:
                    continue
                
                # 检查旧配置依赖
                for pattern_name, pattern_info in self.legacy_patterns.items():
                    if re.search(pattern_info["pattern"], line):
                        violation = {
                            "line": line_num,
                            "content": line.strip(),
                            "pattern": pattern_name,
                            "description": pattern_info["description"],
                            "severity": pattern_info["severity"],
                            "replacement": pattern_info["replacement"]
                        }
                        result["violations"].append(violation)
                        result["total_violations"] += 1
                        result["severity_counts"][pattern_info["severity"]] += 1
        
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def scan_directory(self, directory: Path, exclude_patterns: List[str] = None) -> Dict[str, Any]:
        """扫描目录"""
        if exclude_patterns is None:
            exclude_patterns = [
                r"__pycache__",
                r"\.git",
                r"\.pytest_cache",
                r"node_modules",
                r"venv",
                r"\.venv",
                r"build",
                r"dist"
            ]
        
        results = {
            "scan_directory": str(directory),
            "total_files": 0,
            "files_with_violations": 0,
            "total_violations": 0,
            "severity_summary": {"high": 0, "medium": 0, "low": 0},
            "file_results": [],
            "violation_summary": defaultdict(int),
            "hotspots": []
        }
        
        # 查找Python文件
        python_files = []
        for py_file in directory.rglob("*.py"):
            # 检查是否应该排除
            should_exclude = False
            for exclude_pattern in exclude_patterns:
                if re.search(exclude_pattern, str(py_file)):
                    should_exclude = True
                    break
            
            if not should_exclude:
                python_files.append(py_file)
        
        results["total_files"] = len(python_files)
        
        # 扫描每个文件
        for py_file in python_files:
            file_result = self.scan_file(py_file)
            
            if file_result["total_violations"] > 0:
                results["files_with_violations"] += 1
                results["total_violations"] += file_result["total_violations"]
                
                # 更新严重性统计
                for severity, count in file_result["severity_counts"].items():
                    results["severity_summary"][severity] += count
                
                # 更新违规类型统计
                for violation in file_result["violations"]:
                    results["violation_summary"][violation["pattern"]] += 1
                
                results["file_results"].append(file_result)
        
        # 生成热点文件列表（按违规数量排序）
        hotspots = sorted(
            [fr for fr in results["file_results"] if fr["total_violations"] > 0],
            key=lambda x: x["total_violations"],
            reverse=True
        )
        results["hotspots"] = hotspots[:10]  # 前10个热点文件
        
        return results
    
    def generate_migration_priority(self, scan_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成迁移优先级列表"""
        priority_list = []
        
        for file_result in scan_results["file_results"]:
            if file_result["total_violations"] == 0:
                continue
            
            # 计算优先级分数
            score = 0
            high_count = file_result["severity_counts"]["high"]
            medium_count = file_result["severity_counts"]["medium"]
            low_count = file_result["severity_counts"]["low"]
            
            score = high_count * 3 + medium_count * 2 + low_count * 1
            
            # 确定优先级等级
            if score >= 10:
                priority = "P0"
            elif score >= 5:
                priority = "P1"
            elif score >= 2:
                priority = "P2"
            else:
                priority = "P3"
            
            priority_item = {
                "file": file_result["file"],
                "priority": priority,
                "score": score,
                "violations": file_result["total_violations"],
                "high_severity": high_count,
                "medium_severity": medium_count,
                "low_severity": low_count,
                "has_exemptions": len(file_result.get("exemptions", [])) > 0
            }
            
            priority_list.append(priority_item)
        
        # 按优先级和分数排序
        priority_order = {"P0": 0, "P1": 1, "P2": 2, "P3": 3}
        priority_list.sort(key=lambda x: (priority_order[x["priority"]], -x["score"]))
        
        return priority_list
    
    def generate_report(self, scan_results: Dict[str, Any], format: str = "text") -> str:
        """生成扫描报告"""
        if format == "json":
            return json.dumps(scan_results, indent=2, ensure_ascii=False)
        
        lines = ["旧配置依赖扫描报告", "=" * 50, ""]
        
        # 总体统计
        lines.extend([
            f"📊 扫描统计",
            f"   扫描目录: {scan_results['scan_directory']}",
            f"   总文件数: {scan_results['total_files']}",
            f"   有违规文件: {scan_results['files_with_violations']}",
            f"   总违规数: {scan_results['total_violations']}",
            ""
        ])
        
        # 严重性分布
        severity_summary = scan_results["severity_summary"]
        lines.extend([
            f"🚨 严重性分布",
            f"   高危 (High): {severity_summary['high']}",
            f"   中危 (Medium): {severity_summary['medium']}",
            f"   低危 (Low): {severity_summary['low']}",
            ""
        ])
        
        # 违规类型统计
        if scan_results["violation_summary"]:
            lines.append("📋 违规类型统计:")
            for pattern, count in sorted(scan_results["violation_summary"].items(), 
                                       key=lambda x: x[1], reverse=True):
                pattern_info = self.legacy_patterns.get(pattern, {})
                description = pattern_info.get("description", pattern)
                lines.append(f"   {description}: {count}")
            lines.append("")
        
        # 热点文件
        if scan_results["hotspots"]:
            lines.append("🔥 热点文件 (前10个):")
            for i, hotspot in enumerate(scan_results["hotspots"][:10], 1):
                file_path = hotspot["file"]
                violations = hotspot["total_violations"]
                high_count = hotspot["severity_counts"]["high"]
                lines.append(f"   {i}. {file_path} ({violations} 违规, {high_count} 高危)")
            lines.append("")
        
        # 迁移优先级
        priority_list = self.generate_migration_priority(scan_results)
        if priority_list:
            lines.append("⚡ 迁移优先级 (前10个):")
            for i, item in enumerate(priority_list[:10], 1):
                file_path = Path(item["file"]).name  # 只显示文件名
                priority = item["priority"]
                score = item["score"]
                violations = item["violations"]
                exemption_mark = " 🛡️" if item["has_exemptions"] else ""
                lines.append(f"   {i}. [{priority}] {file_path} (分数:{score}, 违规:{violations}){exemption_mark}")
            lines.append("")
        
        # 建议
        lines.extend([
            "💡 建议",
            "   1. 优先处理P0和P1级别的文件",
            "   2. 关注高危违规项，影响最大",
            "   3. 查看迁移指南: docs/development/配置管理指南.md",
            "   4. 使用配置验证工具验证迁移结果",
            ""
        ])
        
        return "\n".join(lines)
    
    def check_git_changes(self, base_branch: str = "main") -> Dict[str, Any]:
        """检查Git变更中的旧配置依赖"""
        try:
            # 获取变更的文件
            result = subprocess.run(
                ["git", "diff", "--name-only", f"origin/{base_branch}...HEAD"],
                capture_output=True, text=True, check=True
            )
            changed_files = [f.strip() for f in result.stdout.split('\n') if f.strip().endswith('.py')]
            
            # 获取新增的行
            violations = []
            for file_path in changed_files:
                if not Path(file_path).exists():
                    continue
                
                try:
                    result = subprocess.run(
                        ["git", "diff", f"origin/{base_branch}...HEAD", file_path],
                        capture_output=True, text=True, check=True
                    )
                    
                    added_lines = []
                    for line in result.stdout.split('\n'):
                        if line.startswith('+') and not line.startswith('+++'):
                            added_lines.append(line[1:])  # 移除+号
                    
                    # 检查新增行中的旧配置依赖
                    for line_content in added_lines:
                        for pattern_name, pattern_info in self.legacy_patterns.items():
                            if re.search(pattern_info["pattern"], line_content):
                                violations.append({
                                    "file": file_path,
                                    "content": line_content.strip(),
                                    "pattern": pattern_name,
                                    "description": pattern_info["description"],
                                    "severity": pattern_info["severity"]
                                })
                
                except subprocess.CalledProcessError:
                    continue
            
            return {
                "changed_files": changed_files,
                "violations": violations,
                "violation_count": len(violations)
            }
        
        except subprocess.CalledProcessError as e:
            return {
                "error": f"Git命令执行失败: {e}",
                "changed_files": [],
                "violations": [],
                "violation_count": 0
            }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="旧配置依赖扫描工具")
    parser.add_argument("--directory", default="backend", help="扫描目录")
    parser.add_argument("--format", choices=["text", "json"], default="text", help="输出格式")
    parser.add_argument("--output", help="输出文件路径")
    parser.add_argument("--git-check", action="store_true", help="检查Git变更")
    parser.add_argument("--base-branch", default="main", help="Git基础分支")
    parser.add_argument("--priority-only", action="store_true", help="只显示优先级列表")
    
    args = parser.parse_args()
    
    scanner = LegacyDependencyScanner()
    
    if args.git_check:
        # Git变更检查模式
        print("检查Git变更中的旧配置依赖...")
        git_results = scanner.check_git_changes(args.base_branch)
        
        if git_results.get("error"):
            print(f"❌ {git_results['error']}")
            return 1
        
        if git_results["violation_count"] > 0:
            print(f"❌ 在Git变更中发现 {git_results['violation_count']} 个旧配置依赖:")
            for violation in git_results["violations"]:
                print(f"   {violation['file']}: {violation['description']}")
                print(f"      内容: {violation['content']}")
            return 1
        else:
            print("✅ Git变更中未发现旧配置依赖")
            return 0
    
    else:
        # 目录扫描模式
        scan_dir = Path(args.directory)
        if not scan_dir.exists():
            print(f"❌ 目录不存在: {scan_dir}")
            return 1
        
        print(f"扫描目录: {scan_dir}")
        results = scanner.scan_directory(scan_dir)
        
        if args.priority_only:
            # 只显示优先级列表
            priority_list = scanner.generate_migration_priority(results)
            print("迁移优先级列表:")
            print("=" * 50)
            for i, item in enumerate(priority_list, 1):
                exemption_mark = " 🛡️" if item["has_exemptions"] else ""
                print(f"{i:2d}. [{item['priority']}] {item['file']} "
                      f"(分数:{item['score']}, 违规:{item['violations']}){exemption_mark}")
        else:
            # 生成完整报告
            report = scanner.generate_report(results, args.format)
            
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(report)
                print(f"报告已保存到: {args.output}")
            else:
                print(report)
        
        # 返回退出码
        if results["total_violations"] > 0:
            return 1
        else:
            return 0


if __name__ == "__main__":
    sys.exit(main())
