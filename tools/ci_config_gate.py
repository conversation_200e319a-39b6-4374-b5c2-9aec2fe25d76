#!/usr/bin/env python3
"""
CI配置门禁脚本

在CI/CD流程中执行配置相关的检查，确保配置变更的安全性和正确性
"""

import os
import sys
import json
import subprocess
import argparse
from pathlib import Path
from typing import List, Dict, Any, Tuple
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from tools.config_static_checker import ConfigStaticChecker
from tools.config_impact_analyzer import ConfigImpactAnalyzer
from tools.legacy_dependency_scanner import LegacyDependencyScanner


class CIConfigGate:
    """CI配置门禁"""
    
    def __init__(self):
        self.results = {
            'static_check': None,
            'impact_analysis': None,
            'legacy_scan': None,
            'test_results': None,
            'overall_status': 'unknown'
        }
        
        # 门禁阈值
        self.thresholds = {
            'max_errors': 0,           # 最大错误数
            'max_warnings': 10,        # 最大警告数
            'max_legacy_violations': 5, # 最大旧依赖违规数
            'min_test_coverage': 0.8,   # 最小测试覆盖率
            'max_critical_impact': 0    # 最大关键影响数
        }
        
        # 检查项配置
        self.check_config = {
            'run_static_check': True,
            'run_impact_analysis': True,
            'run_legacy_scan': True,
            'run_tests': True,
            'fail_fast': False  # 是否快速失败
        }
    
    def run_all_checks(self) -> bool:
        """运行所有检查"""
        print("🚀 开始CI配置门禁检查...")
        print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        success = True
        
        try:
            # 1. 静态检查
            if self.check_config['run_static_check']:
                if not self._run_static_check():
                    success = False
                    if self.check_config['fail_fast']:
                        return False
            
            # 2. 影响分析
            if self.check_config['run_impact_analysis']:
                if not self._run_impact_analysis():
                    success = False
                    if self.check_config['fail_fast']:
                        return False
            
            # 3. 旧依赖扫描
            if self.check_config['run_legacy_scan']:
                if not self._run_legacy_scan():
                    success = False
                    if self.check_config['fail_fast']:
                        return False
            
            # 4. 运行测试
            if self.check_config['run_tests']:
                if not self._run_tests():
                    success = False
                    if self.check_config['fail_fast']:
                        return False
            
            # 5. 综合评估
            self.results['overall_status'] = 'passed' if success else 'failed'
            
            return success
            
        except Exception as e:
            print(f"❌ 门禁检查过程中出现异常: {e}")
            self.results['overall_status'] = 'error'
            return False
    
    def _run_static_check(self) -> bool:
        """运行静态检查"""
        print("\n📋 执行配置静态检查...")
        
        try:
            checker = ConfigStaticChecker()
            results = checker.check_all()
            
            # 统计结果
            errors = checker.stats['errors']
            warnings = checker.stats['warnings']
            
            self.results['static_check'] = {
                'errors': errors,
                'warnings': warnings,
                'details': [
                    {
                        'type': r.check_type,
                        'severity': r.severity,
                        'message': r.message,
                        'file': r.file_path
                    }
                    for r in results
                ]
            }
            
            print(f"   错误: {errors}, 警告: {warnings}")
            
            # 检查阈值
            if errors > self.thresholds['max_errors']:
                print(f"❌ 错误数超过阈值: {errors} > {self.thresholds['max_errors']}")
                return False
            
            if warnings > self.thresholds['max_warnings']:
                print(f"⚠️  警告数超过阈值: {warnings} > {self.thresholds['max_warnings']}")
                return False
            
            print("✅ 静态检查通过")
            return True
            
        except Exception as e:
            print(f"❌ 静态检查失败: {e}")
            self.results['static_check'] = {'error': str(e)}
            return False
    
    def _run_impact_analysis(self) -> bool:
        """运行影响分析"""
        print("\n🔍 执行配置影响分析...")
        
        try:
            # 检测配置变更
            changed_keys = self._detect_config_changes()
            
            if not changed_keys:
                print("   未检测到配置变更")
                self.results['impact_analysis'] = {'changed_keys': [], 'impact': 'none'}
                return True
            
            print(f"   检测到 {len(changed_keys)} 个配置变更: {', '.join(changed_keys)}")
            
            # 分析影响
            analyzer = ConfigImpactAnalyzer()
            analysis = analyzer.analyze_multiple_keys(changed_keys)
            
            # 提取关键信息
            max_impact = analysis['combined_impact']['max_impact_level']
            affected_components = analysis['combined_impact']['affected_components']
            critical_count = len([
                key for key, data in analysis['individual_analysis'].items()
                if data['impact_analysis']['impact_level'] == 'critical'
            ])
            
            self.results['impact_analysis'] = {
                'changed_keys': changed_keys,
                'max_impact_level': max_impact,
                'affected_components': affected_components,
                'critical_impact_count': critical_count
            }
            
            print(f"   最大影响级别: {max_impact}")
            print(f"   影响组件数: {len(affected_components)}")
            print(f"   关键影响数: {critical_count}")
            
            # 检查阈值
            if critical_count > self.thresholds['max_critical_impact']:
                print(f"❌ 关键影响数超过阈值: {critical_count} > {self.thresholds['max_critical_impact']}")
                return False
            
            print("✅ 影响分析通过")
            return True
            
        except Exception as e:
            print(f"❌ 影响分析失败: {e}")
            self.results['impact_analysis'] = {'error': str(e)}
            return False
    
    def _detect_config_changes(self) -> List[str]:
        """检测配置变更"""
        try:
            # 使用git diff检测配置文件变更
            result = subprocess.run([
                'git', 'diff', '--name-only', 'HEAD~1', 'HEAD',
                '--', 'backend/config/', '*.yaml', '*.yml', '*.json'
            ], capture_output=True, text=True, cwd=Path.cwd())
            
            if result.returncode != 0:
                print(f"   Git diff执行失败: {result.stderr}")
                return []
            
            changed_files = result.stdout.strip().split('\n') if result.stdout.strip() else []
            
            # 简化实现：假设所有关键配置键都可能受影响
            if changed_files:
                return [
                    'app.debug', 'llm.default_model', 'database.connection.path',
                    'logging.level', 'performance.cache.enabled'
                ]
            
            return []
            
        except Exception as e:
            print(f"   检测配置变更失败: {e}")
            return []
    
    def _run_legacy_scan(self) -> bool:
        """运行旧依赖扫描"""
        print("\n🔍 执行旧依赖扫描...")
        
        try:
            scanner = LegacyDependencyScanner()
            
            # 扫描特定目录
            scan_dirs = ['backend/agents', 'backend/handlers', 'backend/services']
            all_violations = []
            
            for scan_dir in scan_dirs:
                if Path(scan_dir).exists():
                    violations = scanner.scan_directory(scan_dir)
                    all_violations.extend(violations)
            
            # 统计违规
            violation_count = len(all_violations)
            high_priority_count = len([v for v in all_violations if v.priority == 'P0'])
            
            self.results['legacy_scan'] = {
                'total_violations': violation_count,
                'high_priority_violations': high_priority_count,
                'violations': [
                    {
                        'file': v.file_path,
                        'line': v.line_number,
                        'type': v.violation_type,
                        'priority': v.priority,
                        'message': v.message
                    }
                    for v in all_violations[:20]  # 只保留前20个
                ]
            }
            
            print(f"   总违规数: {violation_count}")
            print(f"   高优先级违规: {high_priority_count}")
            
            # 检查阈值
            if high_priority_count > self.thresholds['max_legacy_violations']:
                print(f"❌ 旧依赖违规数超过阈值: {high_priority_count} > {self.thresholds['max_legacy_violations']}")
                return False
            
            print("✅ 旧依赖扫描通过")
            return True
            
        except Exception as e:
            print(f"❌ 旧依赖扫描失败: {e}")
            self.results['legacy_scan'] = {'error': str(e)}
            return False
    
    def _run_tests(self) -> bool:
        """运行测试"""
        print("\n🧪 执行配置相关测试...")
        
        try:
            # 运行配置相关的测试
            test_commands = [
                ['python', '-m', 'pytest', 'tests/config/', '-v', '--tb=short'],
                ['python', '-m', 'pytest', 'tests/config/test_env_override.py', '-v'],
                ['python', '-m', 'pytest', 'tests/config/test_merge_correctness.py', '-v']
            ]
            
            all_passed = True
            test_results = []
            
            for cmd in test_commands:
                print(f"   运行: {' '.join(cmd)}")
                
                result = subprocess.run(
                    cmd, 
                    capture_output=True, 
                    text=True, 
                    cwd=Path.cwd()
                )
                
                test_result = {
                    'command': ' '.join(cmd),
                    'returncode': result.returncode,
                    'stdout': result.stdout[-1000:] if result.stdout else '',  # 保留最后1000字符
                    'stderr': result.stderr[-1000:] if result.stderr else ''
                }
                test_results.append(test_result)
                
                if result.returncode != 0:
                    print(f"   ❌ 测试失败: {cmd[0]}")
                    all_passed = False
                else:
                    print(f"   ✅ 测试通过: {cmd[0]}")
            
            self.results['test_results'] = {
                'overall_passed': all_passed,
                'individual_results': test_results
            }
            
            if not all_passed:
                print("❌ 部分测试失败")
                return False
            
            print("✅ 所有测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            self.results['test_results'] = {'error': str(e)}
            return False
    
    def generate_report(self) -> Dict[str, Any]:
        """生成门禁报告"""
        return {
            'timestamp': datetime.now().isoformat(),
            'overall_status': self.results['overall_status'],
            'thresholds': self.thresholds,
            'check_results': self.results,
            'summary': self._generate_summary()
        }
    
    def _generate_summary(self) -> Dict[str, Any]:
        """生成摘要"""
        summary = {
            'total_checks': len([k for k, v in self.check_config.items() if k.startswith('run_') and v]),
            'passed_checks': 0,
            'failed_checks': 0,
            'recommendations': []
        }
        
        # 统计通过/失败的检查
        for check_name, result in self.results.items():
            if check_name == 'overall_status':
                continue
                
            if result and not isinstance(result, dict) or (isinstance(result, dict) and 'error' not in result):
                summary['passed_checks'] += 1
            else:
                summary['failed_checks'] += 1
        
        # 生成建议
        if self.results['static_check'] and self.results['static_check'].get('errors', 0) > 0:
            summary['recommendations'].append("修复配置静态检查中发现的错误")
        
        if self.results['impact_analysis'] and self.results['impact_analysis'].get('critical_impact_count', 0) > 0:
            summary['recommendations'].append("评估关键配置变更的影响，考虑分批发布")
        
        if self.results['legacy_scan'] and self.results['legacy_scan'].get('high_priority_violations', 0) > 0:
            summary['recommendations'].append("优先处理高优先级的旧依赖违规")
        
        return summary
    
    def save_report(self, output_file: str):
        """保存报告"""
        report = self.generate_report()
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📄 门禁报告已保存到: {output_file}")
    
    def print_summary(self):
        """打印摘要"""
        print("\n" + "="*60)
        print("🎯 CI配置门禁检查摘要")
        print("="*60)
        
        status_emoji = "✅" if self.results['overall_status'] == 'passed' else "❌"
        print(f"总体状态: {status_emoji} {self.results['overall_status'].upper()}")
        
        summary = self._generate_summary()
        print(f"检查项目: {summary['passed_checks']}/{summary['total_checks']} 通过")
        
        if summary['recommendations']:
            print("\n📋 建议:")
            for i, rec in enumerate(summary['recommendations'], 1):
                print(f"  {i}. {rec}")
        
        print("\n" + "="*60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="CI配置门禁检查")
    parser.add_argument("--config", help="门禁配置文件")
    parser.add_argument("--output", default="ci_gate_report.json", help="报告输出文件")
    parser.add_argument("--fail-fast", action="store_true", help="快速失败模式")
    parser.add_argument("--skip-tests", action="store_true", help="跳过测试")
    parser.add_argument("--skip-legacy-scan", action="store_true", help="跳过旧依赖扫描")
    
    args = parser.parse_args()
    
    # 创建门禁实例
    gate = CIConfigGate()
    
    # 配置检查项
    if args.fail_fast:
        gate.check_config['fail_fast'] = True
    
    if args.skip_tests:
        gate.check_config['run_tests'] = False
    
    if args.skip_legacy_scan:
        gate.check_config['run_legacy_scan'] = False
    
    # 加载自定义配置
    if args.config and Path(args.config).exists():
        try:
            with open(args.config, 'r', encoding='utf-8') as f:
                custom_config = json.load(f)
            
            # 更新阈值
            if 'thresholds' in custom_config:
                gate.thresholds.update(custom_config['thresholds'])
            
            # 更新检查配置
            if 'check_config' in custom_config:
                gate.check_config.update(custom_config['check_config'])
                
        except Exception as e:
            print(f"⚠️  加载配置文件失败: {e}")
    
    # 运行检查
    success = gate.run_all_checks()
    
    # 保存报告
    gate.save_report(args.output)
    
    # 打印摘要
    gate.print_summary()
    
    # 返回退出码
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
