#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
旧托底使用度监控工具

监控旧配置系统的使用情况，生成使用度报告，为清理决策提供数据支持
"""

import os
import sys
import json
import argparse
import sqlite3
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from collections import defaultdict, Counter

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from backend.config.legacy.wrapper import LegacyConfigWrapper
from backend.config.metrics_collector import get_metrics_collector


class LegacyUsageMonitor:
    """旧托底使用度监控器"""
    
    def __init__(self, db_path: str = "legacy_usage.db"):
        self.db_path = db_path
        self.init_database()
        
        # 使用度阈值
        self.cleanup_thresholds = {
            'zero_usage_days': 30,      # 30天零使用可清理
            'low_usage_threshold': 10,   # 低使用阈值（次/天）
            'low_usage_days': 60,       # 低使用60天可清理
            'deprecated_days': 90       # 标记为deprecated 90天后可清理
        }
    
    def init_database(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS legacy_usage (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_key TEXT NOT NULL,
                    access_count INTEGER DEFAULT 0,
                    last_accessed TIMESTAMP,
                    first_accessed TIMESTAMP,
                    access_source TEXT,  -- 调用来源
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS usage_daily_stats (
                    date TEXT NOT NULL,
                    config_key TEXT NOT NULL,
                    access_count INTEGER DEFAULT 0,
                    unique_sources INTEGER DEFAULT 0,
                    PRIMARY KEY (date, config_key)
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS cleanup_candidates (
                    config_key TEXT PRIMARY KEY,
                    reason TEXT NOT NULL,
                    last_usage_date TIMESTAMP,
                    usage_count INTEGER DEFAULT 0,
                    cleanup_eligible_date TIMESTAMP,
                    status TEXT DEFAULT 'candidate',  -- candidate, approved, cleaned
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_legacy_usage_key 
                ON legacy_usage(config_key)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_legacy_usage_accessed 
                ON legacy_usage(last_accessed)
            """)
    
    def record_usage(self, config_key: str, source: str = "unknown"):
        """记录配置键使用"""
        now = datetime.now()
        
        with sqlite3.connect(self.db_path) as conn:
            # 更新或插入使用记录
            conn.execute("""
                INSERT INTO legacy_usage (config_key, access_count, last_accessed, first_accessed, access_source)
                VALUES (?, 1, ?, ?, ?)
                ON CONFLICT(config_key) DO UPDATE SET
                    access_count = access_count + 1,
                    last_accessed = ?,
                    access_source = ?
            """, (config_key, now, now, source, now, source))
            
            # 更新每日统计
            today = now.date().isoformat()
            conn.execute("""
                INSERT INTO usage_daily_stats (date, config_key, access_count, unique_sources)
                VALUES (?, ?, 1, 1)
                ON CONFLICT(date, config_key) DO UPDATE SET
                    access_count = access_count + 1
            """, (today, config_key))
    
    def get_usage_stats(self, days: int = 30) -> Dict[str, Any]:
        """获取使用统计"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            # 总体统计
            total_keys = conn.execute(
                "SELECT COUNT(DISTINCT config_key) FROM legacy_usage"
            ).fetchone()[0]
            
            active_keys = conn.execute(
                "SELECT COUNT(DISTINCT config_key) FROM legacy_usage WHERE last_accessed > ?",
                (cutoff_date,)
            ).fetchone()[0]
            
            # 使用频率分布
            usage_distribution = conn.execute("""
                SELECT 
                    CASE 
                        WHEN access_count = 0 THEN 'zero'
                        WHEN access_count <= 10 THEN 'low'
                        WHEN access_count <= 100 THEN 'medium'
                        ELSE 'high'
                    END as usage_level,
                    COUNT(*) as count
                FROM legacy_usage
                WHERE last_accessed > ?
                GROUP BY usage_level
            """, (cutoff_date,)).fetchall()
            
            # 最常用的键
            top_used_keys = conn.execute("""
                SELECT config_key, access_count, last_accessed
                FROM legacy_usage
                WHERE last_accessed > ?
                ORDER BY access_count DESC
                LIMIT 20
            """, (cutoff_date,)).fetchall()
            
            # 最近未使用的键
            unused_keys = conn.execute("""
                SELECT config_key, access_count, last_accessed
                FROM legacy_usage
                WHERE last_accessed < ? OR last_accessed IS NULL
                ORDER BY last_accessed ASC
                LIMIT 20
            """, (cutoff_date,)).fetchall()
            
            return {
                "period_days": days,
                "total_keys": total_keys,
                "active_keys": active_keys,
                "inactive_keys": total_keys - active_keys,
                "usage_distribution": [dict(row) for row in usage_distribution],
                "top_used_keys": [dict(row) for row in top_used_keys],
                "unused_keys": [dict(row) for row in unused_keys]
            }
    
    def identify_cleanup_candidates(self) -> List[Dict[str, Any]]:
        """识别清理候选"""
        now = datetime.now()
        candidates = []
        
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            # 零使用候选
            zero_usage_cutoff = now - timedelta(days=self.cleanup_thresholds['zero_usage_days'])
            zero_usage_keys = conn.execute("""
                SELECT config_key, access_count, last_accessed
                FROM legacy_usage
                WHERE (last_accessed < ? OR last_accessed IS NULL)
                AND access_count = 0
            """, (zero_usage_cutoff,)).fetchall()
            
            for row in zero_usage_keys:
                candidates.append({
                    "config_key": row["config_key"],
                    "reason": "zero_usage",
                    "last_usage_date": row["last_accessed"],
                    "usage_count": row["access_count"],
                    "cleanup_eligible_date": zero_usage_cutoff.isoformat(),
                    "priority": "high"
                })
            
            # 低使用候选
            low_usage_cutoff = now - timedelta(days=self.cleanup_thresholds['low_usage_days'])
            low_usage_keys = conn.execute("""
                SELECT config_key, access_count, last_accessed
                FROM legacy_usage
                WHERE last_accessed < ?
                AND access_count > 0 AND access_count <= ?
            """, (low_usage_cutoff, self.cleanup_thresholds['low_usage_threshold'])).fetchall()
            
            for row in low_usage_keys:
                candidates.append({
                    "config_key": row["config_key"],
                    "reason": "low_usage",
                    "last_usage_date": row["last_accessed"],
                    "usage_count": row["access_count"],
                    "cleanup_eligible_date": low_usage_cutoff.isoformat(),
                    "priority": "medium"
                })
        
        # 保存候选到数据库
        self._save_cleanup_candidates(candidates)
        
        return candidates
    
    def _save_cleanup_candidates(self, candidates: List[Dict[str, Any]]):
        """保存清理候选到数据库"""
        with sqlite3.connect(self.db_path) as conn:
            for candidate in candidates:
                conn.execute("""
                    INSERT OR REPLACE INTO cleanup_candidates 
                    (config_key, reason, last_usage_date, usage_count, cleanup_eligible_date)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    candidate["config_key"],
                    candidate["reason"],
                    candidate["last_usage_date"],
                    candidate["usage_count"],
                    candidate["cleanup_eligible_date"]
                ))
    
    def generate_cleanup_report(self, output_file: str = None) -> Dict[str, Any]:
        """生成清理报告"""
        stats = self.get_usage_stats(30)
        candidates = self.identify_cleanup_candidates()
        
        # 按优先级分组候选
        candidates_by_priority = defaultdict(list)
        for candidate in candidates:
            candidates_by_priority[candidate["priority"]].append(candidate)
        
        # 生成建议
        recommendations = []
        
        if candidates_by_priority["high"]:
            recommendations.append({
                "priority": "high",
                "action": "immediate_cleanup",
                "description": f"立即清理 {len(candidates_by_priority['high'])} 个零使用配置键",
                "keys": [c["config_key"] for c in candidates_by_priority["high"][:10]]
            })
        
        if candidates_by_priority["medium"]:
            recommendations.append({
                "priority": "medium",
                "action": "scheduled_cleanup",
                "description": f"计划清理 {len(candidates_by_priority['medium'])} 个低使用配置键",
                "keys": [c["config_key"] for c in candidates_by_priority["medium"][:10]]
            })
        
        report = {
            "generated_at": datetime.now().isoformat(),
            "usage_stats": stats,
            "cleanup_candidates": {
                "total": len(candidates),
                "by_priority": {
                    priority: len(keys) 
                    for priority, keys in candidates_by_priority.items()
                },
                "details": candidates
            },
            "recommendations": recommendations,
            "thresholds": self.cleanup_thresholds
        }
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        return report
    
    def approve_cleanup(self, config_keys: List[str], reason: str = "approved"):
        """批准清理配置键"""
        with sqlite3.connect(self.db_path) as conn:
            for key in config_keys:
                conn.execute("""
                    UPDATE cleanup_candidates 
                    SET status = 'approved'
                    WHERE config_key = ?
                """, (key,))
    
    def mark_cleaned(self, config_keys: List[str]):
        """标记配置键已清理"""
        with sqlite3.connect(self.db_path) as conn:
            for key in config_keys:
                conn.execute("""
                    UPDATE cleanup_candidates 
                    SET status = 'cleaned'
                    WHERE config_key = ?
                """, (key,))
    
    def get_cleanup_status(self) -> Dict[str, Any]:
        """获取清理状态"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            status_counts = conn.execute("""
                SELECT status, COUNT(*) as count
                FROM cleanup_candidates
                GROUP BY status
            """).fetchall()
            
            recent_cleanups = conn.execute("""
                SELECT config_key, reason, created_at
                FROM cleanup_candidates
                WHERE status = 'cleaned'
                ORDER BY created_at DESC
                LIMIT 10
            """).fetchall()
            
            return {
                "status_counts": {row["status"]: row["count"] for row in status_counts},
                "recent_cleanups": [dict(row) for row in recent_cleanups]
            }
    
    def export_usage_data(self, output_file: str, format: str = "json"):
        """导出使用数据"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            usage_data = conn.execute("""
                SELECT config_key, access_count, last_accessed, first_accessed, access_source
                FROM legacy_usage
                ORDER BY access_count DESC
            """).fetchall()
            
            data = [dict(row) for row in usage_data]
            
            if format == "json":
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False, default=str)
            elif format == "csv":
                import csv
                with open(output_file, 'w', newline='', encoding='utf-8') as f:
                    if data:
                        writer = csv.DictWriter(f, fieldnames=data[0].keys())
                        writer.writeheader()
                        writer.writerows(data)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="旧托底使用度监控工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # stats命令
    stats_parser = subparsers.add_parser("stats", help="显示使用统计")
    stats_parser.add_argument("--days", type=int, default=30, help="统计天数")
    
    # candidates命令
    subparsers.add_parser("candidates", help="识别清理候选")
    
    # report命令
    report_parser = subparsers.add_parser("report", help="生成清理报告")
    report_parser.add_argument("--output", help="输出文件路径")
    
    # approve命令
    approve_parser = subparsers.add_parser("approve", help="批准清理")
    approve_parser.add_argument("keys", nargs="+", help="要批准清理的配置键")
    
    # status命令
    subparsers.add_parser("status", help="显示清理状态")
    
    # export命令
    export_parser = subparsers.add_parser("export", help="导出使用数据")
    export_parser.add_argument("--output", required=True, help="输出文件路径")
    export_parser.add_argument("--format", choices=["json", "csv"], default="json", help="输出格式")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        monitor = LegacyUsageMonitor()
        
        if args.command == "stats":
            stats = monitor.get_usage_stats(args.days)
            print(f"📊 旧配置使用统计 (最近 {args.days} 天)")
            print(f"总配置键数: {stats['total_keys']}")
            print(f"活跃配置键: {stats['active_keys']}")
            print(f"非活跃配置键: {stats['inactive_keys']}")
            
            print("\n使用频率分布:")
            for dist in stats['usage_distribution']:
                print(f"  {dist['usage_level']}: {dist['count']} 个")
            
            print(f"\n最常用配置键 (前10个):")
            for key_info in stats['top_used_keys'][:10]:
                print(f"  {key_info['config_key']}: {key_info['access_count']} 次")
        
        elif args.command == "candidates":
            candidates = monitor.identify_cleanup_candidates()
            print(f"🧹 清理候选识别完成，发现 {len(candidates)} 个候选")
            
            by_priority = defaultdict(list)
            for candidate in candidates:
                by_priority[candidate["priority"]].append(candidate)
            
            for priority in ["high", "medium", "low"]:
                if by_priority[priority]:
                    print(f"\n{priority.upper()} 优先级 ({len(by_priority[priority])} 个):")
                    for candidate in by_priority[priority][:5]:
                        print(f"  {candidate['config_key']} - {candidate['reason']}")
        
        elif args.command == "report":
            report = monitor.generate_cleanup_report(args.output)
            print("📋 清理报告生成完成")
            
            if args.output:
                print(f"报告已保存到: {args.output}")
            else:
                print(json.dumps(report, indent=2, ensure_ascii=False, default=str))
        
        elif args.command == "approve":
            monitor.approve_cleanup(args.keys)
            print(f"✅ 已批准清理 {len(args.keys)} 个配置键")
        
        elif args.command == "status":
            status = monitor.get_cleanup_status()
            print("📈 清理状态:")
            for status_name, count in status['status_counts'].items():
                print(f"  {status_name}: {count} 个")
            
            if status['recent_cleanups']:
                print("\n最近清理:")
                for cleanup in status['recent_cleanups']:
                    print(f"  {cleanup['config_key']} - {cleanup['created_at']}")
        
        elif args.command == "export":
            monitor.export_usage_data(args.output, args.format)
            print(f"📤 使用数据已导出到: {args.output}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
