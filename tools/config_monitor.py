#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置监控CLI工具

提供命令行界面来查看配置监控指标、告警和面板数据
"""

import sys
import json
import argparse
import time
from pathlib import Path
from typing import Dict, Any
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from backend.config.monitoring.dashboard import get_config_dashboard
from backend.config.monitoring.prometheus_exporter import generate_prometheus_metrics
from backend.config.metrics_collector import get_metrics_collector
from backend.config.manager import get_config_manager


class ConfigMonitorCLI:
    """配置监控CLI"""
    
    def __init__(self):
        self.dashboard = get_config_dashboard()
        self.metrics_collector = get_metrics_collector()
        self.config_manager = get_config_manager()
    
    def show_metrics(self, format: str = "table"):
        """显示指标数据"""
        print("📊 配置监控指标")
        print("=" * 60)
        
        metrics = self.metrics_collector.get_metrics()
        
        if format == "json":
            print(json.dumps(metrics, indent=2, ensure_ascii=False))
            return
        
        # 表格格式显示
        print(f"📅 更新时间: {metrics.get('timestamp', 'N/A')}")
        print()
        
        # 配置加载指标
        print("🔄 配置加载指标:")
        print(f"  加载次数: {metrics.get('config_load_count', 0)}")
        print(f"  总加载时间: {metrics.get('config_load_time_total', 0):.4f} 秒")
        print(f"  平均加载时间: {metrics.get('average_load_time', 0):.4f} 秒")
        print(f"  加载错误: {metrics.get('config_load_errors', 0)}")
        print()
        
        # 配置验证指标
        print("✅ 配置验证指标:")
        print(f"  验证次数: {metrics.get('config_validation_count', 0)}")
        print(f"  平均验证时间: {metrics.get('average_validation_time', 0):.4f} 秒")
        print(f"  验证错误: {metrics.get('config_validation_errors', 0)}")
        print()
        
        # 环境变量覆盖指标
        print("🌍 环境变量覆盖指标:")
        print(f"  覆盖次数: {metrics.get('env_override_count', 0)}")
        print(f"  拒绝次数: {metrics.get('env_override_rejected_count', 0)}")
        print()
        
        # 其他指标
        print("📋 其他指标:")
        print(f"  配置降级次数: {metrics.get('config_fallback_count', 0)}")
        print(f"  未知键次数: {metrics.get('unknown_key_count', 0)}")
    
    def show_alerts(self, limit: int = 20):
        """显示告警信息"""
        print("🚨 配置告警信息")
        print("=" * 60)
        
        alerts = self.dashboard.get_alerts(limit)
        
        if not alerts:
            print("✅ 当前无告警")
            return
        
        # 按严重程度分组
        severity_groups = {}
        for alert in alerts:
            severity = alert.get('severity', 'unknown')
            if severity not in severity_groups:
                severity_groups[severity] = []
            severity_groups[severity].append(alert)
        
        # 显示告警
        severity_icons = {
            'critical': '🔴',
            'high': '🟠',
            'medium': '🟡',
            'low': '🔵'
        }
        
        for severity in ['critical', 'high', 'medium', 'low']:
            if severity in severity_groups:
                print(f"\n{severity_icons.get(severity, '⚪')} {severity.upper()} 级别告警:")
                for alert in severity_groups[severity]:
                    status = "❌ 活跃" if not alert.get('resolved', False) else "✅ 已解决"
                    print(f"  [{status}] {alert.get('rule_name', 'N/A')}")
                    print(f"      描述: {alert.get('description', 'N/A')}")
                    print(f"      当前值: {alert.get('value', 'N/A')}, 阈值: {alert.get('threshold', 'N/A')}")
                    print(f"      时间: {alert.get('timestamp', 'N/A')}")
                    print()
    
    def show_alert_rules(self):
        """显示告警规则"""
        print("📋 告警规则配置")
        print("=" * 60)
        
        rules = self.dashboard.get_alert_rules()
        
        if not rules:
            print("⚠️ 未配置告警规则")
            return
        
        for rule in rules:
            status = "✅ 启用" if rule.get('enabled', False) else "❌ 禁用"
            print(f"[{status}] {rule.get('name', 'N/A')}")
            print(f"  指标: {rule.get('metric', 'N/A')}")
            print(f"  条件: {rule.get('operator', 'N/A')} {rule.get('threshold', 'N/A')}")
            print(f"  严重程度: {rule.get('severity', 'N/A')}")
            print(f"  描述: {rule.get('description', 'N/A')}")
            print()
    
    def show_dashboard(self):
        """显示监控面板"""
        print("📊 配置监控面板")
        print("=" * 60)
        
        dashboard_data = self.dashboard.get_dashboard_data()
        
        # 基本信息
        print(f"📅 更新时间: {dashboard_data.get('timestamp', 'N/A')}")
        print(f"🚨 活跃告警: {dashboard_data.get('total_alerts', 0)}")
        print()
        
        # 配置摘要
        config_summary = dashboard_data.get('config_summary', {})
        print("⚙️ 配置摘要:")
        print(f"  配置已加载: {'✅' if config_summary.get('config_loaded', False) else '❌'}")
        print(f"  环境变量覆盖: {'✅' if config_summary.get('env_override_enabled', False) else '❌'}")
        
        keys_by_source = config_summary.get('keys_by_source', {})
        if keys_by_source:
            print("  配置来源分布:")
            for source, count in keys_by_source.items():
                print(f"    {source}: {count} 个键")
        print()
        
        # 指标摘要
        metrics = dashboard_data.get('metrics', {})
        print("📈 关键指标:")
        print(f"  配置加载: {metrics.get('config_load_count', 0)} 次")
        print(f"  平均加载时间: {metrics.get('average_load_time', 0):.4f} 秒")
        print(f"  加载错误: {metrics.get('config_load_errors', 0)} 次")
        print(f"  验证错误: {metrics.get('config_validation_errors', 0)} 次")
        print(f"  环境覆盖: {metrics.get('env_override_count', 0)} 次")
        print(f"  覆盖拒绝: {metrics.get('env_override_rejected_count', 0)} 次")
    
    def show_config_sources(self):
        """显示配置来源信息"""
        print("📂 配置来源信息")
        print("=" * 60)
        
        config_with_sources = self.config_manager.dump_config(include_sources=True)
        sources = config_with_sources.get('sources', {})
        
        if not sources:
            print("⚠️ 无配置来源信息")
            return
        
        # 按来源分组
        source_groups = {}
        for key, source in sources.items():
            if source not in source_groups:
                source_groups[source] = []
            source_groups[source].append(key)
        
        # 显示各来源的配置键
        source_icons = {
            'config_files': '📄',
            'environment': '🌍',
            'runtime': '⚡',
            'defaults': '🔧'
        }
        
        for source, keys in source_groups.items():
            icon = source_icons.get(source, '📋')
            print(f"\n{icon} {source.upper()} ({len(keys)} 个键):")
            for key in sorted(keys)[:10]:  # 只显示前10个
                print(f"  - {key}")
            if len(keys) > 10:
                print(f"  ... 还有 {len(keys) - 10} 个键")
    
    def export_prometheus(self, output_file: str = None):
        """导出Prometheus指标"""
        print("📊 导出Prometheus指标")
        print("=" * 60)
        
        try:
            prometheus_data = generate_prometheus_metrics()
            
            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(prometheus_data)
                print(f"✅ Prometheus指标已导出到: {output_file}")
            else:
                print(prometheus_data)
                
        except Exception as e:
            print(f"❌ 导出失败: {e}")
    
    def watch_metrics(self, interval: int = 5):
        """实时监控指标"""
        print(f"👀 实时监控配置指标 (每 {interval} 秒刷新，按 Ctrl+C 退出)")
        print("=" * 60)
        
        try:
            while True:
                # 清屏（简单实现）
                print("\033[2J\033[H", end="")
                
                # 显示当前时间
                print(f"🕐 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print()
                
                # 显示关键指标
                metrics = self.metrics_collector.get_metrics()
                
                print("📊 实时指标:")
                print(f"  配置加载: {metrics.get('config_load_count', 0)} 次")
                print(f"  平均加载时间: {metrics.get('average_load_time', 0):.4f} 秒")
                print(f"  加载错误: {metrics.get('config_load_errors', 0)} 次")
                print(f"  验证错误: {metrics.get('config_validation_errors', 0)} 次")
                print(f"  环境覆盖: {metrics.get('env_override_count', 0)} 次")
                print(f"  覆盖拒绝: {metrics.get('env_override_rejected_count', 0)} 次")
                print(f"  配置降级: {metrics.get('config_fallback_count', 0)} 次")
                print(f"  未知键: {metrics.get('unknown_key_count', 0)} 次")
                
                # 显示活跃告警
                alerts = self.dashboard.get_alerts(5)
                active_alerts = [a for a in alerts if not a.get('resolved', False)]
                
                print(f"\n🚨 活跃告警: {len(active_alerts)}")
                for alert in active_alerts[:3]:  # 只显示前3个
                    severity_icon = {'critical': '🔴', 'high': '🟠', 'medium': '🟡', 'low': '🔵'}.get(
                        alert.get('severity', 'low'), '⚪'
                    )
                    print(f"  {severity_icon} {alert.get('rule_name', 'N/A')}: {alert.get('description', 'N/A')}")
                
                print(f"\n下次刷新: {interval} 秒后...")
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n\n👋 监控已停止")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="配置监控CLI工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # metrics命令
    metrics_parser = subparsers.add_parser("metrics", help="显示指标数据")
    metrics_parser.add_argument("--format", choices=["table", "json"], default="table", help="输出格式")
    
    # alerts命令
    alerts_parser = subparsers.add_parser("alerts", help="显示告警信息")
    alerts_parser.add_argument("--limit", type=int, default=20, help="显示告警数量限制")
    
    # rules命令
    subparsers.add_parser("rules", help="显示告警规则")
    
    # dashboard命令
    subparsers.add_parser("dashboard", help="显示监控面板")
    
    # sources命令
    subparsers.add_parser("sources", help="显示配置来源信息")
    
    # prometheus命令
    prometheus_parser = subparsers.add_parser("prometheus", help="导出Prometheus指标")
    prometheus_parser.add_argument("--output", help="输出文件路径")
    
    # watch命令
    watch_parser = subparsers.add_parser("watch", help="实时监控指标")
    watch_parser.add_argument("--interval", type=int, default=5, help="刷新间隔（秒）")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        cli = ConfigMonitorCLI()
        
        if args.command == "metrics":
            cli.show_metrics(args.format)
        elif args.command == "alerts":
            cli.show_alerts(args.limit)
        elif args.command == "rules":
            cli.show_alert_rules()
        elif args.command == "dashboard":
            cli.show_dashboard()
        elif args.command == "sources":
            cli.show_config_sources()
        elif args.command == "prometheus":
            cli.export_prometheus(args.output)
        elif args.command == "watch":
            cli.watch_metrics(args.interval)
        
        return 0
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
