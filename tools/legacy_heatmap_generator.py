#!/usr/bin/env python3
"""
旧配置访问热力图生成器

生成旧配置键访问的热力图和迁移优先级报告
"""

import argparse
import json
import sys
import time
from pathlib import Path
from typing import Dict, List, Any, Tuple
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import pandas as pd
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from backend.config.legacy.access_tracker import LegacyAccessTracker
from backend.config.legacy.wrapper import LegacyConfigWrapper


class LegacyHeatmapGenerator:
    """旧配置访问热力图生成器"""
    
    def __init__(self, access_tracker: LegacyAccessTracker):
        self.access_tracker = access_tracker
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Deja<PERSON>u Sans']
        plt.rcParams['axes.unicode_minus'] = False
    
    def generate_access_frequency_heatmap(self, output_path: str = "legacy_access_heatmap.png"):
        """生成访问频次热力图"""
        stats = self.access_tracker.get_access_stats()
        
        if not stats:
            print("没有访问数据，无法生成热力图")
            return
        
        # 准备数据
        keys = list(stats.keys())
        access_counts = [stats[key]['count'] for key in keys]
        
        # 创建热力图数据矩阵
        data_matrix = np.array(access_counts).reshape(-1, 1)
        
        # 创建图形
        plt.figure(figsize=(12, max(8, len(keys) * 0.3)))
        
        # 生成热力图
        sns.heatmap(
            data_matrix,
            yticklabels=keys,
            xticklabels=['访问次数'],
            annot=True,
            fmt='d',
            cmap='YlOrRd',
            cbar_kws={'label': '访问次数'}
        )
        
        plt.title('旧配置键访问频次热力图', fontsize=16, pad=20)
        plt.xlabel('指标', fontsize=12)
        plt.ylabel('配置键', fontsize=12)
        plt.tight_layout()
        
        # 保存图片
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"访问频次热力图已保存到: {output_path}")
    
    def generate_caller_heatmap(self, output_path: str = "legacy_caller_heatmap.png"):
        """生成调用者热力图"""
        caller_analysis = self.access_tracker.get_caller_analysis()
        
        if not caller_analysis:
            print("没有调用者数据，无法生成热力图")
            return
        
        # 准备数据
        callers = list(caller_analysis.keys())
        caller_counts = [caller_analysis[caller]['count'] for caller in callers]
        unique_keys = [caller_analysis[caller]['unique_keys'] for caller in callers]
        
        # 限制显示数量
        max_callers = 20
        if len(callers) > max_callers:
            # 按访问次数排序，取前20个
            sorted_data = sorted(
                zip(callers, caller_counts, unique_keys),
                key=lambda x: x[1],
                reverse=True
            )[:max_callers]
            callers, caller_counts, unique_keys = zip(*sorted_data)
        
        # 创建数据矩阵
        data_matrix = np.column_stack([caller_counts, unique_keys])
        
        # 创建图形
        plt.figure(figsize=(10, max(8, len(callers) * 0.4)))
        
        # 生成热力图
        sns.heatmap(
            data_matrix,
            yticklabels=[Path(caller.split(':')[0]).name + f":{caller.split(':')[1]}" for caller in callers],
            xticklabels=['访问次数', '唯一键数'],
            annot=True,
            fmt='d',
            cmap='Blues',
            cbar_kws={'label': '数值'}
        )
        
        plt.title('旧配置调用者热力图', fontsize=16, pad=20)
        plt.xlabel('指标', fontsize=12)
        plt.ylabel('调用者 (文件:行号)', fontsize=12)
        plt.tight_layout()
        
        # 保存图片
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"调用者热力图已保存到: {output_path}")
    
    def generate_time_distribution_chart(self, output_path: str = "legacy_time_distribution.png"):
        """生成时间分布图"""
        stats = self.access_tracker.get_access_stats()
        
        # 收集所有小时分布数据
        hourly_data = {}
        for key, data in stats.items():
            for hour, count in data['hourly_distribution'].items():
                hourly_data[hour] = hourly_data.get(hour, 0) + count
        
        if not hourly_data:
            print("没有时间分布数据，无法生成图表")
            return
        
        # 准备数据
        hours = list(range(24))
        counts = [hourly_data.get(hour, 0) for hour in hours]
        
        # 创建图形
        plt.figure(figsize=(12, 6))
        
        # 生成柱状图
        bars = plt.bar(hours, counts, color='skyblue', alpha=0.7)
        
        # 添加数值标签
        for bar, count in zip(bars, counts):
            if count > 0:
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                        str(count), ha='center', va='bottom')
        
        plt.title('旧配置访问时间分布', fontsize=16, pad=20)
        plt.xlabel('小时', fontsize=12)
        plt.ylabel('访问次数', fontsize=12)
        plt.xticks(hours)
        plt.grid(axis='y', alpha=0.3)
        plt.tight_layout()
        
        # 保存图片
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"时间分布图已保存到: {output_path}")
    
    def generate_priority_matrix(self, output_path: str = "legacy_priority_matrix.png"):
        """生成迁移优先级矩阵图"""
        priority_list = self.access_tracker.get_migration_priority()
        
        if not priority_list:
            print("没有优先级数据，无法生成矩阵图")
            return
        
        # 准备数据
        keys = [item['key'] for item in priority_list[:20]]  # 前20个
        access_counts = [item['access_count'] for item in priority_list[:20]]
        caller_counts = [item['caller_count'] for item in priority_list[:20]]
        priority_scores = [item['priority_score'] for item in priority_list[:20]]
        
        # 创建数据矩阵
        data_matrix = np.column_stack([access_counts, caller_counts, priority_scores])
        
        # 创建图形
        plt.figure(figsize=(10, max(8, len(keys) * 0.4)))
        
        # 生成热力图
        sns.heatmap(
            data_matrix,
            yticklabels=keys,
            xticklabels=['访问次数', '调用者数', '优先级分数'],
            annot=True,
            fmt='.1f',
            cmap='RdYlBu_r',
            cbar_kws={'label': '数值'}
        )
        
        plt.title('旧配置迁移优先级矩阵', fontsize=16, pad=20)
        plt.xlabel('指标', fontsize=12)
        plt.ylabel('配置键', fontsize=12)
        plt.tight_layout()
        
        # 保存图片
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"优先级矩阵图已保存到: {output_path}")
    
    def generate_comprehensive_report(self, output_dir: str = "legacy_reports"):
        """生成综合报告"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        print(f"生成综合报告到目录: {output_path}")
        
        # 生成各种图表
        self.generate_access_frequency_heatmap(str(output_path / "access_frequency_heatmap.png"))
        self.generate_caller_heatmap(str(output_path / "caller_heatmap.png"))
        self.generate_time_distribution_chart(str(output_path / "time_distribution.png"))
        self.generate_priority_matrix(str(output_path / "priority_matrix.png"))
        
        # 生成文本报告
        self.generate_text_report(str(output_path / "migration_report.md"))
        
        # 生成JSON数据
        json_data = self.access_tracker.export_access_data('json')
        with open(output_path / "access_data.json", 'w', encoding='utf-8') as f:
            f.write(json_data)
        
        print("综合报告生成完成")
    
    def generate_text_report(self, output_path: str):
        """生成文本报告"""
        stats = self.access_tracker.get_access_stats()
        priority_list = self.access_tracker.get_migration_priority()
        hottest_keys = self.access_tracker.get_hottest_keys(10)
        caller_analysis = self.access_tracker.get_caller_analysis()
        
        report_lines = [
            "# 旧配置系统迁移分析报告",
            "",
            f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"**分析周期**: {self.access_tracker.get_stats()['tracking_duration']:.1f} 秒",
            "",
            "## 概览统计",
            "",
            f"- **总访问次数**: {self.access_tracker.total_accesses}",
            f"- **唯一配置键**: {len(stats)}",
            f"- **活跃调用者**: {len(caller_analysis)}",
            "",
            "## 热门配置键 (Top 10)",
            "",
        ]
        
        for i, (key, count) in enumerate(hottest_keys, 1):
            report_lines.append(f"{i}. `{key}` - {count} 次访问")
        
        report_lines.extend([
            "",
            "## 迁移优先级 (Top 10)",
            "",
        ])
        
        for i, item in enumerate(priority_list[:10], 1):
            report_lines.append(
                f"{i}. **[{item['priority_level']}]** `{item['key']}` "
                f"(分数: {item['priority_score']:.1f}, 访问: {item['access_count']}, "
                f"调用者: {item['caller_count']})"
            )
        
        report_lines.extend([
            "",
            "## 主要调用者分析",
            "",
        ])
        
        # 按访问次数排序调用者
        sorted_callers = sorted(
            caller_analysis.items(),
            key=lambda x: x[1]['count'],
            reverse=True
        )[:10]
        
        for caller, data in sorted_callers:
            file_name = Path(caller.split(':')[0]).name
            line_num = caller.split(':')[1]
            report_lines.append(
                f"- `{file_name}:{line_num}` - {data['count']} 次访问, "
                f"{data['unique_keys']} 个唯一键"
            )
        
        report_lines.extend([
            "",
            "## 详细配置键分析",
            "",
        ])
        
        for key, data in sorted(stats.items(), key=lambda x: x[1]['count'], reverse=True)[:20]:
            report_lines.extend([
                f"### `{key}`",
                "",
                f"- **访问次数**: {data['count']}",
                f"- **首次访问**: {datetime.fromtimestamp(data['first_access']).strftime('%Y-%m-%d %H:%M:%S') if data['first_access'] else 'N/A'}",
                f"- **最后访问**: {datetime.fromtimestamp(data['last_access']).strftime('%Y-%m-%d %H:%M:%S') if data['last_access'] else 'N/A'}",
                f"- **使用方法**: {', '.join(data['methods'].keys())}",
                f"- **主要调用者**: {data['top_callers'][0][0] if data['top_callers'] else 'N/A'}",
                "",
            ])
        
        report_lines.extend([
            "## 迁移建议",
            "",
            "### 高优先级迁移 (P0/P1)",
            "",
        ])
        
        high_priority = [item for item in priority_list if item['priority_level'] in ['P0', 'P1']]
        for item in high_priority[:10]:
            report_lines.append(f"- `{item['key']}` - 立即迁移，影响面大")
        
        report_lines.extend([
            "",
            "### 中优先级迁移 (P2)",
            "",
        ])
        
        medium_priority = [item for item in priority_list if item['priority_level'] == 'P2']
        for item in medium_priority[:5]:
            report_lines.append(f"- `{item['key']}` - 计划迁移，影响面中等")
        
        report_lines.extend([
            "",
            "### 低优先级迁移 (P3)",
            "",
            "- 其他配置键可以在后续版本中逐步迁移",
            "",
            "## 迁移工具",
            "",
            "- 使用 `tools/legacy_dependency_scanner.py` 扫描旧依赖",
            "- 使用 `backend.config.legacy.wrapper.LegacyConfigWrapper` 进行渐进式迁移",
            "- 参考 `docs/development/配置迁移指南.md` 获取详细指导",
            "",
            "---",
            "",
            "*此报告由旧配置访问追踪器自动生成*"
        ])
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        print(f"文本报告已保存到: {output_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="旧配置访问热力图生成器")
    parser.add_argument("--data-file", help="访问数据JSON文件路径")
    parser.add_argument("--output-dir", default="legacy_reports", help="输出目录")
    parser.add_argument("--format", choices=["png", "svg", "pdf"], default="png", help="图片格式")
    
    args = parser.parse_args()
    
    # 创建访问追踪器
    tracker = LegacyAccessTracker()
    
    # 如果提供了数据文件，加载数据
    if args.data_file:
        try:
            with open(args.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 这里可以添加从JSON数据重建追踪器状态的逻辑
            print(f"从 {args.data_file} 加载数据")
            
        except Exception as e:
            print(f"加载数据文件失败: {e}")
            return 1
    else:
        # 模拟一些访问数据用于演示
        print("使用模拟数据生成报告...")
        
        # 模拟访问
        sample_keys = [
            'system.debug_mode', 'llm.default.model', 'database.path',
            'business_rules.retry.max_attempts', 'thresholds.completion_threshold',
            'message_templates.system.greeting', 'llm.models.openai.api_key',
            'system.performance.cache_enabled', 'knowledge_base.enabled'
        ]
        
        import random
        for _ in range(1000):
            key = random.choice(sample_keys)
            method = random.choice(['get_config_value', 'get_llm_config', 'get_threshold'])
            tracker.record_access(key, method)
    
    # 生成热力图
    generator = LegacyHeatmapGenerator(tracker)
    generator.generate_comprehensive_report(args.output_dir)
    
    return 0


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"生成热力图失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
