#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
旧托底使用度周报生成器

生成旧配置系统使用度的周报和趋势分析，为日落决策提供数据支持
"""

import os
import sys
import json
import sqlite3
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from collections import defaultdict

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from tools.legacy_usage_monitor import LegacyUsageMonitor


class LegacyUsageReporter:
    """旧托底使用度周报生成器"""
    
    def __init__(self, db_path: str = "legacy_usage.db"):
        self.usage_monitor = LegacyUsageMonitor(db_path)
        self.db_path = db_path
    
    def generate_weekly_report(self, weeks_back: int = 4) -> Dict[str, Any]:
        """生成周报"""
        print(f"📊 生成最近 {weeks_back} 周的使用度报告...")
        
        end_date = datetime.now()
        start_date = end_date - timedelta(weeks=weeks_back)
        
        # 获取周度数据
        weekly_data = self._get_weekly_usage_data(start_date, end_date)
        
        # 生成趋势分析
        trend_analysis = self._analyze_usage_trends(weekly_data)
        
        # 获取组件使用度分析
        component_analysis = self._analyze_component_usage(start_date, end_date)
        
        # 生成日落建议
        sunset_recommendations = self._generate_sunset_recommendations(weekly_data, trend_analysis)
        
        report = {
            "report_type": "weekly_legacy_usage",
            "generated_at": datetime.now().isoformat(),
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "weeks": weeks_back
            },
            "summary": self._generate_summary(weekly_data),
            "weekly_data": weekly_data,
            "trend_analysis": trend_analysis,
            "component_analysis": component_analysis,
            "sunset_recommendations": sunset_recommendations,
            "charts_data": self._prepare_charts_data(weekly_data)
        }
        
        return report
    
    def _get_weekly_usage_data(self, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """获取周度使用数据"""
        weekly_data = []
        
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            current_date = start_date
            while current_date < end_date:
                week_end = current_date + timedelta(days=7)
                
                # 获取该周的使用统计
                week_stats = conn.execute("""
                    SELECT 
                        COUNT(DISTINCT config_key) as unique_keys,
                        SUM(access_count) as total_accesses,
                        COUNT(DISTINCT access_source) as unique_sources
                    FROM usage_daily_stats
                    WHERE date >= ? AND date < ?
                """, (current_date.date().isoformat(), week_end.date().isoformat())).fetchone()
                
                # 获取该周最活跃的键
                top_keys = conn.execute("""
                    SELECT config_key, SUM(access_count) as week_total
                    FROM usage_daily_stats
                    WHERE date >= ? AND date < ?
                    GROUP BY config_key
                    ORDER BY week_total DESC
                    LIMIT 10
                """, (current_date.date().isoformat(), week_end.date().isoformat())).fetchall()
                
                # 获取新增的键
                new_keys = conn.execute("""
                    SELECT COUNT(DISTINCT config_key) as new_keys_count
                    FROM legacy_usage
                    WHERE first_accessed >= ? AND first_accessed < ?
                """, (current_date.isoformat(), week_end.isoformat())).fetchone()
                
                weekly_data.append({
                    "week_start": current_date.isoformat(),
                    "week_end": week_end.isoformat(),
                    "stats": dict(week_stats) if week_stats else {},
                    "top_keys": [dict(row) for row in top_keys],
                    "new_keys_count": new_keys["new_keys_count"] if new_keys else 0
                })
                
                current_date = week_end
        
        return weekly_data
    
    def _analyze_usage_trends(self, weekly_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析使用趋势"""
        if len(weekly_data) < 2:
            return {"trend": "insufficient_data", "message": "数据不足，无法分析趋势"}
        
        # 提取关键指标
        unique_keys = [week["stats"].get("unique_keys", 0) for week in weekly_data]
        total_accesses = [week["stats"].get("total_accesses", 0) for week in weekly_data]
        
        # 计算趋势
        def calculate_trend(values):
            if len(values) < 2:
                return "stable"
            
            recent_avg = sum(values[-2:]) / 2
            earlier_avg = sum(values[:-2]) / max(1, len(values) - 2)
            
            change_rate = (recent_avg - earlier_avg) / max(1, earlier_avg)
            
            if change_rate > 0.1:
                return "increasing"
            elif change_rate < -0.1:
                return "decreasing"
            else:
                return "stable"
        
        keys_trend = calculate_trend(unique_keys)
        access_trend = calculate_trend(total_accesses)
        
        # 计算变化率
        keys_change = ((unique_keys[-1] - unique_keys[0]) / max(1, unique_keys[0])) * 100 if unique_keys else 0
        access_change = ((total_accesses[-1] - total_accesses[0]) / max(1, total_accesses[0])) * 100 if total_accesses else 0
        
        return {
            "keys_trend": keys_trend,
            "access_trend": access_trend,
            "keys_change_percent": round(keys_change, 2),
            "access_change_percent": round(access_change, 2),
            "overall_trend": "decreasing" if keys_trend == "decreasing" and access_trend == "decreasing" else "mixed",
            "trend_summary": self._generate_trend_summary(keys_trend, access_trend, keys_change, access_change)
        }
    
    def _analyze_component_usage(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """分析组件使用度"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            # 按来源分析使用度
            source_stats = conn.execute("""
                SELECT 
                    access_source,
                    COUNT(DISTINCT config_key) as unique_keys,
                    SUM(access_count) as total_accesses
                FROM legacy_usage
                WHERE last_accessed >= ? AND last_accessed <= ?
                GROUP BY access_source
                ORDER BY total_accesses DESC
            """, (start_date.isoformat(), end_date.isoformat())).fetchall()
            
            # 按配置键前缀分析（推断组件）
            prefix_stats = conn.execute("""
                SELECT 
                    CASE 
                        WHEN config_key LIKE 'app.%' THEN 'app'
                        WHEN config_key LIKE 'database.%' THEN 'database'
                        WHEN config_key LIKE 'llm.%' THEN 'llm'
                        WHEN config_key LIKE 'logging.%' THEN 'logging'
                        WHEN config_key LIKE 'performance.%' THEN 'performance'
                        ELSE 'other'
                    END as component,
                    COUNT(DISTINCT config_key) as unique_keys,
                    SUM(access_count) as total_accesses
                FROM legacy_usage
                WHERE last_accessed >= ? AND last_accessed <= ?
                GROUP BY component
                ORDER BY total_accesses DESC
            """, (start_date.isoformat(), end_date.isoformat())).fetchall()
            
            return {
                "by_source": [dict(row) for row in source_stats],
                "by_component": [dict(row) for row in prefix_stats],
                "analysis_period": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat()
                }
            }
    
    def _generate_sunset_recommendations(self, weekly_data: List[Dict[str, Any]], trend_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成日落建议"""
        recommendations = {
            "sunset_readiness": "not_ready",
            "readiness_score": 0,
            "blocking_factors": [],
            "recommendations": [],
            "timeline_estimate": None
        }
        
        if not weekly_data:
            recommendations["blocking_factors"].append("缺少使用数据")
            return recommendations
        
        # 计算准备度分数
        score = 0
        
        # 检查使用趋势
        if trend_analysis.get("overall_trend") == "decreasing":
            score += 30
            recommendations["recommendations"].append("使用趋势呈下降态势，有利于日落")
        else:
            recommendations["blocking_factors"].append("使用趋势未呈明显下降")
        
        # 检查最近使用量
        latest_week = weekly_data[-1] if weekly_data else {}
        unique_keys = latest_week.get("stats", {}).get("unique_keys", 999)
        total_accesses = latest_week.get("stats", {}).get("total_accesses", 999)
        
        if unique_keys <= 5:
            score += 25
            recommendations["recommendations"].append(f"活跃配置键数量较少({unique_keys}个)")
        else:
            recommendations["blocking_factors"].append(f"仍有{unique_keys}个活跃配置键")
        
        if total_accesses <= 50:
            score += 25
            recommendations["recommendations"].append(f"总访问量较低({total_accesses}次/周)")
        else:
            recommendations["blocking_factors"].append(f"周访问量仍较高({total_accesses}次)")
        
        # 检查新增键趋势
        recent_new_keys = sum(week.get("new_keys_count", 0) for week in weekly_data[-2:])
        if recent_new_keys == 0:
            score += 20
            recommendations["recommendations"].append("最近无新增配置键")
        else:
            recommendations["blocking_factors"].append(f"最近仍有{recent_new_keys}个新增配置键")
        
        # 确定准备度状态
        recommendations["readiness_score"] = score
        
        if score >= 80:
            recommendations["sunset_readiness"] = "ready"
            recommendations["timeline_estimate"] = "1-2周内可执行日落"
        elif score >= 60:
            recommendations["sunset_readiness"] = "nearly_ready"
            recommendations["timeline_estimate"] = "4-6周内可执行日落"
        elif score >= 40:
            recommendations["sunset_readiness"] = "preparing"
            recommendations["timeline_estimate"] = "2-3个月内可执行日落"
        else:
            recommendations["sunset_readiness"] = "not_ready"
            recommendations["timeline_estimate"] = "需要继续观察，暂无明确时间表"
        
        return recommendations
    
    def _generate_summary(self, weekly_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成摘要"""
        if not weekly_data:
            return {"message": "无数据"}
        
        latest_week = weekly_data[-1]
        first_week = weekly_data[0]
        
        return {
            "total_weeks": len(weekly_data),
            "latest_week_keys": latest_week.get("stats", {}).get("unique_keys", 0),
            "latest_week_accesses": latest_week.get("stats", {}).get("total_accesses", 0),
            "change_from_first_week": {
                "keys": latest_week.get("stats", {}).get("unique_keys", 0) - first_week.get("stats", {}).get("unique_keys", 0),
                "accesses": latest_week.get("stats", {}).get("total_accesses", 0) - first_week.get("stats", {}).get("total_accesses", 0)
            }
        }
    
    def _generate_trend_summary(self, keys_trend: str, access_trend: str, keys_change: float, access_change: float) -> str:
        """生成趋势摘要"""
        trend_descriptions = {
            "increasing": "上升",
            "decreasing": "下降", 
            "stable": "稳定"
        }
        
        keys_desc = trend_descriptions.get(keys_trend, keys_trend)
        access_desc = trend_descriptions.get(access_trend, access_trend)
        
        return f"配置键数量呈{keys_desc}趋势({keys_change:+.1f}%)，访问量呈{access_desc}趋势({access_change:+.1f}%)"
    
    def _prepare_charts_data(self, weekly_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """准备图表数据"""
        weeks = [week["week_start"][:10] for week in weekly_data]  # 只取日期部分
        unique_keys = [week["stats"].get("unique_keys", 0) for week in weekly_data]
        total_accesses = [week["stats"].get("total_accesses", 0) for week in weekly_data]
        
        return {
            "usage_trend": {
                "labels": weeks,
                "datasets": [
                    {
                        "label": "活跃配置键数量",
                        "data": unique_keys,
                        "type": "line"
                    },
                    {
                        "label": "总访问次数",
                        "data": total_accesses,
                        "type": "bar"
                    }
                ]
            }
        }
    
    def generate_html_report(self, report_data: Dict[str, Any]) -> str:
        """生成HTML格式报告"""
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>旧托底使用度周报</title>
    <style>
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 40px; }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; }}
        .section {{ margin: 20px 0; padding: 20px; border: 1px solid #e1e5e9; border-radius: 8px; }}
        .metric {{ display: inline-block; margin: 10px 20px 10px 0; }}
        .metric-value {{ font-size: 24px; font-weight: bold; color: #2c3e50; }}
        .metric-label {{ font-size: 14px; color: #666; }}
        .trend-up {{ color: #e74c3c; }}
        .trend-down {{ color: #27ae60; }}
        .trend-stable {{ color: #f39c12; }}
        .recommendation {{ padding: 15px; margin: 10px 0; border-radius: 4px; }}
        .rec-ready {{ background-color: #d4edda; border-left: 4px solid #27ae60; }}
        .rec-nearly {{ background-color: #fff3cd; border-left: 4px solid #f39c12; }}
        .rec-not-ready {{ background-color: #f8d7da; border-left: 4px solid #e74c3c; }}
        table {{ width: 100%; border-collapse: collapse; margin: 15px 0; }}
        th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
        th {{ background-color: #f8f9fa; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🕰️ 旧托底使用度周报</h1>
        <p>报告生成时间: {generated_at}</p>
        <p>统计周期: {period_start} 至 {period_end} ({weeks} 周)</p>
    </div>
    
    <div class="section">
        <h2>📊 使用度摘要</h2>
        <div class="metric">
            <div class="metric-value">{latest_keys}</div>
            <div class="metric-label">本周活跃配置键</div>
        </div>
        <div class="metric">
            <div class="metric-value">{latest_accesses}</div>
            <div class="metric-label">本周总访问次数</div>
        </div>
        <div class="metric">
            <div class="metric-value {keys_trend_class}">{keys_change:+.1f}%</div>
            <div class="metric-label">配置键数量变化</div>
        </div>
        <div class="metric">
            <div class="metric-value {access_trend_class}">{access_change:+.1f}%</div>
            <div class="metric-label">访问量变化</div>
        </div>
    </div>
    
    <div class="section">
        <h2>📈 趋势分析</h2>
        <p>{trend_summary}</p>
    </div>
    
    <div class="section">
        <h2>🎯 日落建议</h2>
        <div class="recommendation {rec_class}">
            <h3>准备度评估: {readiness} (得分: {readiness_score}/100)</h3>
            <p><strong>预计时间表:</strong> {timeline}</p>
            {recommendations_html}
            {blocking_factors_html}
        </div>
    </div>
    
    <div class="section">
        <h2>📋 组件使用度分析</h2>
        {component_table}
    </div>
    
    <div class="section">
        <h2>📅 周度数据详情</h2>
        {weekly_table}
    </div>
</body>
</html>
        """
        
        # 准备模板数据
        summary = report_data["summary"]
        trend = report_data["trend_analysis"]
        sunset = report_data["sunset_recommendations"]
        
        # 趋势样式类
        keys_trend_class = f"trend-{trend['keys_trend'].replace('ing', '')}"
        access_trend_class = f"trend-{trend['access_trend'].replace('ing', '')}"
        
        # 日落建议样式类
        rec_class_map = {
            "ready": "rec-ready",
            "nearly_ready": "rec-nearly",
            "not_ready": "rec-not-ready",
            "preparing": "rec-nearly"
        }
        rec_class = rec_class_map.get(sunset["sunset_readiness"], "rec-not-ready")
        
        # 生成建议和阻塞因素HTML
        recommendations_html = ""
        if sunset["recommendations"]:
            recommendations_html = "<p><strong>有利因素:</strong></p><ul>"
            for rec in sunset["recommendations"]:
                recommendations_html += f"<li>{rec}</li>"
            recommendations_html += "</ul>"
        
        blocking_factors_html = ""
        if sunset["blocking_factors"]:
            blocking_factors_html = "<p><strong>阻塞因素:</strong></p><ul>"
            for factor in sunset["blocking_factors"]:
                blocking_factors_html += f"<li>{factor}</li>"
            blocking_factors_html += "</ul>"
        
        # 生成组件表格
        component_table = "<table><tr><th>组件</th><th>配置键数</th><th>访问次数</th></tr>"
        for comp in report_data["component_analysis"]["by_component"]:
            component_table += f"<tr><td>{comp['component']}</td><td>{comp['unique_keys']}</td><td>{comp['total_accesses']}</td></tr>"
        component_table += "</table>"
        
        # 生成周度表格
        weekly_table = "<table><tr><th>周期</th><th>活跃键数</th><th>访问次数</th><th>新增键数</th></tr>"
        for week in report_data["weekly_data"]:
            start_date = week["week_start"][:10]
            stats = week["stats"]
            weekly_table += f"<tr><td>{start_date}</td><td>{stats.get('unique_keys', 0)}</td><td>{stats.get('total_accesses', 0)}</td><td>{week['new_keys_count']}</td></tr>"
        weekly_table += "</table>"
        
        return html_template.format(
            generated_at=report_data["generated_at"][:19],
            period_start=report_data["period"]["start_date"][:10],
            period_end=report_data["period"]["end_date"][:10],
            weeks=report_data["period"]["weeks"],
            latest_keys=summary["latest_week_keys"],
            latest_accesses=summary["latest_week_accesses"],
            keys_change=trend["keys_change_percent"],
            access_change=trend["access_change_percent"],
            keys_trend_class=keys_trend_class,
            access_trend_class=access_trend_class,
            trend_summary=trend["trend_summary"],
            readiness=sunset["sunset_readiness"].replace("_", " ").title(),
            readiness_score=sunset["readiness_score"],
            timeline=sunset["timeline_estimate"],
            rec_class=rec_class,
            recommendations_html=recommendations_html,
            blocking_factors_html=blocking_factors_html,
            component_table=component_table,
            weekly_table=weekly_table
        )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="旧托底使用度周报生成器")
    parser.add_argument("--weeks", type=int, default=4, help="统计周数")
    parser.add_argument("--output", help="输出文件路径")
    parser.add_argument("--format", choices=["json", "html"], default="json", help="输出格式")
    
    args = parser.parse_args()
    
    try:
        reporter = LegacyUsageReporter()
        report = reporter.generate_weekly_report(args.weeks)
        
        if args.format == "html":
            content = reporter.generate_html_report(report)
            file_ext = "html"
        else:
            content = json.dumps(report, indent=2, ensure_ascii=False)
            file_ext = "json"
        
        if args.output:
            output_file = args.output
        else:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"legacy_usage_report_{timestamp}.{file_ext}"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"📄 周报已生成: {output_file}")
        
        # 显示摘要
        summary = report["summary"]
        sunset = report["sunset_recommendations"]
        
        print(f"\n📊 本周摘要:")
        print(f"  活跃配置键: {summary['latest_week_keys']} 个")
        print(f"  总访问次数: {summary['latest_week_accesses']} 次")
        print(f"  日落准备度: {sunset['sunset_readiness']} ({sunset['readiness_score']}/100)")
        print(f"  预计时间表: {sunset['timeline_estimate']}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 生成周报失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
