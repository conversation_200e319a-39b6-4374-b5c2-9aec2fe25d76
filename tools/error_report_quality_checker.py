#!/usr/bin/env python3
"""
错误报告质量检查工具

评估配置验证错误报告的质量和可读性
"""

import sys
import json
import argparse
from pathlib import Path
from typing import Dict, List, Any, Tuple
import re

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from backend.config.validation.negative_test_cases import ConfigValidationNegativeTests
from backend.config.validation.error_formatter import ConfigErrorFormatter


class ErrorReportQualityChecker:
    """错误报告质量检查器"""
    
    def __init__(self):
        self.quality_criteria = {
            "has_chinese": {
                "weight": 0.15,
                "description": "包含中文说明"
            },
            "has_solutions": {
                "weight": 0.25,
                "description": "提供解决方案"
            },
            "has_examples": {
                "weight": 0.20,
                "description": "包含示例代码"
            },
            "has_location": {
                "weight": 0.15,
                "description": "指明错误位置"
            },
            "has_input_value": {
                "weight": 0.10,
                "description": "显示输入值"
            },
            "has_help_links": {
                "weight": 0.10,
                "description": "提供帮助链接"
            },
            "clear_message": {
                "weight": 0.05,
                "description": "错误信息清晰"
            }
        }
    
    def evaluate_error_report(self, formatted_error: str, test_case_name: str = "") -> Dict[str, Any]:
        """评估单个错误报告的质量"""
        scores = {}
        total_score = 0.0
        
        # 检查各项质量标准
        scores["has_chinese"] = self._check_has_chinese(formatted_error)
        scores["has_solutions"] = self._check_has_solutions(formatted_error)
        scores["has_examples"] = self._check_has_examples(formatted_error)
        scores["has_location"] = self._check_has_location(formatted_error)
        scores["has_input_value"] = self._check_has_input_value(formatted_error)
        scores["has_help_links"] = self._check_has_help_links(formatted_error)
        scores["clear_message"] = self._check_clear_message(formatted_error)
        
        # 计算加权总分
        for criterion, score in scores.items():
            weight = self.quality_criteria[criterion]["weight"]
            total_score += score * weight
        
        return {
            "test_case": test_case_name,
            "total_score": total_score,
            "scores": scores,
            "grade": self._get_grade(total_score),
            "recommendations": self._get_recommendations(scores)
        }
    
    def _check_has_chinese(self, text: str) -> float:
        """检查是否包含中文"""
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        return min(chinese_chars / 10, 1.0)  # 至少10个中文字符得满分
    
    def _check_has_solutions(self, text: str) -> float:
        """检查是否有解决方案"""
        solution_indicators = ["解决方案", "💡", "建议", "修复"]
        return 1.0 if any(indicator in text for indicator in solution_indicators) else 0.0
    
    def _check_has_examples(self, text: str) -> float:
        """检查是否有示例"""
        example_indicators = ["示例", "📝", "例如", "example"]
        return 1.0 if any(indicator in text for indicator in example_indicators) else 0.0
    
    def _check_has_location(self, text: str) -> float:
        """检查是否指明错误位置"""
        location_indicators = ["位置", "📍", "->", "路径"]
        return 1.0 if any(indicator in text for indicator in location_indicators) else 0.0
    
    def _check_has_input_value(self, text: str) -> float:
        """检查是否显示输入值"""
        input_indicators = ["输入值", "🔍", "当前值", "实际输入"]
        return 1.0 if any(indicator in text for indicator in input_indicators) else 0.0
    
    def _check_has_help_links(self, text: str) -> float:
        """检查是否提供帮助链接"""
        help_indicators = ["更多帮助", "📚", "文档", "查看"]
        return 1.0 if any(indicator in text for indicator in help_indicators) else 0.0
    
    def _check_clear_message(self, text: str) -> float:
        """检查错误信息是否清晰"""
        # 简单的清晰度评估：避免过于技术性的术语
        technical_terms = ["ValidationError", "pydantic", "type=", "input_value="]
        technical_count = sum(1 for term in technical_terms if term in text)
        
        # 技术术语越少，清晰度越高
        return max(0.0, 1.0 - technical_count * 0.2)
    
    def _get_grade(self, score: float) -> str:
        """根据分数获取等级"""
        if score >= 0.9:
            return "优秀"
        elif score >= 0.8:
            return "良好"
        elif score >= 0.7:
            return "中等"
        elif score >= 0.6:
            return "及格"
        else:
            return "需改进"
    
    def _get_recommendations(self, scores: Dict[str, float]) -> List[str]:
        """根据评分获取改进建议"""
        recommendations = []
        
        if scores["has_chinese"] < 0.5:
            recommendations.append("增加中文说明，提高本地化程度")
        
        if scores["has_solutions"] < 1.0:
            recommendations.append("添加具体的解决方案和修复步骤")
        
        if scores["has_examples"] < 1.0:
            recommendations.append("提供配置示例代码")
        
        if scores["has_location"] < 1.0:
            recommendations.append("明确指出错误发生的位置")
        
        if scores["has_input_value"] < 1.0:
            recommendations.append("显示导致错误的输入值")
        
        if scores["has_help_links"] < 1.0:
            recommendations.append("添加相关文档和帮助链接")
        
        if scores["clear_message"] < 0.8:
            recommendations.append("简化技术术语，使用更通俗的语言")
        
        return recommendations
    
    def run_comprehensive_evaluation(self) -> Dict[str, Any]:
        """运行综合评估"""
        print("开始错误报告质量评估...")
        
        # 运行负例测试
        test_suite = ConfigValidationNegativeTests()
        formatter = ConfigErrorFormatter()
        
        evaluation_results = {
            "total_cases": 0,
            "average_score": 0.0,
            "grade_distribution": {},
            "detailed_results": []
        }
        
        # 评估每个测试用例的错误格式化质量
        for test_case in test_suite.test_cases:
            if "_yaml_content" in test_case.config_data:
                continue  # 跳过YAML内容测试
            
            try:
                # 触发错误并格式化
                from backend.config.validation.schemas import LLMConfigSchema
                LLMConfigSchema(**test_case.config_data)
            except Exception as e:
                formatted_error = formatter.format_validation_error(e, f"{test_case.name}.yaml")
                
                # 评估质量
                quality_result = self.evaluate_error_report(formatted_error, test_case.name)
                evaluation_results["detailed_results"].append(quality_result)
                evaluation_results["total_cases"] += 1
        
        # 计算统计信息
        if evaluation_results["total_cases"] > 0:
            total_score = sum(result["total_score"] for result in evaluation_results["detailed_results"])
            evaluation_results["average_score"] = total_score / evaluation_results["total_cases"]
            
            # 统计等级分布
            grade_counts = {}
            for result in evaluation_results["detailed_results"]:
                grade = result["grade"]
                grade_counts[grade] = grade_counts.get(grade, 0) + 1
            
            evaluation_results["grade_distribution"] = grade_counts
        
        return evaluation_results
    
    def generate_quality_report(self, results: Dict[str, Any], format: str = "text") -> str:
        """生成质量报告"""
        if format == "json":
            return json.dumps(results, indent=2, ensure_ascii=False)
        
        lines = ["错误报告质量评估报告", "=" * 50, ""]
        
        # 总体统计
        lines.extend([
            f"📊 总体统计",
            f"   评估用例数: {results['total_cases']}",
            f"   平均得分: {results['average_score']:.2f}",
            f"   总体等级: {self._get_grade(results['average_score'])}",
            ""
        ])
        
        # 等级分布
        if results["grade_distribution"]:
            lines.append("📈 等级分布:")
            for grade, count in results["grade_distribution"].items():
                percentage = count / results["total_cases"] * 100
                lines.append(f"   {grade}: {count} ({percentage:.1f}%)")
            lines.append("")
        
        # 详细结果（显示前5个）
        lines.append("📋 详细结果 (前5个):")
        for i, result in enumerate(results["detailed_results"][:5]):
            lines.extend([
                f"   {i+1}. {result['test_case']}",
                f"      得分: {result['total_score']:.2f} ({result['grade']})",
                f"      改进建议: {len(result['recommendations'])} 项"
            ])
            
            if result["recommendations"]:
                for rec in result["recommendations"][:2]:  # 只显示前2个建议
                    lines.append(f"        • {rec}")
            lines.append("")
        
        # 质量标准说明
        lines.extend([
            "📏 质量标准:",
            "   优秀 (≥0.9): 错误信息完整、清晰、有用",
            "   良好 (≥0.8): 错误信息较为完整，有改进空间",
            "   中等 (≥0.7): 错误信息基本可用，需要改进",
            "   及格 (≥0.6): 错误信息可读，但缺少关键信息",
            "   需改进 (<0.6): 错误信息难以理解或缺少重要信息",
            ""
        ])
        
        return "\n".join(lines)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="错误报告质量检查工具")
    parser.add_argument("--format", choices=["text", "json"], default="text", help="输出格式")
    parser.add_argument("--output", help="输出文件路径")
    parser.add_argument("--detailed", action="store_true", help="显示详细结果")
    
    args = parser.parse_args()
    
    checker = ErrorReportQualityChecker()
    results = checker.run_comprehensive_evaluation()
    
    # 生成报告
    report = checker.generate_quality_report(results, args.format)
    
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"质量报告已保存到: {args.output}")
    else:
        print(report)
    
    # 显示详细结果
    if args.detailed and args.format == "text":
        print("\n" + "=" * 50)
        print("详细评估结果:")
        
        for result in results["detailed_results"]:
            print(f"\n🔍 {result['test_case']}")
            print(f"   总分: {result['total_score']:.2f} ({result['grade']})")
            print("   各项得分:")
            
            for criterion, score in result["scores"].items():
                description = checker.quality_criteria[criterion]["description"]
                print(f"     {description}: {score:.2f}")
            
            if result["recommendations"]:
                print("   改进建议:")
                for rec in result["recommendations"]:
                    print(f"     • {rec}")
    
    # 返回退出码
    average_score = results.get("average_score", 0.0)
    if average_score >= 0.8:
        print(f"\n✅ 错误报告质量良好 (平均分: {average_score:.2f})")
        return 0
    else:
        print(f"\n⚠️  错误报告质量需要改进 (平均分: {average_score:.2f})")
        return 1


if __name__ == "__main__":
    sys.exit(main())
