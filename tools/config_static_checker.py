#!/usr/bin/env python3
"""
配置静态检查工具

完善的配置文件静态检查，包括：
1. YAML语法检查
2. Schema验证
3. 未知键检查
4. 跨文件一致性检查
5. 引用键存在性检查
6. 配置完整性验证
"""

import os
import sys
import yaml
import json
import argparse
from pathlib import Path
from typing import Dict, List, Any, Set, Tuple, Optional
from dataclasses import dataclass, field
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from backend.config.validation.schemas import RootConfigSchema
from backend.config.security.whitelist_manager import ConfigWhitelistManager


@dataclass
class CheckResult:
    """检查结果"""
    check_type: str
    file_path: str
    severity: str  # 'error', 'warning', 'info'
    message: str
    line_number: Optional[int] = None
    column_number: Optional[int] = None
    suggestion: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)


class ConfigStaticChecker:
    """配置静态检查器"""
    
    def __init__(self, config_dir: str = "backend/config"):
        self.config_dir = Path(config_dir)
        self.results: List[CheckResult] = []
        self.whitelist_manager = ConfigWhitelistManager()
        
        # 检查统计
        self.stats = {
            'files_checked': 0,
            'errors': 0,
            'warnings': 0,
            'info': 0
        }
        
        # 配置数据缓存
        self.config_data: Dict[str, Any] = {}
        self.all_config_keys: Set[str] = set()
        self.referenced_keys: Set[str] = set()
    
    def add_result(self, result: CheckResult):
        """添加检查结果"""
        self.results.append(result)
        self.stats[result.severity + 's'] += 1
    
    def check_all(self) -> List[CheckResult]:
        """执行所有检查"""
        print("开始配置静态检查...")
        
        # 1. 发现配置文件
        config_files = self._discover_config_files()
        print(f"发现 {len(config_files)} 个配置文件")
        
        # 2. YAML语法检查
        self._check_yaml_syntax(config_files)
        
        # 3. 加载所有配置数据
        self._load_all_configs(config_files)
        
        # 4. Schema验证
        self._check_schema_validation()
        
        # 5. 未知键检查
        self._check_unknown_keys()
        
        # 6. 跨文件一致性检查
        self._check_cross_file_consistency()
        
        # 7. 引用键存在性检查
        self._check_reference_keys()
        
        # 8. 配置完整性检查
        self._check_config_completeness()
        
        # 9. 安全性检查
        self._check_security_issues()
        
        print(f"检查完成: {self.stats['errors']} 错误, {self.stats['warnings']} 警告, {self.stats['info']} 信息")
        return self.results
    
    def _discover_config_files(self) -> List[Path]:
        """发现配置文件"""
        config_files = []
        
        # 扫描配置目录
        for pattern in ["*.yaml", "*.yml", "*.json"]:
            config_files.extend(self.config_dir.rglob(pattern))
        
        # 排除某些文件
        excluded_patterns = [
            "test_*", "*_test.*", "*.bak", "*.tmp",
            "node_modules", ".git", "__pycache__"
        ]
        
        filtered_files = []
        for file_path in config_files:
            if not any(pattern in str(file_path) for pattern in excluded_patterns):
                filtered_files.append(file_path)
        
        return filtered_files
    
    def _check_yaml_syntax(self, config_files: List[Path]):
        """检查YAML语法"""
        print("检查YAML语法...")
        
        for file_path in config_files:
            if file_path.suffix not in ['.yaml', '.yml']:
                continue
                
            self.stats['files_checked'] += 1
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    yaml.safe_load(f)
                    
            except yaml.YAMLError as e:
                line_num = getattr(e, 'problem_mark', None)
                line_number = line_num.line + 1 if line_num else None
                column_number = line_num.column + 1 if line_num else None
                
                self.add_result(CheckResult(
                    check_type="yaml_syntax",
                    file_path=str(file_path),
                    severity="error",
                    message=f"YAML语法错误: {e}",
                    line_number=line_number,
                    column_number=column_number,
                    suggestion="请检查YAML语法，确保缩进和格式正确"
                ))
                
            except Exception as e:
                self.add_result(CheckResult(
                    check_type="file_access",
                    file_path=str(file_path),
                    severity="error",
                    message=f"文件访问错误: {e}",
                    suggestion="请检查文件权限和编码"
                ))
    
    def _load_all_configs(self, config_files: List[Path]):
        """加载所有配置数据"""
        print("加载配置数据...")
        
        for file_path in config_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    if file_path.suffix in ['.yaml', '.yml']:
                        data = yaml.safe_load(f)
                    elif file_path.suffix == '.json':
                        data = json.load(f)
                    else:
                        continue
                
                if data:
                    self.config_data[str(file_path)] = data
                    self._extract_config_keys(data, str(file_path))
                    
            except Exception as e:
                # 语法错误已在前面检查过，这里跳过
                continue
    
    def _extract_config_keys(self, data: Any, file_path: str, prefix: str = ""):
        """提取配置键"""
        if isinstance(data, dict):
            for key, value in data.items():
                full_key = f"{prefix}.{key}" if prefix else key
                self.all_config_keys.add(full_key)
                
                if isinstance(value, dict):
                    self._extract_config_keys(value, file_path, full_key)
                elif isinstance(value, str) and self._looks_like_config_reference(value):
                    self.referenced_keys.add(value)
    
    def _looks_like_config_reference(self, value: str) -> bool:
        """判断字符串是否像配置引用"""
        # 简单的启发式规则
        if len(value.split('.')) >= 2 and not value.startswith('http'):
            # 包含点号且不是URL
            return True
        return False
    
    def _check_schema_validation(self):
        """检查Schema验证"""
        print("检查Schema验证...")
        
        # 合并所有配置数据
        merged_config = {}
        for file_path, data in self.config_data.items():
            self._deep_merge(merged_config, data)
        
        try:
            # 使用Pydantic Schema验证
            schema = RootConfigSchema(**merged_config)
            
            self.add_result(CheckResult(
                check_type="schema_validation",
                file_path="merged_config",
                severity="info",
                message="Schema验证通过",
                context={"validated_keys": len(self.all_config_keys)}
            ))
            
        except Exception as e:
            self.add_result(CheckResult(
                check_type="schema_validation",
                file_path="merged_config",
                severity="error",
                message=f"Schema验证失败: {e}",
                suggestion="请检查配置结构是否符合Schema定义"
            ))
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]):
        """深度合并字典"""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._deep_merge(base[key], value)
            else:
                base[key] = value
    
    def _check_unknown_keys(self):
        """检查未知键"""
        print("检查未知键...")
        
        # 获取白名单允许的键
        allowed_keys = self.whitelist_manager.get_allowed_keys()
        
        unknown_keys = []
        for key in self.all_config_keys:
            is_allowed, rule = self.whitelist_manager.is_key_allowed(key)
            if not is_allowed:
                unknown_keys.append(key)
        
        if unknown_keys:
            for key in unknown_keys:
                # 找到包含此键的文件
                containing_files = []
                for file_path, data in self.config_data.items():
                    if self._key_exists_in_data(key, data):
                        containing_files.append(file_path)
                
                self.add_result(CheckResult(
                    check_type="unknown_key",
                    file_path=", ".join(containing_files),
                    severity="warning",
                    message=f"未知配置键: {key}",
                    suggestion="请检查键名是否正确，或更新白名单配置",
                    context={"key": key, "files": containing_files}
                ))
        else:
            self.add_result(CheckResult(
                check_type="unknown_key",
                file_path="all_files",
                severity="info",
                message="未发现未知配置键"
            ))
    
    def _key_exists_in_data(self, key: str, data: Any, prefix: str = "") -> bool:
        """检查键是否存在于数据中"""
        if isinstance(data, dict):
            for k, v in data.items():
                full_key = f"{prefix}.{k}" if prefix else k
                if full_key == key:
                    return True
                if isinstance(v, dict) and self._key_exists_in_data(key, v, full_key):
                    return True
        return False
    
    def _check_cross_file_consistency(self):
        """检查跨文件一致性"""
        print("检查跨文件一致性...")
        
        # 检查重复定义的键
        key_definitions = {}
        for file_path, data in self.config_data.items():
            self._collect_key_definitions(data, file_path, key_definitions)
        
        # 查找重复定义
        for key, files in key_definitions.items():
            if len(files) > 1:
                # 检查值是否一致
                values = []
                for file_path in files:
                    value = self._get_key_value(key, self.config_data[file_path])
                    values.append((file_path, value))
                
                # 比较所有值
                first_value = values[0][1]
                inconsistent_files = []
                
                for file_path, value in values[1:]:
                    if value != first_value:
                        inconsistent_files.append(file_path)
                
                if inconsistent_files:
                    self.add_result(CheckResult(
                        check_type="cross_file_consistency",
                        file_path=", ".join([values[0][0]] + inconsistent_files),
                        severity="error",
                        message=f"配置键 '{key}' 在多个文件中定义了不同的值",
                        suggestion="请确保同一配置键在所有文件中的值保持一致",
                        context={
                            "key": key,
                            "definitions": [{"file": f, "value": str(v)} for f, v in values]
                        }
                    ))
                else:
                    self.add_result(CheckResult(
                        check_type="cross_file_consistency",
                        file_path=", ".join(files),
                        severity="warning",
                        message=f"配置键 '{key}' 在多个文件中重复定义（值相同）",
                        suggestion="考虑将重复定义合并到一个文件中",
                        context={"key": key, "files": files}
                    ))
    
    def _collect_key_definitions(self, data: Any, file_path: str, 
                               key_definitions: Dict[str, List[str]], prefix: str = ""):
        """收集键定义"""
        if isinstance(data, dict):
            for key, value in data.items():
                full_key = f"{prefix}.{key}" if prefix else key
                
                if full_key not in key_definitions:
                    key_definitions[full_key] = []
                key_definitions[full_key].append(file_path)
                
                if isinstance(value, dict):
                    self._collect_key_definitions(value, file_path, key_definitions, full_key)
    
    def _get_key_value(self, key: str, data: Any) -> Any:
        """获取键值"""
        keys = key.split('.')
        current = data
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return None
        return current
    
    def _check_reference_keys(self):
        """检查引用键存在性"""
        print("检查引用键存在性...")
        
        missing_references = []
        for ref_key in self.referenced_keys:
            if ref_key not in self.all_config_keys:
                missing_references.append(ref_key)
        
        for missing_key in missing_references:
            # 找到引用此键的位置
            referencing_files = []
            for file_path, data in self.config_data.items():
                if self._find_references_in_data(missing_key, data):
                    referencing_files.append(file_path)
            
            self.add_result(CheckResult(
                check_type="missing_reference",
                file_path=", ".join(referencing_files),
                severity="error",
                message=f"引用的配置键不存在: {missing_key}",
                suggestion="请检查引用的键名是否正确，或添加相应的配置定义",
                context={"referenced_key": missing_key, "referencing_files": referencing_files}
            ))
    
    def _find_references_in_data(self, ref_key: str, data: Any) -> bool:
        """在数据中查找引用"""
        if isinstance(data, dict):
            for value in data.values():
                if self._find_references_in_data(ref_key, value):
                    return True
        elif isinstance(data, list):
            for item in data:
                if self._find_references_in_data(ref_key, item):
                    return True
        elif isinstance(data, str):
            return ref_key in data
        return False
    
    def _check_config_completeness(self):
        """检查配置完整性"""
        print("检查配置完整性...")
        
        # 检查必需的配置键
        required_keys = [
            "app.name",
            "app.version",
            "llm.default_model",
            "database.connection.path"
        ]
        
        missing_required = []
        for key in required_keys:
            if key not in self.all_config_keys:
                missing_required.append(key)
        
        for missing_key in missing_required:
            self.add_result(CheckResult(
                check_type="missing_required",
                file_path="all_files",
                severity="error",
                message=f"缺少必需的配置键: {missing_key}",
                suggestion="请添加必需的配置键定义",
                context={"required_key": missing_key}
            ))
        
        # 检查配置组的完整性
        self._check_config_groups()
    
    def _check_config_groups(self):
        """检查配置组完整性"""
        # 定义配置组
        config_groups = {
            "llm": ["default_model", "temperature", "max_tokens"],
            "database.connection": ["path", "timeout"],
            "logging": ["level", "format"]
        }
        
        for group_prefix, required_subkeys in config_groups.items():
            group_keys = [key for key in self.all_config_keys if key.startswith(group_prefix + ".")]
            
            missing_subkeys = []
            for subkey in required_subkeys:
                full_key = f"{group_prefix}.{subkey}"
                if full_key not in self.all_config_keys:
                    missing_subkeys.append(subkey)
            
            if missing_subkeys and group_keys:  # 如果组存在但缺少子键
                self.add_result(CheckResult(
                    check_type="incomplete_group",
                    file_path="all_files",
                    severity="warning",
                    message=f"配置组 '{group_prefix}' 不完整，缺少: {', '.join(missing_subkeys)}",
                    suggestion=f"请添加缺少的配置键: {', '.join(f'{group_prefix}.{k}' for k in missing_subkeys)}",
                    context={"group": group_prefix, "missing_keys": missing_subkeys}
                ))
    
    def _check_security_issues(self):
        """检查安全问题"""
        print("检查安全问题...")
        
        security_issues = []
        
        for file_path, data in self.config_data.items():
            self._scan_security_issues(data, file_path, security_issues)
        
        for issue in security_issues:
            self.add_result(CheckResult(
                check_type="security_issue",
                file_path=issue["file"],
                severity="warning",
                message=issue["message"],
                suggestion=issue["suggestion"],
                context=issue["context"]
            ))
    
    def _scan_security_issues(self, data: Any, file_path: str, issues: List[Dict[str, Any]], prefix: str = ""):
        """扫描安全问题"""
        if isinstance(data, dict):
            for key, value in data.items():
                full_key = f"{prefix}.{key}" if prefix else key
                
                # 检查敏感信息
                if isinstance(value, str):
                    is_sensitive, level, reason = self.whitelist_manager.detector.detect_sensitive_value(value, full_key)
                    if is_sensitive and level.value in ['secret', 'confidential']:
                        issues.append({
                            "file": file_path,
                            "message": f"配置文件中包含敏感信息: {full_key}",
                            "suggestion": "敏感信息应通过环境变量设置，不应直接写在配置文件中",
                            "context": {"key": full_key, "sensitivity_level": level.value, "reason": reason}
                        })
                
                if isinstance(value, dict):
                    self._scan_security_issues(value, file_path, issues, full_key)
    
    def generate_report(self, output_format: str = "text") -> str:
        """生成检查报告"""
        if output_format == "json":
            return self._generate_json_report()
        elif output_format == "html":
            return self._generate_html_report()
        else:
            return self._generate_text_report()
    
    def _generate_text_report(self) -> str:
        """生成文本报告"""
        lines = [
            "配置静态检查报告",
            "=" * 50,
            f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"检查文件数: {self.stats['files_checked']}",
            f"发现问题: {self.stats['errors']} 错误, {self.stats['warnings']} 警告, {self.stats['info']} 信息",
            ""
        ]
        
        # 按严重程度分组
        by_severity = {"error": [], "warning": [], "info": []}
        for result in self.results:
            by_severity[result.severity].append(result)
        
        for severity in ["error", "warning", "info"]:
            if by_severity[severity]:
                lines.append(f"{severity.upper()}S ({len(by_severity[severity])})")
                lines.append("-" * 20)
                
                for result in by_severity[severity]:
                    lines.append(f"[{result.check_type}] {result.file_path}")
                    lines.append(f"  {result.message}")
                    if result.suggestion:
                        lines.append(f"  建议: {result.suggestion}")
                    if result.line_number:
                        lines.append(f"  位置: 第{result.line_number}行")
                    lines.append("")
        
        return "\n".join(lines)
    
    def _generate_json_report(self) -> str:
        """生成JSON报告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "stats": self.stats,
            "results": [
                {
                    "check_type": r.check_type,
                    "file_path": r.file_path,
                    "severity": r.severity,
                    "message": r.message,
                    "line_number": r.line_number,
                    "column_number": r.column_number,
                    "suggestion": r.suggestion,
                    "context": r.context
                }
                for r in self.results
            ]
        }
        return json.dumps(report, indent=2, ensure_ascii=False)
    
    def _generate_html_report(self) -> str:
        """生成HTML报告"""
        # 简化的HTML报告
        html = f"""
        <html>
        <head><title>配置静态检查报告</title></head>
        <body>
        <h1>配置静态检查报告</h1>
        <p>检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>统计: {self.stats['errors']} 错误, {self.stats['warnings']} 警告, {self.stats['info']} 信息</p>
        <ul>
        """
        
        for result in self.results:
            color = {"error": "red", "warning": "orange", "info": "blue"}[result.severity]
            html += f'<li style="color: {color}"><strong>[{result.check_type}]</strong> {result.message} ({result.file_path})</li>'
        
        html += "</ul></body></html>"
        return html


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="配置静态检查工具")
    parser.add_argument("--config-dir", default="backend/config", help="配置目录路径")
    parser.add_argument("--format", choices=["text", "json", "html"], default="text", help="报告格式")
    parser.add_argument("--output", help="输出文件路径")
    parser.add_argument("--fail-on-error", action="store_true", help="有错误时返回非零退出码")
    
    args = parser.parse_args()
    
    checker = ConfigStaticChecker(args.config_dir)
    results = checker.check_all()
    
    # 生成报告
    report = checker.generate_report(args.format)
    
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"报告已保存到: {args.output}")
    else:
        print(report)
    
    # 根据结果决定退出码
    if args.fail_on_error and checker.stats['errors'] > 0:
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
