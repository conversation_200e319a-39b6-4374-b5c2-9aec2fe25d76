#!/usr/bin/env python3
"""
配置影响分析CLI工具

分析配置键变更对系统组件的影响，生成影响报告和测试清单
"""

import argparse
import json
import sys
import yaml
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from backend.config.dependency.graph import get_dependency_graph, ImpactLevel
from backend.config.dependency.registry import register_all_system_dependencies


class ConfigImpactAnalyzer:
    """配置影响分析器"""
    
    def __init__(self):
        # 初始化依赖图
        self.registry = register_all_system_dependencies()
        self.graph = get_dependency_graph()
    
    def analyze_single_key(self, config_key: str) -> Dict[str, Any]:
        """分析单个配置键的影响"""
        impact = self.graph.analyze_config_impact(config_key)
        
        return {
            "config_key": config_key,
            "impact_analysis": {
                "affected_components": impact.affected_components,
                "impact_level": impact.impact_level.value,
                "risk_assessment": impact.risk_assessment,
                "test_requirements": impact.test_requirements,
                "rollback_strategy": impact.rollback_strategy,
                "deployment_notes": impact.deployment_notes
            },
            "component_details": self._get_component_details(impact.affected_components)
        }
    
    def analyze_multiple_keys(self, config_keys: List[str]) -> Dict[str, Any]:
        """分析多个配置键的影响"""
        results = {}
        all_affected_components = set()
        max_impact_level = ImpactLevel.LOW
        
        for key in config_keys:
            analysis = self.analyze_single_key(key)
            results[key] = analysis
            
            # 收集所有受影响的组件
            all_affected_components.update(analysis["impact_analysis"]["affected_components"])
            
            # 更新最大影响级别
            key_impact = ImpactLevel(analysis["impact_analysis"]["impact_level"])
            if self._compare_impact_level(key_impact, max_impact_level) > 0:
                max_impact_level = key_impact
        
        return {
            "analyzed_keys": config_keys,
            "individual_analysis": results,
            "combined_impact": {
                "total_affected_components": len(all_affected_components),
                "affected_components": list(all_affected_components),
                "max_impact_level": max_impact_level.value,
                "combined_risk_assessment": self._generate_combined_risk_assessment(
                    config_keys, all_affected_components, max_impact_level
                )
            },
            "component_details": self._get_component_details(list(all_affected_components))
        }
    
    def _compare_impact_level(self, level1: ImpactLevel, level2: ImpactLevel) -> int:
        """比较影响级别"""
        level_order = {
            ImpactLevel.LOW: 0,
            ImpactLevel.MEDIUM: 1,
            ImpactLevel.HIGH: 2,
            ImpactLevel.CRITICAL: 3
        }
        return level_order[level1] - level_order[level2]
    
    def _generate_combined_risk_assessment(self, keys: List[str], 
                                         components: List[str], 
                                         max_impact: ImpactLevel) -> str:
        """生成组合风险评估"""
        if max_impact == ImpactLevel.CRITICAL:
            return f"极高风险：{len(keys)} 个配置键变更影响 {len(components)} 个组件，" \
                   f"可能导致系统无法启动或核心功能完全失效"
        elif max_impact == ImpactLevel.HIGH:
            return f"高风险：{len(keys)} 个配置键变更影响 {len(components)} 个组件，" \
                   f"可能导致重要功能异常"
        elif max_impact == ImpactLevel.MEDIUM:
            return f"中等风险：{len(keys)} 个配置键变更影响 {len(components)} 个组件，" \
                   f"可能导致部分功能降级"
        else:
            return f"低风险：{len(keys)} 个配置键变更影响 {len(components)} 个组件，" \
                   f"影响较小"
    
    def _get_component_details(self, component_names: List[str]) -> Dict[str, Any]:
        """获取组件详细信息"""
        details = {}
        
        for name in component_names:
            component = self.graph.components.get(name)
            if component:
                details[name] = {
                    "type": component.component_type.value,
                    "description": component.description,
                    "file_path": component.file_path,
                    "maintainer": component.maintainer,
                    "test_files": component.test_files,
                    "dependencies": [
                        {
                            "config_key": dep.config_key,
                            "type": dep.dependency_type.value,
                            "impact": dep.impact_level.value,
                            "description": dep.description,
                            "fallback": dep.fallback_strategy
                        }
                        for dep in self.graph.get_component_dependencies(name)
                    ]
                }
        
        return details
    
    def generate_test_checklist(self, config_keys: List[str]) -> Dict[str, Any]:
        """生成测试检查清单"""
        analysis = self.analyze_multiple_keys(config_keys)
        
        # 收集所有测试文件
        test_files = set()
        for component_name in analysis["combined_impact"]["affected_components"]:
            component = self.graph.components.get(component_name)
            if component:
                test_files.update(component.test_files)
        
        # 按影响级别分类测试要求
        critical_tests = []
        high_tests = []
        medium_tests = []
        low_tests = []
        
        for key, key_analysis in analysis["individual_analysis"].items():
            impact_level = key_analysis["impact_analysis"]["impact_level"]
            test_reqs = key_analysis["impact_analysis"]["test_requirements"]
            
            if impact_level == "critical":
                critical_tests.extend(test_reqs)
            elif impact_level == "high":
                high_tests.extend(test_reqs)
            elif impact_level == "medium":
                medium_tests.extend(test_reqs)
            else:
                low_tests.extend(test_reqs)
        
        return {
            "config_keys": config_keys,
            "test_summary": {
                "total_test_files": len(test_files),
                "critical_tests": len(set(critical_tests)),
                "high_tests": len(set(high_tests)),
                "medium_tests": len(set(medium_tests)),
                "low_tests": len(set(low_tests))
            },
            "test_checklist": {
                "critical_priority": list(set(critical_tests)),
                "high_priority": list(set(high_tests)),
                "medium_priority": list(set(medium_tests)),
                "low_priority": list(set(low_tests))
            },
            "all_test_files": list(test_files),
            "test_commands": self._generate_test_commands(list(test_files))
        }
    
    def _generate_test_commands(self, test_files: List[str]) -> List[str]:
        """生成测试命令"""
        commands = []
        
        if test_files:
            # 单元测试命令
            commands.append(f"python -m pytest {' '.join(test_files)} -v")
            
            # 覆盖率测试命令
            commands.append(f"python -m pytest {' '.join(test_files)} --cov --cov-report=html")
            
            # 集成测试命令
            commands.append("python -m pytest tests/integration/ -v")
        
        return commands
    
    def generate_deployment_plan(self, config_keys: List[str]) -> Dict[str, Any]:
        """生成部署计划"""
        analysis = self.analyze_multiple_keys(config_keys)
        max_impact = ImpactLevel(analysis["combined_impact"]["max_impact_level"])
        
        # 根据影响级别确定部署策略
        if max_impact == ImpactLevel.CRITICAL:
            deployment_strategy = "蓝绿部署"
            rollback_time = "立即"
            monitoring_duration = "24小时"
        elif max_impact == ImpactLevel.HIGH:
            deployment_strategy = "灰度发布"
            rollback_time = "5分钟内"
            monitoring_duration = "12小时"
        elif max_impact == ImpactLevel.MEDIUM:
            deployment_strategy = "分批发布"
            rollback_time = "30分钟内"
            monitoring_duration = "6小时"
        else:
            deployment_strategy = "直接发布"
            rollback_time = "下个发布窗口"
            monitoring_duration = "2小时"
        
        return {
            "config_keys": config_keys,
            "deployment_strategy": deployment_strategy,
            "rollback_requirements": {
                "max_rollback_time": rollback_time,
                "monitoring_duration": monitoring_duration,
                "rollback_triggers": self._get_rollback_triggers(max_impact)
            },
            "pre_deployment_checklist": [
                "备份当前配置",
                "准备回滚脚本",
                "通知相关团队",
                "设置监控告警"
            ],
            "post_deployment_checklist": [
                "验证配置生效",
                "检查关键指标",
                "运行冒烟测试",
                "更新文档"
            ],
            "monitoring_points": self._get_monitoring_points(analysis["combined_impact"]["affected_components"])
        }
    
    def _get_rollback_triggers(self, impact_level: ImpactLevel) -> List[str]:
        """获取回滚触发条件"""
        if impact_level == ImpactLevel.CRITICAL:
            return [
                "系统无法启动",
                "核心功能完全失效",
                "错误率超过1%",
                "响应时间增加超过50%"
            ]
        elif impact_level == ImpactLevel.HIGH:
            return [
                "重要功能异常",
                "错误率超过5%",
                "响应时间增加超过100%",
                "用户投诉增加"
            ]
        else:
            return [
                "功能异常持续超过1小时",
                "错误率超过10%",
                "性能明显下降"
            ]
    
    def _get_monitoring_points(self, components: List[str]) -> List[str]:
        """获取监控点"""
        monitoring_points = [
            "系统启动状态",
            "配置加载时间",
            "错误日志数量",
            "响应时间"
        ]
        
        # 根据组件类型添加特定监控点
        for component_name in components:
            component = self.graph.components.get(component_name)
            if component:
                if component.component_type.value == "agent":
                    monitoring_points.append("Agent响应时间")
                elif component.component_type.value == "handler":
                    monitoring_points.append("Handler处理成功率")
                elif component.component_type.value == "service":
                    monitoring_points.append("服务可用性")
        
        return list(set(monitoring_points))
    
    def export_analysis_report(self, config_keys: List[str], output_file: str, format: str = "json"):
        """导出分析报告"""
        analysis = self.analyze_multiple_keys(config_keys)
        test_checklist = self.generate_test_checklist(config_keys)
        deployment_plan = self.generate_deployment_plan(config_keys)
        
        report = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "analyzer_version": "1.0",
                "config_keys": config_keys
            },
            "impact_analysis": analysis,
            "test_checklist": test_checklist,
            "deployment_plan": deployment_plan
        }
        
        if format == "json":
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
        elif format == "yaml":
            with open(output_file, 'w', encoding='utf-8') as f:
                yaml.dump(report, f, default_flow_style=False, allow_unicode=True)
        else:
            raise ValueError(f"不支持的格式: {format}")
        
        print(f"分析报告已导出到: {output_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="配置影响分析工具")
    parser.add_argument("config_keys", nargs="+", help="要分析的配置键")
    parser.add_argument("--output", "-o", help="输出文件路径")
    parser.add_argument("--format", choices=["json", "yaml", "text"], default="text", help="输出格式")
    parser.add_argument("--test-only", action="store_true", help="只生成测试清单")
    parser.add_argument("--deploy-only", action="store_true", help="只生成部署计划")
    
    args = parser.parse_args()
    
    try:
        analyzer = ConfigImpactAnalyzer()
        
        if args.test_only:
            # 只生成测试清单
            result = analyzer.generate_test_checklist(args.config_keys)
            title = "测试清单"
        elif args.deploy_only:
            # 只生成部署计划
            result = analyzer.generate_deployment_plan(args.config_keys)
            title = "部署计划"
        else:
            # 完整分析
            result = analyzer.analyze_multiple_keys(args.config_keys)
            title = "影响分析"
        
        if args.output:
            if args.format in ["json", "yaml"]:
                analyzer.export_analysis_report(args.config_keys, args.output, args.format)
            else:
                # 文本格式输出
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(f"# {title}\n\n")
                    f.write(json.dumps(result, indent=2, ensure_ascii=False))
                print(f"{title}已保存到: {args.output}")
        else:
            # 控制台输出
            print(f"# {title}")
            print("=" * 50)
            
            if args.format == "json":
                print(json.dumps(result, indent=2, ensure_ascii=False))
            elif args.format == "yaml":
                print(yaml.dump(result, default_flow_style=False, allow_unicode=True))
            else:
                # 简化的文本输出
                if "combined_impact" in result:
                    impact = result["combined_impact"]
                    print(f"配置键: {', '.join(args.config_keys)}")
                    print(f"影响组件数: {impact['total_affected_components']}")
                    print(f"最大影响级别: {impact['max_impact_level']}")
                    print(f"风险评估: {impact['combined_risk_assessment']}")
                    print(f"受影响组件: {', '.join(impact['affected_components'])}")
                else:
                    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
