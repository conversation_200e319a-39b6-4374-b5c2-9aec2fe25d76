#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
旧托底自动化清理工具

基于使用度数据自动清理旧配置系统的废弃代码和文件
"""

import os
import sys
import json
import shutil
import argparse
from pathlib import Path
from typing import Dict, List, Any, Set
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from tools.legacy_usage_monitor import LegacyUsageMonitor
from tools.legacy_dependency_scanner import LegacyDependencyScanner


class LegacyCleanupAutomation:
    """旧托底自动化清理"""
    
    def __init__(self, dry_run: bool = True):
        self.dry_run = dry_run
        self.usage_monitor = LegacyUsageMonitor()
        self.dependency_scanner = LegacyDependencyScanner()
        
        # 清理配置
        self.cleanup_config = {
            'backup_dir': 'backups/legacy_cleanup',
            'safe_mode': True,  # 安全模式，保留重要文件
            'batch_size': 10,   # 批量处理大小
            'confirmation_required': True  # 需要确认
        }
        
        # 需要保护的文件和目录
        self.protected_paths = {
            'backend/config/manager.py',
            'backend/config/legacy/wrapper.py',
            'backend/config/legacy/__init__.py',
            'tests/',
            'docs/',
            '.git/',
        }
        
        # 清理统计
        self.cleanup_stats = {
            'files_removed': 0,
            'lines_removed': 0,
            'imports_cleaned': 0,
            'references_updated': 0,
            'backup_created': False
        }
    
    def create_backup(self) -> str:
        """创建备份"""
        backup_dir = Path(self.cleanup_config['backup_dir'])
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = backup_dir / f"legacy_backup_{timestamp}"
        
        if not self.dry_run:
            backup_path.mkdir(parents=True, exist_ok=True)
            
            # 备份关键目录
            backup_targets = [
                'backend/config/legacy/',
                'backend/config/old/',
                'backend/utils/legacy_config.py',
            ]
            
            for target in backup_targets:
                target_path = Path(target)
                if target_path.exists():
                    if target_path.is_file():
                        shutil.copy2(target_path, backup_path / target_path.name)
                    else:
                        shutil.copytree(target_path, backup_path / target_path.name, dirs_exist_ok=True)
            
            self.cleanup_stats['backup_created'] = True
            print(f"✅ 备份已创建: {backup_path}")
        else:
            print(f"🔍 [DRY RUN] 将创建备份: {backup_path}")
        
        return str(backup_path)
    
    def identify_cleanup_targets(self) -> Dict[str, List[str]]:
        """识别清理目标"""
        print("🔍 识别清理目标...")
        
        # 获取使用度数据
        cleanup_report = self.usage_monitor.generate_cleanup_report()
        candidates = cleanup_report['cleanup_candidates']['details']
        
        # 获取依赖扫描结果
        scan_results = []
        scan_dirs = ['backend/', 'tools/', 'tests/']
        
        for scan_dir in scan_dirs:
            if Path(scan_dir).exists():
                violations = self.dependency_scanner.scan_directory(scan_dir)
                scan_results.extend(violations)
        
        # 分类清理目标
        cleanup_targets = {
            'unused_config_keys': [],
            'deprecated_files': [],
            'legacy_imports': [],
            'dead_code_blocks': [],
            'obsolete_tests': []
        }
        
        # 处理未使用的配置键
        high_priority_candidates = [
            c for c in candidates 
            if c['priority'] == 'high' and c['reason'] == 'zero_usage'
        ]
        cleanup_targets['unused_config_keys'] = [
            c['config_key'] for c in high_priority_candidates
        ]
        
        # 处理废弃文件
        deprecated_files = set()
        for violation in scan_results:
            if violation.priority == 'P0' and 'deprecated' in violation.message.lower():
                deprecated_files.add(violation.file_path)
        
        cleanup_targets['deprecated_files'] = list(deprecated_files)
        
        # 处理旧导入
        legacy_imports = set()
        for violation in scan_results:
            if 'import' in violation.violation_type.lower():
                legacy_imports.add(f"{violation.file_path}:{violation.line_number}")
        
        cleanup_targets['legacy_imports'] = list(legacy_imports)
        
        return cleanup_targets
    
    def clean_unused_config_keys(self, config_keys: List[str]) -> int:
        """清理未使用的配置键"""
        print(f"🧹 清理 {len(config_keys)} 个未使用的配置键...")
        
        cleaned_count = 0
        
        for config_key in config_keys:
            if self._is_safe_to_remove_key(config_key):
                if not self.dry_run:
                    # 实际清理逻辑
                    self._remove_config_key_references(config_key)
                    self.usage_monitor.mark_cleaned([config_key])
                    cleaned_count += 1
                else:
                    print(f"🔍 [DRY RUN] 将清理配置键: {config_key}")
                    cleaned_count += 1
            else:
                print(f"⚠️  跳过配置键 {config_key} (不安全)")
        
        self.cleanup_stats['references_updated'] += cleaned_count
        return cleaned_count
    
    def clean_deprecated_files(self, file_paths: List[str]) -> int:
        """清理废弃文件"""
        print(f"🗑️  清理 {len(file_paths)} 个废弃文件...")
        
        cleaned_count = 0
        
        for file_path in file_paths:
            if self._is_safe_to_remove_file(file_path):
                file_obj = Path(file_path)
                if file_obj.exists():
                    if not self.dry_run:
                        if file_obj.is_file():
                            # 统计行数
                            try:
                                with open(file_obj, 'r', encoding='utf-8') as f:
                                    lines = len(f.readlines())
                                self.cleanup_stats['lines_removed'] += lines
                            except:
                                pass
                            
                            file_obj.unlink()
                        else:
                            shutil.rmtree(file_obj)
                        
                        cleaned_count += 1
                        print(f"✅ 已删除: {file_path}")
                    else:
                        print(f"🔍 [DRY RUN] 将删除文件: {file_path}")
                        cleaned_count += 1
            else:
                print(f"⚠️  跳过文件 {file_path} (受保护)")
        
        self.cleanup_stats['files_removed'] += cleaned_count
        return cleaned_count
    
    def clean_legacy_imports(self, import_locations: List[str]) -> int:
        """清理旧导入"""
        print(f"📦 清理 {len(import_locations)} 个旧导入...")
        
        cleaned_count = 0
        
        # 按文件分组
        imports_by_file = {}
        for location in import_locations:
            file_path, line_num = location.split(':')
            if file_path not in imports_by_file:
                imports_by_file[file_path] = []
            imports_by_file[file_path].append(int(line_num))
        
        for file_path, line_numbers in imports_by_file.items():
            if self._is_safe_to_modify_file(file_path):
                if not self.dry_run:
                    cleaned_lines = self._remove_import_lines(file_path, line_numbers)
                    cleaned_count += cleaned_lines
                else:
                    print(f"🔍 [DRY RUN] 将清理文件 {file_path} 中的 {len(line_numbers)} 个导入")
                    cleaned_count += len(line_numbers)
        
        self.cleanup_stats['imports_cleaned'] += cleaned_count
        return cleaned_count
    
    def _is_safe_to_remove_key(self, config_key: str) -> bool:
        """检查配置键是否安全删除"""
        # 检查是否是关键配置键
        critical_keys = [
            'app.debug', 'app.name', 'llm.default_model',
            'database.connection', 'logging.level'
        ]
        
        for critical_key in critical_keys:
            if config_key.startswith(critical_key):
                return False
        
        return True
    
    def _is_safe_to_remove_file(self, file_path: str) -> bool:
        """检查文件是否安全删除"""
        file_path = Path(file_path)
        
        # 检查受保护路径
        for protected in self.protected_paths:
            if str(file_path).startswith(protected):
                return False
        
        # 检查文件扩展名
        if file_path.suffix in ['.md', '.txt', '.json', '.yaml', '.yml']:
            return False  # 配置和文档文件需要手动确认
        
        return True
    
    def _is_safe_to_modify_file(self, file_path: str) -> bool:
        """检查文件是否安全修改"""
        return self._is_safe_to_remove_file(file_path)
    
    def _remove_config_key_references(self, config_key: str):
        """移除配置键引用"""
        # 这里应该实现具体的配置键引用清理逻辑
        # 例如：从配置文件中删除键，更新文档等
        pass
    
    def _remove_import_lines(self, file_path: str, line_numbers: List[int]) -> int:
        """移除导入行"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 从后往前删除，避免行号变化
            line_numbers.sort(reverse=True)
            removed_count = 0
            
            for line_num in line_numbers:
                if 0 < line_num <= len(lines):
                    # 检查是否确实是导入行
                    line = lines[line_num - 1].strip()
                    if line.startswith(('import ', 'from ')) and 'legacy' in line.lower():
                        lines.pop(line_num - 1)
                        removed_count += 1
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)
            
            return removed_count
            
        except Exception as e:
            print(f"❌ 清理导入失败 {file_path}: {e}")
            return 0
    
    def run_cleanup(self, targets: Dict[str, List[str]] = None) -> Dict[str, Any]:
        """运行清理"""
        print("🚀 开始旧托底自动化清理...")
        
        if targets is None:
            targets = self.identify_cleanup_targets()
        
        # 创建备份
        backup_path = self.create_backup()
        
        # 执行清理
        results = {}
        
        if targets['unused_config_keys']:
            results['config_keys'] = self.clean_unused_config_keys(targets['unused_config_keys'])
        
        if targets['deprecated_files']:
            results['files'] = self.clean_deprecated_files(targets['deprecated_files'])
        
        if targets['legacy_imports']:
            results['imports'] = self.clean_legacy_imports(targets['legacy_imports'])
        
        # 生成清理报告
        cleanup_report = {
            'timestamp': datetime.now().isoformat(),
            'dry_run': self.dry_run,
            'backup_path': backup_path,
            'targets': targets,
            'results': results,
            'stats': self.cleanup_stats
        }
        
        return cleanup_report
    
    def generate_cleanup_plan(self) -> Dict[str, Any]:
        """生成清理计划"""
        targets = self.identify_cleanup_targets()
        
        plan = {
            'generated_at': datetime.now().isoformat(),
            'cleanup_targets': targets,
            'estimated_impact': {
                'config_keys_to_remove': len(targets['unused_config_keys']),
                'files_to_remove': len(targets['deprecated_files']),
                'imports_to_clean': len(targets['legacy_imports'])
            },
            'safety_checks': {
                'backup_required': True,
                'manual_review_required': True,
                'rollback_plan_available': True
            },
            'execution_steps': [
                "1. 创建完整备份",
                "2. 清理未使用的配置键",
                "3. 移除废弃文件",
                "4. 清理旧导入语句",
                "5. 运行测试验证",
                "6. 更新文档"
            ]
        }
        
        return plan


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="旧托底自动化清理工具")
    parser.add_argument("--dry-run", action="store_true", default=True, help="试运行模式")
    parser.add_argument("--execute", action="store_true", help="执行实际清理")
    parser.add_argument("--plan", action="store_true", help="生成清理计划")
    parser.add_argument("--output", help="输出文件路径")
    parser.add_argument("--batch-size", type=int, default=10, help="批量处理大小")
    
    args = parser.parse_args()
    
    try:
        # 确定运行模式
        dry_run = not args.execute
        if args.execute:
            print("⚠️  执行模式：将进行实际清理操作")
            confirm = input("确认继续？(yes/no): ")
            if confirm.lower() != 'yes':
                print("❌ 操作已取消")
                return 1
        
        automation = LegacyCleanupAutomation(dry_run=dry_run)
        automation.cleanup_config['batch_size'] = args.batch_size
        
        if args.plan:
            # 生成清理计划
            plan = automation.generate_cleanup_plan()
            
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    json.dump(plan, f, indent=2, ensure_ascii=False)
                print(f"📋 清理计划已保存到: {args.output}")
            else:
                print("📋 清理计划:")
                print(json.dumps(plan, indent=2, ensure_ascii=False))
        
        else:
            # 执行清理
            report = automation.run_cleanup()
            
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    json.dump(report, f, indent=2, ensure_ascii=False)
                print(f"📄 清理报告已保存到: {args.output}")
            
            # 显示摘要
            print("\n📊 清理摘要:")
            print(f"  配置键清理: {report['stats']['references_updated']}")
            print(f"  文件删除: {report['stats']['files_removed']}")
            print(f"  导入清理: {report['stats']['imports_cleaned']}")
            print(f"  代码行删除: {report['stats']['lines_removed']}")
            
            if report['stats']['backup_created']:
                print(f"  备份路径: {report['backup_path']}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
