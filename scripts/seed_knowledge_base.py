#!/usr/bin/env python3
"""
种子入库脚本：快速向知识库导入与“由己平台”相关的文本，便于检索验证
"""

import sys
import os
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

SEED_DOCS = [
    "由己平台是一套智能需求采集与知识管理系统，帮助团队系统化收集、整理、澄清和追踪业务需求。",
    "由己平台支持会话式问答、关注点收集、信息提取、文档生成等能力，适合产品需求澄清、项目启动文档输出。",
    "平台采用高效的两层识别架构：关键词加速实现快速匹配，LLM意图识别处理复杂语义理解，确保准确理解用户意图。",
    "平台具备RAG知识库功能，通过向量数据库ChromaDB进行语义检索，可基于企业知识进行回答与建议。",
    "平台日志体系包括app.log、error.log、session.log，并提供性能监控日志，便于问题定位与优化。",
]


def main():
    print("🚀 开始导入种子文档到知识库...")

    try:
        from backend.agents.factory import agent_factory
        rag = agent_factory.get_rag_knowledge_base_agent()
        if not rag:
            print("❌ 无法获取RAG代理")
            return 1

        if not hasattr(rag, "_collection") or rag._collection is None:
            print("❌ RAG集合未初始化")
            return 1

        ids = [f"seed_{i}" for i in range(len(SEED_DOCS))]

        # 先尝试删除旧的种子文档（如果存在）
        try:
            rag._collection.delete(ids=ids)
            print("🔄 已删除旧的种子文档")
        except Exception as e:
            print(f"ℹ️ 删除旧文档时出现异常（可能是首次导入）: {e}")

        # 添加新的种子文档
        rag._collection.add(documents=SEED_DOCS, ids=ids)
        print(f"✅ 已导入 {len(SEED_DOCS)} 条种子文档")

        # 简单检索验证
        query = "你好，请介绍一下由己平台"
        res = rag._collection.query(query_texts=[query], n_results=3)
        docs = res.get("documents", [[]])[0]
        if docs:
            print("✅ 检索验证成功，返回文档：")
            for i, d in enumerate(docs, 1):
                print(f"  {i}. {d[:60]}...")
        else:
            print("⚠️ 检索未命中，但数据已导入，可在产品侧再试一次")

        return 0
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    raise SystemExit(main())
