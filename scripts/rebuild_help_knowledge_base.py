#!/usr/bin/env python3
"""
重建帮助文档知识库脚本
从docs/由己帮助目录加载所有帮助文档到ChromaDB知识库
"""

import os
import sys
import shutil
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

def backup_existing_db(chroma_path):
    """备份现有数据库"""
    if chroma_path.exists():
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = chroma_path.parent / f"chroma_db_backup_{timestamp}"
        print(f"备份现有数据库到: {backup_path}")
        shutil.move(str(chroma_path), str(backup_path))
        return backup_path
    return None

def load_markdown_files(docs_dir):
    """加载所有markdown文件"""
    docs_dir = Path(docs_dir)
    markdown_files = []
    
    if not docs_dir.exists():
        print(f"错误: 文档目录不存在: {docs_dir}")
        return []
    
    # 递归查找所有.md文件
    for md_file in docs_dir.rglob("*.md"):
        try:
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 相对于docs/由己帮助的路径
            relative_path = md_file.relative_to(docs_dir)
            
            # 提取标题（第一行如果是#开头）
            lines = content.split('\n')
            title = None
            for line in lines:
                line = line.strip()
                if line.startswith('# '):
                    title = line[2:].strip()
                    break
            
            # 如果没有找到标题，使用文件名
            if not title:
                title = md_file.stem.replace('-', ' ').replace('_', ' ').title()
            
            # 确定分类
            category = "general"
            if "company" in str(relative_path):
                category = "company"
            elif "developer" in str(relative_path):
                category = "developer"
            
            markdown_files.append({
                'title': title,
                'content': content,
                'source': str(relative_path),
                'category': category,
                'file_path': str(md_file)
            })
            
            print(f"加载文档: {title} ({relative_path})")
            
        except Exception as e:
            print(f"警告: 无法读取文件 {md_file}: {e}")
    
    return markdown_files

def rebuild_knowledge_base():
    """重建知识库"""
    print("开始重建帮助文档知识库...")
    
    # 设置路径
    chroma_path = project_root / "backend" / "data" / "chroma_db"
    docs_path = project_root / "docs" / "由己帮助"
    
    print(f"ChromaDB路径: {chroma_path}")
    print(f"文档路径: {docs_path}")
    
    # 备份现有数据库
    backup_path = backup_existing_db(chroma_path)
    if backup_path:
        print(f"已备份现有数据库到: {backup_path}")
    
    # 加载所有markdown文件
    print(f"\n从 {docs_path} 加载文档...")
    documents = load_markdown_files(docs_path)
    
    if not documents:
        print("错误: 没有找到任何文档")
        return False
    
    print(f"找到 {len(documents)} 个文档")
    
    # 按分类统计
    categories = {}
    for doc in documents:
        cat = doc['category']
        categories[cat] = categories.get(cat, 0) + 1
    
    print("文档分类统计:")
    for cat, count in categories.items():
        print(f"  {cat}: {count} 个文档")
    
    # 初始化RAG知识库代理
    print("\n初始化知识库...")
    try:
        from backend.agents.rag_knowledge_base_agent import RAGKnowledgeBaseAgent
        from backend.agents.llm_service import AutoGenLLMServiceAgent
        
        # 创建LLM服务
        llm_service = AutoGenLLMServiceAgent()
        
        # 创建RAG代理
        agent = RAGKnowledgeBaseAgent(llm_service)
        
        if not agent._collection:
            print("错误: 无法连接到知识库")
            return False
        
        print(f"知识库连接成功，集合名称: {agent._collection.name}")
        
        # 清空现有数据
        print("清空现有数据...")
        try:
            # 获取所有现有文档ID
            existing_data = agent._collection.get()
            if existing_data['ids']:
                agent._collection.delete(ids=existing_data['ids'])
                print(f"删除了 {len(existing_data['ids'])} 个现有文档")
        except Exception as e:
            print(f"清空数据时出错: {e}")
        
        # 添加新文档
        print("\n开始添加文档到知识库...")
        success_count = 0
        
        for i, doc in enumerate(documents):
            try:
                # 准备文档数据
                doc_id = f"help_{i:03d}"
                
                # 分割长文档
                content = doc['content']
                max_chunk_size = 1000  # 每个块的最大字符数
                
                if len(content) <= max_chunk_size:
                    # 短文档直接添加
                    agent._collection.add(
                        documents=[content],
                        metadatas=[{
                            'title': doc['title'],
                            'source': doc['source'],
                            'category': doc['category'],
                            'chunk_index': 0,
                            'total_chunks': 1
                        }],
                        ids=[doc_id]
                    )
                    success_count += 1
                    print(f"  ✓ {doc['title']}")
                else:
                    # 长文档分块
                    chunks = []
                    chunk_size = max_chunk_size
                    for j in range(0, len(content), chunk_size):
                        chunk = content[j:j + chunk_size]
                        chunks.append(chunk)
                    
                    # 添加每个块
                    for j, chunk in enumerate(chunks):
                        chunk_id = f"{doc_id}_chunk_{j}"
                        agent._collection.add(
                            documents=[chunk],
                            metadatas=[{
                                'title': doc['title'],
                                'source': doc['source'],
                                'category': doc['category'],
                                'chunk_index': j,
                                'total_chunks': len(chunks)
                            }],
                            ids=[chunk_id]
                        )
                    
                    success_count += 1
                    print(f"  ✓ {doc['title']} (分为 {len(chunks)} 块)")
                
            except Exception as e:
                print(f"  ✗ 添加文档失败 {doc['title']}: {e}")
        
        # 验证结果
        final_count = agent._collection.count()
        print(f"\n知识库重建完成!")
        print(f"成功添加: {success_count}/{len(documents)} 个文档")
        print(f"知识库总文档数: {final_count}")
        
        # 测试查询
        print("\n测试知识库查询...")
        try:
            test_results = agent._collection.query(
                query_texts=["如何注册账户"],
                n_results=3
            )
            if test_results['documents'] and test_results['documents'][0]:
                print("✓ 查询测试成功")
                for i, (doc, metadata) in enumerate(zip(test_results['documents'][0], test_results['metadatas'][0])):
                    print(f"  {i+1}. {metadata.get('title', '无标题')} ({metadata.get('category', '无分类')})")
            else:
                print("⚠ 查询测试返回空结果")
        except Exception as e:
            print(f"✗ 查询测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"错误: 初始化知识库失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = rebuild_knowledge_base()
    if success:
        print("\n🎉 帮助文档知识库重建成功!")
    else:
        print("\n❌ 帮助文档知识库重建失败!")
        sys.exit(1)
