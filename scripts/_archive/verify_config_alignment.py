#!/usr/bin/env python3
"""
配置对齐验证脚本

验证拆分后的模块化配置与原始unified_config.yaml的内容完全一致
"""

import sys
import os
import yaml
import json
from pathlib import Path
from deepdiff import DeepDiff
from copy import deepcopy
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def load_unified_config():
    """加载原始统一配置"""
    config_file = Path("backend/config/unified_config.yaml")
    if not config_file.exists():
        print(f"❌ 原始配置文件不存在: {config_file}")
        return None
    
    with open(config_file, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)


def load_split_mapping():
    """加载拆分映射表"""
    mapping_file = Path("docs/config_split_mapping.yaml")
    if not mapping_file.exists():
        print(f"❌ 映射文件不存在: {mapping_file}")
        return None
    
    with open(mapping_file, 'r', encoding='utf-8') as f:
        mapping_data = yaml.safe_load(f)
    
    return mapping_data.get("split_mapping", {})


def load_modular_configs(split_mapping):
    """加载所有模块化配置文件"""
    modular_configs = {}
    
    # 按目标文件分组
    file_groups = {}
    for source_key, target_file in split_mapping.items():
        if target_file not in file_groups:
            file_groups[target_file] = []
        file_groups[target_file].append(source_key)
    
    # 加载每个文件
    for target_file, source_keys in file_groups.items():
        target_path = Path("backend/config") / target_file
        
        if not target_path.exists():
            print(f"❌ 模块配置文件不存在: {target_file}")
            continue
        
        try:
            with open(target_path, 'r', encoding='utf-8') as f:
                file_config = yaml.safe_load(f)
            
            # 提取源配置块
            for source_key in source_keys:
                if source_key in file_config:
                    modular_configs[source_key] = file_config[source_key]
                else:
                    print(f"⚠️  配置块 {source_key} 未在 {target_file} 中找到")
                    
        except Exception as e:
            print(f"❌ 加载模块配置文件失败 {target_file}: {e}")
    
    return modular_configs


def compare_configs(original_config, modular_configs):
    """比较原始配置和模块化配置"""
    print("🔍 开始配置对齐验证...")
    
    differences = {}
    missing_keys = []
    extra_keys = []
    
    # 检查原始配置中的每个键
    for key, original_value in original_config.items():
        if key in modular_configs:
            modular_value = modular_configs[key]
            
            # 深度比较
            diff = DeepDiff(original_value, modular_value, ignore_order=True)
            if diff:
                differences[key] = {
                    "diff": diff,
                    "original_type": type(original_value).__name__,
                    "modular_type": type(modular_value).__name__
                }
        else:
            missing_keys.append(key)
    
    # 检查模块化配置中的额外键
    for key in modular_configs:
        if key not in original_config:
            extra_keys.append(key)
    
    return differences, missing_keys, extra_keys


def print_comparison_results(differences, missing_keys, extra_keys):
    """打印比较结果"""
    print("\n📊 配置对齐验证结果:")
    print("=" * 80)
    
    if not differences and not missing_keys and not extra_keys:
        print("🎉 完美对齐！所有配置内容完全一致")
        return True
    
    # 打印差异
    if differences:
        print(f"\n❌ 发现 {len(differences)} 个配置块存在差异:")
        for key, diff_info in differences.items():
            print(f"\n📦 配置块: {key}")
            print(f"   原始类型: {diff_info['original_type']}")
            print(f"   模块类型: {diff_info['modular_type']}")
            
            diff = diff_info['diff']
            if 'values_changed' in diff:
                print("   🔄 值变更:")
                for path, change in diff['values_changed'].items():
                    print(f"     {path}: {change['old_value']} -> {change['new_value']}")
            
            if 'dictionary_item_added' in diff:
                print("   ➕ 新增项:")
                for item in diff['dictionary_item_added']:
                    print(f"     {item}")
            
            if 'dictionary_item_removed' in diff:
                print("   ➖ 删除项:")
                for item in diff['dictionary_item_removed']:
                    print(f"     {item}")
    
    # 打印缺失的键
    if missing_keys:
        print(f"\n❌ 缺失的配置块 ({len(missing_keys)} 个):")
        for key in missing_keys:
            print(f"   - {key}")
    
    # 打印额外的键
    if extra_keys:
        print(f"\n⚠️  额外的配置块 ({len(extra_keys)} 个):")
        for key in extra_keys:
            print(f"   + {key}")
    
    return False


def generate_alignment_report(differences, missing_keys, extra_keys, original_config, modular_configs):
    """生成对齐报告"""
    report = {
        "verification_time": str(datetime.now()),
        "summary": {
            "total_original_blocks": len(original_config),
            "total_modular_blocks": len(modular_configs),
            "differences_count": len(differences),
            "missing_keys_count": len(missing_keys),
            "extra_keys_count": len(extra_keys),
            "is_aligned": len(differences) == 0 and len(missing_keys) == 0 and len(extra_keys) == 0
        },
        "differences": {},
        "missing_keys": missing_keys,
        "extra_keys": extra_keys
    }
    
    # 转换差异信息为可序列化格式
    for key, diff_info in differences.items():
        report["differences"][key] = {
            "original_type": diff_info["original_type"],
            "modular_type": diff_info["modular_type"],
            "diff_summary": str(diff_info["diff"])
        }
    
    # 保存报告
    report_file = Path("docs/config_alignment_report.json")
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 对齐报告已保存到: {report_file}")
    return report


def test_modular_config_loading():
    """测试模块化配置加载"""
    print("\n🧪 测试模块化配置加载...")
    
    try:
        from backend.config.modular_loader import get_modular_config_loader
        
        loader = get_modular_config_loader()
        loader.enable()
        
        # 测试几个关键配置的加载
        test_configs = [
            "business.rules",
            "business.templates", 
            "llm.models",
            "system.base"
        ]
        
        all_loaded = True
        for config_key in test_configs:
            config_data = loader.get_config(config_key, {})
            if config_data:
                print(f"✅ {config_key}: {len(config_data)} 项")
            else:
                print(f"❌ {config_key}: 加载失败")
                all_loaded = False
        
        # 测试配置快照
        snapshot = loader.create_snapshot()
        print(f"✅ 配置快照: {len(snapshot.config_data)} 项配置")
        
        loader.disable()
        return all_loaded
        
    except Exception as e:
        print(f"❌ 模块化配置加载测试失败: {e}")
        return False


def main():
    """主函数"""
    try:
        print("🚀 开始配置对齐验证")
        
        # 加载配置
        original_config = load_unified_config()
        if not original_config:
            return 1
        
        split_mapping = load_split_mapping()
        if not split_mapping:
            return 1
        
        modular_configs = load_modular_configs(split_mapping)
        if not modular_configs:
            print("❌ 无法加载模块化配置")
            return 1
        
        print(f"📊 原始配置: {len(original_config)} 个配置块")
        print(f"📊 模块配置: {len(modular_configs)} 个配置块")
        
        # 比较配置
        differences, missing_keys, extra_keys = compare_configs(original_config, modular_configs)
        
        # 打印结果
        is_aligned = print_comparison_results(differences, missing_keys, extra_keys)
        
        # 生成报告
        report = generate_alignment_report(differences, missing_keys, extra_keys, 
                                         original_config, modular_configs)
        
        # 测试模块化配置加载
        loading_success = test_modular_config_loading()
        
        if is_aligned and loading_success:
            print("\n🎉 配置对齐验证完全通过！")
            return 0
        else:
            print("\n❌ 配置对齐验证存在问题")
            return 1
            
    except Exception as e:
        print(f"❌ 验证过程失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
