#!/usr/bin/env python3
"""
模块化配置测试脚本

测试模块化配置加载器的基本功能：
1. 配置源加载
2. 配置聚合
3. 缓存机制
4. 兼容性包装器
5. 特性开关
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.config.modular_loader import get_modular_config_loader
from backend.config.compatibility_layer import get_compatibility_wrapper
from backend.config.unified_config_loader import get_unified_config


def test_modular_loader_disabled():
    """测试模块化加载器禁用状态"""
    print("=== 测试模块化加载器（禁用状态）===")
    
    loader = get_modular_config_loader()
    print(f"模块化加载器启用状态: {loader.is_enabled()}")
    
    # 测试获取配置（应该返回默认值）
    config = loader.get_config("business.rules", {})
    print(f"获取business.rules配置: {type(config)} (应该是空字典)")
    
    # 测试监控统计
    stats = loader.get_monitoring_stats()
    print(f"监控统计: {stats}")
    
    print("✅ 禁用状态测试通过\n")


def test_modular_loader_enabled():
    """测试模块化加载器启用状态"""
    print("=== 测试模块化加载器（启用状态）===")
    
    loader = get_modular_config_loader()
    
    # 启用模块化加载器
    loader.enable()
    print(f"模块化加载器启用状态: {loader.is_enabled()}")
    
    # 测试获取配置
    business_rules = loader.get_config("business.rules", {})
    print(f"获取business.rules配置: {len(business_rules)} 项配置")
    
    # 测试嵌套配置获取
    retry_config = loader.get_config("business.rules.retry", {})
    print(f"获取business.rules.retry配置: {retry_config}")
    
    # 测试LLM配置
    llm_models = loader.get_config("llm.models", {})
    print(f"获取llm.models配置: {len(llm_models)} 项配置")
    
    # 测试缓存
    business_rules_cached = loader.get_config("business.rules", {})
    print(f"缓存测试: {business_rules == business_rules_cached}")
    
    # 测试监控统计
    stats = loader.get_monitoring_stats()
    print(f"监控统计: {stats}")
    
    # 测试配置快照
    snapshot = loader.create_snapshot()
    print(f"配置快照: 版本={snapshot.version}, 配置项数={len(snapshot.config_data)}")
    
    print("✅ 启用状态测试通过\n")


def test_compatibility_wrapper():
    """测试兼容性包装器"""
    print("=== 测试兼容性包装器 ===")
    
    wrapper = get_compatibility_wrapper()
    
    # 测试配置状态
    status = wrapper.get_config_status()
    print(f"配置状态: {status}")
    
    # 测试传统配置获取（模块化禁用时）
    wrapper.modular_loader.disable()
    business_rules = wrapper.get_business_rules()
    print(f"传统模式获取业务规则: {len(business_rules)} 项")
    
    # 测试模块化配置获取（模块化启用时）
    wrapper.modular_loader.enable()
    business_rules_modular = wrapper.get_business_rules()
    print(f"模块化模式获取业务规则: {len(business_rules_modular)} 项")
    
    # 测试LLM配置获取
    llm_config = wrapper.get_llm_config("default")
    print(f"获取LLM配置: {llm_config}")
    
    # 测试消息模板获取
    template = wrapper.get_message_template("greeting.basic", "默认问候")
    print(f"获取消息模板: {template[:50]}...")
    
    # 测试阈值获取
    threshold = wrapper.get_threshold("confidence.default", 0.7)
    print(f"获取置信度阈值: {threshold}")
    
    print("✅ 兼容性包装器测试通过\n")


def test_unified_config_integration():
    """测试统一配置集成"""
    print("=== 测试统一配置集成 ===")
    
    # 测试get_unified_config函数
    config = get_unified_config()
    print(f"get_unified_config返回类型: {type(config)}")
    
    # 测试配置获取
    try:
        if hasattr(config, 'get_business_rules'):
            business_rules = config.get_business_rules()
            print(f"通过统一接口获取业务规则: {len(business_rules)} 项")
        else:
            print("配置对象没有get_business_rules方法")
    except Exception as e:
        print(f"获取业务规则失败: {e}")
    
    print("✅ 统一配置集成测试通过\n")


def test_error_handling():
    """测试错误处理"""
    print("=== 测试错误处理 ===")
    
    loader = get_modular_config_loader()
    loader.enable()
    
    # 测试不存在的配置
    non_existent = loader.get_config("non.existent.config", "default_value")
    print(f"不存在的配置返回: {non_existent}")
    
    # 测试错误统计
    stats = loader.get_monitoring_stats()
    print(f"错误统计: error_count={stats.get('error_count', 0)}")
    
    print("✅ 错误处理测试通过\n")


def main():
    """主测试函数"""
    print("🚀 开始模块化配置测试\n")
    
    try:
        # 测试禁用状态
        test_modular_loader_disabled()
        
        # 测试启用状态
        test_modular_loader_enabled()
        
        # 测试兼容性包装器
        test_compatibility_wrapper()
        
        # 测试统一配置集成
        test_unified_config_integration()
        
        # 测试错误处理
        test_error_handling()
        
        print("🎉 所有测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
