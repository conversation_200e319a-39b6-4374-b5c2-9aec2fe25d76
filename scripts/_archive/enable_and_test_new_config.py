#!/usr/bin/env python3
"""
启用并测试新配置系统

全面测试新配置系统的各个功能模块，确保系统正常运行
"""

import sys
import os
import time
import json
from pathlib import Path
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def enable_new_config_system():
    """启用新配置系统"""
    print("🚀 启用新配置系统...")
    
    try:
        from backend.config.modular_loader import get_modular_config_loader
        
        loader = get_modular_config_loader()
        
        if loader.is_enabled():
            print("  ✅ 新配置系统已经启用")
        else:
            loader.enable()
            print("  ✅ 新配置系统启用成功")
        
        # 验证启用状态
        stats = loader.get_monitoring_stats()
        print(f"  📊 配置源数量: {stats['sources_count']}")
        print(f"  📊 缓存大小: {stats['cache_size']}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 启用失败: {e}")
        return False


def test_basic_config_access():
    """测试基础配置访问"""
    print("\n🧪 测试基础配置访问...")
    
    try:
        from backend.config.unified_config_loader import get_unified_config
        
        config = get_unified_config()
        
        # 测试各种配置访问
        test_cases = [
            ("业务规则", lambda: config.get_business_rules()),
            ("LLM配置", lambda: config.get_llm_config()),
            ("消息模板", lambda: config.get_message_templates()),
            ("配置值访问", lambda: config.get_config_value("thresholds.confidence.default", 0.7)),
            ("消息模板访问", lambda: config.get_message_template("greeting.basic", "默认问候")),
        ]
        
        results = {}
        for test_name, test_func in test_cases:
            try:
                start_time = time.time()
                result = test_func()
                end_time = time.time()
                
                results[test_name] = {
                    "success": True,
                    "result_type": type(result).__name__,
                    "result_size": len(str(result)),
                    "response_time": end_time - start_time
                }
                
                print(f"  ✅ {test_name}: {results[test_name]['result_type']} ({results[test_name]['response_time']:.4f}s)")
                
            except Exception as e:
                results[test_name] = {
                    "success": False,
                    "error": str(e)
                }
                print(f"  ❌ {test_name}: {e}")
        
        success_count = sum(1 for r in results.values() if r.get("success", False))
        print(f"  📊 成功率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
        
        return results
        
    except Exception as e:
        print(f"  ❌ 基础配置访问测试失败: {e}")
        return {}


def test_system_components():
    """测试系统各组件"""
    print("\n🔧 测试系统各组件...")
    
    component_tests = []
    
    # 测试决策引擎
    try:
        from backend.agents.unified_decision_engine import UnifiedDecisionEngine
        engine = UnifiedDecisionEngine()
        result = engine.process_user_input("你好", "test_user", "test_session")
        component_tests.append(("决策引擎", True, f"返回类型: {type(result).__name__}"))
        print("  ✅ 决策引擎: 正常工作")
    except Exception as e:
        component_tests.append(("决策引擎", False, str(e)))
        print(f"  ❌ 决策引擎: {e}")

    # 测试配置服务
    try:
        from backend.config.unified_config_loader import get_unified_config
        config = get_unified_config()
        llm_config = config.get_llm_config()
        business_rules = config.get_business_rules()
        component_tests.append(("配置服务", True, f"LLM配置: {len(llm_config)} 项"))
        print("  ✅ 配置服务: 正常工作")
    except Exception as e:
        component_tests.append(("配置服务", False, str(e)))
        print(f"  ❌ 配置服务: {e}")

    # 测试LLM客户端工厂
    try:
        from backend.agents.unified_llm_client_factory import UnifiedLLMClientFactory
        factory = UnifiedLLMClientFactory()
        client = factory.create_client("deepseek-chat")
        component_tests.append(("LLM客户端工厂", True, f"客户端类型: {type(client).__name__}"))
        print("  ✅ LLM客户端工厂: 正常工作")
    except Exception as e:
        component_tests.append(("LLM客户端工厂", False, str(e)))
        print(f"  ❌ LLM客户端工厂: {e}")

    # 测试关键词加速器
    try:
        from backend.agents.keyword_accelerator import KeywordAccelerator
        accelerator = KeywordAccelerator()
        result = accelerator.match("你好")
        component_tests.append(("关键词加速器", True, f"匹配结果: {result}"))
        print("  ✅ 关键词加速器: 正常工作")
    except Exception as e:
        component_tests.append(("关键词加速器", False, str(e)))
        print(f"  ❌ 关键词加速器: {e}")
    
    success_count = sum(1 for _, success, _ in component_tests if success)
    print(f"  📊 组件测试成功率: {success_count}/{len(component_tests)} ({success_count/len(component_tests)*100:.1f}%)")
    
    return component_tests


def test_performance_metrics():
    """测试性能指标"""
    print("\n⚡ 测试性能指标...")
    
    try:
        from backend.config.modular_loader import get_modular_config_loader
        from backend.config.unified_config_loader import get_unified_config
        
        loader = get_modular_config_loader()
        config = get_unified_config()
        
        # 性能测试
        operations = [
            ("获取业务规则", lambda: config.get_business_rules()),
            ("获取LLM配置", lambda: config.get_llm_config()),
            ("获取消息模板", lambda: config.get_message_templates()),
            ("获取配置值", lambda: config.get_config_value("thresholds.confidence.default")),
        ]
        
        performance_results = {}
        
        for op_name, operation in operations:
            # 预热
            for _ in range(5):
                operation()
            
            # 性能测试
            start_time = time.time()
            for _ in range(100):
                operation()
            end_time = time.time()
            
            avg_time = (end_time - start_time) / 100
            performance_results[op_name] = {
                "avg_time": avg_time,
                "ops_per_second": 1 / avg_time if avg_time > 0 else 0
            }
            
            print(f"  ⚡ {op_name}: {avg_time*1000:.2f}ms/op ({performance_results[op_name]['ops_per_second']:.0f} ops/s)")
        
        # 获取监控统计
        stats = loader.get_monitoring_stats()
        print(f"  📊 缓存命中率: {stats.get('cache_hit_rate', 0)*100:.1f}%")
        print(f"  📊 总访问次数: {stats.get('access_count', 0)}")
        print(f"  📊 错误率: {stats.get('error_rate', 0)*100:.1f}%")
        
        return performance_results, stats
        
    except Exception as e:
        print(f"  ❌ 性能测试失败: {e}")
        return {}, {}


def test_error_handling():
    """测试错误处理"""
    print("\n🛡️ 测试错误处理...")
    
    try:
        from backend.config.unified_config_loader import get_unified_config
        
        config = get_unified_config()
        
        error_tests = [
            ("不存在的配置", lambda: config.get_config_value("non.existent.config", "default")),
            ("空配置键", lambda: config.get_config_value("", "default")),
            ("None默认值", lambda: config.get_config_value("non.existent", None)),
            ("错误的模板路径", lambda: config.get_message_template("non.existent.template", "default")),
        ]
        
        error_results = {}
        for test_name, test_func in error_tests:
            try:
                result = test_func()
                error_results[test_name] = {
                    "success": True,
                    "result": result,
                    "handled_gracefully": True
                }
                print(f"  ✅ {test_name}: 优雅处理，返回 {result}")
            except Exception as e:
                error_results[test_name] = {
                    "success": False,
                    "error": str(e),
                    "handled_gracefully": False
                }
                print(f"  ❌ {test_name}: 异常 {e}")
        
        graceful_count = sum(1 for r in error_results.values() if r.get("handled_gracefully", False))
        print(f"  📊 优雅处理率: {graceful_count}/{len(error_tests)} ({graceful_count/len(error_tests)*100:.1f}%)")
        
        return error_results
        
    except Exception as e:
        print(f"  ❌ 错误处理测试失败: {e}")
        return {}


def test_real_world_scenario():
    """测试真实世界场景"""
    print("\n🌍 测试真实世界场景...")
    
    try:
        from backend.agents.unified_decision_engine import UnifiedDecisionEngine

        engine = UnifiedDecisionEngine()
        
        # 模拟真实用户交互场景
        scenarios = [
            ("用户问候", "你好", "greeting"),
            ("系统能力查询", "你能做什么", "system_capability_query"),
            ("需求收集", "我想做一个网站", "requirement_collection"),
            ("确认信息", "确认", "confirmation"),
        ]
        
        scenario_results = {}
        
        for scenario_name, user_input, expected_intent in scenarios:
            try:
                start_time = time.time()
                result = engine.process_user_input(user_input, "test_user", f"test_session_{int(time.time())}")
                end_time = time.time()
                
                scenario_results[scenario_name] = {
                    "success": True,
                    "response_time": end_time - start_time,
                    "result_type": type(result).__name__,
                    "has_response": bool(result and hasattr(result, 'response'))
                }
                
                print(f"  ✅ {scenario_name}: {scenario_results[scenario_name]['response_time']:.3f}s")
                
            except Exception as e:
                scenario_results[scenario_name] = {
                    "success": False,
                    "error": str(e)
                }
                print(f"  ❌ {scenario_name}: {e}")
        
        success_count = sum(1 for r in scenario_results.values() if r.get("success", False))
        print(f"  📊 场景测试成功率: {success_count}/{len(scenarios)} ({success_count/len(scenarios)*100:.1f}%)")
        
        return scenario_results
        
    except Exception as e:
        print(f"  ❌ 真实场景测试失败: {e}")
        return {}


def generate_test_report(basic_results, component_results, performance_results, performance_stats, 
                        error_results, scenario_results):
    """生成测试报告"""
    print("\n📄 生成测试报告...")
    
    report = {
        "test_time": datetime.now().isoformat(),
        "config_system": "modular_enabled",
        "summary": {
            "basic_config_success_rate": sum(1 for r in basic_results.values() if r.get("success", False)) / max(len(basic_results), 1),
            "component_success_rate": sum(1 for _, success, _ in component_results if success) / max(len(component_results), 1),
            "error_handling_rate": sum(1 for r in error_results.values() if r.get("handled_gracefully", False)) / max(len(error_results), 1),
            "scenario_success_rate": sum(1 for r in scenario_results.values() if r.get("success", False)) / max(len(scenario_results), 1),
            "overall_health": "healthy"
        },
        "detailed_results": {
            "basic_config_access": basic_results,
            "component_tests": [{"name": name, "success": success, "details": details} for name, success, details in component_results],
            "performance_metrics": performance_results,
            "performance_stats": performance_stats,
            "error_handling": error_results,
            "real_world_scenarios": scenario_results
        },
        "recommendations": []
    }
    
    # 添加建议
    if report["summary"]["basic_config_success_rate"] < 1.0:
        report["recommendations"].append("检查基础配置访问问题")
    
    if report["summary"]["component_success_rate"] < 0.8:
        report["recommendations"].append("部分系统组件存在问题，需要检查")
    
    if report["summary"]["error_handling_rate"] < 0.9:
        report["recommendations"].append("改进错误处理机制")
    
    if not report["recommendations"]:
        report["recommendations"].append("系统运行正常，可以投入使用")
    
    # 保存报告
    report_file = Path("docs/new_config_system_test_report.json")
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"  ✅ 测试报告已保存到: {report_file}")
    return report


def main():
    """主函数"""
    try:
        print("🚀 启用并测试新配置系统")
        print("=" * 60)
        
        # 启用新配置系统
        if not enable_new_config_system():
            print("❌ 无法启用新配置系统，测试终止")
            return 1
        
        # 执行各项测试
        basic_results = test_basic_config_access()
        component_results = test_system_components()
        performance_results, performance_stats = test_performance_metrics()
        error_results = test_error_handling()
        scenario_results = test_real_world_scenario()
        
        # 生成测试报告
        report = generate_test_report(
            basic_results, component_results, performance_results, 
            performance_stats, error_results, scenario_results
        )
        
        # 输出总结
        print(f"\n📊 测试总结:")
        print("=" * 60)
        print(f"基础配置访问: {report['summary']['basic_config_success_rate']*100:.1f}% 成功")
        print(f"系统组件测试: {report['summary']['component_success_rate']*100:.1f}% 成功")
        print(f"错误处理测试: {report['summary']['error_handling_rate']*100:.1f}% 优雅处理")
        print(f"真实场景测试: {report['summary']['scenario_success_rate']*100:.1f}% 成功")
        
        print(f"\n💡 建议:")
        for rec in report["recommendations"]:
            print(f"  - {rec}")
        
        # 判断整体健康状况
        overall_score = (
            report['summary']['basic_config_success_rate'] * 0.3 +
            report['summary']['component_success_rate'] * 0.3 +
            report['summary']['error_handling_rate'] * 0.2 +
            report['summary']['scenario_success_rate'] * 0.2
        )
        
        if overall_score >= 0.9:
            print(f"\n🎉 新配置系统运行状态: 优秀 ({overall_score*100:.1f}%)")
            print("✅ 系统可以正常投入使用！")
            return 0
        elif overall_score >= 0.7:
            print(f"\n⚠️  新配置系统运行状态: 良好 ({overall_score*100:.1f}%)")
            print("⚠️  建议修复发现的问题后再投入使用")
            return 0
        else:
            print(f"\n❌ 新配置系统运行状态: 需要改进 ({overall_score*100:.1f}%)")
            print("❌ 建议修复问题后重新测试")
            return 1
            
    except Exception as e:
        print(f"❌ 测试过程失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
