#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
冗余代码清理脚本

清理重构后遗留的冗余代码，包括：
1. 未使用的导入
2. 重复的配置加载逻辑
3. 废弃的常量定义
4. 无用的注释和代码
"""

import os
import sys
import ast
import re
import logging
from pathlib import Path
from typing import List, Dict, Set, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RedundantCodeCleaner:
    """冗余代码清理器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.issues = []
        
        # 需要检查的文件模式
        self.target_patterns = [
            "backend/agents/**/*.py",
            "backend/config/**/*.py",
            "backend/handlers/**/*.py",
            "backend/utils/**/*.py",
            "backend/services/**/*.py"
        ]
        
        # 排除的文件模式
        self.exclude_patterns = [
            "**/test_*.py",
            "**/*_test.py",
            "**/__pycache__/**",
            "**/.*",
            "**/venv/**",
            "**/node_modules/**"
        ]
        
        # 重构后可能冗余的导入
        self.potentially_unused_imports = {
            'random': ['random.choice', 'random.randint'],
            'time': ['time.sleep'],
            'json': ['json.loads', 'json.dumps'],
            'yaml': ['yaml.load', 'yaml.safe_load'],
            'logging': ['logging.getLogger'],
            'typing': ['List', 'Dict', 'Optional', 'Any']
        }
        
        # 重构后可能冗余的常量模式
        self.redundant_constant_patterns = [
            r'DEFAULT_TEMPERATURE\s*=\s*0\.[0-9]+',
            r'DEFAULT_CONFIDENCE\s*=\s*0\.[0-9]+',
            r'MAX_RETRIES\s*=\s*[0-9]+',
            r'TIMEOUT\s*=\s*[0-9]+',
            r'GREETING_TEMPLATES\s*=\s*\[',
            r'ERROR_MESSAGES\s*=\s*\{',
        ]
    
    def get_target_files(self) -> List[Path]:
        """获取需要检查的文件列表"""
        files = []
        
        for pattern in self.target_patterns:
            for file_path in self.project_root.glob(pattern):
                if file_path.is_file() and file_path.suffix == '.py':
                    # 检查是否应该排除
                    should_exclude = False
                    for exclude_pattern in self.exclude_patterns:
                        if file_path.match(exclude_pattern):
                            should_exclude = True
                            break
                    
                    if not should_exclude:
                        files.append(file_path)
        
        return files
    
    def check_unused_imports(self, file_path: Path) -> List[Dict]:
        """检查未使用的导入"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析AST
            tree = ast.parse(content)
            
            # 收集导入
            imports = []
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append({
                            'name': alias.name,
                            'asname': alias.asname,
                            'line': node.lineno,
                            'type': 'import'
                        })
                elif isinstance(node, ast.ImportFrom):
                    for alias in node.names:
                        imports.append({
                            'name': alias.name,
                            'asname': alias.asname,
                            'module': node.module,
                            'line': node.lineno,
                            'type': 'from_import'
                        })
            
            # 检查每个导入是否被使用
            for imp in imports:
                name_to_check = imp.get('asname') or imp['name']
                if name_to_check and not self._is_import_used(content, name_to_check, imp):
                    issues.append({
                        'type': 'unused_import',
                        'line': imp['line'],
                        'import': imp,
                        'suggestion': f"移除未使用的导入: {imp['name']}"
                    })
        
        except Exception as e:
            logger.warning(f"检查导入失败 {file_path}: {e}")
        
        return issues
    
    def _is_import_used(self, content: str, name: str, import_info: Dict) -> bool:
        """检查导入是否被使用"""
        lines = content.split('\n')
        
        # 跳过导入行本身
        import_line = import_info['line'] - 1
        
        for i, line in enumerate(lines):
            if i == import_line:
                continue
            
            # 检查是否使用了这个名称
            if re.search(rf'\b{re.escape(name)}\b', line):
                return True
        
        return False
    
    def check_redundant_constants(self, file_path: Path) -> List[Dict]:
        """检查冗余的常量定义"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            for i, line in enumerate(lines, 1):
                for pattern in self.redundant_constant_patterns:
                    if re.search(pattern, line):
                        issues.append({
                            'type': 'redundant_constant',
                            'line': i,
                            'content': line.strip(),
                            'suggestion': '考虑使用配置文件中的对应参数'
                        })
        
        except Exception as e:
            logger.warning(f"检查常量失败 {file_path}: {e}")
        
        return issues
    
    def check_duplicate_config_loading(self, file_path: Path) -> List[Dict]:
        """检查重复的配置加载逻辑"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找重复的配置加载调用
            config_calls = re.findall(r'get_unified_config\(\)', content)
            if len(config_calls) > 3:  # 如果超过3次调用，可能有优化空间
                issues.append({
                    'type': 'duplicate_config_loading',
                    'count': len(config_calls),
                    'suggestion': '考虑在类初始化时缓存配置对象'
                })
        
        except Exception as e:
            logger.warning(f"检查配置加载失败 {file_path}: {e}")
        
        return issues
    
    def analyze_file(self, file_path: Path) -> Dict:
        """分析单个文件"""
        logger.info(f"分析文件: {file_path.relative_to(self.project_root)}")
        
        issues = {
            'unused_imports': self.check_unused_imports(file_path),
            'redundant_constants': self.check_redundant_constants(file_path),
            'duplicate_config_loading': self.check_duplicate_config_loading(file_path)
        }
        
        return issues
    
    def run_analysis(self) -> Dict:
        """运行完整分析"""
        logger.info("开始冗余代码分析...")
        
        files = self.get_target_files()
        logger.info(f"找到 {len(files)} 个文件需要分析")
        
        results = {}
        total_issues = 0
        
        for file_path in files:
            file_issues = self.analyze_file(file_path)
            
            # 统计问题数量
            file_issue_count = sum(len(issues) for issues in file_issues.values())
            if file_issue_count > 0:
                results[str(file_path.relative_to(self.project_root))] = file_issues
                total_issues += file_issue_count
        
        logger.info(f"分析完成，发现 {total_issues} 个潜在问题")
        return results
    
    def generate_report(self, results: Dict) -> str:
        """生成清理报告"""
        report = ["# 冗余代码清理报告\n"]
        
        if not results:
            report.append("✅ 未发现需要清理的冗余代码\n")
            return "\n".join(report)
        
        # 统计信息
        total_files = len(results)
        total_issues = sum(
            sum(len(issues) for issues in file_issues.values())
            for file_issues in results.values()
        )
        
        report.append(f"## 📊 统计信息")
        report.append(f"- 检查文件数: {total_files}")
        report.append(f"- 发现问题数: {total_issues}")
        report.append("")
        
        # 详细问题
        report.append("## 🔍 详细问题")
        
        for file_path, file_issues in results.items():
            report.append(f"### {file_path}")
            
            for issue_type, issues in file_issues.items():
                if issues:
                    report.append(f"#### {issue_type}")
                    for issue in issues:
                        if issue_type == 'unused_imports':
                            report.append(f"- 行 {issue['line']}: {issue['suggestion']}")
                        elif issue_type == 'redundant_constants':
                            report.append(f"- 行 {issue['line']}: {issue['content']} - {issue['suggestion']}")
                        elif issue_type == 'duplicate_config_loading':
                            report.append(f"- 发现 {issue['count']} 次配置加载调用 - {issue['suggestion']}")
                    report.append("")
        
        return "\n".join(report)


def main():
    """主函数"""
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    cleaner = RedundantCodeCleaner(project_root)
    results = cleaner.run_analysis()
    
    # 生成报告
    report = cleaner.generate_report(results)
    
    # 保存报告
    report_path = os.path.join(project_root, "redundant_code_report.md")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"冗余代码分析完成，报告已保存到: {report_path}")
    
    # 输出摘要
    if results:
        total_issues = sum(
            sum(len(issues) for issues in file_issues.values())
            for file_issues in results.values()
        )
        print(f"发现 {total_issues} 个潜在的冗余代码问题")
    else:
        print("✅ 未发现需要清理的冗余代码")


if __name__ == "__main__":
    main()
