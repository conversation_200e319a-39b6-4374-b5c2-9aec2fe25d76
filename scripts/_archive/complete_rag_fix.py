#!/usr/bin/env python3
"""
完整的RAG修复脚本

彻底解决RAG系统的ChromaDB问题，包括清理所有相关数据和重新初始化
"""

import sys
import os
import shutil
import sqlite3
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def stop_all_chromadb_processes():
    """停止所有可能的ChromaDB进程"""
    print("🛑 停止所有ChromaDB相关进程...")
    
    try:
        import psutil
        
        # 查找并终止ChromaDB相关进程
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'chroma' in proc.info['name'].lower():
                    proc.terminate()
                    print(f"  ✅ 终止进程: {proc.info['name']} (PID: {proc.info['pid']})")
                elif proc.info['cmdline']:
                    cmdline = ' '.join(proc.info['cmdline']).lower()
                    if 'chroma' in cmdline:
                        proc.terminate()
                        print(f"  ✅ 终止进程: {proc.info['pid']}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
    except ImportError:
        print("  ℹ️  psutil未安装，跳过进程检查")
    except Exception as e:
        print(f"  ⚠️  进程检查失败: {e}")


def clean_all_chromadb_data():
    """清理所有ChromaDB相关数据"""
    print("🧹 清理所有ChromaDB数据...")
    
    # 可能的ChromaDB路径
    chroma_paths = [
        Path("backend/data/chroma_db"),
        Path("backend/data/chroma_db_dev"),
        Path("backend/data/chroma_db_test"),
        Path("chroma_db"),
        Path(".chroma"),
        Path("./chroma"),
    ]
    
    cleaned_count = 0
    for path in chroma_paths:
        if path.exists():
            try:
                if path.is_dir():
                    shutil.rmtree(path)
                else:
                    path.unlink()
                print(f"  ✅ 已清理: {path}")
                cleaned_count += 1
            except Exception as e:
                print(f"  ❌ 清理失败 {path}: {e}")
    
    print(f"  📊 共清理了 {cleaned_count} 个ChromaDB目录")
    return cleaned_count > 0


def fix_chromadb_database_schema():
    """修复ChromaDB数据库结构"""
    print("🔧 修复ChromaDB数据库结构...")
    
    try:
        # 创建新的ChromaDB目录
        chroma_path = Path("backend/data/chroma_db")
        chroma_path.mkdir(parents=True, exist_ok=True)
        
        # 检查是否有SQLite数据库文件
        db_files = list(chroma_path.glob("*.sqlite*"))
        
        for db_file in db_files:
            try:
                print(f"  🔍 检查数据库文件: {db_file}")
                
                # 连接到数据库
                conn = sqlite3.connect(str(db_file))
                cursor = conn.cursor()
                
                # 检查collections表结构
                cursor.execute("PRAGMA table_info(collections)")
                columns = cursor.fetchall()
                column_names = [col[1] for col in columns]
                
                print(f"    📊 现有列: {column_names}")
                
                # 如果缺少topic列，添加它
                if 'topic' not in column_names:
                    print("    ➕ 添加缺失的topic列")
                    cursor.execute("ALTER TABLE collections ADD COLUMN topic TEXT")
                    conn.commit()
                    print("    ✅ topic列添加成功")
                
                conn.close()
                print(f"    ✅ 数据库结构修复完成: {db_file}")
                
            except Exception as e:
                print(f"    ❌ 修复数据库失败 {db_file}: {e}")
                # 如果修复失败，删除损坏的数据库文件
                try:
                    db_file.unlink()
                    print(f"    🗑️  已删除损坏的数据库文件: {db_file}")
                except:
                    pass
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据库结构修复失败: {e}")
        return False


def create_chromadb_with_proper_settings():
    """使用正确的设置创建ChromaDB"""
    print("🚀 使用正确设置创建ChromaDB...")
    
    try:
        import chromadb
        from chromadb.utils import embedding_functions
        
        # 确保目录存在
        chroma_path = "backend/data/chroma_db"
        Path(chroma_path).mkdir(parents=True, exist_ok=True)
        
        # 创建客户端，明确禁用遥测
        settings = chromadb.Settings(
            anonymized_telemetry=False,
            allow_reset=True,
            is_persistent=True
        )
        
        client = chromadb.PersistentClient(
            path=chroma_path,
            settings=settings
        )
        
        print(f"  ✅ ChromaDB客户端创建成功")
        
        # 设置嵌入函数
        try:
            embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
                model_name="moka-ai/m3e-base"
            )
            print(f"  ✅ 嵌入函数设置成功")
        except Exception as e:
            print(f"  ⚠️  嵌入函数设置失败，使用默认: {e}")
            embedding_function = embedding_functions.DefaultEmbeddingFunction()
        
        # 创建集合
        collection_name = "hybrid_knowledge_base"
        
        # 先尝试删除现有集合（如果存在）
        try:
            client.delete_collection(name=collection_name)
            print(f"  🗑️  已删除现有集合: {collection_name}")
        except:
            pass
        
        # 创建新集合
        collection = client.create_collection(
            name=collection_name,
            embedding_function=embedding_function,
            metadata={"hnsw:space": "cosine"}
        )
        
        print(f"  ✅ 集合创建成功: {collection_name}")
        
        # 测试集合功能
        test_doc = ["ChromaDB初始化测试文档"]
        test_id = ["init_test"]
        
        collection.add(
            documents=test_doc,
            ids=test_id
        )
        
        # 查询测试
        results = collection.query(
            query_texts=["测试"],
            n_results=1
        )
        
        if results['documents']:
            print("  ✅ ChromaDB功能测试通过")
            
            # 清理测试数据
            collection.delete(ids=test_id)
            print("  ✅ 测试数据已清理")
            
            return True, client
        else:
            print("  ❌ ChromaDB功能测试失败")
            return False, None
            
    except Exception as e:
        print(f"  ❌ ChromaDB创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None


def update_rag_agent_config():
    """更新RAG代理配置"""
    print("⚙️  更新RAG代理配置...")
    
    try:
        # 检查RAG代理文件
        rag_agent_file = Path("backend/agents/rag_knowledge_base_agent.py")
        
        if not rag_agent_file.exists():
            print("  ❌ RAG代理文件不存在")
            return False
        
        # 读取文件内容
        with open(rag_agent_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否需要更新ChromaDB设置
        if "anonymized_telemetry=False" not in content:
            print("  🔄 更新ChromaDB设置...")
            
            # 替换ChromaDB客户端创建代码
            old_pattern = "chromadb.PersistentClient(path=chroma_path)"
            new_pattern = """chromadb.PersistentClient(
                path=chroma_path,
                settings=chromadb.Settings(
                    anonymized_telemetry=False,
                    allow_reset=True,
                    is_persistent=True
                )
            )"""
            
            if old_pattern in content:
                content = content.replace(old_pattern, new_pattern)
                
                # 写回文件
                with open(rag_agent_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("  ✅ RAG代理配置已更新")
            else:
                print("  ℹ️  RAG代理配置无需更新")
        else:
            print("  ✅ RAG代理配置已是最新")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 更新RAG代理配置失败: {e}")
        return False


def test_complete_rag_system():
    """测试完整的RAG系统"""
    print("🧪 测试完整RAG系统...")
    
    try:
        # 重新导入模块以获取最新配置
        import importlib
        
        # 清理模块缓存
        modules_to_reload = [
            'backend.agents.rag_knowledge_base_agent',
            'backend.config.knowledge_base_config',
            'backend.agents.factory'
        ]
        
        for module_name in modules_to_reload:
            if module_name in sys.modules:
                importlib.reload(sys.modules[module_name])
        
        # 测试RAG代理创建
        from backend.agents.factory import agent_factory
        
        rag_agent = agent_factory.get_rag_knowledge_base_agent()
        
        if rag_agent:
            print("  ✅ RAG代理创建成功")
            
            # 测试简单查询
            try:
                # 这里只测试代理是否能正常初始化，不执行实际查询
                if hasattr(rag_agent, 'config'):
                    print("  ✅ RAG代理配置访问正常")
                    return True
                else:
                    print("  ⚠️  RAG代理配置访问异常")
                    return False
            except Exception as e:
                print(f"  ❌ RAG代理测试失败: {e}")
                return False
        else:
            print("  ❌ RAG代理创建失败")
            return False
            
    except Exception as e:
        print(f"  ❌ RAG系统测试失败: {e}")
        return False


def main():
    """主函数"""
    try:
        print("🚀 开始完整RAG修复")
        print("=" * 60)
        
        # 执行修复步骤
        steps = [
            ("停止ChromaDB进程", stop_all_chromadb_processes),
            ("清理ChromaDB数据", clean_all_chromadb_data),
            ("修复数据库结构", fix_chromadb_database_schema),
            ("创建ChromaDB", lambda: create_chromadb_with_proper_settings()[0]),
            ("更新RAG代理配置", update_rag_agent_config),
            ("测试RAG系统", test_complete_rag_system)
        ]
        
        success_count = 0
        for step_name, step_func in steps:
            print(f"\n📋 {step_name}")
            try:
                if step_func():
                    success_count += 1
                else:
                    print(f"❌ {step_name} 失败")
            except Exception as e:
                print(f"❌ {step_name} 异常: {e}")
        
        print(f"\n📊 修复结果: {success_count}/{len(steps)} 步骤成功")
        
        if success_count >= len(steps) - 1:  # 允许一个步骤失败
            print("\n🎉 RAG系统完整修复完成！")
            print("\n💡 重要提示:")
            print("1. 请重新启动应用程序以使配置生效")
            print("2. 如果仍有问题，请检查ChromaDB版本兼容性")
            print("3. 确保没有其他进程占用ChromaDB数据库")
            return 0
        else:
            print(f"\n❌ RAG系统修复失败")
            return 1
            
    except Exception as e:
        print(f"❌ 修复过程失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
