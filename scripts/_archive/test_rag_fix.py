#!/usr/bin/env python3
"""
测试RAG修复效果

模拟实际运行环境测试RAG系统
"""

import sys
import os
import time
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def simulate_real_usage():
    """模拟真实使用场景"""
    print("🎭 模拟真实使用场景...")
    
    try:
        # 模拟KnowledgeBaseHandler的使用方式
        from backend.agents.factory import agent_factory
        
        print("  📋 从AgentFactory获取RAG代理...")
        rag_agent = agent_factory.get_rag_knowledge_base_agent()
        
        if rag_agent:
            print("  ✅ 成功从AgentFactory获取RAG代理")
            
            # 模拟RAG查询
            test_query = "你好，请介绍一下由己平台"
            print(f"  🔍 开始RAG查询: '{test_query}'")
            
            # 检查ChromaDB是否正常初始化
            if hasattr(rag_agent, '_chroma_client') and rag_agent._chroma_client:
                print("  ✅ ChromaDB客户端初始化成功")
                
                if hasattr(rag_agent, '_collection') and rag_agent._collection:
                    print("  ✅ ChromaDB集合初始化成功")
                    print(f"  📊 集合名称: {rag_agent._collection.name}")
                    
                    try:
                        doc_count = rag_agent._collection.count()
                        print(f"  📊 集合文档数量: {doc_count}")
                        
                        # 尝试添加测试文档
                        test_doc = ["由己平台测试文档：这是一个智能需求采集系统。"]
                        test_id = ["test_doc_real_usage"]
                        
                        rag_agent._collection.add(
                            documents=test_doc,
                            ids=test_id
                        )
                        print("  ✅ 成功添加测试文档")
                        
                        # 尝试查询
                        results = rag_agent._collection.query(
                            query_texts=[test_query],
                            n_results=1
                        )
                        
                        if results['documents'] and results['documents'][0]:
                            print("  ✅ 查询成功")
                            doc = results['documents'][0][0]
                            distance = results['distances'][0][0] if results['distances'] else 0
                            print(f"  📄 找到文档: {doc[:50]}... (距离: {distance:.3f})")
                        else:
                            print("  ⚠️  查询未找到结果")
                        
                        # 清理测试文档
                        rag_agent._collection.delete(ids=test_id)
                        print("  ✅ 测试文档已清理")
                        
                        return True
                        
                    except Exception as query_error:
                        print(f"  ❌ 查询操作失败: {query_error}")
                        return False
                else:
                    print("  ❌ ChromaDB集合初始化失败")
                    return False
            else:
                print("  ❌ ChromaDB客户端初始化失败")
                return False
        else:
            print("  ❌ 无法从AgentFactory获取RAG代理")
            return False
            
    except Exception as e:
        print(f"  ❌ 模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_multiple_initializations():
    """测试多次初始化"""
    print("\n🔄 测试多次初始化...")
    
    success_count = 0
    total_attempts = 3
    
    for i in range(total_attempts):
        print(f"  📋 第 {i+1} 次初始化...")
        
        try:
            from backend.agents.factory import agent_factory
            
            # 每次都重新获取代理
            rag_agent = agent_factory.get_rag_knowledge_base_agent()
            
            if rag_agent and hasattr(rag_agent, '_collection') and rag_agent._collection:
                print(f"    ✅ 第 {i+1} 次初始化成功")
                success_count += 1
            else:
                print(f"    ❌ 第 {i+1} 次初始化失败")
                
        except Exception as e:
            print(f"    ❌ 第 {i+1} 次初始化异常: {e}")
        
        # 短暂等待
        time.sleep(1)
    
    print(f"  📊 多次初始化成功率: {success_count}/{total_attempts}")
    return success_count == total_attempts


def test_error_recovery():
    """测试错误恢复能力"""
    print("\n🛠️  测试错误恢复能力...")
    
    try:
        # 故意创建一个损坏的ChromaDB文件
        import sqlite3
        from pathlib import Path
        
        chroma_path = Path("backend/data/chroma_db")
        chroma_path.mkdir(parents=True, exist_ok=True)
        
        # 创建一个损坏的SQLite文件
        corrupt_db = chroma_path / "test_corrupt.sqlite"
        with open(corrupt_db, 'w') as f:
            f.write("这不是一个有效的SQLite文件")
        
        print("  📝 已创建损坏的数据库文件")
        
        # 现在尝试初始化RAG代理
        from backend.agents.factory import agent_factory
        
        rag_agent = agent_factory.get_rag_knowledge_base_agent()
        
        if rag_agent and hasattr(rag_agent, '_collection') and rag_agent._collection:
            print("  ✅ 错误恢复成功，RAG代理正常工作")
            return True
        else:
            print("  ❌ 错误恢复失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 错误恢复测试失败: {e}")
        return False


def main():
    """主函数"""
    try:
        print("🚀 测试RAG修复效果")
        print("=" * 50)
        
        # 测试项目
        tests = [
            ("模拟真实使用", simulate_real_usage),
            ("多次初始化测试", test_multiple_initializations),
            ("错误恢复测试", test_error_recovery)
        ]
        
        success_count = 0
        
        for test_name, test_func in tests:
            print(f"\n📋 {test_name}")
            if test_func():
                success_count += 1
                print(f"  🎉 {test_name} 通过")
            else:
                print(f"  ❌ {test_name} 失败")
        
        # 输出结果
        print(f"\n📊 测试结果总结:")
        print("=" * 50)
        print(f"成功测试: {success_count}/{len(tests)}")
        print(f"成功率: {success_count/len(tests)*100:.1f}%")
        
        if success_count == len(tests):
            print("\n🎉 RAG修复完全成功！")
            print("✅ 系统现在可以正常处理ChromaDB问题")
            print("✅ 自动修复机制工作正常")
            print("✅ 错误恢复能力良好")
            return 0
        elif success_count >= len(tests) * 0.7:
            print("\n⚠️  RAG修复基本成功")
            print("⚠️  部分功能可能需要进一步优化")
            return 0
        else:
            print("\n❌ RAG修复效果不佳")
            print("❌ 需要进一步检查和修复")
            return 1
            
    except Exception as e:
        print(f"❌ 测试过程失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
