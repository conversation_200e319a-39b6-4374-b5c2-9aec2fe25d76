#!/usr/bin/env python3
"""
Git钩子设置脚本
设置Git钩子以自动运行代码质量检查
"""

import os
import shutil
import stat
from pathlib import Path

def setup_git_hooks():
    """设置Git钩子"""
    project_root = Path(__file__).parent.parent
    git_hooks_dir = project_root / ".git" / "hooks"
    source_hooks_dir = project_root / ".githooks"
    
    # 检查是否是Git仓库
    if not (project_root / ".git").exists():
        print("❌ 当前目录不是Git仓库")
        return False
    
    # 创建hooks目录（如果不存在）
    git_hooks_dir.mkdir(exist_ok=True)
    
    # 复制钩子文件
    hooks_to_setup = ["pre-commit"]
    
    for hook_name in hooks_to_setup:
        source_hook = source_hooks_dir / hook_name
        target_hook = git_hooks_dir / hook_name
        
        if not source_hook.exists():
            print(f"⚠️ 源钩子文件不存在: {source_hook}")
            continue
        
        # 备份现有钩子
        if target_hook.exists():
            backup_hook = git_hooks_dir / f"{hook_name}.backup"
            shutil.copy2(target_hook, backup_hook)
            print(f"📦 已备份现有钩子: {backup_hook}")
        
        # 复制新钩子
        shutil.copy2(source_hook, target_hook)
        
        # 设置执行权限
        target_hook.chmod(target_hook.stat().st_mode | stat.S_IEXEC)
        
        print(f"✅ 已设置Git钩子: {hook_name}")
    
    return True

def main():
    """主函数"""
    print("🔧 设置Git钩子...")
    
    if setup_git_hooks():
        print("\n🎉 Git钩子设置完成！")
        print("\n📋 已设置的钩子:")
        print("- pre-commit: 提交前运行代码质量检查")
        print("\n💡 使用说明:")
        print("- 正常提交代码时会自动运行检查")
        print("- 如需跳过检查: git commit --no-verify")
        print("- 手动运行检查: python scripts/pre_commit_check.py")
    else:
        print("\n❌ Git钩子设置失败")

if __name__ == "__main__":
    main()
