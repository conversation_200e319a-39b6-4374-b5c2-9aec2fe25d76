#!/usr/bin/env python3
"""
RAG配置修复脚本

修复RAG知识库相关的配置问题，包括ChromaDB配置和数据库结构问题
"""

import sys
import os
import shutil
import yaml
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def backup_chromadb():
    """备份现有的ChromaDB数据"""
    print("🔄 备份现有ChromaDB数据...")
    
    chroma_path = Path("backend/data/chroma_db")
    backup_path = Path("backend/data/chroma_db_backup")
    
    if chroma_path.exists():
        if backup_path.exists():
            shutil.rmtree(backup_path)
        shutil.copytree(chroma_path, backup_path)
        print(f"  ✅ 已备份到: {backup_path}")
        return True
    else:
        print("  ℹ️  ChromaDB目录不存在，无需备份")
        return False


def clean_chromadb():
    """清理有问题的ChromaDB数据"""
    print("🧹 清理ChromaDB数据...")
    
    chroma_path = Path("backend/data/chroma_db")
    
    if chroma_path.exists():
        try:
            shutil.rmtree(chroma_path)
            print("  ✅ 已清理旧的ChromaDB数据")
            return True
        except Exception as e:
            print(f"  ❌ 清理失败: {e}")
            return False
    else:
        print("  ℹ️  ChromaDB目录不存在，无需清理")
        return True


def create_chromadb_config():
    """创建ChromaDB配置文件"""
    print("📝 创建ChromaDB配置文件...")
    
    try:
        # 创建ChromaDB配置目录
        config_dir = Path("backend/data")
        config_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建ChromaDB设置文件
        chroma_settings = {
            "anonymized_telemetry": False,
            "allow_reset": True,
            "is_persistent": True
        }
        
        settings_file = config_dir / "chroma_settings.yaml"
        with open(settings_file, 'w', encoding='utf-8') as f:
            yaml.dump(chroma_settings, f, default_flow_style=False)
        
        print(f"  ✅ ChromaDB设置文件已创建: {settings_file}")
        return True
        
    except Exception as e:
        print(f"  ❌ 创建配置文件失败: {e}")
        return False


def initialize_chromadb():
    """初始化ChromaDB"""
    print("🚀 初始化ChromaDB...")
    
    try:
        import chromadb
        from chromadb.utils import embedding_functions
        
        # 创建ChromaDB客户端
        chroma_path = "backend/data/chroma_db"
        client = chromadb.PersistentClient(
            path=chroma_path,
            settings=chromadb.Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # 设置嵌入函数
        embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
            model_name="moka-ai/m3e-base"
        )
        
        # 创建集合
        collection_name = "hybrid_knowledge_base"
        try:
            # 尝试获取现有集合
            collection = client.get_collection(
                name=collection_name,
                embedding_function=embedding_function
            )
            print(f"  ✅ 找到现有集合: {collection_name}")
        except:
            # 创建新集合
            collection = client.create_collection(
                name=collection_name,
                embedding_function=embedding_function,
                metadata={"hnsw:space": "cosine"}
            )
            print(f"  ✅ 创建新集合: {collection_name}")
        
        # 测试集合功能
        test_docs = ["这是一个测试文档"]
        test_ids = ["test_doc_1"]
        
        collection.add(
            documents=test_docs,
            ids=test_ids
        )
        
        # 查询测试
        results = collection.query(
            query_texts=["测试"],
            n_results=1
        )
        
        if results['documents']:
            print("  ✅ ChromaDB功能测试通过")
            
            # 清理测试数据
            collection.delete(ids=test_ids)
            print("  ✅ 测试数据已清理")
            
            return True
        else:
            print("  ❌ ChromaDB功能测试失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 初始化ChromaDB失败: {e}")
        return False


def test_rag_config_access():
    """测试RAG配置访问"""
    print("🧪 测试RAG配置访问...")
    
    try:
        from backend.config.modular_loader import get_modular_config_loader
        
        loader = get_modular_config_loader()
        loader.enable()
        
        # 测试知识库配置访问
        kb_config = loader.get_config("data.knowledge_base", {})
        if kb_config:
            print(f"  ✅ 知识库配置加载成功: {len(kb_config)} 项")
            
            # 检查关键配置项
            if "knowledge_base" in kb_config:
                kb_settings = kb_config["knowledge_base"]
                chroma_config = kb_settings.get("chroma_db", {})
                
                print(f"  📊 ChromaDB路径: {chroma_config.get('path', 'N/A')}")
                print(f"  📊 集合名称: {chroma_config.get('collection_name', 'N/A')}")
                print(f"  📊 嵌入模型: {chroma_config.get('embedding_model', 'N/A')}")
                
                return True
            else:
                print("  ❌ 知识库配置结构不正确")
                return False
        else:
            print("  ❌ 知识库配置加载失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 配置访问测试失败: {e}")
        return False


def test_rag_agent():
    """测试RAG代理"""
    print("🤖 测试RAG代理...")
    
    try:
        from backend.config.knowledge_base_config import get_knowledge_base_config_manager
        
        # 获取知识库配置管理器
        config_manager = get_knowledge_base_config_manager()
        config = config_manager.get_config()
        
        if config:
            print("  ✅ 知识库配置管理器工作正常")
            print(f"  📊 知识库启用状态: {config.enabled}")
            print(f"  📊 ChromaDB路径: {config.chroma_db.get('path', 'N/A')}")
            
            return True
        else:
            print("  ❌ 知识库配置管理器获取配置失败")
            return False
            
    except Exception as e:
        print(f"  ❌ RAG代理测试失败: {e}")
        return False


def update_split_mapping():
    """更新拆分映射表"""
    print("📋 更新配置拆分映射表...")
    
    try:
        mapping_file = Path("docs/config_split_mapping.yaml")
        
        if mapping_file.exists():
            with open(mapping_file, 'r', encoding='utf-8') as f:
                mapping_data = yaml.safe_load(f)
            
            # 添加知识库配置映射
            if "split_mapping" in mapping_data:
                mapping_data["split_mapping"]["knowledge_base"] = "data/knowledge_base.yaml"
                mapping_data["split_mapping"]["integrations"] = "data/knowledge_base.yaml"  # 合并到知识库配置
                
                # 更新统计信息
                if "analysis_summary" in mapping_data:
                    mapping_data["analysis_summary"]["target_files"] = len(set(mapping_data["split_mapping"].values()))
                
                # 保存更新后的映射
                with open(mapping_file, 'w', encoding='utf-8') as f:
                    yaml.dump(mapping_data, f, default_flow_style=False, allow_unicode=True)
                
                print("  ✅ 配置拆分映射表已更新")
                return True
            else:
                print("  ❌ 映射表格式不正确")
                return False
        else:
            print("  ⚠️  映射表文件不存在，跳过更新")
            return True
            
    except Exception as e:
        print(f"  ❌ 更新映射表失败: {e}")
        return False


def main():
    """主函数"""
    try:
        print("🚀 开始修复RAG配置问题")
        print("=" * 50)
        
        # 执行修复步骤
        steps = [
            ("备份ChromaDB数据", backup_chromadb),
            ("清理ChromaDB数据", clean_chromadb),
            ("创建ChromaDB配置", create_chromadb_config),
            ("初始化ChromaDB", initialize_chromadb),
            ("测试RAG配置访问", test_rag_config_access),
            ("测试RAG代理", test_rag_agent),
            ("更新拆分映射表", update_split_mapping)
        ]
        
        success_count = 0
        for step_name, step_func in steps:
            print(f"\n📋 {step_name}")
            if step_func():
                success_count += 1
            else:
                print(f"❌ {step_name} 失败")
        
        print(f"\n📊 修复结果: {success_count}/{len(steps)} 步骤成功")
        
        if success_count >= len(steps) - 1:  # 允许一个步骤失败
            print("\n🎉 RAG配置修复完成！")
            print("\n💡 建议:")
            print("1. 重新启动应用程序")
            print("2. 测试知识库功能")
            print("3. 如果仍有问题，请检查日志文件")
            return 0
        else:
            print(f"\n❌ RAG配置修复失败，请检查错误信息")
            return 1
            
    except Exception as e:
        print(f"❌ 修复过程失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
