#!/bin/bash

# 日志查看便捷脚本

LOG_DIR="logs"
TOOLS_DIR="tools"

# 确保工具脚本可执行
chmod +x "$TOOLS_DIR/log_formatter.py"
chmod +x "$TOOLS_DIR/log_viewer.py"

echo "=== 日志查看工具 ==="
echo "1. 格式化查看 session.log"
echo "2. 格式化查看 app.log"
echo "3. 交互式查看 session.log"
echo "4. 交互式查看 app.log"
echo "5. 实时跟踪 session.log"
echo "6. 实时跟踪 app.log"
echo "7. 查看业务流程"
echo "8. 查看错误日志"

read -p "请选择 (1-8): " choice

case $choice in
    1)
        python3 "$TOOLS_DIR/log_formatter.py" "$LOG_DIR/session.log"
        ;;
    2)
        python3 "$TOOLS_DIR/log_formatter.py" "$LOG_DIR/app.log"
        ;;
    3)
        python3 "$TOOLS_DIR/log_viewer.py" "$LOG_DIR/session.log"
        ;;
    4)
        python3 "$TOOLS_DIR/log_viewer.py" "$LOG_DIR/app.log"
        ;;
    5)
        python3 "$TOOLS_DIR/log_formatter.py" "$LOG_DIR/session.log" -f
        ;;
    6)
        python3 "$TOOLS_DIR/log_formatter.py" "$LOG_DIR/app.log" -f
        ;;
    7)
        python3 "$TOOLS_DIR/log_viewer.py" "$LOG_DIR/session.log" --business
        ;;
    8)
        python3 "$TOOLS_DIR/log_viewer.py" "$LOG_DIR/app.log" --errors
        ;;
    *)
        echo "无效选择"
        ;;
esac