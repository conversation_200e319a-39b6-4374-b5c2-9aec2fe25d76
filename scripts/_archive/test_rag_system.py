#!/usr/bin/env python3
"""
RAG系统完整测试脚本

测试RAG知识库系统的各个组件，确保配置修复后系统正常工作
"""

import sys
import os
import time
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_chromadb_connection():
    """测试ChromaDB连接"""
    print("🔗 测试ChromaDB连接...")
    
    try:
        import chromadb
        from chromadb.utils import embedding_functions
        
        # 创建客户端
        client = chromadb.PersistentClient(
            path="backend/data/chroma_db",
            settings=chromadb.Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # 获取集合
        embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
            model_name="moka-ai/m3e-base"
        )
        
        collection = client.get_collection(
            name="hybrid_knowledge_base",
            embedding_function=embedding_function
        )
        
        print(f"  ✅ ChromaDB连接成功")
        print(f"  📊 集合名称: {collection.name}")
        print(f"  📊 文档数量: {collection.count()}")
        
        return True, collection
        
    except Exception as e:
        print(f"  ❌ ChromaDB连接失败: {e}")
        return False, None


def test_knowledge_base_config():
    """测试知识库配置"""
    print("\n⚙️  测试知识库配置...")
    
    try:
        from backend.config.knowledge_base_config import get_knowledge_base_config_manager
        
        config_manager = get_knowledge_base_config_manager()
        config = config_manager.get_config()
        
        if config:
            print(f"  ✅ 配置加载成功")
            print(f"  📊 知识库启用: {config.enabled}")
            print(f"  📊 ChromaDB路径: {config.chroma_db.get('path', 'N/A')}")
            print(f"  📊 集合名称: {config.chroma_db.get('collection_name', 'N/A')}")
            print(f"  📊 嵌入模型: {config.chroma_db.get('embedding_model', 'N/A')}")
            print(f"  📊 检索Top-K: {config.retrieval.get('top_k', 'N/A')}")
            print(f"  📊 相似度阈值: {config.retrieval.get('similarity_threshold', 'N/A')}")
            
            return True, config
        else:
            print("  ❌ 配置加载失败")
            return False, None
            
    except Exception as e:
        print(f"  ❌ 知识库配置测试失败: {e}")
        return False, None


def test_rag_agent_creation():
    """测试RAG代理创建"""
    print("\n🤖 测试RAG代理创建...")
    
    try:
        from backend.agents.factory import agent_factory
        
        # 获取RAG代理
        rag_agent = agent_factory.get_rag_knowledge_base_agent()
        
        if rag_agent:
            print(f"  ✅ RAG代理创建成功")
            print(f"  📊 代理类型: {type(rag_agent).__name__}")
            print(f"  📊 代理名称: {getattr(rag_agent, 'agent_name', 'N/A')}")
            
            return True, rag_agent
        else:
            print("  ❌ RAG代理创建失败")
            return False, None
            
    except Exception as e:
        print(f"  ❌ RAG代理创建测试失败: {e}")
        return False, None


def test_document_ingestion(collection):
    """测试文档摄入"""
    print("\n📄 测试文档摄入...")
    
    try:
        # 准备测试文档
        test_documents = [
            "这是一个关于需求采集的测试文档。需求采集是软件开发过程中的重要环节。",
            "用户界面设计需要考虑用户体验和可用性。好的界面设计能够提高用户满意度。",
            "数据库设计应该遵循范式理论，确保数据的一致性和完整性。",
            "测试是软件质量保证的重要手段，包括单元测试、集成测试和系统测试。"
        ]
        
        test_ids = [f"test_doc_{i}" for i in range(len(test_documents))]
        test_metadatas = [{"source": "test", "type": "document"} for _ in test_documents]
        
        # 添加文档到集合
        collection.add(
            documents=test_documents,
            ids=test_ids,
            metadatas=test_metadatas
        )
        
        print(f"  ✅ 成功添加 {len(test_documents)} 个测试文档")
        print(f"  📊 集合文档总数: {collection.count()}")
        
        return True, test_ids
        
    except Exception as e:
        print(f"  ❌ 文档摄入测试失败: {e}")
        return False, []


def test_document_retrieval(collection):
    """测试文档检索"""
    print("\n🔍 测试文档检索...")
    
    try:
        # 测试查询
        test_queries = [
            "需求采集",
            "用户界面",
            "数据库设计",
            "软件测试"
        ]
        
        for query in test_queries:
            print(f"  🔍 查询: '{query}'")
            
            results = collection.query(
                query_texts=[query],
                n_results=2
            )
            
            if results['documents'] and results['documents'][0]:
                print(f"    ✅ 找到 {len(results['documents'][0])} 个相关文档")
                for i, doc in enumerate(results['documents'][0]):
                    distance = results['distances'][0][i] if results['distances'] else 0
                    print(f"    📄 文档 {i+1}: {doc[:50]}... (距离: {distance:.3f})")
            else:
                print(f"    ⚠️  未找到相关文档")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 文档检索测试失败: {e}")
        return False


def test_rag_query(rag_agent):
    """测试RAG查询"""
    print("\n💬 测试RAG查询...")
    
    try:
        # 测试查询
        test_queries = [
            "什么是需求采集？",
            "如何设计用户界面？",
            "数据库设计的原则是什么？"
        ]
        
        for query in test_queries:
            print(f"  💬 查询: '{query}'")
            
            try:
                # 这里需要根据实际的RAG代理接口调用
                # 由于可能需要LLM服务，我们先测试配置访问
                if hasattr(rag_agent, 'config'):
                    config = rag_agent.config
                    print(f"    ✅ RAG代理配置正常")
                    print(f"    📊 检索配置: top_k={config.retrieval.get('top_k', 'N/A')}")
                else:
                    print(f"    ⚠️  RAG代理配置访问异常")
                    
            except Exception as query_error:
                print(f"    ❌ 查询失败: {query_error}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ RAG查询测试失败: {e}")
        return False


def cleanup_test_data(collection, test_ids):
    """清理测试数据"""
    print("\n🧹 清理测试数据...")
    
    try:
        if test_ids:
            collection.delete(ids=test_ids)
            print(f"  ✅ 已清理 {len(test_ids)} 个测试文档")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 清理测试数据失败: {e}")
        return False


def main():
    """主函数"""
    try:
        print("🚀 RAG系统完整测试")
        print("=" * 50)
        
        # 测试步骤
        success_count = 0
        total_tests = 0
        
        # 1. 测试ChromaDB连接
        total_tests += 1
        chromadb_ok, collection = test_chromadb_connection()
        if chromadb_ok:
            success_count += 1
        
        # 2. 测试知识库配置
        total_tests += 1
        config_ok, config = test_knowledge_base_config()
        if config_ok:
            success_count += 1
        
        # 3. 测试RAG代理创建
        total_tests += 1
        agent_ok, rag_agent = test_rag_agent_creation()
        if agent_ok:
            success_count += 1
        
        # 4. 测试文档摄入（如果ChromaDB可用）
        test_ids = []
        if chromadb_ok and collection:
            total_tests += 1
            ingestion_ok, test_ids = test_document_ingestion(collection)
            if ingestion_ok:
                success_count += 1
        
        # 5. 测试文档检索（如果文档摄入成功）
        if chromadb_ok and collection and test_ids:
            total_tests += 1
            retrieval_ok = test_document_retrieval(collection)
            if retrieval_ok:
                success_count += 1
        
        # 6. 测试RAG查询（如果代理创建成功）
        if agent_ok and rag_agent:
            total_tests += 1
            query_ok = test_rag_query(rag_agent)
            if query_ok:
                success_count += 1
        
        # 清理测试数据
        if chromadb_ok and collection and test_ids:
            cleanup_test_data(collection, test_ids)
        
        # 输出测试结果
        print(f"\n📊 测试结果总结:")
        print("=" * 50)
        print(f"成功测试: {success_count}/{total_tests}")
        print(f"成功率: {success_count/total_tests*100:.1f}%")
        
        if success_count == total_tests:
            print("\n🎉 RAG系统测试完全通过！")
            print("✅ RAG知识库系统已准备就绪")
            return 0
        elif success_count >= total_tests * 0.8:
            print("\n⚠️  RAG系统测试基本通过")
            print("⚠️  部分功能可能需要进一步配置")
            return 0
        else:
            print("\n❌ RAG系统测试失败")
            print("❌ 请检查配置和依赖")
            return 1
            
    except Exception as e:
        print(f"❌ 测试过程失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
