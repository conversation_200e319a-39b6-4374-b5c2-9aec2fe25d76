#!/usr/bin/env python3
"""
双写验证测试脚本

对比新旧配置系统的输出结果，确保完全一致
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime
from deepdiff import DeepDiff

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_config_access_apis():
    """测试配置访问API的一致性"""
    print("🧪 测试配置访问API一致性...")
    
    from backend.config.unified_config_loader import get_unified_config
    from backend.config.modular_loader import get_modular_config_loader
    from backend.config.compatibility_layer import get_compatibility_wrapper
    
    # 获取配置实例
    legacy_config = get_unified_config()
    modular_loader = get_modular_config_loader()
    wrapper = get_compatibility_wrapper()
    
    # 测试用例
    test_cases = [
        # 基础配置访问
        {
            "name": "业务规则配置",
            "legacy_call": lambda: legacy_config.get_business_rules(),
            "modular_call": lambda: wrapper.get_business_rules(),
            "key": "business_rules"
        },
        {
            "name": "LLM配置",
            "legacy_call": lambda: legacy_config.get_llm_config(),
            "modular_call": lambda: wrapper.get_llm_config(),
            "key": "llm_config"
        },
        {
            "name": "消息模板",
            "legacy_call": lambda: legacy_config.get_message_templates(),
            "modular_call": lambda: wrapper.get_message_templates(),
            "key": "message_templates"
        },
        # 特定配置值访问
        {
            "name": "置信度阈值",
            "legacy_call": lambda: legacy_config.get_config_value("thresholds.confidence.default", 0.7),
            "modular_call": lambda: wrapper.get_threshold("confidence.default", 0.7),
            "key": "confidence_threshold"
        },
        {
            "name": "重试配置",
            "legacy_call": lambda: legacy_config.get_config_value("business_rules.retry", {}),
            "modular_call": lambda: wrapper.get_config_value("business_rules.retry", {}),
            "key": "retry_config"
        },
        # 消息模板访问
        {
            "name": "问候模板",
            "legacy_call": lambda: legacy_config.get_message_template("greeting.basic", "默认问候"),
            "modular_call": lambda: wrapper.get_message_template("greeting.basic", "默认问候"),
            "key": "greeting_template"
        }
    ]
    
    results = {}
    all_passed = True
    
    # 启用模块化配置
    modular_loader.enable()
    
    for test_case in test_cases:
        try:
            print(f"  🔍 测试: {test_case['name']}")
            
            # 获取传统配置结果
            legacy_result = test_case["legacy_call"]()
            
            # 获取模块化配置结果
            modular_result = test_case["modular_call"]()
            
            # 比较结果
            diff = DeepDiff(legacy_result, modular_result, ignore_order=True)
            
            if diff:
                print(f"    ❌ 结果不一致")
                print(f"       差异: {diff}")
                results[test_case["key"]] = {
                    "passed": False,
                    "legacy_result": legacy_result,
                    "modular_result": modular_result,
                    "diff": str(diff)
                }
                all_passed = False
            else:
                print(f"    ✅ 结果一致")
                results[test_case["key"]] = {
                    "passed": True,
                    "result_type": type(legacy_result).__name__,
                    "result_size": len(str(legacy_result))
                }
                
        except Exception as e:
            print(f"    ❌ 测试失败: {e}")
            results[test_case["key"]] = {
                "passed": False,
                "error": str(e)
            }
            all_passed = False
    
    # 禁用模块化配置
    modular_loader.disable()
    
    return results, all_passed


def test_performance_comparison():
    """测试性能对比"""
    print("\n⚡ 测试性能对比...")
    
    import time
    from backend.config.unified_config_loader import get_unified_config
    from backend.config.modular_loader import get_modular_config_loader
    from backend.config.compatibility_layer import get_compatibility_wrapper
    
    legacy_config = get_unified_config()
    modular_loader = get_modular_config_loader()
    wrapper = get_compatibility_wrapper()
    
    # 性能测试用例
    test_operations = [
        ("get_business_rules", lambda c: c.get_business_rules()),
        ("get_llm_config", lambda c: c.get_llm_config()),
        ("get_message_templates", lambda c: c.get_message_templates()),
    ]
    
    performance_results = {}
    
    for op_name, operation in test_operations:
        print(f"  🏃 测试操作: {op_name}")
        
        # 测试传统配置性能
        start_time = time.time()
        for _ in range(100):  # 执行100次
            try:
                operation(legacy_config)
            except:
                pass
        legacy_time = time.time() - start_time
        
        # 测试模块化配置性能
        modular_loader.enable()
        start_time = time.time()
        for _ in range(100):  # 执行100次
            try:
                operation(wrapper)
            except:
                pass
        modular_time = time.time() - start_time
        modular_loader.disable()
        
        performance_results[op_name] = {
            "legacy_time": legacy_time,
            "modular_time": modular_time,
            "speedup": legacy_time / modular_time if modular_time > 0 else 0
        }
        
        print(f"    传统配置: {legacy_time:.4f}s")
        print(f"    模块配置: {modular_time:.4f}s")
        print(f"    性能比: {performance_results[op_name]['speedup']:.2f}x")
    
    return performance_results


def test_edge_cases():
    """测试边界情况"""
    print("\n🔬 测试边界情况...")
    
    from backend.config.compatibility_layer import get_compatibility_wrapper
    from backend.config.modular_loader import get_modular_config_loader
    
    wrapper = get_compatibility_wrapper()
    modular_loader = get_modular_config_loader()
    
    edge_cases = [
        {
            "name": "不存在的配置键",
            "test": lambda: wrapper.get_config_value("non.existent.key", "default"),
            "expected": "default"
        },
        {
            "name": "空配置键",
            "test": lambda: wrapper.get_config_value("", "default"),
            "expected": "default"
        },
        {
            "name": "None默认值",
            "test": lambda: wrapper.get_config_value("non.existent", None),
            "expected": None
        },
        {
            "name": "模块化禁用时的回退",
            "test": lambda: (modular_loader.disable(), wrapper.get_business_rules())[1],
            "expected_type": dict
        }
    ]
    
    edge_results = {}
    all_passed = True
    
    modular_loader.enable()
    
    for case in edge_cases:
        try:
            print(f"  🧪 测试: {case['name']}")
            result = case["test"]()
            
            if "expected" in case:
                if result == case["expected"]:
                    print(f"    ✅ 通过")
                    edge_results[case["name"]] = {"passed": True, "result": result}
                else:
                    print(f"    ❌ 失败: 期望 {case['expected']}, 得到 {result}")
                    edge_results[case["name"]] = {"passed": False, "expected": case["expected"], "actual": result}
                    all_passed = False
            elif "expected_type" in case:
                if isinstance(result, case["expected_type"]):
                    print(f"    ✅ 通过")
                    edge_results[case["name"]] = {"passed": True, "result_type": type(result).__name__}
                else:
                    print(f"    ❌ 失败: 期望类型 {case['expected_type']}, 得到 {type(result)}")
                    edge_results[case["name"]] = {"passed": False, "expected_type": case["expected_type"].__name__, "actual_type": type(result).__name__}
                    all_passed = False
                    
        except Exception as e:
            print(f"    ❌ 异常: {e}")
            edge_results[case["name"]] = {"passed": False, "error": str(e)}
            all_passed = False
    
    return edge_results, all_passed


def generate_verification_report(api_results, api_passed, performance_results, edge_results, edge_passed):
    """生成验证报告"""
    report = {
        "verification_time": datetime.now().isoformat(),
        "summary": {
            "api_tests_passed": api_passed,
            "edge_tests_passed": edge_passed,
            "overall_passed": api_passed and edge_passed
        },
        "api_test_results": api_results,
        "performance_results": performance_results,
        "edge_case_results": edge_results,
        "recommendations": []
    }
    
    # 添加建议
    if not api_passed:
        report["recommendations"].append("修复API一致性问题")
    
    if not edge_passed:
        report["recommendations"].append("修复边界情况处理")
    
    # 性能建议
    avg_speedup = sum(r["speedup"] for r in performance_results.values()) / len(performance_results)
    if avg_speedup > 1.2:
        report["recommendations"].append("模块化配置性能优于传统配置")
    elif avg_speedup < 0.8:
        report["recommendations"].append("考虑优化模块化配置性能")
    
    # 保存报告
    report_file = Path("docs/dual_write_verification_report.json")
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 双写验证报告已保存到: {report_file}")
    return report


def main():
    """主函数"""
    try:
        print("🚀 开始双写验证测试")
        
        # 测试API一致性
        api_results, api_passed = test_config_access_apis()
        
        # 测试性能对比
        performance_results = test_performance_comparison()
        
        # 测试边界情况
        edge_results, edge_passed = test_edge_cases()
        
        # 生成报告
        report = generate_verification_report(
            api_results, api_passed, performance_results, edge_results, edge_passed
        )
        
        # 输出总结
        print(f"\n📊 双写验证总结:")
        print("=" * 50)
        print(f"API一致性测试: {'✅ 通过' if api_passed else '❌ 失败'}")
        print(f"边界情况测试: {'✅ 通过' if edge_passed else '❌ 失败'}")
        print(f"整体验证结果: {'✅ 通过' if report['summary']['overall_passed'] else '❌ 失败'}")
        
        if report["recommendations"]:
            print(f"\n💡 建议:")
            for rec in report["recommendations"]:
                print(f"  - {rec}")
        
        if report['summary']['overall_passed']:
            print("\n🎉 双写验证完全通过！新旧配置系统输出完全一致")
            return 0
        else:
            print("\n❌ 双写验证存在问题，需要修复")
            return 1
            
    except Exception as e:
        print(f"❌ 双写验证失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
