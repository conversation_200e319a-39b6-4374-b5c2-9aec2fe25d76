#!/usr/bin/env python3
"""
配置结构分析脚本

分析unified_config.yaml的详细结构，生成精确的拆分映射表
"""

import sys
import os
import yaml
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def analyze_config_structure():
    """分析配置结构"""
    config_file = Path("backend/config/unified_config.yaml")
    
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return
    
    print("🔍 开始分析unified_config.yaml结构...")
    
    # 加载配置
    with open(config_file, 'r', encoding='utf-8') as f:
        config_data = yaml.safe_load(f)
    
    # 分析顶级配置块
    print(f"\n📊 配置文件总行数: {sum(1 for _ in open(config_file, 'r', encoding='utf-8'))}")
    print(f"📊 顶级配置块数量: {len(config_data)}")
    
    # 定义拆分映射
    split_mapping = {
        # 业务配置
        "business_rules": "business/rules.yaml",
        "business_templates": "business/templates.yaml", 
        "message_templates": "business/templates.yaml",  # 合并到templates
        "message_reply_system": "business/templates.yaml",  # 合并到templates
        "error_fallback_templates": "business/templates.yaml",  # 合并到templates
        "system_fallback_templates": "business/templates.yaml",  # 合并到templates
        "strategy_templates": "business/templates.yaml",  # 合并到templates
        "thresholds": "business/thresholds.yaml",
        "strategies": "business/rules.yaml",  # 合并到rules
        
        # LLM配置
        "llm": "llm/models.yaml",
        
        # 系统配置
        "system": "system/base.yaml",
        "performance": "system/performance.yaml",
        "security": "system/security.yaml",
        "development": "system/base.yaml",  # 合并到base
        
        # 数据配置
        "database": "data/database.yaml",
        "conversation": "data/database.yaml",  # 合并到database
        
        # 动态配置
        "keyword_rules": "dynamic/keywords.yaml",
        "direct_domain_selection": "dynamic/keywords.yaml",  # 合并到keywords
        "domain_selection_mapping": "dynamic/keywords.yaml",  # 合并到keywords
        
        # 其他配置
        "integrations": "system/base.yaml",  # 合并到base
        "knowledge_base": "system/base.yaml",  # 合并到base
    }
    
    print("\n📋 配置块分析结果:")
    print("=" * 80)
    
    total_items = 0
    mapping_stats = {}
    
    for key, value in config_data.items():
        item_count = count_config_items(value)
        total_items += item_count
        
        target_file = split_mapping.get(key, "未映射")
        if target_file not in mapping_stats:
            mapping_stats[target_file] = {"blocks": 0, "items": 0}
        mapping_stats[target_file]["blocks"] += 1
        mapping_stats[target_file]["items"] += item_count
        
        print(f"📦 {key:<30} -> {target_file:<25} ({item_count:>4} 项)")
    
    print("=" * 80)
    print(f"📊 总配置项数: {total_items}")
    
    # 按目标文件统计
    print(f"\n📁 按目标文件统计:")
    print("=" * 60)
    for target_file, stats in sorted(mapping_stats.items()):
        if target_file != "未映射":
            print(f"📄 {target_file:<30} {stats['blocks']:>2} 块, {stats['items']:>4} 项")
    
    if "未映射" in mapping_stats:
        print(f"⚠️  未映射配置块: {mapping_stats['未映射']['blocks']} 块, {mapping_stats['未映射']['items']} 项")
    
    # 生成详细的拆分计划
    generate_split_plan(config_data, split_mapping)
    
    return split_mapping, config_data


def count_config_items(obj, depth=0):
    """递归计算配置项数量"""
    if isinstance(obj, dict):
        count = len(obj)
        for value in obj.values():
            if isinstance(value, (dict, list)):
                count += count_config_items(value, depth + 1)
        return count
    elif isinstance(obj, list):
        count = len(obj)
        for item in obj:
            if isinstance(item, (dict, list)):
                count += count_config_items(item, depth + 1)
        return count
    else:
        return 1


def generate_split_plan(config_data, split_mapping):
    """生成详细的拆分计划"""
    print(f"\n📋 详细拆分计划:")
    print("=" * 80)
    
    # 按目标文件分组
    file_groups = {}
    for source_key, target_file in split_mapping.items():
        if source_key in config_data:
            if target_file not in file_groups:
                file_groups[target_file] = []
            file_groups[target_file].append(source_key)
    
    for target_file, source_keys in sorted(file_groups.items()):
        print(f"\n📄 {target_file}")
        print("-" * 50)
        
        total_items = 0
        for source_key in source_keys:
            if source_key in config_data:
                item_count = count_config_items(config_data[source_key])
                total_items += item_count
                print(f"  📦 {source_key} ({item_count} 项)")
                
                # 显示主要子配置
                if isinstance(config_data[source_key], dict):
                    for sub_key in list(config_data[source_key].keys())[:5]:  # 只显示前5个
                        print(f"    - {sub_key}")
                    if len(config_data[source_key]) > 5:
                        print(f"    ... 还有 {len(config_data[source_key]) - 5} 个配置项")
        
        print(f"  📊 总计: {total_items} 项")


def validate_split_mapping(config_data, split_mapping):
    """验证拆分映射的完整性"""
    print(f"\n✅ 验证拆分映射完整性:")
    print("=" * 50)
    
    mapped_keys = set(split_mapping.keys())
    config_keys = set(config_data.keys())
    
    # 检查未映射的配置
    unmapped_keys = config_keys - mapped_keys
    if unmapped_keys:
        print(f"⚠️  未映射的配置块: {unmapped_keys}")
    else:
        print("✅ 所有配置块都已映射")
    
    # 检查映射到不存在配置的情况
    invalid_mappings = mapped_keys - config_keys
    if invalid_mappings:
        print(f"⚠️  映射到不存在配置的键: {invalid_mappings}")
    else:
        print("✅ 所有映射都有效")
    
    return len(unmapped_keys) == 0 and len(invalid_mappings) == 0


def main():
    """主函数"""
    try:
        split_mapping, config_data = analyze_config_structure()
        
        # 验证映射完整性
        is_valid = validate_split_mapping(config_data, split_mapping)
        
        if is_valid:
            print(f"\n🎉 配置结构分析完成，映射表有效！")
            
            # 保存映射表到文件
            mapping_file = Path("docs/config_split_mapping.yaml")
            with open(mapping_file, 'w', encoding='utf-8') as f:
                yaml.dump({
                    "split_mapping": split_mapping,
                    "analysis_summary": {
                        "total_blocks": len(config_data),
                        "total_items": sum(count_config_items(v) for v in config_data.values()),
                        "target_files": len(set(split_mapping.values()))
                    }
                }, f, default_flow_style=False, allow_unicode=True)
            
            print(f"📄 映射表已保存到: {mapping_file}")
        else:
            print(f"\n❌ 映射表存在问题，请检查后重试")
            return 1
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
