#!/usr/bin/env python3
"""
配置监控系统启动脚本
快速启动配置监控功能
"""

import sys
import os
import webbrowser
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

def start_monitoring_service():
    """启动配置监控服务"""
    print("🚀 启动配置监控服务...")
    
    try:
        from backend.services.config_monitoring_service import ConfigMonitoringService
        
        # 创建监控服务实例
        service = ConfigMonitoringService()
        
        # 执行快速健康检查
        print("📊 执行配置健康检查...")
        config_result = service.check_config_integrity()
        
        print(f"✅ 配置健康度: {config_result['health_score']:.1f}%")
        print(f"✅ 配置状态: {config_result['status']}")
        print(f"✅ 有效配置: {config_result['valid_configs']}/{config_result['total_configs']}")
        
        if config_result['missing_configs']:
            print(f"⚠️  缺失配置: {len(config_result['missing_configs'])}个")
            for config in config_result['missing_configs'][:3]:
                print(f"    - {config}")
        
        return service
        
    except Exception as e:
        print(f"❌ 启动配置监控服务失败: {e}")
        return None

def start_scheduler():
    """启动定时任务调度器"""
    print("⏰ 启动定时任务调度器...")
    
    try:
        from backend.tasks.config_monitoring_scheduler import start_monitoring_scheduler
        
        start_monitoring_scheduler()
        print("✅ 定时任务调度器启动成功")
        print("📅 定时任务:")
        print("    - 每日配置检查: 09:00")
        print("    - 每周硬编码扫描: 周一 10:00")
        print("    - 每小时健康检查")
        
        return True
        
    except Exception as e:
        print(f"❌ 启动定时任务调度器失败: {e}")
        return False

def open_monitoring_page():
    """打开监控页面"""
    print("🌐 打开配置监控页面...")
    
    try:
        # 获取前端页面路径
        html_path = project_root / "frontend" / "admin" / "config_monitoring.html"
        
        if html_path.exists():
            # 转换为file:// URL
            file_url = f"file://{html_path.absolute()}"
            print(f"📄 监控页面: {file_url}")
            
            # 尝试打开浏览器
            webbrowser.open(file_url)
            print("✅ 监控页面已在浏览器中打开")
            return True
        else:
            print(f"❌ 监控页面不存在: {html_path}")
            return False
            
    except Exception as e:
        print(f"❌ 打开监控页面失败: {e}")
        return False

def show_api_info():
    """显示API信息"""
    print("\n📡 配置监控API端点:")
    print("=" * 50)
    
    api_endpoints = [
        ("GET", "/admin/config-monitoring/dashboard", "获取监控仪表板数据"),
        ("GET", "/admin/config-monitoring/config-integrity", "检查配置完整性"),
        ("GET", "/admin/config-monitoring/hardcode-scan", "扫描硬编码回归"),
        ("POST", "/admin/config-monitoring/run-full-check", "运行完整检查"),
        ("GET", "/admin/config-monitoring/health-summary", "获取健康度摘要"),
        ("GET", "/admin/config-monitoring/project-stats", "获取项目统计"),
        ("GET", "/admin/config-monitoring/ping", "健康检查"),
    ]
    
    for method, endpoint, description in api_endpoints:
        print(f"{method:4} http://localhost:8000{endpoint}")
        print(f"     {description}")
        print()

def show_usage_examples():
    """显示使用示例"""
    print("\n💡 使用示例:")
    print("=" * 50)
    
    examples = [
        ("检查配置健康度", "curl -X GET 'http://localhost:8000/admin/config-monitoring/health-summary'"),
        ("执行完整检查", "curl -X POST 'http://localhost:8000/admin/config-monitoring/run-full-check'"),
        ("获取项目统计", "curl -X GET 'http://localhost:8000/admin/config-monitoring/project-stats'"),
    ]
    
    for title, command in examples:
        print(f"📋 {title}:")
        print(f"   {command}")
        print()

def main():
    """主函数"""
    print("🔧 配置监控系统启动器")
    print("=" * 50)
    
    # 1. 启动监控服务
    service = start_monitoring_service()
    if not service:
        print("❌ 配置监控服务启动失败，退出")
        return
    
    print()
    
    # 2. 启动定时任务调度器
    scheduler_ok = start_scheduler()
    
    print()
    
    # 3. 打开监控页面
    page_ok = open_monitoring_page()
    
    # 4. 显示API信息
    show_api_info()
    
    # 5. 显示使用示例
    show_usage_examples()
    
    print("🎉 配置监控系统启动完成!")
    print("\n📋 下一步:")
    print("1. 启动FastAPI服务: uvicorn backend.api.main:app --reload")
    print("2. 访问监控页面查看配置状态")
    print("3. 使用API端点进行程序化访问")
    
    # 保持调度器运行
    if scheduler_ok:
        print("\n⏰ 定时任务调度器正在后台运行...")
        print("按 Ctrl+C 停止")
        try:
            import time
            while True:
                time.sleep(60)
        except KeyboardInterrupt:
            print("\n🛑 停止定时任务调度器...")
            from backend.tasks.config_monitoring_scheduler import stop_monitoring_scheduler
            stop_monitoring_scheduler()
            print("✅ 定时任务调度器已停止")

if __name__ == "__main__":
    main()
