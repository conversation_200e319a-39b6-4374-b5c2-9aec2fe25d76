#!/usr/bin/env python3
"""
配置内容拆分脚本

将unified_config.yaml的内容按照映射表拆分到各个模块文件中
"""

import sys
import os
import yaml
from pathlib import Path
from datetime import datetime
from copy import deepcopy

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def load_split_mapping():
    """加载拆分映射表"""
    mapping_file = Path("docs/config_split_mapping.yaml")
    if not mapping_file.exists():
        print(f"❌ 映射文件不存在: {mapping_file}")
        return None
    
    with open(mapping_file, 'r', encoding='utf-8') as f:
        mapping_data = yaml.safe_load(f)
    
    return mapping_data.get("split_mapping", {})


def load_unified_config():
    """加载统一配置文件"""
    config_file = Path("backend/config/unified_config.yaml")
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return None
    
    with open(config_file, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)


def create_file_header(target_file, source_blocks):
    """创建文件头部注释"""
    file_type = Path(target_file).stem
    
    headers = {
        "rules": "业务规则配置",
        "templates": "业务消息模板配置", 
        "thresholds": "业务阈值配置",
        "models": "LLM模型配置",
        "scenarios": "LLM场景参数配置",
        "prompts": "LLM提示词配置",
        "base": "系统基础配置",
        "performance": "系统性能配置",
        "security": "系统安全配置",
        "database": "数据库配置",
        "storage": "存储配置",
        "keywords": "动态关键词配置",
        "versions": "动态版本配置"
    }
    
    description = headers.get(file_type, "配置文件")
    
    header = f"""# ============================================================================
# {description}（从unified_config.yaml拆分）
# ============================================================================
# 文件说明：从unified_config.yaml拆分的{description}
# 拆分时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# 源配置块：{', '.join(source_blocks)}
# 维护责任：配置管理团队
# 更新频率：根据业务需求
# ============================================================================

"""
    return header


def merge_configs(target_config, source_config, source_key):
    """合并配置到目标配置中"""
    if source_key in target_config:
        # 如果目标配置中已存在该键，进行深度合并
        if isinstance(target_config[source_key], dict) and isinstance(source_config, dict):
            for key, value in source_config.items():
                target_config[source_key][key] = deepcopy(value)
        else:
            # 直接覆盖
            target_config[source_key] = deepcopy(source_config)
    else:
        # 直接添加
        target_config[source_key] = deepcopy(source_config)


def split_config_content():
    """拆分配置内容"""
    print("🔄 开始拆分配置内容...")
    
    # 加载映射表和配置
    split_mapping = load_split_mapping()
    if not split_mapping:
        return False
    
    unified_config = load_unified_config()
    if not unified_config:
        return False
    
    # 按目标文件分组
    file_groups = {}
    for source_key, target_file in split_mapping.items():
        if source_key in unified_config:
            if target_file not in file_groups:
                file_groups[target_file] = {}
            file_groups[target_file][source_key] = unified_config[source_key]
    
    print(f"📊 将拆分到 {len(file_groups)} 个文件")
    
    # 处理每个目标文件
    success_count = 0
    for target_file, source_configs in file_groups.items():
        try:
            target_path = Path("backend/config") / target_file
            
            # 确保目录存在
            target_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 读取现有文件内容（如果存在）
            existing_config = {}
            if target_path.exists():
                try:
                    with open(target_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # 跳过注释行，只解析YAML内容
                        yaml_start = content.find('\n# ============================================================================\n_metadata:')
                        if yaml_start == -1:
                            yaml_start = content.find('\n\n')
                            if yaml_start != -1:
                                yaml_content = content[yaml_start+2:]
                            else:
                                yaml_content = content
                        else:
                            yaml_content = content
                        
                        if yaml_content.strip():
                            existing_config = yaml.safe_load(yaml_content) or {}
                except Exception as e:
                    print(f"⚠️  读取现有文件失败 {target_file}: {e}")
                    existing_config = {}
            
            # 合并配置
            merged_config = deepcopy(existing_config)
            for source_key, source_config in source_configs.items():
                merged_config[source_key] = deepcopy(source_config)
            
            # 添加元数据
            merged_config["_metadata"] = {
                "file_version": "1.0",
                "split_date": datetime.now().isoformat(),
                "source_blocks": list(source_configs.keys()),
                "config_type": Path(target_file).stem,
                "total_items": sum(count_items(config) for config in source_configs.values())
            }
            
            # 生成文件内容
            header = create_file_header(target_file, list(source_configs.keys()))
            
            # 写入文件
            with open(target_path, 'w', encoding='utf-8') as f:
                f.write(header)
                yaml.dump(merged_config, f, default_flow_style=False, allow_unicode=True, 
                         sort_keys=False, indent=2, width=120)
            
            print(f"✅ {target_file} ({len(source_configs)} 块, {merged_config['_metadata']['total_items']} 项)")
            success_count += 1
            
        except Exception as e:
            print(f"❌ 处理文件失败 {target_file}: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎉 拆分完成！成功处理 {success_count}/{len(file_groups)} 个文件")
    return success_count == len(file_groups)


def count_items(obj):
    """计算配置项数量"""
    if isinstance(obj, dict):
        return len(obj) + sum(count_items(v) for v in obj.values() if isinstance(v, (dict, list)))
    elif isinstance(obj, list):
        return len(obj) + sum(count_items(item) for item in obj if isinstance(item, (dict, list)))
    else:
        return 1


def verify_split_result():
    """验证拆分结果"""
    print("\n🔍 验证拆分结果...")
    
    # 加载原始配置
    unified_config = load_unified_config()
    if not unified_config:
        return False
    
    # 加载映射表
    split_mapping = load_split_mapping()
    if not split_mapping:
        return False
    
    # 检查每个拆分文件
    all_valid = True
    for source_key, target_file in split_mapping.items():
        if source_key not in unified_config:
            continue
            
        target_path = Path("backend/config") / target_file
        if not target_path.exists():
            print(f"❌ 目标文件不存在: {target_file}")
            all_valid = False
            continue
        
        try:
            with open(target_path, 'r', encoding='utf-8') as f:
                target_config = yaml.safe_load(f)
            
            if source_key not in target_config:
                print(f"❌ 配置块 {source_key} 未在 {target_file} 中找到")
                all_valid = False
            else:
                print(f"✅ {source_key} -> {target_file}")
                
        except Exception as e:
            print(f"❌ 验证文件失败 {target_file}: {e}")
            all_valid = False
    
    if all_valid:
        print("🎉 所有拆分文件验证通过！")
    else:
        print("❌ 部分拆分文件验证失败")
    
    return all_valid


def main():
    """主函数"""
    try:
        # 拆分配置内容
        if not split_config_content():
            print("❌ 配置拆分失败")
            return 1
        
        # 验证拆分结果
        if not verify_split_result():
            print("❌ 拆分结果验证失败")
            return 1
        
        print("\n🎉 配置内容拆分完成！")
        return 0
        
    except Exception as e:
        print(f"❌ 拆分过程失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
