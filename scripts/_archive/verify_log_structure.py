#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证日志目录结构脚本

此脚本用于验证项目的日志目录结构是否符合要求：
1. 所有日志文件存放在项目根目录 logs 目录下
2. 项目的性能日志文件存放在 logs/performance 下
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def verify_log_structure():
    """验证日志目录结构"""
    print("=== 验证日志目录结构 ===")
    
    # 检查项目根目录
    print(f"项目根目录: {project_root}")
    
    # 检查 logs 目录
    logs_dir = project_root / "logs"
    print(f"日志目录: {logs_dir}")
    print(f"日志目录存在: {logs_dir.exists()}")
    
    if logs_dir.exists():
        print("日志目录内容:")
        for item in logs_dir.iterdir():
            if item.is_file():
                print(f"  📄 {item.name} ({item.stat().st_size} bytes)")
            elif item.is_dir():
                print(f"  📁 {item.name}/")
    
    # 检查 logs/performance 目录
    performance_dir = logs_dir / "performance"
    print(f"\n性能日志目录: {performance_dir}")
    print(f"性能日志目录存在: {performance_dir.exists()}")
    
    if performance_dir.exists():
        print("性能日志目录内容:")
        for item in performance_dir.iterdir():
            if item.is_file():
                print(f"  📄 {item.name} ({item.stat().st_size} bytes)")
            elif item.is_dir():
                print(f"  📁 {item.name}/")
    
    # 测试配置导入
    print("\n=== 测试配置导入 ===")
    try:
        from backend.config.settings import LOG_DIR, PERFORMANCE_LOG_DIR
        print(f"LOG_DIR: {LOG_DIR}")
        print(f"PERFORMANCE_LOG_DIR: {PERFORMANCE_LOG_DIR}")
        print(f"LOG_DIR 存在: {LOG_DIR.exists()}")
        print(f"PERFORMANCE_LOG_DIR 存在: {PERFORMANCE_LOG_DIR.exists()}")
    except ImportError as e:
        print(f"导入配置失败: {e}")
    
    # 测试日志配置
    print("\n=== 测试日志配置 ===")
    try:
        from backend.config.logging_config import _LOG_CONFIG
        print(f"日志配置目录: {_LOG_CONFIG['log_dir']}")
        print(f"性能日志配置目录: {_LOG_CONFIG['performance_log_dir']}")
        print(f"日志配置目录存在: {_LOG_CONFIG['log_dir'].exists()}")
        print(f"性能日志配置目录存在: {_LOG_CONFIG['performance_log_dir'].exists()}")
    except ImportError as e:
        print(f"导入日志配置失败: {e}")
    
    # 测试性能监控初始化
    print("\n=== 测试性能监控初始化 ===")
    try:
        from backend.utils.performance_init import init_performance_monitor
        monitor = init_performance_monitor(enabled=True)
        if monitor:
            print(f"性能监控数据目录: {monitor.data_dir}")
            print(f"性能监控启用状态: {monitor.enabled}")
        else:
            print("性能监控初始化失败")
    except ImportError as e:
        print(f"导入性能监控失败: {e}")
    except Exception as e:
        print(f"性能监控初始化异常: {e}")

def create_log_structure():
    """创建标准的日志目录结构"""
    print("\n=== 创建日志目录结构 ===")
    
    # 创建 logs 目录
    logs_dir = project_root / "logs"
    logs_dir.mkdir(exist_ok=True)
    print(f"创建日志目录: {logs_dir}")
    
    # 创建 logs/performance 目录
    performance_dir = logs_dir / "performance"
    performance_dir.mkdir(exist_ok=True)
    print(f"创建性能日志目录: {performance_dir}")
    
    # 创建 README 文件说明目录结构
    readme_content = """# 日志目录结构说明

## 目录结构
```
logs/
├── app.log              # 应用主日志文件
├── error.log            # 错误日志文件
├── session.log          # 会话日志文件
└── performance/         # 性能日志目录
    ├── performance_*.json   # 性能监控数据文件
    └── performance_backup/  # 性能日志备份目录（如需要）
```

## 日志文件说明

### 主要日志文件（存放在 logs/ 目录下）
- **app.log**: 应用程序主要日志，记录业务流程、状态切换、调试信息
- **error.log**: 错误和异常日志，记录ERROR和CRITICAL级别的日志
- **session.log**: 会话操作日志，记录用户会话相关的操作

### 性能日志文件（存放在 logs/performance/ 目录下）
- **performance_*.json**: 性能监控数据文件，包含API响应时间、资源使用情况等
- **performance_backup/**: 性能日志备份目录，当主目录不可用时使用

## 配置说明
- 所有日志文件都存放在项目根目录的 `logs` 目录下
- 性能相关的日志文件存放在 `logs/performance` 子目录下
- 日志文件支持自动轮转，默认单文件最大10MB，保留7个备份文件
"""
    
    readme_path = logs_dir / "README.md"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print(f"创建说明文件: {readme_path}")

if __name__ == "__main__":
    # 创建日志目录结构
    create_log_structure()
    
    # 验证日志目录结构
    verify_log_structure()
    
    print("\n=== 验证完成 ===")
