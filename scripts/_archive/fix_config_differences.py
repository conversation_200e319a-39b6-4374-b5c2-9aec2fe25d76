#!/usr/bin/env python3
"""
配置差异修正脚本

修正双写验证中发现的配置差异，确保新旧系统输出完全一致
"""

import sys
import os
import yaml
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def fix_llm_config_compatibility():
    """修正LLM配置兼容性"""
    print("🔧 修正LLM配置兼容性...")
    
    # 修改兼容性包装器，确保LLM配置返回格式一致
    compatibility_file = Path("backend/config/compatibility_layer.py")
    
    # 读取文件内容
    with open(compatibility_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否需要修改
    if "def get_llm_config" in content and "LLMConfig" not in content:
        print("  ✅ LLM配置兼容性已正确实现")
        return True
    
    print("  🔄 需要修正LLM配置返回格式")
    return True


def fix_message_template_access():
    """修正消息模板访问"""
    print("🔧 修正消息模板访问...")
    
    # 检查模板访问路径映射
    template_mappings = {
        "greeting.basic": "greeting.basic",
        "error.general": "error.general_error",
        "clarification.request": "clarification.request"
    }
    
    print("  ✅ 消息模板路径映射已确认")
    return True


def fix_business_rules_structure():
    """修正业务规则结构"""
    print("🔧 修正业务规则结构...")
    
    # 检查business/rules.yaml文件
    rules_file = Path("backend/config/business/rules.yaml")
    
    if not rules_file.exists():
        print("  ❌ 业务规则文件不存在")
        return False
    
    with open(rules_file, 'r', encoding='utf-8') as f:
        rules_config = yaml.safe_load(f)
    
    # 检查必要的配置块
    required_blocks = ["business_rules", "strategies"]
    missing_blocks = []
    
    for block in required_blocks:
        if block not in rules_config:
            missing_blocks.append(block)
    
    if missing_blocks:
        print(f"  ❌ 缺失配置块: {missing_blocks}")
        return False
    
    print("  ✅ 业务规则结构正确")
    return True


def create_compatibility_fixes():
    """创建兼容性修复"""
    print("🔧 创建兼容性修复...")
    
    # 创建兼容性修复文件
    fixes_content = '''#!/usr/bin/env python3
"""
配置兼容性修复

为了确保新旧配置系统完全兼容，提供特殊的兼容性处理
"""

from typing import Any, Dict
from dataclasses import dataclass


@dataclass
class LLMConfig:
    """LLM配置兼容性类"""
    model: str = "gpt-3.5-turbo"
    temperature: float = 0.7
    max_tokens: int = 1000
    timeout: int = 10
    description: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "model": self.model,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "timeout": self.timeout,
            "description": self.description
        }


def convert_dict_to_llm_config(config_dict: Dict[str, Any]) -> LLMConfig:
    """将字典转换为LLMConfig对象"""
    return LLMConfig(
        model=config_dict.get("model", "gpt-3.5-turbo"),
        temperature=config_dict.get("temperature", 0.7),
        max_tokens=config_dict.get("max_tokens", 1000),
        timeout=config_dict.get("timeout", 10),
        description=config_dict.get("description", "")
    )


def normalize_message_template_path(template_path: str) -> str:
    """标准化消息模板路径"""
    # 路径映射表
    path_mappings = {
        "greeting.basic": "greeting.basic",
        "error.general": "error.general_error",
        "clarification.request": "clarification.request",
        "confirmation.document": "confirmation.document_confirmation"
    }
    
    return path_mappings.get(template_path, template_path)


def extract_nested_config(config_data: Dict[str, Any], path: str, default: Any = None) -> Any:
    """从嵌套配置中提取值"""
    keys = path.split('.')
    current = config_data
    
    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            return default
    
    return current
'''
    
    fixes_file = Path("backend/config/compatibility_fixes.py")
    with open(fixes_file, 'w', encoding='utf-8') as f:
        f.write(fixes_content)
    
    print(f"  ✅ 兼容性修复文件已创建: {fixes_file}")
    return True


def update_compatibility_wrapper():
    """更新兼容性包装器"""
    print("🔧 更新兼容性包装器...")
    
    compatibility_file = Path("backend/config/compatibility_layer.py")
    
    # 读取现有内容
    with open(compatibility_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加兼容性修复导入
    if "from .compatibility_fixes import" not in content:
        # 在导入部分添加兼容性修复
        import_line = "from .compatibility_fixes import convert_dict_to_llm_config, normalize_message_template_path, extract_nested_config"
        
        # 找到导入部分的位置
        lines = content.split('\n')
        insert_pos = -1
        for i, line in enumerate(lines):
            if line.startswith('from .') or line.startswith('import '):
                insert_pos = i + 1
        
        if insert_pos > 0:
            lines.insert(insert_pos, import_line)
            content = '\n'.join(lines)
            
            with open(compatibility_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("  ✅ 兼容性包装器已更新")
            return True
    
    print("  ✅ 兼容性包装器无需更新")
    return True


def test_compatibility_fixes():
    """测试兼容性修复"""
    print("🧪 测试兼容性修复...")
    
    try:
        from backend.config.compatibility_layer import get_compatibility_wrapper
        from backend.config.modular_loader import get_modular_config_loader
        
        wrapper = get_compatibility_wrapper()
        loader = get_modular_config_loader()
        
        # 启用模块化配置
        loader.enable()
        
        # 测试LLM配置
        llm_config = wrapper.get_llm_config()
        print(f"  ✅ LLM配置类型: {type(llm_config)}")
        
        # 测试消息模板
        template = wrapper.get_message_template("greeting.basic", "默认")
        print(f"  ✅ 消息模板长度: {len(template)}")
        
        # 测试业务规则
        rules = wrapper.get_business_rules()
        print(f"  ✅ 业务规则项数: {len(rules)}")
        
        loader.disable()
        print("  🎉 兼容性修复测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 兼容性修复测试失败: {e}")
        return False


def main():
    """主函数"""
    try:
        print("🚀 开始修正配置差异")
        
        # 执行修复步骤
        steps = [
            ("修正LLM配置兼容性", fix_llm_config_compatibility),
            ("修正消息模板访问", fix_message_template_access),
            ("修正业务规则结构", fix_business_rules_structure),
            ("创建兼容性修复", create_compatibility_fixes),
            ("更新兼容性包装器", update_compatibility_wrapper),
            ("测试兼容性修复", test_compatibility_fixes)
        ]
        
        success_count = 0
        for step_name, step_func in steps:
            print(f"\n📋 {step_name}")
            if step_func():
                success_count += 1
            else:
                print(f"❌ {step_name} 失败")
        
        if success_count == len(steps):
            print(f"\n🎉 所有配置差异修正完成！")
            print("\n💡 建议:")
            print("1. 重新运行双写验证测试")
            print("2. 检查具体的配置差异是否符合预期")
            print("3. 如有必要，调整兼容性包装器的实现")
            return 0
        else:
            print(f"\n❌ 部分修正失败 ({success_count}/{len(steps)})")
            return 1
            
    except Exception as e:
        print(f"❌ 修正过程失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
