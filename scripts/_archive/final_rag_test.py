#!/usr/bin/env python3
"""
最终RAG系统测试

在新的Python进程中测试RAG系统，避免ChromaDB单例问题
"""

import sys
import os
import subprocess
import time
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_rag_in_subprocess():
    """在子进程中测试RAG系统"""
    print("🔄 在新进程中测试RAG系统...")
    
    # 创建测试脚本
    test_script = '''
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_rag_system():
    try:
        print("🤖 测试RAG代理创建...")
        from backend.agents.factory import agent_factory
        
        rag_agent = agent_factory.get_rag_knowledge_base_agent()
        
        if rag_agent:
            print("  ✅ RAG代理创建成功")
            print(f"  📊 代理类型: {type(rag_agent).__name__}")
            
            # 测试配置访问
            if hasattr(rag_agent, 'config'):
                config = rag_agent.config
                print("  ✅ RAG代理配置访问正常")
                print(f"  📊 知识库启用: {config.enabled}")
                print(f"  📊 ChromaDB路径: {config.chroma_db.get('path', 'N/A')}")
                
                # 测试ChromaDB连接
                if hasattr(rag_agent, '_chroma_client') and rag_agent._chroma_client:
                    print("  ✅ ChromaDB客户端初始化成功")
                    
                    # 测试集合访问
                    if hasattr(rag_agent, '_collection') and rag_agent._collection:
                        print("  ✅ ChromaDB集合访问成功")
                        print(f"  📊 集合名称: {rag_agent._collection.name}")
                        print(f"  📊 文档数量: {rag_agent._collection.count()}")
                        return True
                    else:
                        print("  ⚠️  ChromaDB集合未初始化")
                        return False
                else:
                    print("  ❌ ChromaDB客户端初始化失败")
                    return False
            else:
                print("  ❌ RAG代理配置访问失败")
                return False
        else:
            print("  ❌ RAG代理创建失败")
            return False
            
    except Exception as e:
        print(f"  ❌ RAG系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_rag_system()
    exit(0 if success else 1)
'''
    
    # 写入临时测试文件
    test_file = Path("temp_rag_test.py")
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    try:
        # 在子进程中运行测试
        result = subprocess.run(
            [sys.executable, str(test_file)],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        success = result.returncode == 0
        
        # 清理临时文件
        test_file.unlink()
        
        return success
        
    except subprocess.TimeoutExpired:
        print("  ❌ 测试超时")
        test_file.unlink()
        return False
    except Exception as e:
        print(f"  ❌ 子进程测试失败: {e}")
        if test_file.exists():
            test_file.unlink()
        return False


def test_rag_query_functionality():
    """测试RAG查询功能"""
    print("\n💬 测试RAG查询功能...")
    
    # 创建查询测试脚本
    query_script = '''
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_rag_query():
    try:
        from backend.agents.factory import agent_factory
        
        rag_agent = agent_factory.get_rag_knowledge_base_agent()
        
        if not rag_agent:
            print("❌ RAG代理创建失败")
            return False
        
        # 添加测试文档
        test_docs = [
            "由己平台是一个智能需求采集系统，帮助用户整理和记录业务需求。",
            "系统支持多种文档格式，包括Markdown和纯文本文件。",
            "平台提供智能问答功能，可以根据用户输入生成相关建议。"
        ]
        
        test_ids = [f"test_doc_{i}" for i in range(len(test_docs))]
        
        # 添加文档到知识库
        if hasattr(rag_agent, '_collection') and rag_agent._collection:
            rag_agent._collection.add(
                documents=test_docs,
                ids=test_ids
            )
            print(f"✅ 成功添加 {len(test_docs)} 个测试文档")
            
            # 测试查询
            query = "由己平台是什么？"
            print(f"🔍 查询: {query}")
            
            results = rag_agent._collection.query(
                query_texts=[query],
                n_results=2
            )
            
            if results['documents'] and results['documents'][0]:
                print(f"✅ 查询成功，找到 {len(results['documents'][0])} 个相关文档")
                for i, doc in enumerate(results['documents'][0]):
                    distance = results['distances'][0][i] if results['distances'] else 0
                    print(f"  📄 文档 {i+1}: {doc[:50]}... (相似度: {1-distance:.3f})")
                
                # 清理测试数据
                rag_agent._collection.delete(ids=test_ids)
                print("✅ 测试数据已清理")
                return True
            else:
                print("❌ 查询失败，未找到相关文档")
                return False
        else:
            print("❌ ChromaDB集合未初始化")
            return False
            
    except Exception as e:
        print(f"❌ RAG查询测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_rag_query()
    exit(0 if success else 1)
'''
    
    # 写入临时测试文件
    query_test_file = Path("temp_rag_query_test.py")
    with open(query_test_file, 'w', encoding='utf-8') as f:
        f.write(query_script)
    
    try:
        # 在子进程中运行查询测试
        result = subprocess.run(
            [sys.executable, str(query_test_file)],
            capture_output=True,
            text=True,
            timeout=120
        )
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        success = result.returncode == 0
        
        # 清理临时文件
        query_test_file.unlink()
        
        return success
        
    except subprocess.TimeoutExpired:
        print("  ❌ 查询测试超时")
        query_test_file.unlink()
        return False
    except Exception as e:
        print(f"  ❌ 查询测试失败: {e}")
        if query_test_file.exists():
            query_test_file.unlink()
        return False


def main():
    """主函数"""
    try:
        print("🚀 最终RAG系统测试")
        print("=" * 50)
        
        # 测试RAG系统基本功能
        basic_test_passed = test_rag_in_subprocess()
        
        # 测试RAG查询功能
        query_test_passed = test_rag_query_functionality()
        
        # 输出测试结果
        print(f"\n📊 测试结果总结:")
        print("=" * 50)
        print(f"基本功能测试: {'✅ 通过' if basic_test_passed else '❌ 失败'}")
        print(f"查询功能测试: {'✅ 通过' if query_test_passed else '❌ 失败'}")
        
        if basic_test_passed and query_test_passed:
            print("\n🎉 RAG系统测试完全通过！")
            print("✅ RAG知识库系统已完全修复并正常工作")
            print("\n💡 系统现在可以正常处理知识库查询，包括：")
            print("  - ChromaDB连接和集合管理")
            print("  - 文档向量化和存储")
            print("  - 语义相似度检索")
            print("  - 查询结果排序和过滤")
            return 0
        elif basic_test_passed:
            print("\n⚠️  RAG系统基本功能正常，但查询功能需要进一步检查")
            return 0
        else:
            print("\n❌ RAG系统仍存在问题，需要进一步排查")
            return 1
            
    except Exception as e:
        print(f"❌ 测试过程失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
