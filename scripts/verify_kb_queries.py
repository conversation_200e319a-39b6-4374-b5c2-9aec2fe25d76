#!/usr/bin/env python3
"""
验证知识库检索效果
- 使用 RAG 代理的集合执行一组预置查询
- 统计命中率与相似度（1 - distance）
- 生成 JSON 与 Markdown 报告

用法：
  python scripts/verify_kb_queries.py
"""

import sys
import os
import json
from pathlib import Path
from statistics import mean
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

QUERIES = [
    "由己平台是什么？",
    "由己平台支持哪些功能？",
    "如何使用由己平台进行需求采集？",
    "知识库如何维护与导入？",
    "日志与监控如何查看？",
    "RAG 知识库如何工作？",
]

TOP_K = 3
REPORT_JSON = Path("docs/kb_verification_report.json")
REPORT_MD = Path("docs/kb_verification_report.md")


def verify():
    from backend.agents.factory import agent_factory

    rag = agent_factory.get_rag_knowledge_base_agent()
    if not rag or not getattr(rag, "_collection", None):
        raise RuntimeError("无法获取RAG集合")

    col = rag._collection

    results = []
    successes = 0
    top1_sims = []
    avg_sims = []

    for q in QUERIES:
        try:
            res = col.query(query_texts=[q], n_results=TOP_K)
            docs = (res.get("documents") or [[]])[0] or []
            dists = (res.get("distances") or [[]])[0] or []
            sims = [max(0.0, 1 - d) for d in dists] if dists else []
            hit = len(docs) > 0
            if hit:
                successes += 1
                if sims:
                    top1_sims.append(sims[0])
                    avg_sims.append(mean(sims))
            results.append({
                "query": q,
                "hits": len(docs),
                "top_k": TOP_K,
                "top1_similarity": sims[0] if sims else 0.0,
                "avg_similarity": mean(sims) if sims else 0.0,
                "documents_preview": [d[:80] for d in docs]
            })
        except Exception as e:
            results.append({
                "query": q,
                "error": str(e)
            })

    summary = {
        "time": datetime.now().isoformat(),
        "total_queries": len(QUERIES),
        "successes": successes,
        "success_rate": round(successes / max(1, len(QUERIES)), 3),
        "avg_top1_similarity": round(mean(top1_sims), 3) if top1_sims else 0.0,
        "avg_mean_similarity": round(mean(avg_sims), 3) if avg_sims else 0.0,
        "top_k": TOP_K,
    }

    report = {
        "summary": summary,
        "details": results
    }

    REPORT_JSON.parent.mkdir(parents=True, exist_ok=True)
    REPORT_JSON.write_text(json.dumps(report, ensure_ascii=False, indent=2), encoding="utf-8")

    # Markdown 简报
    lines = []
    lines.append(f"# 知识库检索验证报告\n")
    lines.append(f"- 时间: {summary['time']}")
    lines.append(f"- 查询数: {summary['total_queries']}, 命中: {summary['successes']} ({summary['success_rate']*100:.1f}%)")
    lines.append(f"- 平均Top1相似度: {summary['avg_top1_similarity']}")
    lines.append(f"- 平均均值相似度: {summary['avg_mean_similarity']}")
    lines.append("")
    lines.append("## 详情")
    for r in results:
        if "error" in r:
            lines.append(f"- Q: {r['query']}\n  - ❌ 错误: {r['error']}")
        else:
            lines.append(
                f"- Q: {r['query']}\n  - ✅ 命中: {r['hits']}\n  - Top1相似度: {r['top1_similarity']:.3f}\n  - 平均相似度: {r['avg_similarity']:.3f}\n  - 预览: {(' | '.join(r['documents_preview']))}"
            )
    REPORT_MD.write_text("\n".join(lines), encoding="utf-8")

    return report


def main():
    try:
        report = verify()
        s = report["summary"]
        print("✅ 验证完成")
        print(f"- 查询数: {s['total_queries']}, 命中: {s['successes']} ({s['success_rate']*100:.1f}%)")
        print(f"- 平均Top1相似度: {s['avg_top1_similarity']}")
        print(f"- 平均均值相似度: {s['avg_mean_similarity']}")
        print(f"- 报告: {REPORT_JSON} | {REPORT_MD}")
        return 0
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    raise SystemExit(main())

