#!/usr/bin/env python3
"""
补写已导入文档的 role 元数据：根据 source_file 路径包含 company/developer 自动标注
用法：
  python scripts/backfill_role_metadata.py
"""

import sys
import os
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def detect_role_from_path(path_str: str) -> str | None:
    p = Path(path_str)
    parts = {x.lower() for x in p.parts}
    if "company" in parts:
        return "company"
    if "developer" in parts:
        return "developer"
    return None


def main():
    from backend.agents.factory import agent_factory
    rag = agent_factory.get_rag_knowledge_base_agent()
    if not rag or not getattr(rag, "_collection", None):
        print("❌ 无法获取RAG集合")
        return 1

    col = rag._collection
    try:
        data = col.get(include=["metadatas"])  # ids 总是返回，无需写在 include
        metadatas = data.get("metadatas") or []
        ids = data.get("ids") or []
    except Exception as e:
        print(f"❌ 获取集合失败: {e}")
        return 1

    to_update_ids = []
    to_update_metas = []
    for idx, meta in enumerate(metadatas):
        if not isinstance(meta, dict):
            continue
        role = meta.get("role")
        if role:
            continue
        source_file = meta.get("source_file") or meta.get("source") or ""
        if not source_file:
            continue
        detected = detect_role_from_path(source_file)
        if detected:
            # 只更新 role 字段
            new_meta = dict(meta)
            new_meta["role"] = detected
            to_update_ids.append(ids[idx])
            to_update_metas.append(new_meta)

    if not to_update_ids:
        print("ℹ️  没有需要补写的记录")
        return 0

    try:
        col.update(ids=to_update_ids, metadatas=to_update_metas)
        print(f"✅ 已补写 role 字段: {len(to_update_ids)} 条")
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    raise SystemExit(main())

