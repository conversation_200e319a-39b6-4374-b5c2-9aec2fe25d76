#!/usr/bin/env python3
"""
高优先级文件迁移脚本
自动将高优先级文件从统一配置迁移到模块化配置
"""

import re
from pathlib import Path

def migrate_file(file_path):
    """迁移单个文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换导入语句
        content = re.sub(
            r'from \.unified_config_loader import get_unified_config',
            'from .modular_loader import get_modular_config_loader',
            content
        )
        
        # 替换实例化
        content = re.sub(
            r'get_unified_config\(\)',
            'get_modular_config_loader()',
            content
        )
        
        # 替换方法调用
        content = re.sub(
            r'\.get_config_value\(',
            '.get_config(',
            content
        )
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 迁移完成: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {file_path}, 错误: {e}")
        return False

def main():
    """主函数"""
    high_priority_files = [
        "backend/tasks/config_monitoring_scheduler.py",
        "backend/agents/session_context.py",
        "backend/agents/unified_llm_client_factory.py",
        "backend/agents/knowledge_base.py",
        "backend/agents/keyword_accelerator.py",
        "backend/agents/decision_engine_factory.py",
        "backend/agents/agent_instance_pool.py",
        "backend/agents/template_version_manager.py",
        "backend/agents/llm_service.py",
        "backend/agents/category_classifier.py",
        "backend/agents/message_reply_manager.py",
        "backend/agents/conversation_flow_reply_mixin.py",
        "backend/agents/review_and_refine.py",
        "backend/agents/document_generator.py",
        "backend/agents/simplified_decision_engine.py",
        "backend/agents/conversation_state_machine.py",
        "backend/agents/information_extractor.py",
        "backend/agents/rag_knowledge_base_agent.py",
        "backend/agents/intent_classification_llm.py",
        "backend/agents/unified_state_manager.py",
    ]
    
    print("🚀 开始迁移高优先级文件...")
    success_count = 0
    
    for file_path in high_priority_files:
        if migrate_file(Path(file_path)):
            success_count += 1
    
    print(f"\n📊 迁移完成: {success_count}/{len(high_priority_files)} 个文件")

if __name__ == "__main__":
    main()
