#!/usr/bin/env python3
"""
将单个文件导入到知识库集合（md/txt）
用法：
  python scripts/import_single_file.py --file "docs/由己帮助/general.md" --chunk-size 800 --overlap 100 --min-chunk-length 50
"""

import sys
import os
from pathlib import Path
import argparse
import uuid

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def parse_args():
    p = argparse.ArgumentParser(description="Import a single file into KB")
    p.add_argument("--file", required=True, help="Path to the file (.md/.txt)")
    p.add_argument("--chunk-size", type=int, default=800)
    p.add_argument("--overlap", type=int, default=100)
    p.add_argument("--min-chunk-length", type=int, default=50)
    p.add_argument("--dry-run", action="store_true")
    return p.parse_args()


def normalize_newlines(text: str) -> str:
    return text.replace("\r\n", "\n").replace("\r", "\n")


def split_into_chunks(text: str, chunk_size: int, overlap: int, min_len: int) -> list[str]:
    text = normalize_newlines(text)
    paras = [p.strip() for p in text.split("\n\n") if p.strip()]
    chunks: list[str] = []
    buf = ""
    for para in paras:
        if not buf:
            buf = para
        elif len(buf) + 2 + len(para) <= chunk_size:
            buf = f"{buf}\n\n{para}"
        else:
            if len(buf) >= min_len:
                chunks.append(buf)
            if overlap > 0 and len(buf) > overlap:
                prefix = buf[-overlap:]
                buf = f"{prefix}\n\n{para}"
            else:
                buf = para
    if buf and len(buf) >= min_len:
        chunks.append(buf)
    if not chunks and len(text) >= min_len:
        chunks = [text[:chunk_size]]
    return chunks


def main():
    args = parse_args()
    fpath = Path(args.file)
    if not fpath.exists():
        print(f"❌ 文件不存在: {fpath}")
        return 1

    try:
        text = fpath.read_text(encoding="utf-8", errors="ignore")
    except Exception as e:
        print(f"❌ 读取失败: {e}")
        return 1

    chunks = split_into_chunks(text, args.chunk_size, args.overlap, args.min_chunk_length)
    print(f"📄 文件: {fpath}，分块: {len(chunks)}")
    if args.dry_run:
        for i, c in enumerate(chunks[:3]):
            preview = c[:80].replace("\n", " ")
            print(f"  预览[{i}]: {preview}...")
        return 0

    try:
        from backend.agents.factory import agent_factory
        rag = agent_factory.get_rag_knowledge_base_agent()
        if not rag or not getattr(rag, "_collection", None):
            print("❌ 无法获取RAG集合")
            return 1
        collection = rag._collection
    except Exception as e:
        print(f"❌ 初始化RAG失败: {e}")
        return 1

    base_id = uuid.uuid4().hex
    ids = [f"kb_single_{base_id}_{i}" for i in range(len(chunks))]
    total = len(chunks)
    metadatas = [
        {
            "source_file": str(fpath),
            "chunk_index": i,
            "total_chunks": total,
            "role": None
        }
        for i in range(total)
    ]

    try:
        collection.add(documents=chunks, ids=ids, metadatas=metadatas)
        print(f"✅ 已导入 {len(chunks)} 块。集合总量: {collection.count()}")
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    raise SystemExit(main())

