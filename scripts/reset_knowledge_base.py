#!/usr/bin/env python3
"""
reset_knowledge_base.py

生产环境（未上线）知识库重建工具：
- 备份 backend/data/chroma_db -> backend/data/chroma_db_backup_YYYYmmdd_HHMMSS
- 删除 backend/data/chroma_db
- 重建 ChromaDB 持久化客户端与集合（hybrid_knowledge_base, cosine, 禁用遥测）
- 可选 --seed 调用种子脚本导入数据并验证

使用:
  python scripts/reset_knowledge_base.py --seed
  python scripts/reset_knowledge_base.py --path backend/data/chroma_db
"""

import sys
import os
import shutil
from pathlib import Path
from datetime import datetime
import subprocess

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

DEFAULT_PATH = "backend/data/chroma_db"
COLLECTION_NAME = "hybrid_knowledge_base"


def parse_args():
    import argparse
    p = argparse.ArgumentParser(description="Reset knowledge base (backup -> delete -> recreate -> optional seed)")
    p.add_argument("--path", default=DEFAULT_PATH, help="ChromaDB data path (default: backend/data/chroma_db)")
    p.add_argument("--no-backup", action="store_true", help="Do not backup before deleting")
    p.add_argument("--seed", action="store_true", help="Run seed_knowledge_base.py after reset")
    return p.parse_args()


def backup_dir(path: Path) -> Path | None:
    if not path.exists():
        print(f"ℹ️  {path} 不存在，跳过备份")
        return None
    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup = path.parent / f"{path.name}_backup_{ts}"
    print(f"🔄 备份 {path} -> {backup}")
    shutil.copytree(path, backup)
    # 简单统计
    total_files = sum(len(files) for _, _, files in os.walk(backup))
    size_bytes = sum((Path(root) / f).stat().st_size for root, _, files in os.walk(backup) for f in files)
    print(f"  ✅ 备份完成，文件数: {total_files}，大小: {size_bytes/1024/1024:.2f} MB")
    return backup


def delete_dir(path: Path):
    if path.exists():
        print(f"🗑️  删除 {path}")
        shutil.rmtree(path)
        print("  ✅ 删除完成")
    else:
        print(f"ℹ️  {path} 不存在，跳过删除")


def recreate_kb(path: Path):
    print("🚀 重建 ChromaDB 客户端与集合...")
    path.mkdir(parents=True, exist_ok=True)
    try:
        import chromadb
        settings = chromadb.Settings(
            anonymized_telemetry=False,
            allow_reset=True,
            is_persistent=True
        )
        client = chromadb.PersistentClient(path=str(path), settings=settings)
        # 先删除同名集合（如果存在）
        try:
            client.delete_collection(name=COLLECTION_NAME)
        except Exception:
            pass
        # 创建集合（cosine）
        from chromadb.utils import embedding_functions
        embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
            model_name="moka-ai/m3e-base"
        )
        col = client.create_collection(
            name=COLLECTION_NAME,
            embedding_function=embedding_function,
            metadata={"hnsw:space": "cosine"}
        )
        print(f"  ✅ 集合创建成功: {COLLECTION_NAME}，当前文档数: {col.count()}")
        return True
    except Exception as e:
        print(f"  ❌ 重建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_seed():
    print("🌱 运行种子导入脚本...")
    try:
        result = subprocess.run([sys.executable, "scripts/seed_knowledge_base.py"], capture_output=True, text=True, timeout=300)
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"  ❌ 种子导入失败: {e}")
        return False


def main():
    args = parse_args()
    kb_path = Path(args.path)

    print("=== 知识库重建开始 ===")
    print(f"目标路径: {kb_path}")

    backup_path = None
    if not args.no_backup:
        backup_path = backup_dir(kb_path)

    delete_dir(kb_path)

    ok = recreate_kb(kb_path)
    if not ok:
        print("❌ 重建失败，准备回滚...")
        if backup_path and not kb_path.exists():
            print("↩️  回滚：恢复备份目录")
            shutil.copytree(backup_path, kb_path)
            print("  ✅ 已从备份恢复")
        return 1

    if args.seed:
        if run_seed():
            print("✅ 种子导入完成")
        else:
            print("⚠️  种子导入失败，可稍后手动执行 scripts/seed_knowledge_base.py")

    print("=== 知识库重建完成 ===")
    return 0


if __name__ == "__main__":
    raise SystemExit(main())

