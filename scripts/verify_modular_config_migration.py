#!/usr/bin/env python3
"""
模块化配置迁移验证脚本

验证从统一配置到模块化配置的完整迁移：
1. 验证模块化配置加载器工作正常
2. 验证所有关键配置都能正确加载
3. 验证配置服务接口正常工作
4. 检查是否还有旧配置系统的残留
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_modular_config_loader():
    """测试模块化配置加载器"""
    print("🔧 测试模块化配置加载器...")
    
    try:
        from backend.config.modular_loader import get_modular_config_loader
        
        loader = get_modular_config_loader()
        loader.enable()
        
        # 测试关键配置模块
        test_configs = [
            "business.rules",
            "business.templates", 
            "business.thresholds",
            "llm.models",
            "data.knowledge_base",
            "system.base"
        ]
        
        success_count = 0
        for config_key in test_configs:
            config_data = loader.get_config(config_key, {})
            if config_data:
                print(f"  ✅ {config_key}: {len(config_data)} 项配置")
                success_count += 1
            else:
                print(f"  ❌ {config_key}: 配置为空或不存在")
        
        print(f"  📊 配置加载成功率: {success_count}/{len(test_configs)}")
        return success_count == len(test_configs)
        
    except Exception as e:
        print(f"  ❌ 模块化配置加载器测试失败: {e}")
        return False

def test_config_service():
    """测试配置服务接口"""
    print("\n🔧 测试配置服务接口...")
    
    try:
        from backend.config.service import config_service
        
        # 测试LLM配置获取
        llm_config = config_service.get_llm_config("default")
        print(f"  ✅ LLM配置获取成功: {type(llm_config)}")
        
        # 测试业务规则获取
        retry_rule = config_service.get_business_rule("retry.max_attempts", 3)
        print(f"  ✅ 业务规则获取成功: {retry_rule}")
        
        # 测试消息模板获取
        template = config_service.get_message_template("greeting.basic")
        print(f"  ✅ 消息模板获取成功: {len(template)} 字符")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 配置服务接口测试失败: {e}")
        return False

def test_preloader():
    """测试配置预加载器"""
    print("\n🔧 测试配置预加载器...")
    
    try:
        from backend.config.preloader import config_preloader
        
        # 检查预加载配置列表
        config_order = config_preloader.config_load_order
        print(f"  📋 预加载配置列表: {config_order}")
        
        # 验证配置列表是否为模块化格式
        modular_format = all('.' in config for config in config_order)
        if modular_format:
            print("  ✅ 预加载器已使用模块化配置格式")
        else:
            print("  ❌ 预加载器仍使用旧配置格式")
        
        return modular_format
        
    except Exception as e:
        print(f"  ❌ 配置预加载器测试失败: {e}")
        return False

def check_old_config_usage():
    """检查是否还有旧配置系统的使用"""
    print("\n🔍 检查旧配置系统残留...")
    
    backend_dir = project_root / "backend"
    old_imports = []
    
    # 搜索旧配置导入
    for py_file in backend_dir.rglob("*.py"):
        if py_file.name.startswith('.') or '__pycache__' in str(py_file):
            continue
            
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查是否还有旧配置导入
            if 'from .unified_config_loader import' in content:
                old_imports.append(str(py_file.relative_to(project_root)))
            elif 'unified_config_loader' in content and 'import' in content:
                old_imports.append(str(py_file.relative_to(project_root)))
                
        except Exception as e:
            print(f"  ⚠️ 无法读取文件 {py_file}: {e}")
    
    if old_imports:
        print("  ❌ 发现旧配置系统残留:")
        for file_path in old_imports[:10]:  # 只显示前10个
            print(f"    - {file_path}")
        if len(old_imports) > 10:
            print(f"    ... 还有 {len(old_imports) - 10} 个文件")
        return False
    else:
        print("  ✅ 未发现旧配置系统残留")
        return True

def main():
    """主函数"""
    print("🚀 开始模块化配置迁移验证\n")
    
    tests = [
        ("模块化配置加载器", test_modular_config_loader),
        ("配置服务接口", test_config_service),
        ("配置预加载器", test_preloader),
        ("旧配置残留检查", check_old_config_usage),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果摘要
    print("\n" + "="*50)
    print("📊 测试结果摘要:")
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {status} {test_name}")
        if success:
            success_count += 1
    
    print(f"\n🎯 总体成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count == len(results):
        print("🎉 模块化配置迁移验证完全成功！")
        return True
    else:
        print("⚠️ 模块化配置迁移存在问题，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
