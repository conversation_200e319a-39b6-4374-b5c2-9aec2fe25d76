#!/usr/bin/env python3
"""
Pre-commit 设置脚本

自动安装和配置pre-commit钩子
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(cmd, check=True, capture_output=False):
    """运行命令"""
    print(f"执行命令: {' '.join(cmd)}")
    try:
        result = subprocess.run(
            cmd, 
            check=check, 
            capture_output=capture_output, 
            text=True,
            cwd=Path(__file__).parent.parent
        )
        if capture_output:
            return result.stdout.strip()
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if capture_output:
            print(f"错误输出: {e.stderr}")
        return False


def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True


def check_git_repo():
    """检查是否在Git仓库中"""
    print("检查Git仓库...")
    if not Path(".git").exists():
        print("❌ 当前目录不是Git仓库")
        return False
    
    print("✅ Git仓库检查通过")
    return True


def install_pre_commit():
    """安装pre-commit"""
    print("安装pre-commit...")
    
    # 检查是否已安装
    try:
        version = run_command(["pre-commit", "--version"], capture_output=True)
        if version:
            print(f"✅ pre-commit已安装: {version}")
            return True
    except:
        pass
    
    # 尝试使用pip安装
    success = run_command([sys.executable, "-m", "pip", "install", "pre-commit"])
    if success:
        print("✅ pre-commit安装成功")
        return True
    else:
        print("❌ pre-commit安装失败")
        return False


def install_project_dependencies():
    """安装项目依赖"""
    print("安装项目依赖...")
    
    dependencies = [
        "pydantic",
        "pyyaml", 
        "black",
        "isort",
        "flake8",
        "bandit"
    ]
    
    for dep in dependencies:
        print(f"安装 {dep}...")
        success = run_command([sys.executable, "-m", "pip", "install", dep])
        if not success:
            print(f"❌ {dep} 安装失败")
            return False
    
    print("✅ 项目依赖安装完成")
    return True


def setup_pre_commit_hooks():
    """设置pre-commit钩子"""
    print("设置pre-commit钩子...")
    
    # 检查配置文件是否存在
    config_file = Path(".pre-commit-config.yaml")
    if not config_file.exists():
        print("❌ .pre-commit-config.yaml 文件不存在")
        return False
    
    # 安装钩子
    success = run_command(["pre-commit", "install"])
    if not success:
        print("❌ pre-commit钩子安装失败")
        return False
    
    print("✅ pre-commit钩子安装成功")
    return True


def test_pre_commit():
    """测试pre-commit配置"""
    print("测试pre-commit配置...")
    
    # 运行所有钩子
    print("运行所有pre-commit钩子...")
    success = run_command(["pre-commit", "run", "--all-files"], check=False)
    
    if success:
        print("✅ 所有pre-commit钩子测试通过")
    else:
        print("⚠️  部分pre-commit钩子测试失败，这是正常的")
        print("请根据输出信息修复相关问题")
    
    return True


def create_git_hooks_info():
    """创建Git钩子信息文件"""
    print("创建Git钩子信息...")
    
    hooks_dir = Path(".githooks")
    hooks_dir.mkdir(exist_ok=True)
    
    info_content = """# Git钩子信息

## Pre-commit钩子

本项目使用pre-commit来确保代码质量和配置一致性。

### 安装的钩子

1. **基础检查**
   - 移除行尾空白
   - 确保文件以换行符结尾
   - YAML/JSON/TOML语法检查
   - 检查合并冲突标记
   - 检查大文件

2. **Python代码质量**
   - Black代码格式化
   - isort导入排序
   - flake8代码风格检查
   - bandit安全检查

3. **配置验证**
   - 配置Schema验证
   - 硬编码检测
   - 配置完整性检查
   - 禁止新增旧配置依赖
   - 关键配置键检查

### 使用方法

```bash
# 手动运行所有钩子
pre-commit run --all-files

# 运行特定钩子
pre-commit run config-schema-validation

# 跳过钩子提交（不推荐）
git commit --no-verify
```

### 更新钩子

```bash
pre-commit autoupdate
```
"""
    
    info_file = hooks_dir / "README.md"
    with open(info_file, 'w', encoding='utf-8') as f:
        f.write(info_content)
    
    print(f"✅ Git钩子信息已创建: {info_file}")
    return True


def main():
    """主函数"""
    print("开始设置pre-commit...")
    print("=" * 50)
    
    # 检查前置条件
    if not check_python_version():
        sys.exit(1)
    
    if not check_git_repo():
        sys.exit(1)
    
    # 安装依赖
    if not install_pre_commit():
        sys.exit(1)
    
    if not install_project_dependencies():
        sys.exit(1)
    
    # 设置钩子
    if not setup_pre_commit_hooks():
        sys.exit(1)
    
    # 创建信息文件
    create_git_hooks_info()
    
    # 测试配置
    test_pre_commit()
    
    print("\n" + "=" * 50)
    print("✅ pre-commit设置完成！")
    print("\n建议:")
    print("1. 运行 'pre-commit run --all-files' 检查现有文件")
    print("2. 查看 .githooks/README.md 了解更多信息")
    print("3. 提交代码时会自动运行钩子检查")
    print("\n如果遇到问题，可以使用 'git commit --no-verify' 跳过钩子")
    print("但请尽量修复问题而不是跳过检查")


if __name__ == "__main__":
    main()
