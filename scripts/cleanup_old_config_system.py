#!/usr/bin/env python3
"""
旧配置系统清理脚本

按照配置管理最佳实践，分阶段清理旧的统一配置系统：
1. 识别关键文件和测试文件
2. 提供迁移建议
3. 安全地移除或重构旧配置引用
"""

import sys
import os
from pathlib import Path
import re

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def analyze_old_config_usage():
    """分析旧配置系统的使用情况"""
    print("🔍 分析旧配置系统使用情况...")
    
    backend_dir = project_root / "backend"
    old_config_files = []
    
    # 搜索旧配置导入
    for py_file in backend_dir.rglob("*.py"):
        if py_file.name.startswith('.') or '__pycache__' in str(py_file):
            continue
            
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查是否还有旧配置导入
            if ('unified_config_loader' in content and 'import' in content) or \
               ('from .unified_config_loader import' in content):
                old_config_files.append({
                    'file': str(py_file.relative_to(project_root)),
                    'type': 'import',
                    'priority': get_file_priority(py_file)
                })
                
        except Exception as e:
            print(f"  ⚠️ 无法读取文件 {py_file}: {e}")
    
    return old_config_files

def get_file_priority(file_path):
    """确定文件的迁移优先级"""
    file_str = str(file_path)
    
    # 高优先级：核心业务文件
    if any(keyword in file_str for keyword in [
        'agents/', 'handlers/', 'core/', 'api/', 'tasks/'
    ]):
        return 'HIGH'
    
    # 中优先级：配置和工具文件
    elif any(keyword in file_str for keyword in [
        'config/', 'utils/', 'services/'
    ]):
        return 'MEDIUM'
    
    # 低优先级：测试文件
    elif 'test' in file_str:
        return 'LOW'
    
    return 'MEDIUM'

def generate_migration_plan(old_config_files):
    """生成迁移计划"""
    print("\n📋 生成迁移计划...")
    
    # 按优先级分组
    by_priority = {'HIGH': [], 'MEDIUM': [], 'LOW': []}
    for file_info in old_config_files:
        by_priority[file_info['priority']].append(file_info['file'])
    
    print(f"\n🔴 高优先级文件 ({len(by_priority['HIGH'])} 个):")
    for file_path in by_priority['HIGH'][:10]:  # 只显示前10个
        print(f"  - {file_path}")
    if len(by_priority['HIGH']) > 10:
        print(f"  ... 还有 {len(by_priority['HIGH']) - 10} 个文件")
    
    print(f"\n🟡 中优先级文件 ({len(by_priority['MEDIUM'])} 个):")
    for file_path in by_priority['MEDIUM'][:5]:  # 只显示前5个
        print(f"  - {file_path}")
    if len(by_priority['MEDIUM']) > 5:
        print(f"  ... 还有 {len(by_priority['MEDIUM']) - 5} 个文件")
    
    print(f"\n🟢 低优先级文件 ({len(by_priority['LOW'])} 个):")
    for file_path in by_priority['LOW'][:5]:  # 只显示前5个
        print(f"  - {file_path}")
    if len(by_priority['LOW']) > 5:
        print(f"  ... 还有 {len(by_priority['LOW']) - 5} 个文件")
    
    return by_priority

def suggest_migration_actions(by_priority):
    """建议迁移操作"""
    print("\n💡 迁移建议:")
    
    print("\n🔴 高优先级文件处理建议:")
    print("  1. 立即迁移到模块化配置")
    print("  2. 更新导入语句：unified_config_loader → modular_loader")
    print("  3. 更新方法调用：get_config_value() → get_config()")
    print("  4. 测试功能完整性")
    
    print("\n🟡 中优先级文件处理建议:")
    print("  1. 分批迁移，每次处理5-10个文件")
    print("  2. 重点关注配置文件和工具类")
    print("  3. 保持向后兼容性")
    
    print("\n🟢 低优先级文件处理建议:")
    print("  1. 最后处理测试文件")
    print("  2. 可以考虑暂时保留兼容层")
    print("  3. 逐步更新测试用例")
    
    print("\n⚠️ 风险控制建议:")
    print("  1. 每次迁移后运行完整测试")
    print("  2. 保留旧配置文件作为备份")
    print("  3. 分阶段部署，观察系统稳定性")

def create_migration_script(by_priority):
    """创建具体的迁移脚本"""
    print("\n🛠️ 创建迁移脚本...")
    
    # 创建高优先级文件的迁移脚本
    high_priority_script = project_root / "scripts" / "migrate_high_priority_files.py"
    
    script_content = '''#!/usr/bin/env python3
"""
高优先级文件迁移脚本
自动将高优先级文件从统一配置迁移到模块化配置
"""

import re
from pathlib import Path

def migrate_file(file_path):
    """迁移单个文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换导入语句
        content = re.sub(
            r'from \.unified_config_loader import get_unified_config',
            'from .modular_loader import get_modular_config_loader',
            content
        )
        
        # 替换实例化
        content = re.sub(
            r'get_unified_config\(\)',
            'get_modular_config_loader()',
            content
        )
        
        # 替换方法调用
        content = re.sub(
            r'\.get_config_value\(',
            '.get_config(',
            content
        )
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 迁移完成: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {file_path}, 错误: {e}")
        return False

def main():
    """主函数"""
    high_priority_files = [
'''
    
    # 添加高优先级文件列表
    for file_path in by_priority['HIGH'][:20]:  # 限制前20个文件
        script_content += f'        "{file_path}",\n'
    
    script_content += '''    ]
    
    print("🚀 开始迁移高优先级文件...")
    success_count = 0
    
    for file_path in high_priority_files:
        if migrate_file(Path(file_path)):
            success_count += 1
    
    print(f"\\n📊 迁移完成: {success_count}/{len(high_priority_files)} 个文件")

if __name__ == "__main__":
    main()
'''
    
    with open(high_priority_script, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"  ✅ 创建迁移脚本: {high_priority_script}")
    
    return high_priority_script

def main():
    """主函数"""
    print("🚀 开始旧配置系统清理分析\n")
    
    # 分析旧配置使用情况
    old_config_files = analyze_old_config_usage()
    
    if not old_config_files:
        print("🎉 未发现旧配置系统残留，迁移完成！")
        return True
    
    # 生成迁移计划
    by_priority = generate_migration_plan(old_config_files)
    
    # 提供迁移建议
    suggest_migration_actions(by_priority)
    
    # 创建迁移脚本
    migration_script = create_migration_script(by_priority)
    
    print(f"\n🎯 下一步操作:")
    print(f"  1. 运行高优先级文件迁移: python {migration_script}")
    print(f"  2. 运行验证脚本: python scripts/verify_modular_config_migration.py")
    print(f"  3. 测试系统功能完整性")
    print(f"  4. 逐步处理中低优先级文件")
    
    return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
