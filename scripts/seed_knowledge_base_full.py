#!/usr/bin/env python3
"""
完整知识库种子脚本：导入由己帮助文档到知识库，支持用户查询
"""

import sys
import os
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def load_help_documents():
    """加载docs/由己帮助目录下的所有文档"""
    docs = []
    help_dir = Path(__file__).parent.parent / "docs" / "由己帮助"
    
    if not help_dir.exists():
        print(f"❌ 帮助文档目录不存在: {help_dir}")
        return []
    
    # 递归读取所有.md文件
    for md_file in help_dir.rglob("*.md"):
        try:
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:
                    # 添加文件路径信息，便于追踪来源
                    relative_path = md_file.relative_to(help_dir)
                    docs.append({
                        'content': content,
                        'source': str(relative_path),
                        'id': f"help_{str(relative_path).replace('/', '_').replace('.md', '')}"
                    })
                    print(f"✅ 加载文档: {relative_path}")
        except Exception as e:
            print(f"⚠️ 读取文件失败 {md_file}: {e}")
    
    return docs

# 基础平台信息（保留原有的技术架构描述）
PLATFORM_DOCS = [
    {
        'content': "由己平台是一套智能需求采集与知识管理系统，帮助团队系统化收集、整理、澄清和追踪业务需求。",
        'source': "platform_intro",
        'id': "platform_intro"
    },
    {
        'content': "由己平台支持会话式问答、关注点收集、信息提取、文档生成等能力，适合产品需求澄清、项目启动文档输出。",
        'source': "platform_features",
        'id': "platform_features"
    },
    {
        'content': "平台采用高效的两层识别架构：关键词加速实现快速匹配，LLM意图识别处理复杂语义理解，确保准确理解用户意图。",
        'source': "platform_architecture",
        'id': "platform_architecture"
    },
    {
        'content': "平台具备RAG知识库功能，通过向量数据库ChromaDB进行语义检索，可基于企业知识进行回答与建议。",
        'source': "platform_rag",
        'id': "platform_rag"
    },
    {
        'content': "平台日志体系包括app.log、error.log、session.log，并提供性能监控日志，便于问题定位与优化。",
        'source': "platform_logging",
        'id': "platform_logging"
    }
]

def main():
    print("🚀 开始导入完整知识库文档...")

    try:
        from backend.agents.factory import agent_factory
        rag = agent_factory.get_rag_knowledge_base_agent()
        if not rag:
            print("❌ 无法获取RAG代理")
            return 1

        if not hasattr(rag, "_collection") or rag._collection is None:
            print("❌ RAG集合未初始化")
            return 1

        # 加载帮助文档
        help_docs = load_help_documents()
        all_docs = PLATFORM_DOCS + help_docs
        
        print(f"📚 总共加载了 {len(all_docs)} 个文档")
        
        # 准备数据
        documents = [doc['content'] for doc in all_docs]
        ids = [doc['id'] for doc in all_docs]
        metadatas = [{'source': doc['source']} for doc in all_docs]
        
        # 先删除所有现有文档
        try:
            # 获取所有现有文档ID
            existing = rag._collection.get()
            if existing['ids']:
                rag._collection.delete(ids=existing['ids'])
                print(f"🔄 已删除 {len(existing['ids'])} 个旧文档")
        except Exception as e:
            print(f"ℹ️ 删除旧文档时出现异常（可能是首次导入）: {e}")
        
        # 添加新文档
        rag._collection.add(
            documents=documents, 
            ids=ids,
            metadatas=metadatas
        )
        print(f"✅ 已导入 {len(documents)} 个文档到知识库")

        # 验证导入
        test_queries = [
            "如何注册雇主账号",
            "由己平台是什么",
            "平台架构介绍"
        ]
        
        print("\n🔍 验证知识库导入效果:")
        for query in test_queries:
            res = rag._collection.query(query_texts=[query], n_results=2)
            docs = res.get("documents", [[]])[0]
            if docs:
                print(f"✅ 查询 '{query}' 找到 {len(docs)} 个相关文档")
                for i, doc in enumerate(docs, 1):
                    print(f"   {i}. {doc[:80]}...")
            else:
                print(f"⚠️ 查询 '{query}' 未找到相关文档")
            print()

        return 0
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    raise SystemExit(main())
