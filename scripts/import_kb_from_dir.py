#!/usr/bin/env python3
"""
从目录批量导入知识库文档（md/txt）
- 递归扫描目录
- 简单分块（按段落聚合到指定长度，带重叠）
- 写入当前 Chroma 集合（通过 AgentFactory 获取）

用法示例：
  python scripts/import_kb_from_dir.py --dir "docs/由己帮助" --pattern ".md,.txt" --chunk-size 800 --overlap 100 --min-chunk-length 50
"""

import sys
import os
from pathlib import Path
import argparse
import uuid

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

DEFAULT_PATTERNS = [".md", ".txt"]


def detect_role(path: Path) -> str | None:
    """根据路径推断角色：包含 company/developer 目录名时标注角色"""
    parts = {p.lower() for p in path.parts}
    if "company" in parts:
        return "company"
    if "developer" in parts:
        return "developer"
    return None


def parse_args():
    p = argparse.ArgumentParser(description="Import knowledge base from a directory")
    p.add_argument("--dir", required=True, help="Root directory to scan")
    p.add_argument("--pattern", default=",".join(DEFAULT_PATTERNS), help="Comma-separated file extensions, e.g. .md,.txt")
    p.add_argument("--chunk-size", type=int, default=800, help="Target chunk size in characters")
    p.add_argument("--overlap", type=int, default=100, help="Chunk overlap in characters")
    p.add_argument("--min-chunk-length", type=int, default=50, help="Minimum chunk length to include")
    p.add_argument("--max-files", type=int, default=0, help="Limit number of files (0 = no limit)")
    p.add_argument("--dry-run", action="store_true", help="Only print what would be done")
    return p.parse_args()


def normalize_newlines(text: str) -> str:
    return text.replace("\r\n", "\n").replace("\r", "\n")


def split_into_chunks(text: str, chunk_size: int, overlap: int, min_len: int) -> list[str]:
    text = normalize_newlines(text)
    # 基于段落的聚合分块
    paras = [p.strip() for p in text.split("\n\n") if p.strip()]
    chunks: list[str] = []
    buf = ""
    for para in paras:
        if not buf:
            buf = para
        elif len(buf) + 2 + len(para) <= chunk_size:
            buf = f"{buf}\n\n{para}"
        else:
            if len(buf) >= min_len:
                chunks.append(buf)
            # 使用重叠：从 buf 末尾截取 overlap 字符开头
            if overlap > 0 and len(buf) > overlap:
                prefix = buf[-overlap:]
                buf = f"{prefix}\n\n{para}"
            else:
                buf = para
    if buf and len(buf) >= min_len:
        chunks.append(buf)
    # 如果没有分出块且文本较短，直接作为一个块
    if not chunks and len(text) >= min_len:
        chunks = [text[:chunk_size]]
    return chunks


def iter_files(root: Path, exts: list[str]):
    count = 0
    for path in root.rglob("*"):
        if path.is_file() and path.suffix.lower() in exts:
            yield path
            count += 1


def main():
    args = parse_args()
    root = Path(args.dir)
    if not root.exists():
        print(f"❌ 目录不存在: {root}")
        return 1
    exts = [e.strip().lower() for e in args.pattern.split(",") if e.strip()]

    # 收集文件列表
    files = list(iter_files(root, exts))
    if args.max_files > 0:
        files = files[: args.max_files]
    if not files:
        print("⚠️  未找到可导入文件")
        return 0

    print(f"📂 待导入文件数: {len(files)}，目录: {root}")

    # 获取集合
    try:
        from backend.agents.factory import agent_factory
        rag = agent_factory.get_rag_knowledge_base_agent()
        if not rag or not getattr(rag, "_collection", None):
            print("❌ 无法获取RAG集合")
            return 1
        collection = rag._collection
    except Exception as e:
        print(f"❌ 初始化RAG失败: {e}")
        return 1

    total_docs = 0
    total_chunks = 0

    for idx, file_path in enumerate(files, 1):
        try:
            text = file_path.read_text(encoding="utf-8", errors="ignore")
        except Exception as e:
            print(f"⚠️  读取失败，跳过: {file_path} ({e})")
            continue
        chunks = split_into_chunks(text, args.chunk_size, args.overlap, args.min_chunk_length)
        if not chunks:
            continue

        role = detect_role(file_path)

        if args.dry_run:
            print(f"- {file_path}: {len(chunks)} 块, 角色: {role}")
        else:
            # 构造 ids 与 metadatas
            base_id = uuid.uuid4().hex
            ids = [f"kb_{base_id}_{i}" for i in range(len(chunks))]
            total = len(chunks)
            metadatas = [
                {
                    "source_file": str(file_path),
                    "chunk_index": i,
                    "total_chunks": total,
                    "role": role
                }
                for i in range(total)
            ]
            try:
                collection.add(documents=chunks, ids=ids, metadatas=metadatas)
            except Exception as e:
                print(f"⚠️  导入失败 {file_path}: {e}")
                continue
        total_docs += 1
        total_chunks += len(chunks)
        if idx % 20 == 0:
            print(f"  进度: {idx}/{len(files)} 文件，累计块: {total_chunks}")

    print(f"✅ 导入完成。文件数: {total_docs}，块数: {total_chunks}，集合总量: {collection.count()}")
    return 0


if __name__ == "__main__":
    raise SystemExit(main())

