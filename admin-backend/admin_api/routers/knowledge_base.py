"""
知识库管理路由

提供知识库管理的API接口，包括文档管理、查询测试、统计分析等功能。
"""

from fastapi import APIRouter, HTTPException, Query
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field

from admin_services.knowledge_base_service import KnowledgeBaseService
from admin_utils.exceptions import DatabaseError


class QueryRequest(BaseModel):
    """查询请求模型"""
    query_text: str = Field(..., min_length=1, max_length=1000, description="查询内容")
    top_k: int = Field(5, ge=1, le=20, description="返回结果数量")
    similarity_threshold: float = Field(0.7, ge=0.0, le=1.0, description="相似度阈值")
    role_filter: Optional[str] = Field(None, description="角色过滤")


class DocumentListResponse(BaseModel):
    """文档列表响应模型"""
    documents: List[Dict[str, Any]]
    total: int
    page: int
    limit: int
    total_pages: int


class QueryResponse(BaseModel):
    """查询响应模型"""
    query: str
    results: List[Dict[str, Any]]
    total_results: int
    parameters: Dict[str, Any]


class OverviewStatsResponse(BaseModel):
    """概览统计响应模型"""
    total_documents: int
    unique_documents: int
    role_types: int
    categories: int
    role_distribution: Dict[str, int]
    category_distribution: Dict[str, int]


class KnowledgeBaseConfigResponse(BaseModel):
    """知识库配置响应模型"""
    enabled: bool
    retrieval: Dict[str, Any]
    document_processing: Dict[str, Any]
    performance: Dict[str, Any]
    safety: Dict[str, Any]
    chroma_db: Dict[str, Any]
    role_filters: Dict[str, Any]
    logging: Dict[str, Any]


class KnowledgeBaseConfigUpdate(BaseModel):
    """知识库配置更新模型"""
    retrieval: Optional[Dict[str, Any]] = None
    document_processing: Optional[Dict[str, Any]] = None
    performance: Optional[Dict[str, Any]] = None
    safety: Optional[Dict[str, Any]] = None
    role_filters: Optional[Dict[str, Any]] = None
    logging: Optional[Dict[str, Any]] = None


class RetrievalConfigUpdate(BaseModel):
    """检索配置更新模型"""
    top_k: Optional[int] = Field(None, ge=1, le=20, description="检索文档数量")
    similarity_threshold: Optional[float] = Field(None, ge=0.0, le=1.0, description="相似度阈值")
    max_context_length: Optional[int] = Field(None, ge=1000, le=10000, description="最大上下文长度")


class PerformanceConfigUpdate(BaseModel):
    """性能配置更新模型"""
    cache_enabled: Optional[bool] = None
    cache_ttl: Optional[int] = Field(None, ge=300, le=86400, description="缓存TTL(秒)")
    max_concurrent_queries: Optional[int] = Field(None, ge=1, le=20, description="最大并发查询数")


class SystemStatusResponse(BaseModel):
    """系统状态响应模型"""
    chromadb_status: str
    collection_info: Dict[str, Any]
    config_status: Dict[str, Any]
    document_count: int
    components: Dict[str, bool]


router = APIRouter()

# 初始化服务
knowledge_base_service = KnowledgeBaseService()


@router.get("/overview", response_model=OverviewStatsResponse, summary="获取知识库概览统计")
async def get_overview_stats():
    """
    获取知识库概览统计信息
    
    Returns:
        知识库概览统计，包括文档数量、角色分布、分类分布等
    """
    try:
        stats = await knowledge_base_service.get_overview_stats()
        return OverviewStatsResponse(**stats)
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取概览统计失败: {str(e)}")


@router.get("/documents", response_model=DocumentListResponse, summary="获取文档列表")
async def get_documents(
    role: Optional[str] = Query(None, description="角色筛选"),
    category: Optional[str] = Query(None, description="分类筛选"),
    search_term: Optional[str] = Query(None, description="搜索关键词"),
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量")
):
    """
    获取文档列表，支持筛选和分页
    
    Args:
        role: 角色筛选
        category: 分类筛选
        search_term: 搜索关键词
        page: 页码
        limit: 每页数量
    
    Returns:
        分页的文档列表
    """
    try:
        result = await knowledge_base_service.get_documents(
            role=role,
            category=category,
            search_term=search_term,
            page=page,
            limit=limit
        )
        return DocumentListResponse(**result)
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文档列表失败: {str(e)}")


@router.post("/query", response_model=QueryResponse, summary="查询知识库")
async def query_knowledge_base(request: QueryRequest):
    """
    查询知识库，测试知识库搜索功能
    
    Args:
        request: 查询请求，包含查询内容和参数
    
    Returns:
        查询结果列表
    """
    try:
        result = await knowledge_base_service.query_knowledge_base(
            query_text=request.query_text,
            top_k=request.top_k,
            similarity_threshold=request.similarity_threshold,
            role_filter=request.role_filter
        )
        return QueryResponse(**result)
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"知识库查询失败: {str(e)}")


@router.get("/statistics", summary="获取统计分析数据")
async def get_statistics():
    """
    获取知识库统计分析数据
    
    Returns:
        统计分析数据，包括内容长度分布、角色分类统计等
    """
    try:
        stats = await knowledge_base_service.get_statistics()
        return {
            "success": True,
            "data": stats
        }
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计分析失败: {str(e)}")


@router.get("/status", response_model=SystemStatusResponse, summary="获取系统状态")
async def get_system_status():
    """
    获取知识库系统状态
    
    Returns:
        系统状态信息，包括ChromaDB连接状态、配置状态等
    """
    try:
        status = await knowledge_base_service.get_system_status()
        return SystemStatusResponse(**status)
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")


@router.get("/filters", summary="获取可用筛选选项")
async def get_available_filters():
    """
    获取可用的筛选选项
    
    Returns:
        可用的角色和分类筛选选项
    """
    try:
        filters = await knowledge_base_service.get_available_filters()
        return {
            "success": True,
            "data": filters
        }
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取筛选选项失败: {str(e)}")


@router.get("/health", summary="健康检查")
async def health_check():
    """
    知识库服务健康检查

    Returns:
        服务健康状态
    """
    try:
        status = await knowledge_base_service.get_system_status()
        is_healthy = (
            status["chromadb_status"] == "connected" and
            status["components"]["config_manager"] and
            status["components"]["collection"]
        )

        return {
            "status": "healthy" if is_healthy else "unhealthy",
            "service": "knowledge-base",
            "details": status
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "knowledge-base",
            "error": str(e)
        }


# ============================================================================
# 知识库配置管理API
# ============================================================================

@router.get("/config", response_model=KnowledgeBaseConfigResponse, summary="获取知识库配置")
async def get_knowledge_base_config():
    """
    获取当前知识库配置

    Returns:
        知识库完整配置信息
    """
    try:
        config = await knowledge_base_service.get_knowledge_base_config()
        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取知识库配置失败: {str(e)}")


@router.put("/config", summary="更新知识库配置")
async def update_knowledge_base_config(config_update: KnowledgeBaseConfigUpdate):
    """
    更新知识库配置

    Args:
        config_update: 配置更新数据

    Returns:
        更新结果
    """
    try:
        success = await knowledge_base_service.update_knowledge_base_config(
            config_update.model_dump(exclude_unset=True)
        )
        if success:
            return {"message": "知识库配置更新成功", "success": True}
        else:
            raise HTTPException(status_code=500, detail="配置更新失败")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新知识库配置失败: {str(e)}")


@router.put("/config/retrieval", summary="更新检索配置")
async def update_retrieval_config(retrieval_config: RetrievalConfigUpdate):
    """
    更新检索相关配置

    Args:
        retrieval_config: 检索配置更新数据

    Returns:
        更新结果
    """
    try:
        config_data = {"retrieval": retrieval_config.model_dump(exclude_unset=True)}
        success = await knowledge_base_service.update_knowledge_base_config(config_data)
        if success:
            return {"message": "检索配置更新成功", "success": True}
        else:
            raise HTTPException(status_code=500, detail="检索配置更新失败")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新检索配置失败: {str(e)}")


@router.put("/config/performance", summary="更新性能配置")
async def update_performance_config(performance_config: PerformanceConfigUpdate):
    """
    更新性能相关配置

    Args:
        performance_config: 性能配置更新数据

    Returns:
        更新结果
    """
    try:
        config_data = {"performance": performance_config.model_dump(exclude_unset=True)}
        success = await knowledge_base_service.update_knowledge_base_config(config_data)
        if success:
            return {"message": "性能配置更新成功", "success": True}
        else:
            raise HTTPException(status_code=500, detail="性能配置更新失败")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新性能配置失败: {str(e)}")


@router.post("/config/reload", summary="重新加载配置")
async def reload_knowledge_base_config():
    """
    重新加载知识库配置

    Returns:
        重新加载结果
    """
    try:
        success = await knowledge_base_service.reload_config()
        if success:
            return {"message": "知识库配置重新加载成功", "success": True}
        else:
            raise HTTPException(status_code=500, detail="配置重新加载失败")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重新加载配置失败: {str(e)}")


@router.post("/config/validate", summary="验证配置")
async def validate_knowledge_base_config():
    """
    验证知识库配置

    Returns:
        验证结果
    """
    try:
        result = await knowledge_base_service.validate_config()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置验证失败: {str(e)}")


@router.get("/config/presets", summary="获取配置预设")
async def get_config_presets():
    """
    获取知识库配置预设方案

    Returns:
        配置预设列表
    """
    try:
        presets = await knowledge_base_service.get_config_presets()
        return presets
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取配置预设失败: {str(e)}")


@router.post("/config/apply-preset/{preset_name}", summary="应用配置预设")
async def apply_config_preset(preset_name: str):
    """
    应用指定的配置预设

    Args:
        preset_name: 预设名称

    Returns:
        应用结果
    """
    try:
        success = await knowledge_base_service.apply_config_preset(preset_name)
        if success:
            return {"message": f"配置预设 '{preset_name}' 应用成功", "success": True}
        else:
            raise HTTPException(status_code=404, detail=f"配置预设 '{preset_name}' 不存在")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"应用配置预设失败: {str(e)}")
