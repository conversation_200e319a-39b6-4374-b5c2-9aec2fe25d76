# Pre-commit 配置文件
# 在每次提交前自动运行代码质量检查和配置验证

repos:
  # 基础代码质量检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
        name: 移除行尾空白
      - id: end-of-file-fixer
        name: 确保文件以换行符结尾
      - id: check-yaml
        name: YAML语法检查
        args: ['--unsafe']
      - id: check-json
        name: JSON语法检查
      - id: check-toml
        name: TOML语法检查
      - id: check-merge-conflict
        name: 检查合并冲突标记
      - id: check-added-large-files
        name: 检查大文件
        args: ['--maxkb=1000']
      - id: mixed-line-ending
        name: 检查混合行结束符
        args: ['--fix=lf']

  # Python代码格式化
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        name: Python代码格式化
        args: ['--line-length=100']
        language_version: python3

  # Python导入排序
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        name: Python导入排序
        args: ['--profile=black', '--line-length=100']

  # Python代码风格检查
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        name: Python代码风格检查
        args: ['--max-line-length=100', '--ignore=E203,W503,E501']

  # 安全检查
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        name: Python安全检查
        args: ['-r', 'backend/', '-f', 'json', '-o', 'bandit-report.json']
        pass_filenames: false

  # 自定义配置验证钩子
  - repo: local
    hooks:
      # 配置文件Schema验证
      - id: config-schema-validation
        name: 配置Schema验证
        entry: python -m backend.config.validation.test_schemas
        language: system
        files: ^backend/config/.*\.yaml$
        pass_filenames: false

      # 硬编码检测
      - id: hardcode-detection
        name: 硬编码检测
        entry: python tools/hardcode_detector.py
        language: system
        args: ['backend/', 'text']
        files: ^backend/.*\.py$
        pass_filenames: false

      # 配置完整性检查
      - id: config-integrity-check
        name: 配置完整性检查
        entry: python tools/config_validator.py
        language: system
        args: ['--config-dir', 'backend/config/', '--strict']
        files: ^backend/config/.*\.yaml$
        pass_filenames: false

      # 禁止新增旧配置依赖
      - id: legacy-config-check
        name: 禁止新增旧配置依赖
        entry: python tools/legacy_dependency_scanner.py
        language: system
        args: ['--git-check', '--base-branch', 'main']
        files: ^backend/.*\.py$
        pass_filenames: false

      # 关键配置键检查
      - id: critical-config-keys-check
        name: 关键配置键检查
        entry: python -c "
import sys
import yaml
import os
from pathlib import Path

# 关键配置键列表
critical_keys = {
    'backend/config/data/database.yaml': ['database.connection.path'],
    'backend/config/llm/models.yaml': ['default_model', 'models'],
    'backend/config/system/security.yaml': ['authentication.enabled'],
    'backend/config/system/performance.yaml': ['cache.enabled']
}

violations = []
for config_file, required_keys in critical_keys.items():
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f) or {}
            
            for key_path in required_keys:
                keys = key_path.split('.')
                current = config_data
                missing = False
                
                for key in keys:
                    if isinstance(current, dict) and key in current:
                        current = current[key]
                    else:
                        violations.append(f'{config_file}: 缺少关键配置键 {key_path}')
                        missing = True
                        break
                        
        except Exception as e:
            violations.append(f'{config_file}: 配置文件解析失败 {e}')

if violations:
    print('❌ 关键配置键检查失败:')
    for violation in violations:
        print(f'  {violation}')
    sys.exit(1)
else:
    print('✅ 关键配置键检查通过')
"
        language: system
        files: ^backend/config/.*\.yaml$
        pass_filenames: false

# 配置选项
default_stages: [commit]
fail_fast: false
