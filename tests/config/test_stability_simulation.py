#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置系统稳定性模拟测试

注入各种异常情况，验证系统的降级处理和资源管理能力
"""

import os
import gc
import time
import pytest
import tempfile
import threading
import psutil
from pathlib import Path
from unittest.mock import patch, MagicMock
from typing import Dict, Any, List

from backend.config.manager import ConfigManager
from backend.config.modular_loader import ModularConfigLoader
from backend.config.metrics_collector import get_metrics_collector


class TestStabilitySimulation:
    """稳定性模拟测试"""
    
    def setup_method(self):
        """测试前设置"""
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
        
        # 重置指标收集器
        metrics_collector = get_metrics_collector()
        metrics_collector.reset_metrics()
    
    def teardown_method(self):
        """测试后清理"""
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
        
        # 强制垃圾回收
        gc.collect()
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_corrupted_config_handling(self, mock_loader_class):
        """测试损坏配置的处理"""
        # 模拟损坏的配置数据
        corrupted_configs = [
            None,  # 空配置
            {},    # 空字典
            {"invalid": float('inf')},  # 无效值
            {"nested": {"circular": "${nested.circular}"}},  # 循环引用
            {"malformed": "{{invalid_template}}"},  # 格式错误的模板
        ]
        
        for i, corrupted_config in enumerate(corrupted_configs):
            mock_loader = MagicMock()
            mock_loader.enabled = True
            mock_loader.create_snapshot.return_value.config_data = corrupted_config
            mock_loader_class.return_value = mock_loader
            
            # 配置管理器应该能处理损坏的配置
            try:
                config_manager = ConfigManager(enable_env_override=True)
                
                # 尝试访问配置，应该返回默认值而不是崩溃
                value = config_manager.get("nonexistent.key", "default_value")
                assert value == "default_value", f"损坏配置 {i} 处理失败"
                
                # 验证系统仍然可以设置运行时配置
                config_manager.set_runtime("test.key", "test_value")
                assert config_manager.get("test.key") == "test_value"
                
            except Exception as e:
                pytest.fail(f"损坏配置 {i} 导致系统崩溃: {e}")
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_missing_config_fallback(self, mock_loader_class):
        """测试缺失配置的回退机制"""
        # 模拟配置加载失败
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.side_effect = FileNotFoundError("Config file not found")
        mock_loader_class.return_value = mock_loader
        
        # 配置管理器应该能处理配置缺失
        config_manager = ConfigManager(enable_env_override=True)
        
        # 验证回退到默认值
        assert config_manager.get("app.debug", False) is False
        assert config_manager.get("app.name", "Default App") == "Default App"
        
        # 验证环境变量仍然有效
        os.environ["AID_CONF__APP__DEBUG"] = "true"
        config_manager.reload()
        assert config_manager.get("app.debug", False) is True
        
        # 验证运行时配置仍然有效
        config_manager.set_runtime("app.name", "Runtime App")
        assert config_manager.get("app.name") == "Runtime App"
    
    def test_type_error_handling(self):
        """测试类型错误处理"""
        config_manager = ConfigManager(enable_env_override=True)
        
        # 测试各种类型错误的环境变量
        type_error_cases = [
            ("AID_CONF__TEST__INVALID_JSON", '{"invalid": json}'),
            ("AID_CONF__TEST__INVALID_NUMBER", "not_a_number"),
            ("AID_CONF__TEST__INVALID_BOOL", "maybe"),
            ("AID_CONF__TEST__EMPTY_VALUE", ""),
        ]
        
        for env_key, env_value in type_error_cases:
            os.environ[env_key] = env_value
        
        # 重新加载配置
        config_manager.reload()
        
        # 验证类型错误不会导致系统崩溃
        # 无效的JSON应该被当作字符串处理
        assert config_manager.get("test.invalid_json") == '{"invalid": json}'
        
        # 无效的数字应该被当作字符串处理
        assert config_manager.get("test.invalid_number") == "not_a_number"
        
        # 无效的布尔值应该被当作字符串处理
        assert config_manager.get("test.invalid_bool") == "maybe"
        
        # 空值应该被正确处理
        assert config_manager.get("test.empty_value") == ""
    
    def test_boundary_value_handling(self):
        """测试边界值处理"""
        config_manager = ConfigManager(enable_env_override=True)
        
        # 测试各种边界值
        boundary_cases = [
            ("AID_CONF__TEST__MAX_INT", str(2**63 - 1)),
            ("AID_CONF__TEST__MIN_INT", str(-2**63)),
            ("AID_CONF__TEST__LARGE_FLOAT", "1.7976931348623157e+308"),
            ("AID_CONF__TEST__SMALL_FLOAT", "2.2250738585072014e-308"),
            ("AID_CONF__TEST__VERY_LONG_STRING", "x" * 10000),
            ("AID_CONF__TEST__UNICODE_STRING", "测试中文🎉"),
            ("AID_CONF__TEST__SPECIAL_CHARS", "!@#$%^&*()[]{}|\\:;\"'<>,.?/~`"),
        ]
        
        for env_key, env_value in boundary_cases:
            os.environ[env_key] = env_value
        
        # 重新加载配置
        config_manager.reload()
        
        # 验证边界值被正确处理
        assert config_manager.get("test.max_int") == 2**63 - 1
        assert config_manager.get("test.min_int") == -2**63
        assert isinstance(config_manager.get("test.large_float"), float)
        assert isinstance(config_manager.get("test.small_float"), float)
        assert len(config_manager.get("test.very_long_string")) == 10000
        assert config_manager.get("test.unicode_string") == "测试中文🎉"
        assert config_manager.get("test.special_chars") == "!@#$%^&*()[]{}|\\:;\"'<>,.?/~`"
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_memory_leak_detection(self, mock_loader_class):
        """测试内存泄漏检测"""
        # 模拟配置
        file_config = {
            "memory_test": {
                "data": "test_data"
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        # 获取初始内存使用
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # 执行大量配置操作
        config_manager = ConfigManager(enable_env_override=True)
        
        for i in range(1000):
            # 创建和删除大量运行时配置
            for j in range(100):
                key = f"memory_test.temp_{j}"
                config_manager.set_runtime(key, f"value_{i}_{j}")
            
            # 读取配置
            for j in range(100):
                config_manager.get(f"memory_test.temp_{j}")
                config_manager.get_with_source(f"memory_test.temp_{j}")
            
            # 清理运行时配置
            for j in range(100):
                config_manager.clear_runtime(f"memory_test.temp_{j}")
            
            # 定期检查内存使用
            if i % 100 == 0:
                gc.collect()
                current_memory = process.memory_info().rss
                memory_growth = current_memory - initial_memory
                
                # 内存增长不应该超过100MB
                assert memory_growth < 100 * 1024 * 1024, f"内存泄漏检测: 增长 {memory_growth / 1024 / 1024:.2f}MB"
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_concurrent_stress_test(self, mock_loader_class):
        """测试并发压力测试"""
        file_config = {
            "stress_test": {
                "shared_data": "initial_value"
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 并发测试参数
        num_threads = 20
        operations_per_thread = 500
        errors = []
        completed_operations = []
        
        def stress_worker(worker_id):
            """压力测试工作线程"""
            try:
                for i in range(operations_per_thread):
                    operation = i % 4
                    
                    if operation == 0:
                        # 读取配置
                        value = config_manager.get("stress_test.shared_data")
                        completed_operations.append(f"read_{worker_id}_{i}")
                    
                    elif operation == 1:
                        # 设置运行时配置
                        config_manager.set_runtime(f"stress_test.worker_{worker_id}", f"value_{i}")
                        completed_operations.append(f"set_{worker_id}_{i}")
                    
                    elif operation == 2:
                        # 获取配置来源
                        value, source = config_manager.get_with_source("stress_test.shared_data")
                        completed_operations.append(f"source_{worker_id}_{i}")
                    
                    elif operation == 3:
                        # 清理运行时配置
                        config_manager.clear_runtime(f"stress_test.worker_{worker_id}")
                        completed_operations.append(f"clear_{worker_id}_{i}")
                    
                    # 随机延迟
                    if i % 50 == 0:
                        time.sleep(0.001)
                        
            except Exception as e:
                errors.append(f"Worker {worker_id} error: {e}")
        
        # 启动压力测试线程
        threads = []
        start_time = time.time()
        
        for i in range(num_threads):
            thread = threading.Thread(target=stress_worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=30)
        
        end_time = time.time()
        
        # 验证测试结果
        assert len(errors) == 0, f"并发压力测试出现错误: {errors[:5]}"  # 只显示前5个错误
        
        expected_operations = num_threads * operations_per_thread
        actual_operations = len(completed_operations)
        
        # 允许一定的操作丢失（由于线程竞争）
        success_rate = actual_operations / expected_operations
        assert success_rate > 0.95, f"操作成功率过低: {success_rate:.3f}"
        
        # 验证性能
        total_time = end_time - start_time
        ops_per_second = actual_operations / total_time
        assert ops_per_second > 1000, f"并发性能过低: {ops_per_second:.0f} ops/s"
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_long_running_stability(self, mock_loader_class):
        """测试长时间运行稳定性"""
        file_config = {
            "stability_test": {
                "counter": 0,
                "data": "stable_data"
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 长时间运行测试（模拟）
        start_time = time.time()
        test_duration = 10  # 10秒的长稳测试
        operation_count = 0
        error_count = 0
        
        while time.time() - start_time < test_duration:
            try:
                # 模拟各种操作
                operation = operation_count % 10
                
                if operation == 0:
                    config_manager.get("stability_test.data")
                elif operation == 1:
                    config_manager.set_runtime("stability_test.counter", operation_count)
                elif operation == 2:
                    config_manager.get_with_source("stability_test.data")
                elif operation == 3:
                    config_manager.get_section("stability_test")
                elif operation == 4:
                    config_manager.reload()
                elif operation == 5:
                    config_manager.get_config_summary()
                elif operation == 6:
                    config_manager.clear_runtime("stability_test.counter")
                elif operation == 7:
                    # 模拟环境变量变化
                    os.environ["AID_CONF__STABILITY_TEST__DATA"] = f"env_data_{operation_count}"
                elif operation == 8:
                    config_manager.dump_config()
                elif operation == 9:
                    # 强制垃圾回收
                    gc.collect()
                
                operation_count += 1
                
                # 定期检查系统状态
                if operation_count % 1000 == 0:
                    # 验证基本功能仍然正常
                    value = config_manager.get("stability_test.data")
                    assert value is not None
                    
                    # 检查指标收集器
                    metrics = get_metrics_collector().get_metrics()
                    assert isinstance(metrics, dict)
                
            except Exception as e:
                error_count += 1
                if error_count > 10:  # 允许少量错误
                    pytest.fail(f"长稳测试错误过多: {e}")
        
        # 验证长稳测试结果
        assert operation_count > 5000, f"长稳测试操作数过少: {operation_count}"
        error_rate = error_count / operation_count
        assert error_rate < 0.01, f"长稳测试错误率过高: {error_rate:.3f}"
        
        # 验证系统仍然响应
        final_value = config_manager.get("stability_test.data")
        assert final_value is not None
    
    def test_resource_cleanup_on_shutdown(self):
        """测试关闭时的资源清理"""
        # 创建多个配置管理器实例
        managers = []
        
        for i in range(10):
            manager = ConfigManager(enable_env_override=True)
            manager.set_runtime(f"test.manager_{i}", f"value_{i}")
            managers.append(manager)
        
        # 记录初始资源使用
        initial_objects = len(gc.get_objects())
        
        # 模拟关闭清理
        for manager in managers:
            # 清理运行时配置
            for j in range(10):
                manager.clear_runtime(f"test.manager_{j}")
            
            # 清理引用
            del manager
        
        # 强制垃圾回收
        gc.collect()
        
        # 验证资源被正确清理
        final_objects = len(gc.get_objects())
        object_growth = final_objects - initial_objects
        
        # 允许一定的对象增长，但不应该过多
        assert object_growth < 100, f"资源清理不完整，对象增长: {object_growth}"


class TestErrorInjection:
    """错误注入测试"""
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_random_error_injection(self, mock_loader_class):
        """测试随机错误注入"""
        import random
        
        # 正常配置
        normal_config = {
            "error_injection": {
                "stable_key": "stable_value",
                "test_key": "test_value"
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        
        # 随机注入错误
        def random_config_loader():
            if random.random() < 0.1:  # 10%概率出错
                raise Exception("Random config load error")
            return MagicMock(config_data=normal_config)
        
        mock_loader.create_snapshot.side_effect = lambda: random_config_loader()
        mock_loader_class.return_value = mock_loader
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 执行大量操作，验证系统在随机错误下的稳定性
        success_count = 0
        total_operations = 1000
        
        for i in range(total_operations):
            try:
                # 随机选择操作
                operation = random.choice([
                    lambda: config_manager.get("error_injection.stable_key"),
                    lambda: config_manager.set_runtime("error_injection.temp", f"temp_{i}"),
                    lambda: config_manager.reload(),
                    lambda: config_manager.get_config_summary(),
                ])
                
                result = operation()
                success_count += 1
                
            except Exception:
                # 允许部分操作失败
                pass
        
        # 验证大部分操作成功
        success_rate = success_count / total_operations
        assert success_rate > 0.8, f"随机错误注入下成功率过低: {success_rate:.3f}"


class TestPerformanceBenchmark:
    """性能基准测试"""

    @patch('backend.config.manager.ModularConfigLoader')
    def test_config_load_performance(self, mock_loader_class):
        """测试配置加载性能"""
        # 生成大型配置
        large_config = {}
        for i in range(1000):
            section = f"section_{i}"
            large_config[section] = {}
            for j in range(100):
                large_config[section][f"key_{j}"] = f"value_{i}_{j}"

        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = large_config
        mock_loader_class.return_value = mock_loader

        # 测试加载性能
        start_time = time.time()
        config_manager = ConfigManager(enable_env_override=True)
        load_time = time.time() - start_time

        # 验证加载时间在合理范围内
        assert load_time < 2.0, f"配置加载时间过长: {load_time:.3f}s"

        # 测试访问性能
        start_time = time.time()
        for i in range(1000):
            config_manager.get(f"section_{i % 100}.key_{i % 10}")
        access_time = time.time() - start_time

        # 验证访问性能
        assert access_time < 0.1, f"配置访问时间过长: {access_time:.3f}s"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
