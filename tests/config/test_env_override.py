#!/usr/bin/env python3
"""
环境变量覆盖测试

测试环境变量覆盖功能、白名单机制、敏感信息脱敏等
"""

import os
import pytest
import logging
from unittest.mock import patch, MagicMock
from io import StringIO

from backend.config.env_parser import EnvironmentVariableParser, EnvironmentConfigLoader
from backend.config.security.whitelist_manager import ConfigWhitelistManager, SensitivityLevel
from backend.config.manager import ConfigManager


class TestEnvironmentVariableParser:
    """环境变量解析器测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.parser = EnvironmentVariableParser()
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
    
    def teardown_method(self):
        """测试后清理"""
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
    
    def test_env_name_to_config_key(self):
        """测试环境变量名到配置键的转换"""
        # 测试基本转换
        assert self.parser._env_name_to_config_key("AID_CONF__APP__DEBUG") == "app.debug"
        assert self.parser._env_name_to_config_key("AID_CONF__LLM__DEFAULT_MODEL") == "llm.default_model"
        assert self.parser._env_name_to_config_key("AID_CONF__DATABASE__CONNECTION__TIMEOUT") == "database.connection.timeout"
    
    def test_parse_boolean_values(self):
        """测试布尔值解析"""
        # 真值测试
        true_values = ["true", "True", "TRUE", "yes", "Yes", "YES", "1", "on", "On", "ON"]
        for value in true_values:
            assert self.parser._parse_value(value, "test") is True
        
        # 假值测试
        false_values = ["false", "False", "FALSE", "no", "No", "NO", "0", "off", "Off", "OFF"]
        for value in false_values:
            assert self.parser._parse_value(value, "test") is False
    
    def test_parse_numeric_values(self):
        """测试数值解析"""
        # 整数
        assert self.parser._parse_value("42", "test") == 42
        assert self.parser._parse_value("-10", "test") == -10
        
        # 浮点数
        assert self.parser._parse_value("3.14", "test") == 3.14
        assert self.parser._parse_value("-2.5", "test") == -2.5
        
        # 科学计数法
        assert self.parser._parse_value("1e3", "test") == 1000.0
    
    def test_parse_json_values(self):
        """测试JSON值解析"""
        # JSON对象
        json_obj = '{"key": "value", "number": 42}'
        result = self.parser._parse_value(json_obj, "test")
        assert result == {"key": "value", "number": 42}
        
        # JSON数组
        json_array = '["item1", "item2", 123]'
        result = self.parser._parse_value(json_array, "test")
        assert result == ["item1", "item2", 123]
        
        # 无效JSON应该返回原字符串
        invalid_json = '{"invalid": json}'
        result = self.parser._parse_value(invalid_json, "test")
        assert result == invalid_json
    
    def test_parse_string_values(self):
        """测试字符串值解析"""
        assert self.parser._parse_value("hello", "test") == "hello"
        assert self.parser._parse_value("", "test") == ""
        assert self.parser._parse_value("123abc", "test") == "123abc"
    
    def test_parse_all_env_vars(self):
        """测试解析所有环境变量"""
        # 设置测试环境变量
        os.environ["AID_CONF__APP__DEBUG"] = "true"
        os.environ["AID_CONF__LLM__TEMPERATURE"] = "0.7"
        os.environ["AID_CONF__DATABASE__TIMEOUT"] = "30"
        os.environ["AID_CONF__LLM__MODELS"] = '{"gpt-4": {"max_tokens": 4000}}'
        
        config = self.parser.parse_all_env_vars()
        
        assert config["app"]["debug"] is True
        assert config["llm"]["temperature"] == 0.7
        assert config["database"]["timeout"] == 30
        assert config["llm"]["models"] == {"gpt-4": {"max_tokens": 4000}}
    
    def test_sensitive_value_detection(self):
        """测试敏感值检测"""
        # API密钥
        api_key = "sk-1234567890abcdef1234567890abcdef"
        assert self.parser._is_sensitive_value(api_key) is True
        
        # 普通字符串
        normal_string = "hello world"
        assert self.parser._is_sensitive_value(normal_string) is False
        
        # 长随机字符串
        long_string = "a" * 40
        assert self.parser._is_sensitive_value(long_string) is True
    
    def test_safe_log_value(self):
        """测试安全日志值"""
        # 敏感值应该被脱敏
        api_key = "sk-1234567890abcdef1234567890abcdef"
        assert self.parser._safe_log_value(api_key) == "[REDACTED]"
        
        # 普通值应该正常显示
        normal_value = "hello"
        assert self.parser._safe_log_value(normal_value) == "hello"
        
        # 长字符串应该被截断
        long_string = "a" * 200
        result = self.parser._safe_log_value(long_string)
        assert "[str:" in result and "chars]" in result


class TestEnvironmentConfigLoader:
    """环境配置加载器测试"""
    
    def setup_method(self):
        """测试前设置"""
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
    
    def teardown_method(self):
        """测试后清理"""
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
    
    def test_load_env_config_without_whitelist(self):
        """测试无白名单的环境配置加载"""
        os.environ["AID_CONF__APP__DEBUG"] = "true"
        os.environ["AID_CONF__LLM__TEMPERATURE"] = "0.8"
        
        loader = EnvironmentConfigLoader()
        config = loader.load_env_config()
        
        assert config["app"]["debug"] is True
        assert config["llm"]["temperature"] == 0.8
    
    def test_load_env_config_with_whitelist(self):
        """测试有白名单的环境配置加载"""
        os.environ["AID_CONF__APP__DEBUG"] = "true"
        os.environ["AID_CONF__UNKNOWN__KEY"] = "should_be_rejected"
        
        allowed_keys = {"app.debug"}
        loader = EnvironmentConfigLoader(allowed_keys)
        config = loader.load_env_config()
        
        # 允许的键应该存在
        assert config["app"]["debug"] is True
        
        # 不允许的键应该被拒绝
        assert "unknown" not in config
    
    def test_get_env_override_info(self):
        """测试获取环境覆盖信息"""
        os.environ["AID_CONF__APP__DEBUG"] = "true"
        
        loader = EnvironmentConfigLoader()
        loader.load_env_config()
        
        info = loader.get_env_override_info()
        assert "parser_summary" in info
        assert "env_vars_with_prefix" in info


class TestConfigWhitelistManager:
    """配置白名单管理器测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.manager = ConfigWhitelistManager()
    
    def test_is_key_allowed(self):
        """测试配置键是否允许"""
        # 测试允许的键
        is_allowed, rule = self.manager.is_key_allowed("app.debug")
        assert is_allowed is True
        assert rule is not None
        
        # 测试不允许的键
        is_allowed, rule = self.manager.is_key_allowed("unknown.key")
        assert is_allowed is False
        assert rule is None
    
    def test_validate_env_override(self):
        """测试环境变量覆盖验证"""
        # 测试允许的配置
        is_valid, message, log_info = self.manager.validate_env_override("app.debug", "true")
        assert is_valid is True
        assert "验证通过" in message
        assert log_info["config_key"] == "app.debug"
        
        # 测试不允许的配置
        is_valid, message, log_info = self.manager.validate_env_override("unknown.key", "value")
        assert is_valid is False
        assert "不允许" in message
    
    def test_sensitive_info_detection(self):
        """测试敏感信息检测"""
        # 测试敏感键
        is_sensitive, level, reason = self.manager.detector.detect_sensitive_key("llm.openai.api_key")
        assert is_sensitive is True
        assert level in [SensitivityLevel.SECRET, SensitivityLevel.CONFIDENTIAL]
        
        # 测试普通键
        is_sensitive, level, reason = self.manager.detector.detect_sensitive_key("app.debug")
        assert is_sensitive is False
        assert level == SensitivityLevel.PUBLIC
    
    def test_mask_sensitive_value(self):
        """测试敏感值遮蔽"""
        # 测试不同敏感度级别的遮蔽
        assert self.manager.detector.mask_sensitive_value("public", SensitivityLevel.PUBLIC) == "public"
        assert self.manager.detector.mask_sensitive_value("internal", SensitivityLevel.INTERNAL) == "[INTERNAL:8 chars]"
        assert self.manager.detector.mask_sensitive_value("confidential", SensitivityLevel.CONFIDENTIAL) == "[CONFIDENTIAL]"
        assert self.manager.detector.mask_sensitive_value("secret", SensitivityLevel.SECRET) == "[SECRET]"


class TestConfigManagerIntegration:
    """配置管理器集成测试"""
    
    def setup_method(self):
        """测试前设置"""
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
    
    def teardown_method(self):
        """测试后清理"""
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
    
    @patch('backend.config.manager.ConfigManager._load_all_configs')
    def test_env_override_integration(self, mock_load):
        """测试环境变量覆盖集成"""
        # 模拟基础配置
        mock_load.return_value = None
        
        # 设置环境变量
        os.environ["AID_CONF__APP__DEBUG"] = "true"
        
        # 创建配置管理器（禁用实际加载）
        config_manager = ConfigManager()
        config_manager._config_cache = {"app": {"debug": False}}  # 模拟基础配置
        
        # 测试环境变量覆盖验证
        is_valid, message = config_manager.validate_env_override("app.debug", "true")
        assert is_valid is True
    
    def test_runtime_config_setting(self):
        """测试运行时配置设置"""
        config_manager = ConfigManager()
        config_manager._config_cache = {}
        
        # 设置运行时配置
        config_manager.set_runtime("test.key", "test_value")
        
        # 验证配置和来源
        value, source = config_manager.get_with_source("test.key")
        assert value == "test_value"
        assert source == "runtime"


class TestLoggingAndSensitivity:
    """日志和敏感信息测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.log_stream = StringIO()
        self.handler = logging.StreamHandler(self.log_stream)
        self.logger = logging.getLogger("backend.config.env_parser")
        self.logger.addHandler(self.handler)
        self.logger.setLevel(logging.DEBUG)
    
    def teardown_method(self):
        """测试后清理"""
        self.logger.removeHandler(self.handler)
        self.handler.close()
    
    def test_sensitive_info_logging(self):
        """测试敏感信息日志脱敏"""
        parser = EnvironmentVariableParser()
        
        # 设置敏感环境变量
        os.environ["AID_CONF__LLM__API_KEY"] = "sk-1234567890abcdef1234567890abcdef"
        
        try:
            config = parser.parse_all_env_vars()
            
            # 检查日志内容
            log_content = self.log_stream.getvalue()
            
            # 敏感信息应该被脱敏
            assert "sk-1234567890abcdef1234567890abcdef" not in log_content
            assert "[REDACTED]" in log_content or "api_key" in log_content.lower()
            
        finally:
            if "AID_CONF__LLM__API_KEY" in os.environ:
                del os.environ["AID_CONF__LLM__API_KEY"]
    
    def test_config_source_logging(self):
        """测试配置来源日志"""
        manager = ConfigWhitelistManager()
        
        # 验证环境变量覆盖
        is_valid, message, log_info = manager.validate_env_override("app.debug", "true")
        
        # 检查日志信息包含来源
        assert "config_key" in log_info
        assert "value" in log_info
        assert "sensitivity_level" in log_info


class TestErrorHandling:
    """错误处理测试"""
    
    def test_invalid_json_handling(self):
        """测试无效JSON处理"""
        parser = EnvironmentVariableParser()
        
        # 无效JSON应该返回原字符串，不应该抛出异常
        result = parser._parse_value('{"invalid": json}', "test_key")
        assert result == '{"invalid": json}'
        
        # 检查解析摘要中的错误记录
        summary = parser.get_parsing_summary()
        # 这里不应该有解析错误，因为无效JSON被当作字符串处理
        assert summary["parsing_errors"] == 0
    
    def test_unknown_key_rejection(self):
        """测试未知键拒绝"""
        os.environ["AID_CONF__UNKNOWN__KEY"] = "should_be_rejected"
        
        try:
            allowed_keys = {"app.debug", "llm.temperature"}
            loader = EnvironmentConfigLoader(allowed_keys)
            config = loader.load_env_config()
            
            # 未知键应该被拒绝
            assert "unknown" not in config
            
        finally:
            if "AID_CONF__UNKNOWN__KEY" in os.environ:
                del os.environ["AID_CONF__UNKNOWN__KEY"]
    
    def test_empty_value_handling(self):
        """测试空值处理"""
        parser = EnvironmentVariableParser()
        
        # 空字符串应该返回空字符串
        assert parser._parse_value("", "test") == ""
        
        # None值应该被正确处理
        assert parser._parse_value(None, "test") is None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
