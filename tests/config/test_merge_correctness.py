#!/usr/bin/env python3
"""
配置合并正确性测试

测试配置覆盖链的正确性，确保 defaults → files → env → runtime 的合并逻辑正确
"""

import os
import pytest
import tempfile
import yaml
from pathlib import Path
from unittest.mock import patch, MagicMock
from typing import Dict, Any

from backend.config.manager import ConfigManager
from backend.config.modular_loader import ModularConfigLoader


class TestConfigMergeCorrectness:
    """配置合并正确性测试"""
    
    def setup_method(self):
        """测试前设置"""
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
        
        # 创建临时配置目录
        self.temp_dir = tempfile.mkdtemp()
        self.config_dir = Path(self.temp_dir)
    
    def teardown_method(self):
        """测试后清理"""
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
        
        # 清理临时文件
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def _create_config_files(self, configs: Dict[str, Dict[str, Any]]):
        """创建配置文件"""
        for filename, config_data in configs.items():
            file_path = self.config_dir / filename
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f)
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_simple_override_chain(self, mock_loader_class):
        """测试简单的覆盖链"""
        # 模拟文件配置
        file_config = {
            "app": {
                "debug": False,
                "name": "Test App",
                "version": "1.0.0"
            },
            "llm": {
                "temperature": 0.7,
                "max_tokens": 2000
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        # 设置环境变量覆盖
        os.environ["AID_CONF__APP__DEBUG"] = "true"
        os.environ["AID_CONF__LLM__TEMPERATURE"] = "0.9"
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 设置运行时覆盖
        config_manager.set_runtime("app.name", "Runtime App")
        
        # 验证覆盖链优先级
        assert config_manager.get("app.debug") is True  # 环境变量覆盖文件
        assert config_manager.get("app.name") == "Runtime App"  # 运行时覆盖环境变量
        assert config_manager.get("app.version") == "1.0.0"  # 保持文件配置
        assert config_manager.get("llm.temperature") == 0.9  # 环境变量覆盖文件
        assert config_manager.get("llm.max_tokens") == 2000  # 保持文件配置
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_nested_config_merge(self, mock_loader_class):
        """测试嵌套配置合并"""
        # 文件配置
        file_config = {
            "database": {
                "connection": {
                    "host": "localhost",
                    "port": 5432,
                    "timeout": 30,
                    "pool": {
                        "min_size": 1,
                        "max_size": 10
                    }
                }
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        # 环境变量部分覆盖
        os.environ["AID_CONF__DATABASE__CONNECTION__HOST"] = "remote-host"
        os.environ["AID_CONF__DATABASE__CONNECTION__POOL__MAX_SIZE"] = "20"
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 运行时部分覆盖
        config_manager.set_runtime("database.connection.timeout", 60)
        
        # 验证嵌套合并
        db_config = config_manager.get_section("database.connection")
        assert db_config["host"] == "remote-host"  # 环境变量覆盖
        assert db_config["port"] == 5432  # 保持文件配置
        assert db_config["timeout"] == 60  # 运行时覆盖
        assert db_config["pool"]["min_size"] == 1  # 保持文件配置
        assert db_config["pool"]["max_size"] == 20  # 环境变量覆盖
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_type_preservation(self, mock_loader_class):
        """测试类型保持"""
        file_config = {
            "test": {
                "boolean_value": False,
                "integer_value": 42,
                "float_value": 3.14,
                "string_value": "hello",
                "list_value": [1, 2, 3],
                "dict_value": {"key": "value"}
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        # 环境变量覆盖（测试类型转换）
        os.environ["AID_CONF__TEST__BOOLEAN_VALUE"] = "true"
        os.environ["AID_CONF__TEST__INTEGER_VALUE"] = "100"
        os.environ["AID_CONF__TEST__FLOAT_VALUE"] = "2.71"
        os.environ["AID_CONF__TEST__STRING_VALUE"] = "world"
        os.environ["AID_CONF__TEST__LIST_VALUE"] = "[4, 5, 6]"
        os.environ["AID_CONF__TEST__DICT_VALUE"] = '{"new_key": "new_value"}'
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 验证类型正确转换
        assert config_manager.get("test.boolean_value") is True
        assert config_manager.get("test.integer_value") == 100
        assert config_manager.get("test.float_value") == 2.71
        assert config_manager.get("test.string_value") == "world"
        assert config_manager.get("test.list_value") == [4, 5, 6]
        assert config_manager.get("test.dict_value") == {"new_key": "new_value"}
        
        # 验证类型
        assert isinstance(config_manager.get("test.boolean_value"), bool)
        assert isinstance(config_manager.get("test.integer_value"), int)
        assert isinstance(config_manager.get("test.float_value"), float)
        assert isinstance(config_manager.get("test.string_value"), str)
        assert isinstance(config_manager.get("test.list_value"), list)
        assert isinstance(config_manager.get("test.dict_value"), dict)
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_source_tracking_accuracy(self, mock_loader_class):
        """测试来源追踪准确性"""
        file_config = {
            "source_test": {
                "file_only": "from_file",
                "env_override": "from_file",
                "runtime_override": "from_file"
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        # 环境变量覆盖
        os.environ["AID_CONF__SOURCE_TEST__ENV_OVERRIDE"] = "from_env"
        os.environ["AID_CONF__SOURCE_TEST__RUNTIME_OVERRIDE"] = "from_env"
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 运行时覆盖
        config_manager.set_runtime("source_test.runtime_override", "from_runtime")
        
        # 验证来源追踪
        value, source = config_manager.get_with_source("source_test.file_only")
        assert value == "from_file"
        assert source == "config_files"
        
        value, source = config_manager.get_with_source("source_test.env_override")
        assert value == "from_env"
        assert source == "environment"
        
        value, source = config_manager.get_with_source("source_test.runtime_override")
        assert value == "from_runtime"
        assert source == "runtime"
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_partial_override_preservation(self, mock_loader_class):
        """测试部分覆盖时其他值的保持"""
        file_config = {
            "complex": {
                "section1": {
                    "key1": "value1",
                    "key2": "value2",
                    "key3": "value3"
                },
                "section2": {
                    "keyA": "valueA",
                    "keyB": "valueB"
                }
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        # 只覆盖部分键
        os.environ["AID_CONF__COMPLEX__SECTION1__KEY2"] = "overridden_value2"
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 验证部分覆盖
        assert config_manager.get("complex.section1.key1") == "value1"  # 未覆盖
        assert config_manager.get("complex.section1.key2") == "overridden_value2"  # 已覆盖
        assert config_manager.get("complex.section1.key3") == "value3"  # 未覆盖
        assert config_manager.get("complex.section2.keyA") == "valueA"  # 未覆盖
        assert config_manager.get("complex.section2.keyB") == "valueB"  # 未覆盖
        
        # 验证整个section仍然完整
        section1 = config_manager.get_section("complex.section1")
        assert len(section1) == 3
        assert section1["key1"] == "value1"
        assert section1["key2"] == "overridden_value2"
        assert section1["key3"] == "value3"
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_empty_and_null_values(self, mock_loader_class):
        """测试空值和null值的处理"""
        file_config = {
            "empty_test": {
                "empty_string": "",
                "null_value": None,
                "zero_value": 0,
                "false_value": False,
                "empty_list": [],
                "empty_dict": {}
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        # 环境变量覆盖空值
        os.environ["AID_CONF__EMPTY_TEST__EMPTY_STRING"] = "not_empty"
        os.environ["AID_CONF__EMPTY_TEST__NULL_VALUE"] = "not_null"
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 验证空值处理
        assert config_manager.get("empty_test.empty_string") == "not_empty"
        assert config_manager.get("empty_test.null_value") == "not_null"
        assert config_manager.get("empty_test.zero_value") == 0
        assert config_manager.get("empty_test.false_value") is False
        assert config_manager.get("empty_test.empty_list") == []
        assert config_manager.get("empty_test.empty_dict") == {}
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_deep_merge_behavior(self, mock_loader_class):
        """测试深度合并行为"""
        file_config = {
            "deep": {
                "level1": {
                    "level2": {
                        "level3": {
                            "key1": "file_value1",
                            "key2": "file_value2"
                        },
                        "other_key": "file_other"
                    }
                }
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        # 深层环境变量覆盖
        os.environ["AID_CONF__DEEP__LEVEL1__LEVEL2__LEVEL3__KEY1"] = "env_value1"
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 深层运行时覆盖
        config_manager.set_runtime("deep.level1.level2.level3.key2", "runtime_value2")
        
        # 验证深度合并
        deep_config = config_manager.get_section("deep.level1.level2.level3")
        assert deep_config["key1"] == "env_value1"  # 环境变量覆盖
        assert deep_config["key2"] == "runtime_value2"  # 运行时覆盖
        
        # 验证其他层级未受影响
        assert config_manager.get("deep.level1.level2.other_key") == "file_other"
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_config_reload_merge(self, mock_loader_class):
        """测试配置重新加载后的合并"""
        initial_config = {
            "reload_test": {
                "value1": "initial1",
                "value2": "initial2"
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = initial_config
        mock_loader_class.return_value = mock_loader
        
        # 环境变量
        os.environ["AID_CONF__RELOAD_TEST__VALUE1"] = "env_value1"
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 运行时配置
        config_manager.set_runtime("reload_test.value2", "runtime_value2")
        
        # 验证初始状态
        assert config_manager.get("reload_test.value1") == "env_value1"
        assert config_manager.get("reload_test.value2") == "runtime_value2"
        
        # 模拟配置文件更新
        updated_config = {
            "reload_test": {
                "value1": "updated1",
                "value2": "updated2",
                "value3": "new_value3"
            }
        }
        mock_loader.create_snapshot.return_value.config_data = updated_config
        
        # 重新加载
        config_manager.reload()
        
        # 验证重新加载后的合并
        assert config_manager.get("reload_test.value1") == "env_value1"  # 环境变量仍然覆盖
        assert config_manager.get("reload_test.value2") == "updated2"  # 运行时配置被清除
        assert config_manager.get("reload_test.value3") == "new_value3"  # 新值正常加载
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_merge_with_disabled_env_override(self, mock_loader_class):
        """测试禁用环境变量覆盖时的合并"""
        file_config = {
            "env_disabled_test": {
                "value1": "file_value1",
                "value2": "file_value2"
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        # 设置环境变量（应该被忽略）
        os.environ["AID_CONF__ENV_DISABLED_TEST__VALUE1"] = "env_value1"
        
        config_manager = ConfigManager(enable_env_override=False)
        
        # 运行时配置仍然有效
        config_manager.set_runtime("env_disabled_test.value2", "runtime_value2")
        
        # 验证环境变量被忽略，运行时配置有效
        assert config_manager.get("env_disabled_test.value1") == "file_value1"  # 环境变量被忽略
        assert config_manager.get("env_disabled_test.value2") == "runtime_value2"  # 运行时有效


class TestMergeEdgeCases:
    """合并边界情况测试"""
    
    def setup_method(self):
        """测试前设置"""
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
    
    def teardown_method(self):
        """测试后清理"""
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_conflicting_types_merge(self, mock_loader_class):
        """测试类型冲突的合并"""
        file_config = {
            "type_conflict": {
                "value": {"nested": "dict"}  # 字典类型
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        # 环境变量设置为字符串（类型冲突）
        os.environ["AID_CONF__TYPE_CONFLICT__VALUE"] = "string_value"
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 环境变量应该覆盖，即使类型不同
        assert config_manager.get("type_conflict.value") == "string_value"
        assert isinstance(config_manager.get("type_conflict.value"), str)
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_unicode_and_special_chars(self, mock_loader_class):
        """测试Unicode和特殊字符的合并"""
        file_config = {
            "unicode_test": {
                "chinese": "中文",
                "emoji": "😀",
                "special": "!@#$%^&*()"
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        # 环境变量包含Unicode
        os.environ["AID_CONF__UNICODE_TEST__CHINESE"] = "覆盖中文"
        os.environ["AID_CONF__UNICODE_TEST__EMOJI"] = "🎉"
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 验证Unicode正确处理
        assert config_manager.get("unicode_test.chinese") == "覆盖中文"
        assert config_manager.get("unicode_test.emoji") == "🎉"
        assert config_manager.get("unicode_test.special") == "!@#$%^&*()"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
