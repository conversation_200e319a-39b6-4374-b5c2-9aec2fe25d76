#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置合并链综合测试套件

全面测试配置合并链的各种场景，包括来源追踪、顺序覆盖、异常处理等
"""

import os
import pytest
import tempfile
import yaml
import json
from pathlib import Path
from unittest.mock import patch, MagicMock
from typing import Dict, Any, List

from backend.config.manager import ConfigManager
from backend.config.modular_loader import ModularConfigLoader
from backend.config.source_tracker import ConfigSourceTracker


class TestMergeChainComprehensive:
    """配置合并链综合测试"""
    
    def setup_method(self):
        """测试前设置"""
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
        
        # 创建临时配置目录
        self.temp_dir = tempfile.mkdtemp()
        self.config_dir = Path(self.temp_dir)
    
    def teardown_method(self):
        """测试后清理"""
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
        
        # 清理临时文件
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_source_tracking_accuracy(self, mock_loader_class):
        """测试来源追踪的准确性"""
        # 模拟文件配置
        file_config = {
            "app": {
                "name": "Test App",
                "debug": False,
                "features": {
                    "feature_a": True,
                    "feature_b": False
                }
            },
            "database": {
                "host": "localhost",
                "port": 5432
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        # 设置环境变量覆盖
        os.environ["AID_CONF__APP__DEBUG"] = "true"
        os.environ["AID_CONF__DATABASE__PORT"] = "3306"
        os.environ["AID_CONF__APP__FEATURES__FEATURE_B"] = "true"
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 设置运行时配置
        config_manager.set_runtime("app.name", "Runtime App")
        config_manager.set_runtime("database.host", "runtime-host")
        
        # 验证来源追踪的准确性
        test_cases = [
            ("app.name", "Runtime App", "runtime"),
            ("app.debug", True, "environment"),
            ("app.features.feature_a", True, "config_files"),
            ("app.features.feature_b", True, "environment"),
            ("database.host", "runtime-host", "runtime"),
            ("database.port", 3306, "environment")
        ]
        
        for key, expected_value, expected_source in test_cases:
            value, source = config_manager.get_with_source(key)
            assert value == expected_value, f"键 {key} 的值不匹配: {value} != {expected_value}"
            assert source == expected_source, f"键 {key} 的来源不匹配: {source} != {expected_source}"
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_merge_order_priority(self, mock_loader_class):
        """测试合并顺序优先级"""
        # 模拟文件配置
        file_config = {
            "priority_test": {
                "value1": "from_file",
                "value2": "from_file",
                "value3": "from_file",
                "value4": "from_file"
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        # 环境变量覆盖（优先级：文件 < 环境变量）
        os.environ["AID_CONF__PRIORITY_TEST__VALUE2"] = "from_env"
        os.environ["AID_CONF__PRIORITY_TEST__VALUE3"] = "from_env"
        os.environ["AID_CONF__PRIORITY_TEST__VALUE4"] = "from_env"
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 运行时覆盖（优先级：环境变量 < 运行时）
        config_manager.set_runtime("priority_test.value3", "from_runtime")
        config_manager.set_runtime("priority_test.value4", "from_runtime")
        
        # 验证优先级顺序
        assert config_manager.get("priority_test.value1") == "from_file"      # 只有文件
        assert config_manager.get("priority_test.value2") == "from_env"       # 环境变量覆盖文件
        assert config_manager.get("priority_test.value3") == "from_runtime"   # 运行时覆盖环境变量
        assert config_manager.get("priority_test.value4") == "from_runtime"   # 运行时最高优先级
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_deep_merge_behavior(self, mock_loader_class):
        """测试深度合并行为"""
        # 模拟文件配置
        file_config = {
            "deep_merge": {
                "level1": {
                    "level2": {
                        "file_key1": "file_value1",
                        "file_key2": "file_value2",
                        "shared_key": "from_file"
                    },
                    "file_only": "file_value"
                }
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        # 环境变量部分覆盖
        os.environ["AID_CONF__DEEP_MERGE__LEVEL1__LEVEL2__SHARED_KEY"] = "from_env"
        os.environ["AID_CONF__DEEP_MERGE__LEVEL1__LEVEL2__ENV_KEY"] = "env_value"
        os.environ["AID_CONF__DEEP_MERGE__LEVEL1__ENV_ONLY"] = "env_value"
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 运行时部分覆盖
        config_manager.set_runtime("deep_merge.level1.level2.shared_key", "from_runtime")
        config_manager.set_runtime("deep_merge.level1.runtime_only", "runtime_value")
        
        # 验证深度合并结果
        level2_config = config_manager.get_section("deep_merge.level1.level2")
        assert level2_config["file_key1"] == "file_value1"      # 文件配置保留
        assert level2_config["file_key2"] == "file_value2"      # 文件配置保留
        assert level2_config["shared_key"] == "from_runtime"    # 运行时覆盖
        assert level2_config["env_key"] == "env_value"          # 环境变量新增
        
        level1_config = config_manager.get_section("deep_merge.level1")
        assert level1_config["file_only"] == "file_value"       # 文件配置保留
        assert level1_config["env_only"] == "env_value"         # 环境变量新增
        assert level1_config["runtime_only"] == "runtime_value" # 运行时新增
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_config_reload_merge_consistency(self, mock_loader_class):
        """测试配置重新加载后的合并一致性"""
        # 初始文件配置
        initial_config = {
            "reload_test": {
                "persistent_key": "initial_value",
                "changeable_key": "initial_value"
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = initial_config
        mock_loader_class.return_value = mock_loader
        
        # 环境变量和运行时配置
        os.environ["AID_CONF__RELOAD_TEST__PERSISTENT_KEY"] = "env_value"
        
        config_manager = ConfigManager(enable_env_override=True)
        config_manager.set_runtime("reload_test.changeable_key", "runtime_value")
        
        # 验证初始状态
        assert config_manager.get("reload_test.persistent_key") == "env_value"
        assert config_manager.get("reload_test.changeable_key") == "runtime_value"
        
        # 模拟文件配置更新
        updated_config = {
            "reload_test": {
                "persistent_key": "updated_value",
                "changeable_key": "updated_value",
                "new_key": "new_value"
            }
        }
        mock_loader.create_snapshot.return_value.config_data = updated_config
        
        # 重新加载配置
        config_manager.reload()
        
        # 验证重新加载后的合并一致性
        assert config_manager.get("reload_test.persistent_key") == "env_value"      # 环境变量仍然覆盖
        assert config_manager.get("reload_test.changeable_key") == "updated_value"  # 运行时配置被清除
        assert config_manager.get("reload_test.new_key") == "new_value"             # 新键正常加载
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_circular_reference_handling(self, mock_loader_class):
        """测试循环引用处理"""
        # 模拟包含循环引用的配置
        file_config = {
            "circular": {
                "key1": "${circular.key2}",
                "key2": "${circular.key3}",
                "key3": "${circular.key1}",  # 循环引用
                "normal_key": "normal_value"
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        config_manager = ConfigManager(enable_env_override=False)
        
        # 验证循环引用不会导致无限递归
        # 应该返回原始字符串值而不是解析后的值
        assert config_manager.get("circular.key1") == "${circular.key2}"
        assert config_manager.get("circular.key2") == "${circular.key3}"
        assert config_manager.get("circular.key3") == "${circular.key1}"
        assert config_manager.get("circular.normal_key") == "normal_value"
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_large_config_merge_performance(self, mock_loader_class):
        """测试大配置合并性能"""
        import time
        
        # 生成大量配置数据
        large_config = {}
        for i in range(100):
            section = f"section_{i}"
            large_config[section] = {}
            for j in range(50):
                large_config[section][f"key_{j}"] = f"value_{i}_{j}"
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = large_config
        mock_loader_class.return_value = mock_loader
        
        # 设置大量环境变量覆盖
        for i in range(0, 100, 10):  # 每10个section设置一个覆盖
            os.environ[f"AID_CONF__SECTION_{i}__KEY_0"] = f"env_override_{i}"
        
        # 测试合并性能
        start_time = time.time()
        config_manager = ConfigManager(enable_env_override=True)
        
        # 设置运行时配置
        for i in range(0, 100, 20):  # 每20个section设置一个运行时覆盖
            config_manager.set_runtime(f"section_{i}.key_1", f"runtime_override_{i}")
        
        merge_time = time.time() - start_time
        
        # 验证合并结果正确性
        assert config_manager.get("section_0.key_0") == "env_override_0"
        assert config_manager.get("section_0.key_1") == "runtime_override_0"
        assert config_manager.get("section_1.key_0") == "value_1_0"  # 未被覆盖
        
        # 验证性能（合并时间应该在合理范围内）
        assert merge_time < 1.0, f"大配置合并时间过长: {merge_time:.3f}s"
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_concurrent_access_thread_safety(self, mock_loader_class):
        """测试并发访问的线程安全性"""
        import threading
        import time
        
        file_config = {
            "concurrent": {
                "shared_counter": 0,
                "test_value": "initial"
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 并发读写测试
        results = []
        errors = []
        
        def reader_thread(thread_id):
            """读取线程"""
            try:
                for i in range(100):
                    value = config_manager.get("concurrent.test_value")
                    results.append(f"reader_{thread_id}_{i}: {value}")
                    time.sleep(0.001)
            except Exception as e:
                errors.append(f"Reader {thread_id} error: {e}")
        
        def writer_thread(thread_id):
            """写入线程"""
            try:
                for i in range(50):
                    config_manager.set_runtime("concurrent.test_value", f"writer_{thread_id}_{i}")
                    time.sleep(0.002)
            except Exception as e:
                errors.append(f"Writer {thread_id} error: {e}")
        
        # 启动多个读写线程
        threads = []
        for i in range(3):
            threads.append(threading.Thread(target=reader_thread, args=(i,)))
            threads.append(threading.Thread(target=writer_thread, args=(i,)))
        
        # 启动所有线程
        for thread in threads:
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=10)
        
        # 验证没有发生异常
        assert len(errors) == 0, f"并发访问出现错误: {errors}"
        assert len(results) > 0, "没有读取到任何结果"
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_memory_usage_stability(self, mock_loader_class):
        """测试内存使用稳定性"""
        import gc
        import sys
        
        file_config = {
            "memory_test": {
                "data": "initial_data"
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = file_config
        mock_loader_class.return_value = mock_loader
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 记录初始内存使用
        gc.collect()
        initial_objects = len(gc.get_objects())
        
        # 执行大量配置操作
        for i in range(1000):
            # 设置运行时配置
            config_manager.set_runtime(f"memory_test.temp_key_{i % 10}", f"temp_value_{i}")
            
            # 读取配置
            config_manager.get("memory_test.data")
            config_manager.get_with_source("memory_test.data")
            
            # 定期清理运行时配置
            if i % 100 == 0:
                for j in range(10):
                    config_manager.clear_runtime(f"memory_test.temp_key_{j}")
        
        # 检查内存使用
        gc.collect()
        final_objects = len(gc.get_objects())
        
        # 验证内存没有显著增长（允许一定的增长空间）
        object_growth = final_objects - initial_objects
        assert object_growth < 1000, f"内存对象增长过多: {object_growth}"
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_error_recovery_and_fallback(self, mock_loader_class):
        """测试错误恢复和回退机制"""
        # 模拟正常配置
        normal_config = {
            "error_test": {
                "normal_key": "normal_value",
                "fallback_key": "original_value"
            }
        }
        
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = normal_config
        mock_loader_class.return_value = mock_loader
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 验证正常情况
        assert config_manager.get("error_test.normal_key") == "normal_value"
        
        # 模拟配置加载错误
        mock_loader.create_snapshot.side_effect = Exception("Config load error")
        
        # 重新加载应该使用缓存的配置
        config_manager.reload()
        
        # 验证仍然可以访问之前的配置
        assert config_manager.get("error_test.normal_key") == "normal_value"
        
        # 验证默认值回退
        assert config_manager.get("error_test.nonexistent_key", "default_value") == "default_value"


class TestSourceTrackerEdgeCases:
    """来源追踪器边界情况测试"""
    
    def test_source_tracker_with_complex_keys(self):
        """测试复杂键的来源追踪"""
        tracker = ConfigSourceTracker()
        
        # 测试各种复杂键格式
        test_cases = [
            "simple_key",
            "nested.key",
            "deeply.nested.key.with.many.levels",
            "key_with_underscores",
            "key-with-dashes",
            "key.with.123.numbers",
            "key.with.special@chars",
        ]
        
        for key in test_cases:
            tracker.set_source(key, "test_source")
            source = tracker.get_source(key)
            assert source == "test_source", f"键 {key} 的来源追踪失败"
    
    def test_source_tracker_memory_efficiency(self):
        """测试来源追踪器的内存效率"""
        tracker = ConfigSourceTracker()
        
        # 添加大量键
        for i in range(10000):
            key = f"test.key.{i}"
            source = f"source_{i % 10}"  # 重复使用源名称
            tracker.set_source(key, source)
        
        # 验证可以正确检索
        assert tracker.get_source("test.key.5000") == "source_0"
        assert tracker.get_source("test.key.9999") == "source_9"
        
        # 验证摘要功能
        summary = tracker.get_source_summary()
        assert summary["total_keys"] == 10000
        assert len(summary["sources"]) == 10


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
