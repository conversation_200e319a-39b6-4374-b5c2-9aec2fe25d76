#!/usr/bin/env python3
"""
配置覆盖链测试

测试 defaults → files → env → runtime 的覆盖链和来源追踪
"""

import os
import pytest
import tempfile
import yaml
from pathlib import Path
from unittest.mock import patch, MagicMock

from backend.config.manager import ConfigManager
from backend.config.modular_loader import ModularConfigLoader


class TestConfigOverrideChain:
    """配置覆盖链测试"""
    
    def setup_method(self):
        """测试前设置"""
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
        
        # 创建临时配置目录
        self.temp_dir = tempfile.mkdtemp()
        self.config_dir = Path(self.temp_dir)
        
        # 创建测试配置文件
        self._create_test_config_files()
    
    def teardown_method(self):
        """测试后清理"""
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
        
        # 清理临时文件
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def _create_test_config_files(self):
        """创建测试配置文件"""
        # 创建默认配置
        defaults_dir = self.config_dir / "defaults"
        defaults_dir.mkdir(parents=True, exist_ok=True)
        
        app_defaults = {
            "app": {
                "debug": False,
                "name": "AI Assistant",
                "version": "1.0.0"
            }
        }
        
        with open(defaults_dir / "app.yaml", 'w') as f:
            yaml.dump(app_defaults, f)
        
        llm_defaults = {
            "llm": {
                "default_model": "gpt-3.5-turbo",
                "temperature": 0.7,
                "max_tokens": 2000
            }
        }
        
        with open(defaults_dir / "llm.yaml", 'w') as f:
            yaml.dump(llm_defaults, f)
        
        # 创建环境配置文件
        env_config = {
            "app": {
                "debug": True,  # 覆盖默认值
                "environment": "development"
            },
            "llm": {
                "temperature": 0.8  # 覆盖默认值
            }
        }
        
        with open(self.config_dir / "development.yaml", 'w') as f:
            yaml.dump(env_config, f)
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_defaults_to_files_override(self, mock_loader_class):
        """测试默认配置到文件配置的覆盖"""
        # 模拟模块化加载器
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = {
            "app": {
                "debug": True,  # 文件配置覆盖默认值
                "name": "AI Assistant",
                "version": "1.0.0"
            },
            "llm": {
                "default_model": "gpt-3.5-turbo",
                "temperature": 0.8,  # 文件配置覆盖默认值
                "max_tokens": 2000
            }
        }
        mock_loader_class.return_value = mock_loader
        
        config_manager = ConfigManager(enable_env_override=False)
        
        # 验证文件配置覆盖了默认配置
        assert config_manager.get("app.debug") is True  # 被文件覆盖
        assert config_manager.get("app.name") == "AI Assistant"  # 保持默认值
        assert config_manager.get("llm.temperature") == 0.8  # 被文件覆盖
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_files_to_env_override(self, mock_loader_class):
        """测试文件配置到环境变量的覆盖"""
        # 模拟模块化加载器
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = {
            "app": {
                "debug": True,
                "name": "AI Assistant"
            },
            "llm": {
                "temperature": 0.8,
                "max_tokens": 2000
            }
        }
        mock_loader_class.return_value = mock_loader
        
        # 设置环境变量
        os.environ["AID_CONF__APP__DEBUG"] = "false"  # 覆盖文件配置
        os.environ["AID_CONF__LLM__MAX_TOKENS"] = "4000"  # 覆盖文件配置
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 验证环境变量覆盖了文件配置
        assert config_manager.get("app.debug") is False  # 被环境变量覆盖
        assert config_manager.get("app.name") == "AI Assistant"  # 保持文件配置
        assert config_manager.get("llm.max_tokens") == 4000  # 被环境变量覆盖
        assert config_manager.get("llm.temperature") == 0.8  # 保持文件配置
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_env_to_runtime_override(self, mock_loader_class):
        """测试环境变量到运行时配置的覆盖"""
        # 模拟模块化加载器
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = {
            "app": {"debug": True}
        }
        mock_loader_class.return_value = mock_loader
        
        # 设置环境变量
        os.environ["AID_CONF__APP__DEBUG"] = "false"
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 验证环境变量生效
        assert config_manager.get("app.debug") is False
        
        # 设置运行时配置
        config_manager.set_runtime("app.debug", True)
        
        # 验证运行时配置覆盖了环境变量
        assert config_manager.get("app.debug") is True
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_complete_override_chain(self, mock_loader_class):
        """测试完整的覆盖链"""
        # 模拟模块化加载器（文件配置）
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = {
            "test": {
                "value": "from_file",
                "file_only": "file_value"
            }
        }
        mock_loader_class.return_value = mock_loader
        
        # 设置环境变量
        os.environ["AID_CONF__TEST__VALUE"] = "from_env"
        os.environ["AID_CONF__TEST__ENV_ONLY"] = "env_value"
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 设置运行时配置
        config_manager.set_runtime("test.value", "from_runtime")
        config_manager.set_runtime("test.runtime_only", "runtime_value")
        
        # 验证覆盖链优先级
        assert config_manager.get("test.value") == "from_runtime"  # 运行时最高优先级
        assert config_manager.get("test.env_only") == "env_value"  # 环境变量次之
        assert config_manager.get("test.file_only") == "file_value"  # 文件配置再次
        assert config_manager.get("test.runtime_only") == "runtime_value"  # 运行时独有
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_source_tracking(self, mock_loader_class):
        """测试来源追踪"""
        # 模拟模块化加载器
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = {
            "test": {
                "file_value": "from_file"
            }
        }
        mock_loader_class.return_value = mock_loader
        
        # 设置环境变量
        os.environ["AID_CONF__TEST__ENV_VALUE"] = "from_env"
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 设置运行时配置
        config_manager.set_runtime("test.runtime_value", "from_runtime")
        
        # 验证来源追踪
        value, source = config_manager.get_with_source("test.file_value")
        assert value == "from_file"
        assert source == "config_files"
        
        value, source = config_manager.get_with_source("test.env_value")
        assert value == "from_env"
        assert source == "environment"
        
        value, source = config_manager.get_with_source("test.runtime_value")
        assert value == "from_runtime"
        assert source == "runtime"
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_nested_config_override(self, mock_loader_class):
        """测试嵌套配置的覆盖"""
        # 模拟模块化加载器
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = {
            "database": {
                "connection": {
                    "host": "localhost",
                    "port": 5432,
                    "timeout": 30
                }
            }
        }
        mock_loader_class.return_value = mock_loader
        
        # 设置环境变量覆盖嵌套配置
        os.environ["AID_CONF__DATABASE__CONNECTION__HOST"] = "remote-host"
        os.environ["AID_CONF__DATABASE__CONNECTION__PORT"] = "3306"
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 验证嵌套配置覆盖
        assert config_manager.get("database.connection.host") == "remote-host"
        assert config_manager.get("database.connection.port") == 3306
        assert config_manager.get("database.connection.timeout") == 30  # 未被覆盖
        
        # 验证获取整个配置段
        db_config = config_manager.get_section("database.connection")
        assert db_config["host"] == "remote-host"
        assert db_config["port"] == 3306
        assert db_config["timeout"] == 30
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_config_reload(self, mock_loader_class):
        """测试配置重新加载"""
        # 模拟模块化加载器
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = {
            "test": {"value": "original"}
        }
        mock_loader_class.return_value = mock_loader
        
        config_manager = ConfigManager(enable_env_override=False)
        
        # 验证初始值
        assert config_manager.get("test.value") == "original"
        
        # 修改模拟数据
        mock_loader.create_snapshot.return_value.config_data = {
            "test": {"value": "updated"}
        }
        
        # 重新加载配置
        config_manager.reload()
        
        # 验证配置已更新
        assert config_manager.get("test.value") == "updated"
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_default_value_fallback(self, mock_loader_class):
        """测试默认值回退"""
        # 模拟空的模块化加载器
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = {}
        mock_loader_class.return_value = mock_loader
        
        config_manager = ConfigManager(enable_env_override=False)
        
        # 测试不存在的键返回默认值
        assert config_manager.get("nonexistent.key", "default") == "default"
        assert config_manager.get("nonexistent.key") is None
        
        # 测试来源追踪返回unknown
        value, source = config_manager.get_with_source("nonexistent.key", "default")
        assert value == "default"
        assert source == "unknown"
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_config_summary(self, mock_loader_class):
        """测试配置摘要"""
        # 模拟模块化加载器
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = {
            "app": {"debug": True}
        }
        mock_loader_class.return_value = mock_loader
        
        # 设置环境变量
        os.environ["AID_CONF__LLM__TEMPERATURE"] = "0.8"
        
        config_manager = ConfigManager(enable_env_override=True)
        
        # 设置运行时配置
        config_manager.set_runtime("test.runtime", "value")
        
        # 获取配置摘要
        summary = config_manager.get_config_summary()
        
        assert summary["config_loaded"] is True
        assert summary["env_override_enabled"] is True
        assert "keys_by_source" in summary
        assert "config_files" in summary["keys_by_source"]
        assert "environment" in summary["keys_by_source"]
        assert "runtime" in summary["keys_by_source"]


class TestErrorScenarios:
    """错误场景测试"""
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_config_loading_failure(self, mock_loader_class):
        """测试配置加载失败"""
        # 模拟加载器抛出异常
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.side_effect = Exception("Loading failed")
        mock_loader_class.return_value = mock_loader
        
        # 配置管理器应该能处理加载失败
        with pytest.raises(Exception):
            ConfigManager(enable_env_override=False)
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_env_override_disabled(self, mock_loader_class):
        """测试环境变量覆盖禁用"""
        # 模拟模块化加载器
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = {
            "app": {"debug": False}
        }
        mock_loader_class.return_value = mock_loader
        
        # 设置环境变量
        os.environ["AID_CONF__APP__DEBUG"] = "true"
        
        # 禁用环境变量覆盖
        config_manager = ConfigManager(enable_env_override=False)
        
        # 环境变量应该不生效
        assert config_manager.get("app.debug") is False
        
        # 验证环境变量覆盖验证返回禁用信息
        is_valid, message = config_manager.validate_env_override("app.debug", "true")
        assert is_valid is False
        assert "禁用" in message


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
