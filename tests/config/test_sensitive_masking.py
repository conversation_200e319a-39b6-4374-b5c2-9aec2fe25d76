#!/usr/bin/env python3
"""
敏感信息脱敏测试

测试敏感信息检测、脱敏和日志安全
"""

import os
import pytest
import logging
from io import StringIO
from unittest.mock import patch

from backend.config.security.whitelist_manager import (
    SensitiveInfoDetector, ConfigWhitelistManager, SensitivityLevel
)
from backend.config.env_parser import EnvironmentVariableParser
from backend.config.manager import ConfigManager


class TestSensitiveInfoDetector:
    """敏感信息检测器测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.detector = SensitiveInfoDetector()
    
    def test_detect_sensitive_keys(self):
        """测试敏感键检测"""
        # 测试各种敏感键模式
        sensitive_keys = [
            "llm.openai.api_key",
            "database.password",
            "system.secret_key",
            "auth.jwt_token",
            "security.private_key",
            "service.credential",
            "config.encryption_key"
        ]
        
        for key in sensitive_keys:
            is_sensitive, level, reason = self.detector.detect_sensitive_key(key)
            assert is_sensitive is True, f"键 '{key}' 应该被识别为敏感"
            assert level in [SensitivityLevel.SECRET, SensitivityLevel.CONFIDENTIAL, SensitivityLevel.INTERNAL]
            assert reason != ""
    
    def test_detect_non_sensitive_keys(self):
        """测试非敏感键检测"""
        non_sensitive_keys = [
            "app.debug",
            "llm.temperature",
            "database.timeout",
            "system.version",
            "performance.cache.enabled"
        ]
        
        for key in non_sensitive_keys:
            is_sensitive, level, reason = self.detector.detect_sensitive_key(key)
            assert is_sensitive is False, f"键 '{key}' 不应该被识别为敏感"
            assert level == SensitivityLevel.PUBLIC
    
    def test_detect_sensitive_values(self):
        """测试敏感值检测"""
        # OpenAI API密钥
        openai_key = "sk-1234567890abcdef1234567890abcdef"
        is_sensitive, level, reason = self.detector.detect_sensitive_value(openai_key)
        assert is_sensitive is True
        assert level == SensitivityLevel.SECRET
        assert "OpenAI API密钥" in reason
        
        # JWT令牌
        jwt_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
        is_sensitive, level, reason = self.detector.detect_sensitive_value(jwt_token)
        assert is_sensitive is True
        assert level == SensitivityLevel.SECRET
        assert "JWT令牌" in reason
        
        # 数据库连接字符串
        db_connection = "mysql://user:password@localhost:3306/database"
        is_sensitive, level, reason = self.detector.detect_sensitive_value(db_connection)
        assert is_sensitive is True
        assert level == SensitivityLevel.CONFIDENTIAL
        
        # 长随机字符串（可能是密钥）
        long_random = "a" * 40
        is_sensitive, level, reason = self.detector.detect_sensitive_value(long_random)
        assert is_sensitive is True
        assert level == SensitivityLevel.CONFIDENTIAL
    
    def test_detect_non_sensitive_values(self):
        """测试非敏感值检测"""
        non_sensitive_values = [
            "true",
            "false",
            "gpt-3.5-turbo",
            "0.7",
            "localhost",
            "development",
            "short"
        ]
        
        for value in non_sensitive_values:
            is_sensitive, level, reason = self.detector.detect_sensitive_value(value)
            assert is_sensitive is False, f"值 '{value}' 不应该被识别为敏感"
            assert level == SensitivityLevel.PUBLIC
    
    def test_mask_sensitive_values(self):
        """测试敏感值遮蔽"""
        test_cases = [
            ("public_value", SensitivityLevel.PUBLIC, "public_value"),
            ("internal_value", SensitivityLevel.INTERNAL, "[INTERNAL:14 chars]"),
            ("confidential_value", SensitivityLevel.CONFIDENTIAL, "[CONFIDENTIAL]"),
            ("secret_value", SensitivityLevel.SECRET, "[SECRET]")
        ]
        
        for value, level, expected in test_cases:
            masked = self.detector.mask_sensitive_value(value, level)
            assert masked == expected, f"敏感度 {level.value} 的值遮蔽不正确"


class TestConfigWhitelistManagerSecurity:
    """配置白名单管理器安全测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.manager = ConfigWhitelistManager()
    
    def test_validate_sensitive_config_override(self):
        """测试敏感配置覆盖验证"""
        # 测试API密钥覆盖
        is_valid, message, log_info = self.manager.validate_env_override(
            "llm.openai.api_key", 
            "sk-1234567890abcdef1234567890abcdef"
        )
        
        assert is_valid is True  # 应该允许，但要脱敏
        assert log_info["is_sensitive"] is True
        assert log_info["sensitivity_level"] in ["secret", "confidential"]
        assert log_info["value"] in ["[SECRET]", "[CONFIDENTIAL]"]  # 应该被脱敏
    
    def test_validate_non_sensitive_config_override(self):
        """测试非敏感配置覆盖验证"""
        is_valid, message, log_info = self.manager.validate_env_override(
            "app.debug", 
            "true"
        )
        
        assert is_valid is True
        assert log_info["is_sensitive"] is False
        assert log_info["sensitivity_level"] == "public"
        assert log_info["value"] == "true"  # 不应该被脱敏
    
    def test_validate_unknown_key_rejection(self):
        """测试未知键拒绝"""
        is_valid, message, log_info = self.manager.validate_env_override(
            "unknown.sensitive.key", 
            "sk-1234567890abcdef1234567890abcdef"
        )
        
        assert is_valid is False
        assert "不允许" in message
    
    def test_security_summary(self):
        """测试安全配置摘要"""
        summary = self.manager.get_security_summary()
        
        assert "total_rules" in summary
        assert "sensitive_patterns" in summary
        assert "rules_by_sensitivity" in summary
        
        # 验证各敏感度级别都有规则
        sensitivity_counts = summary["rules_by_sensitivity"]
        assert sensitivity_counts["public"] > 0
        assert sensitivity_counts["internal"] > 0
        assert sensitivity_counts["confidential"] > 0
        assert sensitivity_counts["secret"] > 0


class TestEnvironmentParserSecurity:
    """环境变量解析器安全测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.parser = EnvironmentVariableParser()
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
    
    def teardown_method(self):
        """测试后清理"""
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
    
    def test_sensitive_value_safe_logging(self):
        """测试敏感值安全日志"""
        # API密钥应该被脱敏
        api_key = "sk-1234567890abcdef1234567890abcdef"
        safe_value = self.parser._safe_log_value(api_key)
        assert safe_value == "[REDACTED]"
        assert api_key not in safe_value
        
        # 长字符串应该被截断
        long_string = "a" * 200
        safe_value = self.parser._safe_log_value(long_string)
        assert "[str:" in safe_value and "chars]" in safe_value
        assert long_string not in safe_value
        
        # 普通值应该正常显示
        normal_value = "hello"
        safe_value = self.parser._safe_log_value(normal_value)
        assert safe_value == "hello"
    
    def test_parsing_with_sensitive_values(self):
        """测试包含敏感值的解析"""
        # 设置敏感环境变量
        os.environ["AID_CONF__LLM__API_KEY"] = "sk-1234567890abcdef1234567890abcdef"
        os.environ["AID_CONF__DATABASE__PASSWORD"] = "super_secret_password"
        os.environ["AID_CONF__APP__DEBUG"] = "true"
        
        config = self.parser.parse_all_env_vars()
        
        # 验证配置正确解析
        assert config["llm"]["api_key"] == "sk-1234567890abcdef1234567890abcdef"
        assert config["database"]["password"] == "super_secret_password"
        assert config["app"]["debug"] is True
        
        # 验证解析摘要不包含敏感信息
        summary = self.parser.get_parsing_summary()
        assert summary["total_parsed"] == 3
        
        # 检查解析记录中的敏感信息脱敏
        for env_name, record in self.parser.parsed_vars.items():
            if "api_key" in env_name.lower() or "password" in env_name.lower():
                # 敏感信息应该被脱敏
                assert record["parsed_value"] != "[REDACTED]"  # 实际值不应该被脱敏存储
                # 但日志中应该被脱敏（这在实际日志输出中验证）


class TestLoggingSecurity:
    """日志安全测试"""
    
    def setup_method(self):
        """测试前设置"""
        # 设置日志捕获
        self.log_stream = StringIO()
        self.handler = logging.StreamHandler(self.log_stream)
        self.logger = logging.getLogger("backend.config")
        self.logger.addHandler(self.handler)
        self.logger.setLevel(logging.DEBUG)
        
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
    
    def teardown_method(self):
        """测试后清理"""
        self.logger.removeHandler(self.handler)
        self.handler.close()
        
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_sensitive_info_not_in_logs(self, mock_loader_class):
        """测试敏感信息不出现在日志中"""
        # 模拟模块化加载器
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = {}
        mock_loader_class.return_value = mock_loader
        
        # 设置敏感环境变量
        sensitive_api_key = "sk-1234567890abcdef1234567890abcdef"
        os.environ["AID_CONF__LLM__API_KEY"] = sensitive_api_key
        
        # 创建配置管理器（这会触发日志）
        config_manager = ConfigManager(enable_env_override=True)
        
        # 获取日志内容
        log_content = self.log_stream.getvalue()
        
        # 验证敏感信息不出现在日志中
        assert sensitive_api_key not in log_content, "敏感API密钥出现在日志中"
        
        # 验证脱敏标记出现在日志中
        assert any(marker in log_content for marker in ["[REDACTED]", "[SECRET]", "[CONFIDENTIAL]"]), \
            "日志中应该包含脱敏标记"
    
    def test_config_source_logging_security(self):
        """测试配置来源日志安全"""
        manager = ConfigWhitelistManager()
        
        # 验证敏感配置
        is_valid, message, log_info = manager.validate_env_override(
            "llm.openai.api_key", 
            "sk-1234567890abcdef1234567890abcdef"
        )
        
        # 验证日志信息中敏感值被脱敏
        assert log_info["value"] in ["[SECRET]", "[CONFIDENTIAL]", "[REDACTED]"]
        assert "sk-1234567890abcdef1234567890abcdef" not in str(log_info)


class TestSecurityIntegration:
    """安全集成测试"""
    
    def setup_method(self):
        """测试前设置"""
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
    
    def teardown_method(self):
        """测试后清理"""
        # 清理环境变量
        for key in list(os.environ.keys()):
            if key.startswith("AID_CONF__"):
                del os.environ[key]
    
    @patch('backend.config.manager.ModularConfigLoader')
    def test_end_to_end_security(self, mock_loader_class):
        """测试端到端安全"""
        # 模拟模块化加载器
        mock_loader = MagicMock()
        mock_loader.enabled = True
        mock_loader.create_snapshot.return_value.config_data = {
            "llm": {"default_model": "gpt-3.5-turbo"}
        }
        mock_loader_class.return_value = mock_loader
        
        # 设置混合环境变量（敏感和非敏感）
        os.environ["AID_CONF__LLM__API_KEY"] = "sk-1234567890abcdef1234567890abcdef"
        os.environ["AID_CONF__APP__DEBUG"] = "true"
        os.environ["AID_CONF__DATABASE__PASSWORD"] = "secret_password"
        
        # 创建配置管理器
        config_manager = ConfigManager(enable_env_override=True)
        
        # 验证配置正确加载
        assert config_manager.get("llm.api_key") == "sk-1234567890abcdef1234567890abcdef"
        assert config_manager.get("app.debug") is True
        assert config_manager.get("database.password") == "secret_password"
        
        # 验证来源追踪
        api_key_value, api_key_source = config_manager.get_with_source("llm.api_key")
        assert api_key_value == "sk-1234567890abcdef1234567890abcdef"
        assert api_key_source == "environment"
        
        debug_value, debug_source = config_manager.get_with_source("app.debug")
        assert debug_value is True
        assert debug_source == "environment"
        
        # 验证配置摘要不泄露敏感信息
        summary = config_manager.get_config_summary()
        summary_str = str(summary)
        assert "sk-1234567890abcdef1234567890abcdef" not in summary_str
        assert "secret_password" not in summary_str


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
