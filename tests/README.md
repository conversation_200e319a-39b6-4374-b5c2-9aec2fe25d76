# 测试套件说明文档

本目录包含了项目的各类测试文件，用于确保系统功能的正确性和稳定性。以下是对每个测试文件的详细说明。

## 目录结构

```
tests/
├── config/                    # 配置相关测试
├── run_integration_tests.py   # 集成测试运行器
├── functional_integration_test.py  # 功能集成测试
├── comprehensive_business_test.py  # 综合业务测试
├── test_composite_handler.py       # 复合处理器测试
├── test_message_templates.py       # 消息模板测试
├── test_message_template_integration.py  # 消息模板集成测试
├── test_threshold_parameters.py    # 阈值参数测试
├── test_threshold_integration.py   # 阈值集成测试
├── test_unified_decision_engine.py # 统一决策引擎测试
└── test_unified_decision_engine_complete.py  # 完整决策引擎测试
```

## 测试文件详细说明

### 1. 集成测试运行器

#### run_integration_tests.py
- **作用**: 运行完整的集成测试套件，生成详细的测试报告，包含性能基准对比分析和业务逻辑完整性验证
- **使用方法**: 
  ```bash
  python run_integration_tests.py [--verbose] [--output-file report.json]
  ```
- **主要功能**:
  - 执行综合集成测试
  - 生成测试报告
  - 性能基准对比分析
  - 业务逻辑完整性验证
  - 小范围用户场景模拟

### 2. 功能集成测试

#### functional_integration_test.py
- **作用**: 验证修改后的文件是否正常工作，测试策略、处理器和Agent的导入功能
- **使用方法**: 
  ```bash
  python functional_integration_test.py
  ```
- **主要功能**:
  - 测试策略文件导入
  - 测试处理器文件导入
  - 测试Agent文件导入
  - 测试消息模板访问
  - 测试阈值配置访问

### 3. 综合业务测试

#### comprehensive_business_test.py
- **作用**: 综合业务流程测试套件，覆盖知识库查询、关注点流程、复合意图识别等业务功能
- **使用方法**: 
  ```bash
  python comprehensive_business_test.py
  ```
- **主要功能**:
  - 知识库查询功能测试
  - 关注点流程完整性测试
  - 复合意图识别准确性测试
  - 决策策略正确性测试
  - 端到端业务流程集成测试

### 4. 复合处理器测试

#### test_composite_handler.py
- **作用**: 测试复合处理器的功能，验证其在处理复杂请求时的正确性
- **使用方法**: 
  ```bash
  python -m pytest test_composite_handler.py
  ```
- **主要功能**:
  - 验证复合处理器的请求处理逻辑
  - 测试多种处理器的协同工作

### 5. 消息模板测试

#### test_message_templates.py
- **作用**: 测试消息模板的正确性和可用性
- **使用方法**: 
  ```bash
  python -m pytest test_message_templates.py
  ```
- **主要功能**:
  - 验证消息模板的格式和内容
  - 测试模板的渲染功能

#### test_message_template_integration.py
- **作用**: 测试消息模板在集成环境中的功能
- **使用方法**: 
  ```bash
  python -m pytest test_message_template_integration.py
  ```
- **主要功能**:
  - 验证消息模板与其他组件的集成
  - 测试模板在实际业务场景中的应用

### 6. 阈值配置测试

#### test_threshold_parameters.py
- **作用**: 测试阈值参数的正确配置和使用
- **使用方法**: 
  ```bash
  python -m pytest test_threshold_parameters.py
  ```
- **主要功能**:
  - 验证各种阈值参数的配置
  - 测试阈值在决策中的作用

#### test_threshold_integration.py
- **作用**: 测试阈值配置在集成环境中的功能
- **使用方法**: 
  ```bash
  python -m pytest test_threshold_integration.py
  ```
- **主要功能**:
  - 验证阈值配置与其他系统的集成
  - 测试阈值在实际业务流程中的应用

### 7. 统一决策引擎测试

#### test_unified_decision_engine.py
- **作用**: 测试统一决策引擎的核心功能
- **使用方法**: 
  ```bash
  python -m pytest test_unified_decision_engine.py
  ```
- **主要功能**:
  - 验证决策引擎的决策逻辑
  - 测试不同场景下的决策准确性

#### test_unified_decision_engine_complete.py
- **作用**: 对统一决策引擎进行完整测试
- **使用方法**: 
  ```bash
  python -m pytest test_unified_decision_engine_complete.py
  ```
- **主要功能**:
  - 全面测试决策引擎的各项功能
  - 验证复杂场景下的决策能力

## 配置测试 (tests/config/)

### test_env_override.py
- **作用**: 测试环境变量覆盖功能
- **使用方法**: 
  ```bash
  python -m pytest config/test_env_override.py
  ```

### test_merge_chain_comprehensive.py
- **作用**: 配置合并链综合测试
- **使用方法**: 
  ```bash
  python -m pytest config/test_merge_chain_comprehensive.py
  ```

### test_merge_correctness.py
- **作用**: 配置合并正确性测试
- **使用方法**: 
  ```bash
  python -m pytest config/test_merge_correctness.py
  ```

### test_override_chain.py
- **作用**: 覆盖链测试
- **使用方法**: 
  ```bash
  python -m pytest config/test_override_chain.py
  ```

### test_sensitive_masking.py
- **作用**: 敏感信息屏蔽测试
- **使用方法**: 
  ```bash
  python -m pytest config/test_sensitive_masking.py
  ```

### test_stability_simulation.py
- **作用**: 稳定性模拟测试
- **使用方法**: 
  ```bash
  python -m pytest config/test_stability_simulation.py
  ```

## 运行所有测试

要运行所有测试，可以使用以下命令:

```bash
# 运行所有测试
python -m pytest

# 运行特定测试文件
python -m pytest test_file_name.py

# 运行测试并显示详细输出
python -m pytest -v

# 运行测试并生成覆盖率报告
python -m pytest --cov=backend
```